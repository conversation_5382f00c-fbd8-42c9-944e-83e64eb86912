
# 如果不采用--split-per-abi，已设置abi只会打 'arm64-v8a' 和 'armeabi-v7a'架构，其余不打到apk内
#flutter build apk --obfuscate --split-debug-info=./obfuscate
#apksigner sign --ks /Users/<USER>/Documents/mooeli_release/moili.keystore --ks-pass pass:ChohoTech  build/app/outputs/apk/release/app-release.apk
#apksigner verify build/app/outputs/apk/release/app-release.apk

BASEDIR=$(dirname "$0")

KEYSTORE_DIR="${BASEDIR}/../resources/moili.keystore"    # moili.keystore路径
APK_ARM32_DIR="${BASEDIR}/../build/app/outputs/apk/release/app-armeabi-v7a-release.apk"
APK_ARM64_DIR="${BASEDIR}/../build/app/outputs/apk/release/app-arm64-v8a-release.apk"
#APK_X8664_DIR="${BASEDIR}/../build/app/outputs/apk/release/app-x86_64-release.apk"
APK_COMMON_DIR="${BASEDIR}/../build/app/outputs/apk/release/app-universal-release.apk"

flutter build apk --release --obfuscate --split-debug-info=./obfuscate  #--split-per-abi
#fluter 签名有问题，在某些华为上无法使用，这里改为手动签名
#要用到apksigner,android-sdk/build-tools/apksigner 配置全局path:
#export PATH=$ANDROID_HOME/build-tools/33.0.2:$PATH
apksigner sign --ks "${KEYSTORE_DIR}" --ks-pass pass:ChohoTech  "${APK_ARM32_DIR}"
apksigner sign --ks "${KEYSTORE_DIR}" --ks-pass pass:ChohoTech  "${APK_ARM64_DIR}"
#apksigner sign --ks "${KEYSTORE_DIR}" --ks-pass pass:ChohoTech  "${APK_X8664_DIR}"
apksigner sign --ks "${KEYSTORE_DIR}" --ks-pass pass:ChohoTech  "${APK_COMMON_DIR}"
# 验证签名
#apksigner verify --verbose "${APK_ARM32_DIR}"
apksigner verify --verbose "${APK_ARM64_DIR}"
#apksigner verify --verbose "${APK_X8664_DIR}"

open build/app/outputs/apk/release
#open build/app/outputs/flutter-apk
