import sys
from pathlib import Path

import numpy as np

salt_dict = [
    [88, 1, 109, 253, 126, 113, 241, 98, 204, 16, 220, 27, 235, 122, 74, 229, 165, 219, 117, 34,
     185, 4, 12, 71, 150, 245, 183, 11, 21, 219, 47, 93, 209, 48, 10, 147, 195, 112, 105, 57, 230,
     170, 132, 197, 199, 189, 127, 164, 85, 64, 78, 65, 201, 167, 32, 147, 96, 254, 178, 100, 225,
     37, 109, 185, 185, 44, 42, 248, 117, 255, 180, 94, 230, 167, 148, 192, 196, 131, 71, 247, 13,
     208, 155, 76, 216, 3, 197, 222, 146, 169, 202, 91, 249, 69, 187, 250, 117, 86, 183, 124, 223,
     65, 8, 128, 181, 92, 209, 186, 73, 219, 236, 235, 130, 175, 20, 143, 80, 200, 157, 124, 12, 70,
     194, 73, 140, 22, 215, 184, 37, 129, 22, 12, 170, 39, 0, 32, 78, 247, 19, 52, 100, 234, 89, 44,
     158, 237, 82, 112, 24, 154, 62, 203, 217, 153, 1, 11, 239, 183, 245, 11, 190, 52, 210, 208,
     111, 109, 48, 79, 107, 220, 201, 86, 133, 18, 174, 4, 121, 214, 8, 3, 228, 230, 62, 33, 170,
     201, 153, 78, 157, 47, 150, 69, 113, 154, 116, 198, 238, 198, 113, 134, 223, 122, 218, 33, 171,
     230, 65, 25, 208, 248, 199, 169, 89, 117, 138, 5, 239, 120, 188, 127, 174, 57, 87, 19, 160,
     127, 38, 184, 12, 23, 239, 23, 230, 150, 42, 175, 10, 55, 165, 46, 134, 107, 130, 44, 59, 31,
     151, 30, 80, 153, 70, 85, 38, 114, 205, 51],
    [82, 51, 118, 28, 13, 50, 123, 240, 145, 20, 203, 79, 186, 197, 205, 92, 99, 155, 134, 29, 58,
     212, 20, 211, 40, 80, 190, 147, 10, 62, 226, 241, 23, 71, 217, 147, 129, 156, 144, 218, 101,
     72, 185, 98, 131, 180, 125, 217, 89, 188, 29, 189, 237, 254, 27, 161, 180, 61, 47, 23, 85, 75,
     57, 225, 138, 145, 131, 244, 58, 169, 103, 228, 76, 204, 80, 125, 88, 189, 152, 43, 193, 139,
     205, 17, 207, 147, 25, 235, 233, 122, 72, 46, 95, 95, 139, 93, 237, 181, 37, 51, 241, 224, 254,
     234, 108, 84, 150, 219, 31, 105, 81, 13, 26, 248, 146, 90, 231, 155, 229, 194, 81, 112, 29, 64,
     243, 215, 246, 129, 206, 126, 188, 145, 180, 115, 217, 122, 135, 12, 78, 167, 111, 115, 187,
     162, 191, 175, 103, 114, 26, 178, 213, 52, 242, 24, 154, 42, 126, 91, 106, 8, 29, 206, 232, 81,
     226, 216, 174, 63, 35, 137, 6, 130, 151, 38, 59, 155, 127, 113, 31, 164, 164, 62, 145, 210, 70,
     242, 240, 87, 238, 40, 200, 97, 235, 114, 38, 184, 120, 147, 82, 54, 186, 20, 249, 60, 30, 79,
     46, 174, 106, 2, 229, 112, 46, 44, 201, 19, 205, 210, 99, 36, 94, 217, 139, 47, 236, 12, 44,
     39, 147, 179, 208, 195, 186, 229, 3, 117, 112, 127, 136, 112, 248, 218, 103, 148, 4, 101, 36,
     73, 211, 114, 56, 41, 104, 136, 72],
    [244, 221, 142, 116, 155, 200, 38, 24, 255, 170, 190, 190, 229, 240, 72, 94, 91, 16, 23, 154,
     135, 84, 77, 128, 125, 4, 4, 150, 206, 79, 122, 243, 32, 63, 233, 182, 82, 85, 127, 37, 8, 114,
     107, 112, 229, 81, 51, 76, 247, 179, 3, 234, 83, 246, 75, 136, 132, 130, 250, 136, 227, 101,
     242, 54, 106, 225, 225, 61, 109, 174, 72, 38, 238, 158, 97, 134, 122, 98, 85, 88, 100, 33, 210,
     169, 175, 183, 99, 255, 97, 40, 34, 62, 10, 169, 166, 163, 33, 138, 21, 177, 2, 244, 49, 106,
     162, 224, 205, 118, 245, 253, 11, 39, 117, 76, 82, 14, 111, 237, 5, 246, 77, 200, 56, 7, 232,
     21, 131, 4, 154, 205, 252, 174, 154, 71, 246, 18, 235, 14, 180, 86, 194, 229, 234, 163, 89, 91,
     186, 22, 51, 223, 79, 189, 227, 212, 236, 219, 94, 90, 62, 14, 46, 84, 116, 31, 85, 137, 188,
     225, 226, 130, 10, 42, 200, 202, 214, 47, 225, 100, 243, 168, 11, 45, 118, 130, 45, 6, 42, 85,
     186, 85, 143, 233, 203, 223, 140, 197, 7, 123, 181, 175, 236, 98, 110, 37, 51, 64, 163, 246,
     214, 175, 140, 131, 119, 114, 127, 51, 158, 211, 81, 253, 63, 236, 145, 43, 187, 218, 235, 155,
     202, 79, 240, 232, 213, 23, 55, 223, 33, 239, 228, 58, 71, 173, 13, 201, 241, 118, 109, 3, 229,
     217, 17, 209, 181, 159, 77],
    [233, 158, 226, 65, 5, 82, 251, 210, 36, 101, 175, 131, 194, 143, 19, 142, 183, 187, 41, 25, 60,
     187, 152, 97, 211, 126, 46, 28, 18, 182, 9, 133, 213, 244, 166, 68, 124, 27, 203, 193, 82, 53,
     249, 94, 63, 123, 230, 128, 187, 133, 139, 42, 178, 211, 29, 201, 167, 145, 205, 214, 173, 66,
     117, 92, 153, 62, 45, 173, 58, 41, 147, 232, 238, 209, 27, 86, 95, 161, 51, 162, 75, 70, 233,
     100, 44, 141, 200, 211, 140, 145, 229, 208, 103, 138, 4, 194, 105, 43, 234, 160, 13, 153, 51,
     114, 51, 203, 101, 24, 147, 167, 59, 139, 67, 107, 191, 246, 15, 252, 212, 228, 169, 151, 107,
     170, 228, 229, 24, 71, 75, 147, 160, 195, 86, 118, 68, 123, 10, 66, 201, 193, 67, 15, 214, 184,
     166, 59, 44, 239, 93, 144, 189, 170, 192, 118, 102, 51, 46, 21, 228, 20, 83, 9, 224, 230, 196,
     21, 26, 30, 19, 173, 152, 244, 23, 139, 76, 85, 214, 229, 27, 39, 147, 74, 56, 1, 243, 126,
     192, 253, 74, 101, 9, 2, 34, 181, 146, 71, 203, 65, 51, 88, 9, 136, 12, 86, 18, 6, 165, 175,
     18, 1, 1, 142, 233, 242, 149, 24, 78, 144, 142, 9, 103, 188, 252, 233, 89, 76, 163, 163, 76,
     17, 196, 167, 79, 250, 76, 21, 107, 154, 67, 127, 113, 98, 110, 145, 58, 222, 78, 223, 19, 1,
     1, 100, 64, 41, 69],
    [222, 252, 13, 157, 126, 41, 100, 192, 220, 87, 22, 246, 65, 7, 12, 184, 74, 179, 229, 0, 206,
     221, 69, 109, 203, 53, 166, 241, 213, 17, 43, 42, 229, 161, 224, 7, 61, 188, 156, 83, 33, 234,
     18, 189, 107, 242, 60, 208, 216, 141, 22, 243, 120, 160, 41, 127, 231, 213, 123, 74, 212, 190,
     8, 227, 143, 251, 42, 0, 3, 42, 163, 183, 121, 246, 44, 46, 85, 125, 139, 229, 71, 123, 178,
     49, 237, 193, 216, 124, 19, 148, 79, 164, 87, 206, 72, 118, 224, 202, 235, 230, 43, 68, 149,
     201, 18, 203, 217, 148, 3, 154, 134, 52, 88, 93, 254, 50, 71, 245, 144, 12, 182, 103, 132, 214,
     195, 172, 225, 46, 126, 244, 103, 158, 133, 16, 254, 243, 172, 72, 142, 132, 198, 67, 140, 166,
     220, 192, 79, 51, 215, 107, 124, 157, 252, 180, 239, 112, 73, 63, 240, 107, 180, 84, 128, 165,
     59, 20, 101, 9, 111, 42, 245, 28, 209, 209, 122, 54, 25, 90, 121, 30, 151, 164, 34, 86, 172,
     236, 219, 160, 20, 148, 238, 184, 92, 13, 58, 187, 8, 62, 62, 149, 125, 60, 123, 254, 84, 14,
     59, 88, 137, 34, 231, 149, 160, 244, 134, 21, 49, 232, 221, 111, 110, 35, 20, 35, 63, 200, 120,
     8, 191, 114, 191, 7, 63, 93, 65, 241, 78, 75, 225, 242, 90, 219, 148, 23, 210, 214, 141, 31,
     102, 63, 3, 27, 243, 231, 167],
    [40, 121, 37, 199, 118, 110, 195, 152, 136, 115, 61, 60, 56, 185, 80, 179, 48, 131, 19, 126,
     201, 142, 33, 210, 115, 92, 69, 121, 176, 44, 217, 195, 163, 40, 229, 68, 127, 226, 124, 126,
     215, 43, 9, 8, 202, 80, 2, 220, 229, 160, 16, 91, 175, 162, 26, 86, 213, 202, 103, 180, 52,
     214, 176, 205, 3, 138, 112, 8, 137, 137, 153, 133, 176, 175, 210, 16, 140, 98, 232, 224, 227,
     122, 222, 46, 182, 113, 231, 56, 229, 234, 37, 172, 108, 171, 212, 36, 138, 8, 154, 32, 71,
     234, 154, 254, 17, 206, 224, 192, 166, 100, 169, 3, 255, 22, 39, 135, 29, 131, 214, 180, 180,
     222, 174, 51, 181, 236, 220, 69, 111, 237, 234, 30, 129, 243, 199, 176, 7, 129, 8, 243, 174,
     86, 5, 35, 34, 163, 87, 41, 145, 162, 3, 81, 10, 155, 137, 97, 19, 51, 190, 70, 219, 98, 96,
     73, 11, 35, 129, 214, 227, 179, 76, 40, 49, 43, 245, 174, 194, 252, 171, 43, 59, 42, 162, 56,
     219, 163, 229, 174, 16, 36, 128, 248, 231, 100, 247, 226, 126, 63, 204, 147, 169, 92, 4, 204,
     53, 53, 77, 146, 215, 233, 68, 18, 120, 199, 240, 76, 124, 158, 160, 147, 16, 151, 176, 122, 4,
     255, 188, 230, 177, 225, 150, 190, 80, 237, 57, 119, 51, 63, 89, 205, 63, 97, 74, 176, 38, 231,
     179, 216, 181, 202, 82, 33, 109, 243, 93],
    [64, 80, 172, 93, 21, 145, 167, 49, 181, 98, 244, 83, 50, 225, 209, 183, 0, 117, 126, 48, 192,
     155, 26, 229, 7, 147, 254, 158, 154, 174, 57, 76, 6, 169, 31, 89, 49, 97, 48, 101, 211, 250,
     194, 233, 203, 45, 215, 164, 60, 124, 175, 255, 19, 248, 154, 74, 30, 141, 58, 181, 192, 167,
     103, 165, 183, 225, 18, 100, 186, 203, 29, 18, 80, 218, 233, 176, 206, 52, 113, 126, 168, 90,
     252, 65, 174, 79, 117, 76, 48, 76, 163, 110, 247, 45, 74, 173, 187, 150, 12, 154, 185, 187,
     183, 184, 237, 252, 160, 187, 76, 9, 76, 47, 9, 46, 58, 135, 249, 198, 224, 61, 72, 30, 241,
     182, 253, 171, 99, 222, 3, 10, 224, 177, 223, 92, 70, 252, 148, 208, 2, 197, 37, 243, 20, 145,
     88, 58, 141, 254, 199, 90, 170, 223, 25, 91, 127, 25, 131, 174, 156, 185, 206, 32, 81, 59, 136,
     227, 37, 18, 202, 239, 69, 53, 34, 149, 192, 180, 22, 89, 248, 5, 239, 172, 94, 21, 158, 96,
     237, 144, 167, 251, 31, 207, 8, 60, 40, 20, 104, 80, 51, 114, 58, 26, 34, 83, 1, 216, 112, 36,
     36, 117, 75, 188, 142, 129, 59, 39, 17, 33, 2, 150, 66, 22, 177, 116, 47, 36, 56, 172, 171,
     194, 129, 129, 61, 111, 12, 152, 104, 112, 236, 146, 114, 218, 175, 198, 214, 222, 25, 127,
     198, 125, 140, 182, 57, 153, 182],
    [16, 113, 111, 159, 182, 112, 108, 9, 223, 188, 90, 130, 116, 46, 149, 133, 79, 51, 153, 80,
     108, 172, 32, 236, 161, 83, 57, 120, 124, 3, 139, 189, 6, 127, 163, 99, 191, 62, 206, 193, 197,
     161, 251, 136, 146, 194, 110, 197, 112, 178, 9, 202, 72, 42, 222, 80, 140, 166, 195, 170, 173,
     168, 72, 112, 98, 44, 157, 154, 238, 19, 94, 133, 118, 156, 10, 122, 138, 224, 238, 148, 47,
     85, 222, 247, 223, 21, 33, 15, 142, 119, 248, 20, 86, 123, 30, 209, 20, 180, 135, 114, 67, 233,
     175, 8, 187, 182, 58, 32, 0, 6, 18, 138, 196, 14, 245, 168, 221, 179, 228, 75, 60, 224, 71, 56,
     37, 245, 129, 38, 238, 188, 32, 13, 35, 214, 115, 111, 14, 173, 9, 141, 77, 191, 240, 37, 190,
     253, 208, 12, 248, 97, 192, 214, 177, 126, 140, 196, 86, 175, 105, 184, 192, 82, 22, 192, 233,
     53, 178, 93, 87, 223, 146, 43, 190, 138, 85, 116, 156, 231, 41, 182, 14, 231, 91, 228, 80, 155,
     15, 23, 175, 104, 28, 24, 146, 216, 156, 211, 104, 125, 249, 246, 60, 29, 115, 113, 142, 202,
     141, 139, 137, 18, 46, 250, 23, 224, 127, 29, 255, 62, 85, 93, 43, 199, 157, 62, 160, 35, 131,
     10, 139, 7, 244, 86, 240, 241, 232, 220, 41, 224, 183, 67, 25, 6, 206, 34, 10, 36, 187, 87, 58,
     63, 27, 162, 165, 74, 145],
    [184, 231, 96, 142, 14, 47, 37, 87, 58, 179, 72, 30, 56, 132, 102, 11, 90, 215, 109, 186, 82,
     39, 21, 90, 202, 40, 79, 123, 164, 12, 131, 110, 81, 44, 228, 175, 209, 226, 33, 32, 115, 128,
     114, 103, 91, 135, 156, 35, 171, 72, 6, 78, 215, 8, 147, 197, 88, 187, 83, 197, 92, 140, 44,
     245, 169, 240, 73, 184, 145, 176, 42, 78, 99, 5, 52, 203, 16, 161, 20, 204, 46, 110, 85, 148,
     252, 147, 216, 161, 159, 171, 89, 70, 67, 80, 210, 163, 59, 180, 113, 132, 240, 157, 164, 157,
     167, 61, 139, 179, 222, 204, 13, 99, 87, 240, 172, 234, 184, 98, 135, 24, 154, 101, 197, 148,
     240, 125, 206, 164, 202, 74, 21, 236, 235, 80, 108, 148, 38, 106, 179, 244, 175, 2, 45, 141,
     31, 49, 218, 226, 183, 185, 145, 169, 43, 197, 22, 232, 37, 197, 10, 142, 47, 109, 78, 194,
     125, 130, 143, 140, 7, 113, 228, 52, 121, 180, 116, 172, 208, 57, 155, 122, 63, 95, 24, 125,
     15, 253, 162, 15, 194, 132, 224, 116, 167, 175, 37, 51, 158, 54, 221, 87, 139, 37, 241, 228,
     197, 6, 245, 63, 249, 236, 6, 109, 134, 197, 105, 239, 191, 133, 16, 12, 229, 12, 7, 98, 221,
     218, 51, 55, 81, 158, 57, 242, 113, 203, 96, 231, 79, 199, 197, 114, 205, 155, 158, 73, 25,
     147, 102, 181, 189, 147, 110, 108, 202, 229, 238]
]


def encrypt_one_pass(plain: bytearray, salt):
    array_len = len(plain) - 8
    for i in range(8, array_len + 8):
        xor_loc = (salt[plain[i % 8]] + array_len + i) % 256
        plain[i] = plain[i] ^ salt[xor_loc]
    return plain


def encrypt(plain: bytearray, slat_id=0):
    salt = salt_dict[slat_id]

    mt19937 = np.random.RandomState(0)
    plain = bytearray(mt19937.randint(0, 255, 8).astype("uint8").tobytes()) + plain

    cipher = encrypt_one_pass(plain, salt)
    cipher.reverse()
    cipher = encrypt_one_pass(cipher, salt)
    return cipher


def main():
    input_path = Path(sys.argv[1])
    output_path = input_path.parent / (input_path.stem + ".ml")

    if str(input_path).endswith(".ml"):
        raise "模型已加密！"

    with open(input_path, "rb") as f:
        nodes_binary_str = bytearray(f.read())
        nodes_binary_str = encrypt(nodes_binary_str)

    with open(output_path, 'wb') as f:
        f.write(nodes_binary_str)
        print('Saved to %s' % output_path.absolute())


if __name__ == "__main__":
    main()
