// import 'dart:io';
//
// import 'package:camera/camera.dart';
// import 'package:image/image.dart' as imageLib;
// import 'package:path_provider/path_provider.dart';

class HImageUtils {
  // static dynamic cameraImg2Uint8ListBringWH(CameraImage cameraImage) {
  //   imageLib.Image image = convertCameraImage(cameraImage);
  //   return {
  //     "Uint8List": image.getBytes(format: imageLib.Format.rgb),
  //     "width": image.width,
  //     "height": image.height,
  //   };
  // }
  //
  // //此函数当时为了压缩图片，但是未测试过，直接使用了ffi的opencv完成图片压缩和resize
  // static Future<void> copyAndResizeImage(String imgPath,
  //     {int width = 0, int height = 0}) async {
  //   int tWidth = width == 0 ? 600 : width;
  //   int tHeight = height == 0 ? (720 * 600 / 1280).round() : height;
  //   imageLib.Image newCopy = imageLib.copyResize(
  //       imageLib.Image.fromBytes(1280, 720, File(imgPath).readAsBytesSync()),
  //       width: tWidth,
  //       height: tHeight);
  //
  //   final fileOnDevice = File(imgPath);
  //   fileOnDevice.writeAsBytes(newCopy.getBytes(), flush: true);
  // }
  //
  // static Future<String> saveImage(imageLib.Image image,
  //     {String picName = "", int rotateAngle = 0}) async {
  //   List<int> jpegBytes = imageLib.JpegEncoder().encodeImage(image);
  //   return _saveImgLogic(jpegBytes, picName);
  // }
  //
  // static Future<String> saveImageByBytes(dynamic bytes,
  //     {String picName = "", int rotateAngle = 0}) async {
  //   return _saveImgLogic(bytes, picName, rotateAngle: rotateAngle);
  // }
  //
  // static Future<String> _saveImgLogic(dynamic bytes, String picName,
  //     {dynamic tempDir, int rotateAngle = 0}) async {
  //   if (picName == "") {
  //     picName = DateTime.now().millisecondsSinceEpoch.toString();
  //   }
  //   final appDir = tempDir ?? await getTemporaryDirectory();
  //   final appPath = appDir.path;
  //   final fileOnDevice = File('$appPath/$picName.jpg');
  //   await fileOnDevice.writeAsBytes(bytes, flush: true);
  //   String imgPath = "$appPath/$picName.jpg";
  //   return imgPath;
  // }
  //
  // static Future<String> saveBytesToFile(dynamic bytes,
  //     {dynamic tempDir, String fileName = ""}) async {
  //   if (fileName == "") {
  //     fileName = DateTime.now().millisecondsSinceEpoch.toString();
  //   }
  //   final appDir = tempDir ?? await getTemporaryDirectory();
  //   final appPath = appDir.path;
  //   final fileOnDevice = File('$appPath/$fileName');
  //   await fileOnDevice.writeAsBytes(bytes, flush: true);
  //   String filePath = "$appPath/$fileName";
  //   return filePath;
  // }
  //
  // static Future<String> saveImageByFfiTempDir(
  //     Directory tempDir, CameraImage cameraImage, int rotateAngle,
  //     {String picName = ""}) async {
  //   imageLib.Image image = convertCameraImage(cameraImage);
  //   if (rotateAngle != 0) {
  //     image = imageLib.copyRotate(image, rotateAngle);
  //   }
  //   if (picName == "") {
  //     picName = DateTime.now().millisecondsSinceEpoch.toString();
  //   }
  //   List<int> jpegBytes = imageLib.JpegEncoder().encodeImage(image);
  //   return _saveImgLogic(jpegBytes, picName, tempDir: tempDir);
  // }
  //
  // /// Converts a [CameraImage] in YUV420 format to [imageLib.Image] in RGB format
  // static imageLib.Image convertCameraImage(CameraImage cameraImage) {
  //   if (cameraImage.format.group == ImageFormatGroup.yuv420) {
  //     return convertYUV420ToImage(cameraImage);
  //   } else if (cameraImage.format.group == ImageFormatGroup.bgra8888) {
  //     return convertBGRA8888ToImage(cameraImage);
  //   } else {
  //     return imageLib.Image(0, 0);
  //   }
  // }
  //
  // /// Converts a [CameraImage] in BGRA888 format to [imageLib.Image] in RGB format
  // static imageLib.Image convertBGRA8888ToImage(CameraImage cameraImage) {
  //   imageLib.Image img = imageLib.Image.fromBytes(
  //       cameraImage.planes[0].width ?? 0,
  //       cameraImage.planes[0].height ?? 0,
  //       cameraImage.planes[0].bytes,
  //       format: imageLib.Format.bgra);
  //   return img;
  // }
  //
  // /// Converts a [CameraImage] in YUV420 format to [imageLib.Image] in RGB format
  // static imageLib.Image convertYUV420ToImage(CameraImage cameraImage) {
  //   final int width = cameraImage.width;
  //   final int height = cameraImage.height;
  //
  //   final int uvRowStride = cameraImage.planes[1].bytesPerRow;
  //   final int uvPixelStride = cameraImage.planes[1].bytesPerPixel ?? 0;
  //   final int yRowStride = cameraImage.planes[0].bytesPerRow;
  //
  //   final image = imageLib.Image(width, height);
  //
  //   for (int w = 0; w < width; w++) {
  //     for (int h = 0; h < height; h++) {
  //       final int uvIndex =
  //           uvPixelStride * (w / 2).floor() + uvRowStride * (h / 2).floor();
  //       final int index = h * width + w;
  //       final int yIndex = h * yRowStride + w;
  //
  //       final y = cameraImage.planes[0].bytes[yIndex];
  //       final u = cameraImage.planes[1].bytes[uvIndex];
  //       final v = cameraImage.planes[2].bytes[uvIndex];
  //
  //       image.data[index] = yuv2rgb(y, u, v);
  //     }
  //   }
  //
  //
  //
  //   return image;
  // }
  //
  // /// Convert a single YUV pixel to RGB
  // static int yuv2rgb(int y, int u, int v) {
  //   // Convert yuv pixel to rgb
  //   int r = (y + v * 1436 / 1024 - 179).round();
  //   int g = (y - u * 46549 / 131072 + 44 - v * 93604 / 131072 + 91).round();
  //   int b = (y + u * 1814 / 1024 - 227).round();
  //
  //   // Clipping RGB values to be inside boundaries [ 0 , 255 ]
  //   r = r.clamp(0, 255);
  //   g = g.clamp(0, 255);
  //   b = b.clamp(0, 255);
  //
  //   return 0xff000000 |
  //       ((b << 16) & 0xff0000) |
  //       ((g << 8) & 0xff00) |
  //       (r & 0xff);
  // }
}
