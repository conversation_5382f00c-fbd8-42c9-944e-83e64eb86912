import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';

class OssUtil {
  //验证文本域
  static const String _policyText =
      '{"expiration": "2069-05-22T03:15:00.000Z","conditions": [["content-length-range", 0, 1048576000]]}'; //UTC时间+8=北京时间

  //进行utf8编码
  // ignore: non_constant_identifier_names
  static final List<int> _policyText_utf8 = utf8.encode(_policyText);

  //进行base64编码
  static String policy = base64.encode(_policyText_utf8);

  static final OssUtil _user = OssUtil._internal();

  factory OssUtil() {
    return _user;
  }

  static OssUtil get instance => _user;

  OssUtil._internal();

  //进行base64编码
  String getPolicyByExpiration(String expiration) {
    String policyText = '{"expiration":"$expiration","conditions":[["content-length-range",0,${1024 * 1024 * 100}]]}';
    return base64.encode(utf8.encode(policyText));
  }

  /*
  *获取signature签名参数
  */
  String getSignature(String accessKeySecret, String policyUtf8) {
    //进行utf8 编码
    List<int> accessKeySecretUtf8 = utf8.encode(accessKeySecret);
    //通过hmac,使用sha1进行加密
    List<int> signaturePre = Hmac(sha1, accessKeySecretUtf8).convert(utf8.encode(policyUtf8)).bytes;
    //最后一步，将上述所得进行base64 编码
    String signature = base64.encode(signaturePre);
    return signature;
  }

  // ignore: slash_for_doc_comments
  /**
   * 生成上传上传图片的名称 ,获得的格式:photo/20171027175940_oCiobK
   * 可以定义上传的路径uploadPath(Oss中保存文件夹的名称)
   * @param uploadPath 上传的路径 如：/photo
   * @return photo/20171027175940_oCiobK
   */
  String getImageUploadName(String uploadPath, String filePath) {
    String imageMame = "";
    var timestamp = DateTime.now().millisecondsSinceEpoch;
    imageMame = "${timestamp}_${getRandom(6)}";
    if (uploadPath != null && uploadPath.isNotEmpty) {
      imageMame = "$uploadPath/$imageMame";
    }
    String imageType = filePath.substring(filePath.lastIndexOf("."), filePath.length);
    return imageMame + imageType;
  }

  String getImageName(String filePath) {
    String imageMame = "";
    var timestamp = DateTime.now().millisecondsSinceEpoch;
    imageMame = "${timestamp}_${getRandom(6)}";
    String imageType = filePath.substring(filePath.lastIndexOf("."), filePath.length);
    return imageMame + imageType;
  }

  /*
  * 生成固定长度的随机字符串
  * */
  String getRandom(int num) {
    String alphabet = 'qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM';
    String left = '';
    for (var i = 0; i < num; i++) {
//    right = right + (min + (Random().nextInt(max - min))).toString();
      left = left + alphabet[Random().nextInt(alphabet.length)];
    }
    return left;
  }

  /*
  * 根据图片本地路径获取图片名称
  * */
  String? getImageNameByPath(String filePath) {
    // ignore: null_aware_before_operator
    return filePath.substring(filePath.lastIndexOf("/") + 1, filePath.length);
  }
}
