import 'dart:async';

import 'package:flutter/services.dart';
import 'package:mooeli/utils/common_utils.dart';

MethodChannel androidMethod = const MethodChannel("com.chohotech.box/call_android");

/// 获取所有 U 盘的名字和路径
Future<List<Map<String, String>>> getUsbDevices() async {
  try {
    final List<dynamic> devices = await androidMethod.invokeMethod('getUsbDevices');
    final List<Map<String, String>> convertedDevices = devices.map((device) {
      if (device is Map) {
        return device.map((key, value) => MapEntry(key.toString(), value.toString()));
      } else {
        throw Exception("Invalid device type");
      }
    }).toList();
    return convertedDevices;
  } on PlatformException catch (e) {
    logger("获取 U 盘信息失败: ${e.message}");
    return [];
  }
}

/// 获取设备序列号
Future<String> getDeviceSn() async {
  try {
    final String deviceSn = await androidMethod.invokeMethod('getDeviceSn');
    return deviceSn;
  } on PlatformException catch (e) {
    logger("getDeviceSn 获取设备序列号失败: ${e.message}");
    return "";
  }
}
