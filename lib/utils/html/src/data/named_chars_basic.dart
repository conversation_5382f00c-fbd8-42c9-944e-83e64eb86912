// This is auto-generated from tool/generate_map.dart.
library html_unescape.named_chars_basic;

const List<String> keys = <String>[
  '&DiacriticalGrave;',
  '&NonBreakingSpace;',
  '&DiacriticalAcute;',
  '&VerticalLine;',
  '&centerdot;',
  '&DoubleDot;',
  '&PlusMinus;',
  '&CenterDot;',
  '&UnderBar;',
  '&circledR;',
  '&Cedilla;',
  '&NewLine;',
  '&brvbar;',
  '&percnt;',
  '&midast;',
  '&lbrace;',
  '&verbar;',
  '&period;',
  '&rbrace;',
  '&yacute;',
  '&curren;',
  '&uacute;',
  '&otilde;',
  '&equals;',
  '&ugrave;',
  '&commat;',
  '&oslash;',
  '&plusmn;',
  '&divide;',
  '&lbrack;',
  '&dollar;',
  '&middot;',
  '&rbrack;',
  '&Oacute;',
  '&lowbar;',
  '&frac14;',
  '&frac12;',
  '&frac34;',
  '&iquest;',
  '&Agrave;',
  '&Aacute;',
  '&Atilde;',
  '&oacute;',
  '&Egrave;',
  '&Eacute;',
  '&Igrave;',
  '&Iacute;',
  '&Ntilde;',
  '&Ograve;',
  '&Otilde;',
  '&Oslash;',
  '&Ugrave;',
  '&Uacute;',
  '&Yacute;',
  '&agrave;',
  '&aacute;',
  '&atilde;',
  '&ccedil;',
  '&egrave;',
  '&eacute;',
  '&igrave;',
  '&iacute;',
  '&ntilde;',
  '&ograve;',
  '&Ccedil;',
  '&ecirc;',
  '&acute;',
  '&curren',
  '&ocirc;',
  '&brvbar',
  '&oacute',
  '&pound;',
  '&ograve',
  '&ugrave',
  '&ntilde',
  '&colon;',
  '&laquo;',
  '&icirc;',
  '&oslash',
  '&iacute',
  '&thorn;',
  '&yacute',
  '&divide',
  '&Egrave',
  '&igrave',
  '&strns;',
  '&iexcl;',
  '&plusmn',
  '&eacute',
  '&szlig;',
  '&egrave',
  '&micro;',
  '&ccedil',
  '&middot',
  '&cedil;',
  '&ucirc;',
  '&aelig;',
  '&comma;',
  '&raquo;',
  '&uacute',
  '&frac14',
  '&frac12',
  '&quest;',
  '&frac34',
  '&iquest',
  '&Agrave',
  '&Aacute',
  '&Acirc;',
  '&Atilde',
  '&Aring;',
  '&angst;',
  '&AElig;',
  '&aring;',
  '&THORN;',
  '&Eacute',
  '&Ecirc;',
  '&Igrave',
  '&atilde',
  '&Iacute',
  '&Icirc;',
  '&acirc;',
  '&grave;',
  '&Ntilde',
  '&Ograve',
  '&aacute',
  '&Oacute',
  '&Ocirc;',
  '&fjlig;',
  '&Otilde',
  '&agrave',
  '&times;',
  '&otilde',
  '&Oslash',
  '&Ugrave',
  '&Uacute',
  '&Ucirc;',
  '&Yacute',
  '&Ccedil',
  '&macr;',
  '&sup2;',
  '&lsqb;',
  '&semi;',
  '&sup3;',
  '&Acirc',
  '&bsol;',
  '&plus;',
  '&Auml;',
  '&aring',
  '&ocirc',
  '&acute',
  '&Aring',
  '&sect;',
  '&AElig',
  '&copy;',
  '&micro',
  '&rsqb;',
  '&times',
  '&yuml;',
  '&para;',
  '&cent;',
  '&Ecirc',
  '&Euml;',
  '&auml;',
  '&thorn',
  '&nbsp;',
  '&uuml;',
  '&ouml;',
  '&ucirc',
  '&Icirc',
  '&Iuml;',
  '&rcub;',
  '&acirc',
  '&COPY;',
  '&cedil',
  '&iexcl',
  '&apos;',
  '&sup1;',
  '&ordf;',
  '&ordm;',
  '&iuml;',
  '&Ocirc',
  '&pound',
  '&raquo',
  '&Ouml;',
  '&laquo',
  '&euml;',
  '&nvgt;',
  '&lpar;',
  '&QUOT;',
  '&lcub;',
  '&half;',
  '&rpar;',
  '&icirc',
  '&ecirc',
  '&Ucirc',
  '&Uuml;',
  '&szlig',
  '&vert;',
  '&excl;',
  '&nvlt;',
  '&THORN',
  '&quot;',
  '&aelig',
  '&bne;',
  '&Ouml',
  '&quot',
  '&yuml',
  '&ouml',
  '&ETH;',
  '&Iuml',
  '&AMP;',
  '&Euml',
  '&auml',
  '&amp;',
  '&Auml',
  '&sup1',
  '&ordm',
  '&ast;',
  '&para',
  '&nbsp',
  '&num;',
  '&sup3',
  '&sup2',
  '&shy;',
  '&uuml',
  '&div;',
  '&euml',
  '&deg;',
  '&macr',
  '&REG;',
  '&reg;',
  '&Uuml',
  '&not;',
  '&ordf',
  '&iuml',
  '&eth;',
  '&COPY',
  '&copy',
  '&Dot;',
  '&cent',
  '&die;',
  '&uml;',
  '&sect',
  '&sol;',
  '&QUOT',
  '&yen;',
  '&Tab;',
  '&Hat;',
  '&ETH',
  '&pm;',
  '&deg',
  '&REG',
  '&reg',
  '&shy',
  '&not',
  '&uml',
  '&yen',
  '&GT;',
  '&gt;',
  '&LT;',
  '&lt;',
  '&AMP',
  '&amp',
  '&eth',
  '&GT',
  '&gt',
  '&LT',
  '&lt'
];

const int maxKeyLength = 18;

const List<String> values = <String>[
  '`',
  ' ',
  '´',
  '|',
  '·',
  '¨',
  '±',
  '·',
  '_',
  '®',
  '¸',
  '\n',
  '¦',
  '%',
  '*',
  '{',
  '|',
  '.',
  '}',
  'ý',
  '¤',
  'ú',
  'õ',
  '=',
  'ù',
  '@',
  'ø',
  '±',
  '÷',
  '[',
  '\$',
  '·',
  ']',
  'Ó',
  '_',
  '¼',
  '½',
  '¾',
  '¿',
  'À',
  'Á',
  'Ã',
  'ó',
  'È',
  'É',
  'Ì',
  'Í',
  'Ñ',
  'Ò',
  'Õ',
  'Ø',
  'Ù',
  'Ú',
  'Ý',
  'à',
  'á',
  'ã',
  'ç',
  'è',
  'é',
  'ì',
  'í',
  'ñ',
  'ò',
  'Ç',
  'ê',
  '´',
  '¤',
  'ô',
  '¦',
  'ó',
  '£',
  'ò',
  'ù',
  'ñ',
  ':',
  '«',
  'î',
  'ø',
  'í',
  'þ',
  'ý',
  '÷',
  'È',
  'ì',
  '¯',
  '¡',
  '±',
  'é',
  'ß',
  'è',
  'µ',
  'ç',
  '·',
  '¸',
  'û',
  'æ',
  ',',
  '»',
  'ú',
  '¼',
  '½',
  '?',
  '¾',
  '¿',
  'À',
  'Á',
  'Â',
  'Ã',
  'Å',
  'Å',
  'Æ',
  'å',
  'Þ',
  'É',
  'Ê',
  'Ì',
  'ã',
  'Í',
  'Î',
  'â',
  '`',
  'Ñ',
  'Ò',
  'á',
  'Ó',
  'Ô',
  'f',
  'Õ',
  'à',
  '×',
  'õ',
  'Ø',
  'Ù',
  'Ú',
  'Û',
  'Ý',
  'Ç',
  '¯',
  '²',
  '[',
  ';',
  '³',
  'Â',
  '\\',
  '+',
  'Ä',
  'å',
  'ô',
  '´',
  'Å',
  '§',
  'Æ',
  '©',
  'µ',
  ']',
  '×',
  'ÿ',
  '¶',
  '¢',
  'Ê',
  'Ë',
  'ä',
  'þ',
  ' ',
  'ü',
  'ö',
  'û',
  'Î',
  'Ï',
  '}',
  'â',
  '©',
  '¸',
  '¡',
  "'",
  '¹',
  'ª',
  'º',
  'ï',
  'Ô',
  '£',
  '»',
  'Ö',
  '«',
  'ë',
  '>',
  '(',
  '\"',
  '{',
  '½',
  ')',
  'î',
  'ê',
  'Û',
  'Ü',
  'ß',
  '|',
  '!',
  '<',
  'Þ',
  '\"',
  'æ',
  '=',
  'Ö',
  '\"',
  'ÿ',
  'ö',
  'Ð',
  'Ï',
  '&',
  'Ë',
  'ä',
  '&',
  'Ä',
  '¹',
  'º',
  '*',
  '¶',
  ' ',
  '#',
  '³',
  '²',
  '­',
  'ü',
  '÷',
  'ë',
  '°',
  '¯',
  '®',
  '®',
  'Ü',
  '¬',
  'ª',
  'ï',
  'ð',
  '©',
  '©',
  '¨',
  '¢',
  '¨',
  '¨',
  '§',
  '/',
  '\"',
  '¥',
  '\t',
  '^',
  'Ð',
  '±',
  '°',
  '®',
  '®',
  '­',
  '¬',
  '¨',
  '¥',
  '>',
  '>',
  '<',
  '<',
  '&',
  '&',
  'ð',
  '>',
  '>',
  '<',
  '<'
];
