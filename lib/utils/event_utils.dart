import 'package:event_bus/event_bus.dart';
import 'package:mooeli/model/diagnose_data.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/scan/scan_main_page.dart';

EventBus eventBus = EventBus();

class EventSetPassword {}

class EventLanguage {}

class EventFeedback {
  bool feedback = false;
  String recordId;

  EventFeedback(this.recordId, {this.feedback = false});
}

class EventAddMonitor {
  String caseId;

  EventAddMonitor(this.caseId);
}

class EventRefreshSection {}

class EventMessageUnread {
  int count;

  EventMessageUnread(this.count);
}

class EventRefreshMessage {}

class EventRefreshUserInfo {}

class EventPatientCount {
  int totalCount = 0;
  int newCount = 0;

  EventPatientCount(this.totalCount, this.newCount);
}

class EventMainTab {
  int tab;

  EventMainTab(this.tab);
}

class EventUserComment {
  String comment;

  EventUserComment(this.comment);
}

class EventIntoUltra {}

class EventHideMask {
  bool hide;

  EventHideMask(this.hide);
}

class EventUltraType {
  int type;

  EventUltraType(this.type);
}

class EventPopScan {
  bool fromRecord = false;

  EventPopScan({this.fromRecord = false});
}

class EventExitScan {}

class EventScanPause {}

class EventRescanUltra {}

class EventCameraAdjust {
  int type;

  EventCameraAdjust(this.type);
}

class EventSmilePhoto {
  String type;
  String path;

  EventSmilePhoto(this.type, this.path);
}

class EventSelectTool {}

class EventOpenTenantDrawer {
  LingyaTenant selectTenant = LingyaTenant();
  List<LingyaTenant> tenantList = [];

  EventOpenTenantDrawer(this.tenantList, this.selectTenant);
}

class EventSwitchTenant {
  LingyaTenant selectTenant = LingyaTenant();

  EventSwitchTenant(this.selectTenant);
}

class EventRecordDialog {}

class EventUploadRecord {}

class EventUploadRecordResult {
  ScanInfo record;

  EventUploadRecordResult(this.record);
}

class EventRefreshRecordList {}

class EventIp {
  String ip;

  EventIp(this.ip);
}

class EventConnectUltra {
  bool connect;

  EventConnectUltra(this.connect);
}

class EventQrCode {
  String code;

  EventQrCode(this.code);
}

class EventRelogin {}

class EventSearchScan {
  String keyword;
  DiagnoseSearchInfo record;

  EventSearchScan(this.keyword, this.record);
}

class EventUpdateDiagnose {
  DiagnoseInfo record;

  EventUpdateDiagnose(this.record);
}

class EventReportStatus {
  int reportId;
  bool success;

  EventReportStatus(this.reportId, this.success);
}

class EventReportDialogClose {}

class EventConfigJson {}

class EventSelectArea {}

class EventScanView {
  ScanView view;

  EventScanView(this.view);
}

class EventScanType {
  int showType;

  EventScanType(this.showType);
}

class EventScanViewRefresh {
  String viewName;

  EventScanViewRefresh(this.viewName);
}

class EventKeyboard {
  bool show;

  EventKeyboard(this.show);
}

class EventClosePdf {}

class EventUploadSingleRecord {
  ScanInfo record;

  EventUploadSingleRecord(this.record);
}

class EventEditActivity {
  ScanActivity activity;

  EventEditActivity(this.activity);
}

class EventDeleteActivity {
  ScanActivity activity;

  EventDeleteActivity(this.activity);
}

class EventDeleteRecord {
  ScanInfo record;

  EventDeleteRecord(this.record);
}

class EventLogin {}

class EventLoging {}

class EventScanLog {
  String message;

  EventScanLog(this.message);
}
