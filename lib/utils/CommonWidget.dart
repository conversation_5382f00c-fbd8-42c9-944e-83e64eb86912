import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';

class CommonWidget {
  //获取通用视频展示预览图
  static Widget getPreVideoImgWidget(BuildContext context, title, imgPath, videoPath, double width, double height,
      {bool isWidthFill = false, dynamic onPress}) {
    double playIconSize = 52.sp;
    if (isWidthFill) {
      double scaleRatio = (1.sw - 48.sp) / width;
      height = scaleRatio * height;
      playIconSize = playIconSize * scaleRatio;
      width = 1.sw;
    }
    return GestureDetector(
        onTap: () {
          if (onPress != null) {
            onPress();
          } else {
            Global.showModalVideoPlayer(context, videoPath, title);
          }
        },
        child: SizedBox(
          width: width,
          height: height,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(8.sp),
                  child: Image.asset(imgPath, fit: BoxFit.cover, width: width, height: height)),
              Align(
                  alignment: Alignment.center,
                  child: Icon(Icons.play_circle_outline, size: playIconSize, color: const Color(0x77ffffff))),
              Container(
                  alignment: Alignment.bottomCenter,
                  width: width == 0 ? double.infinity : width,
                  height: 30.sp,
                  decoration: BoxDecoration(
                    color: const Color(0x66000000),
                    borderRadius: BorderRadius.vertical(bottom: Radius.circular(10.sp)),
                  ),
                  padding: EdgeInsets.all(3.sp),
                  child: Text(title, style: TextStyle(fontSize: 14.sp, color: Colors.white))),
            ],
          ),
        ));
  }

  //获取时代天使Logo标识
  static getAngelAlignLogoWidget(contractData, bool bigSize) {
    String logoName = "res/icons/tooth_60.png";
    // switch (getContractType(contractData)) {
    //   case 1:
    //     logoName = "res/logos/logo_angelalign.png";
    //     break;
    //   case 2:
        logoName = "res/icons/tooth_40.png";
    //     break;
    //   case 3:
    //     logoName = "res/icons/logo_contract_adjust.png";
    //     break;
    //   case 4:
    //     logoName = "res/icons/logo_fixed_adjust.png";
    //     break;
    // }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.sp),
      child: Image.asset(logoName, width: 40.sp),
    );
  }

  //是否应该显示标识
  static bool getAaTypeIsCanShow(contractData) {
    return contractData.containsKey("ProviderType") && contractData["ProviderType"] == "aa";
  }

  //获取品牌标识
  static getAaTypeLogoWidget(contractData) {
    if (getAaTypeIsCanShow(contractData) && contractData["Data"]["ProductInfo"] != null) {
      return getLogoWidget(contractData["Data"]["ProductInfo"]["ProductName"], paddingBottom: 8.sp);
    }
    return const SizedBox();
  }

  static getLogoWidget(String? name, {double paddingBottom = 0}) {
    String logoName = "";
    switch (name) {
      case "champion":
        logoName = "res/logos/logo_champion.png";
        break;
      case "children":
        logoName = "res/logos/logo_children.png";
        break;
      case "classic":
        logoName = "res/logos/logo_classic.png";
        break;
      case "comfos":
        logoName = "res/logos/logo_comfos.png";
        break;
    }
    if (logoName == "") {
      return const SizedBox();
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.sp),
      child: Padding(
        padding: EdgeInsets.only(bottom: paddingBottom),
        child: Image.asset(logoName, width: 61.sp),
      ),
    );
  }
}
