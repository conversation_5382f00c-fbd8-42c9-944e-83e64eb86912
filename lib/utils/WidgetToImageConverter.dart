import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/pages/base_page.dart';

class WidgetToImageConverter extends BasePage {
  Widget child;

  WidgetToImageConverter(this.child, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return WidgetToImageConverterState();
  }

}

class WidgetToImageConverterState extends BasePageState<WidgetToImageConverter> {
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: widget.child, // 将要生成为图片的控件放在这里
    );
  }
}

Future<Uint8List?> captureWidgetToImage(GlobalKey key) async {
  try {
    RenderRepaintBoundary boundary = key.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 4.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    if (byteData != null) {
      Uint8List pngBytes = byteData!.buffer.asUint8List();
      return pngBytes;
    }
  } catch (e) {
    Global.toast("保存图片失败：$e");
  }
  return null;
}
