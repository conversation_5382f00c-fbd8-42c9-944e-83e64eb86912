import 'dart:io';
import 'dart:isolate';

import 'package:flutter/services.dart';
import 'package:mooeli/pages/scan/ToothCameraIsolate.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli_ffi/bindings_ffi.dart';
import 'package:path_provider/path_provider.dart';

enum ToothTaskTypeEnum {
  checkPoint, //中心点检测
  toothDetection, //质量检测
  clarityIncrease, //清晰度优化
}

class TFLiteUtils {
  static ToothCameraIsolate isolate = ToothCameraIsolate();
  static late Directory tempDir;

  static Future<dynamic> init() async {
    tempDir = await getTemporaryDirectory();
    await isolate.start();
    logger("TFLiteUtils init");
  }

  static void dispose() {
    isolate.dispose();
    logger("TFLiteUtils dispose");
  }

  static Future<dynamic> resetCreateTFLiteInstance(ToothTaskTypeEnum type) async {
    String modelName;
    bool isEncrypt = true;
    int modelType = 0;
    switch (type) {
      case ToothTaskTypeEnum.checkPoint:
        modelName = "d32.ml";
        modelType = ModelType.center_detection;
        isEncrypt = true;
        break;
      case ToothTaskTypeEnum.toothDetection:
        modelName = "m332.ml";
        modelType = ModelType.quality_inspection;
        isEncrypt = true;
        break;
      case ToothTaskTypeEnum.clarityIncrease:
        modelName = "rnet.ml"; //小模型
        // modelName = "srcnn.ml"; //大模型
        modelType = ModelType.clarity_enhance;
        isEncrypt = true;
        break;
    }
    ByteData data = await PlatformAssetBundle().load("assets/$modelName");
    isolate.sendPort
        .send({"type": 0, "modelData": data.buffer.asUint8List(), "modelType": modelType, "isEncrypt": isEncrypt});
    logger("TFLiteUtils resetCreateTFLiteInstance $type");
  }

  static clarityIncrease(imgPath) async {
    await callTfliteLogic(imgPath, 4).then((obj) {
      logger("TFLiteUtils clarityIncrease: ${obj.runMs}");
    });
  }

  //ffi logic
  static Future<dynamic> callTfliteLogic(dynamic obj, int type, {String saveImgName = ""}) async {
    ReceivePort responsePort = ReceivePort();
    isolate.sendPort.send({
      "responseSendPort": responsePort.sendPort,
      "type": type,
      "obj": obj,
      "tempDir": tempDir,
      "saveImgName": saveImgName,
    });
    var results = await responsePort.first;
    return results;
  }
}
