import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:mooeli/utils/common_utils.dart';

class AudioPlayerUtil {
  static dynamic listener;
  static final AudioPlayer _audioPlayer = AudioPlayer()..setReleaseMode(ReleaseMode.stop);

  static void clear() {
    listener?.cancel();
    listener = null;
  }

  //使用callBackMillsec记得用mounted检测画面是否仍存在
  static Future<void> playSound(assetsFile,
      {dynamic onCompleteCallback, int callBackMillsec = 0, bool repeat = false}) async {
    logger("playSound: $assetsFile");
    clear();
    try {
      AssetSource source = AssetSource(assetsFile);
      await _audioPlayer.stop();
      await _audioPlayer.setSource(source);
      if (onCompleteCallback != null) {
        if (callBackMillsec > 0) {
          Future.delayed(Duration(milliseconds: callBackMillsec), onCompleteCallback);
          await _audioPlayer.play(source);
        } else {
          await _audioPlayer.play(source);
          listener = _audioPlayer.onPlayerStateChanged.listen((playerState) {
            if (playerState == PlayerState.completed) {
              onCompleteCallback();
            }
          });
        }
      } else if (repeat) {
        await _audioPlayer.play(source);
        listener = _audioPlayer.onPlayerStateChanged.listen((playerState) {
          if (playerState == PlayerState.completed) {
            playSound(assetsFile, repeat: true);
          }
        });
      } else {
        await _audioPlayer.play(source);
      }
    } catch (ex) {
      if (onCompleteCallback != null) {
        onCompleteCallback();
      }
    }
  }

  static Future<void> playLocalSound(localFile) async {
    logger("playSound: $localFile");
    clear();
    try {
      DeviceFileSource source = DeviceFileSource(localFile);
      await _audioPlayer.stop();
      await _audioPlayer.setSource(source);
      await _audioPlayer.play(source);
    } catch (ex) {}
  }

  //停止音频
  static Future<void> stopSound() async {
    logger("playSound: STOP!!!");
    clear();
    try {
      await _audioPlayer.stop();
    } catch (ex) {
      //
    }
  }
}
