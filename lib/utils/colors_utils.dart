import 'dart:ui';

Color color2B = const Color(0xFF2B2B2B);
Color colorBrand = const Color(0xFF6B57C7);
Color colorBrandBorder = const Color(0xFF9E91DB);
Color colorSearchBorder = const Color(0xFF8473D1);
Color colorRed = const Color(0xFFEA605B);
Color color53= const Color(0xFF535353);
Color color7C = const Color(0xFF7C7C7C);
Color color80 = const Color(0xFF808080);
Color colorA4 = const Color(0xFFA4A4A4);
Color colorB8 = const Color(0xFFB8B8B8);
Color colorBB = const Color(0xFFBBBBBB);
Color colorCA = const Color(0xFFCACACA);
Color colorD9 = const Color(0xFFD9D9D9);
Color colorE1 = const Color(0xFFE1E1E1);
Color colorE8 = const Color(0xFFE8E8E8);
Color colorAB = const Color(0xFFABABAB);
Color colorF3 = const Color(0xFFF3F3F3);
Color colorFA = const Color(0xFFFAFAFA);
Color colorFB = const Color(0xFFFBF7FA);
Color colorBg = const Color(0xFFFAF9FE);
Color colorGreen = const Color(0xFF76B283);
Color colorGreen2 = const Color(0xFF50A963);
Color colorCyan = const Color(0xFF6FC9C3);
Color colorPink = const Color(0xFFEF8666);
Color colorBlue = const Color(0xFF387CFF);
Color colorBlueDeep = const Color(0xFF352BA5);
Color colorPink1A = const Color(0x1AEF8666);
Color colorBlue1A = const Color(0x1A387CFF);
Color colorShader = const Color.fromARGB(33, 181, 171, 227);
Color colorToothNormal = const Color(0xFFA69ADD);
Color colorRed_1 = const Color(0xFFE1277D);
Color colorToothDisease = const Color(0xFFEF8666);
Color colorPurpleLight = const Color(0xFFF8F7FC);
Color colorPurpleBg = const Color(0xFFF0EEF9);
Color colorPurpleLavender = const Color(0xFFEBEAF6);
Color colorPurpleLilac = const Color(0xFFF2F0FA);
Color colorPurpleLilacGray = const Color(0xFFE1DDF4);
Color colorRedMuted = const Color(0xFFD15B63);

//colorString: "#76B283"
Color string2Color(String colorString) {
  try {
    int? value = 0x00000000;
    if (colorString.isNotEmpty) {
      if (colorString[0] == "#") {
        colorString = colorString.substring(1);
      }
      value = int.tryParse(colorString, radix: 16);
      if (value != null && value < 0xff000000) {
        value += 0xff000000;
      }
    }
    return Color(value!);
  } catch (ex) {
    return colorBrand;
  }
}
