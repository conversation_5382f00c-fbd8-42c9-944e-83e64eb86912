// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `备案管理系统`
  String get icp_code_system {
    return Intl.message(
      '备案管理系统',
      name: 'icp_code_system',
      desc: '',
      args: [],
    );
  }

  /// `备案号已复制`
  String get icp_code_copied {
    return Intl.message(
      '备案号已复制',
      name: 'icp_code_copied',
      desc: '',
      args: [],
    );
  }

  /// `浙公网安备 33010602012209号      浙ICP备2021003396号-3`
  String get icp_code_only_cn {
    return Intl.message(
      '浙公网安备 33010602012209号      浙ICP备2021003396号-3',
      name: 'icp_code_only_cn',
      desc: '',
      args: [],
    );
  }

  /// `欢迎使用灵芽`
  String get welcome_login_lyoral {
    return Intl.message(
      '欢迎使用灵芽',
      name: 'welcome_login_lyoral',
      desc: '',
      args: [],
    );
  }

  /// `欢迎注册灵芽`
  String get welcome_register_lyoral {
    return Intl.message(
      '欢迎注册灵芽',
      name: 'welcome_register_lyoral',
      desc: '',
      args: [],
    );
  }

  /// `手机号/用户名`
  String get input_account {
    return Intl.message(
      '手机号/用户名',
      name: 'input_account',
      desc: '',
      args: [],
    );
  }

  /// `密码`
  String get input_password {
    return Intl.message(
      '密码',
      name: 'input_password',
      desc: '',
      args: [],
    );
  }

  /// `我已阅读并同意`
  String get read_and_agree {
    return Intl.message(
      '我已阅读并同意',
      name: 'read_and_agree',
      desc: '',
      args: [],
    );
  }

  /// `服务协议`
  String get user_agreement {
    return Intl.message(
      '服务协议',
      name: 'user_agreement',
      desc: '',
      args: [],
    );
  }

  /// `工作台`
  String get home_page {
    return Intl.message(
      '工作台',
      name: 'home_page',
      desc: '',
      args: [],
    );
  }

  /// `病例`
  String get lyoral_page {
    return Intl.message(
      '病例',
      name: 'lyoral_page',
      desc: '',
      args: [],
    );
  }

  /// `消息`
  String get message_page {
    return Intl.message(
      '消息',
      name: 'message_page',
      desc: '',
      args: [],
    );
  }

  /// `我的`
  String get my_page {
    return Intl.message(
      '我的',
      name: 'my_page',
      desc: '',
      args: [],
    );
  }

  /// `早上好，`
  String get good_morning {
    return Intl.message(
      '早上好，',
      name: 'good_morning',
      desc: '',
      args: [],
    );
  }

  /// `中午好，`
  String get good_noon {
    return Intl.message(
      '中午好，',
      name: 'good_noon',
      desc: '',
      args: [],
    );
  }

  /// `下午好，`
  String get good_afternoon {
    return Intl.message(
      '下午好，',
      name: 'good_afternoon',
      desc: '',
      args: [],
    );
  }

  /// `晚上好，`
  String get good_evening {
    return Intl.message(
      '晚上好，',
      name: 'good_evening',
      desc: '',
      args: [],
    );
  }

  /// `晚上好，`
  String get good_night {
    return Intl.message(
      '晚上好，',
      name: 'good_night',
      desc: '',
      args: [],
    );
  }

  /// `数据看板`
  String get data_panel {
    return Intl.message(
      '数据看板',
      name: 'data_panel',
      desc: '',
      args: [],
    );
  }

  /// `患者总数`
  String get total_patient_count {
    return Intl.message(
      '患者总数',
      name: 'total_patient_count',
      desc: '',
      args: [],
    );
  }

  /// `新增患者数`
  String get new_patient_count {
    return Intl.message(
      '新增患者数',
      name: 'new_patient_count',
      desc: '',
      args: [],
    );
  }

  /// `还没有病例哦\n请登录网页端灵芽创建`
  String get no_patient_data {
    return Intl.message(
      '还没有病例哦\n请登录网页端灵芽创建',
      name: 'no_patient_data',
      desc: '',
      args: [],
    );
  }

  /// `功能入口`
  String get entry_panel {
    return Intl.message(
      '功能入口',
      name: 'entry_panel',
      desc: '',
      args: [],
    );
  }

  /// `我的病例`
  String get my_lyoral_case {
    return Intl.message(
      '我的病例',
      name: 'my_lyoral_case',
      desc: '',
      args: [],
    );
  }

  /// `MOOELI`
  String get my_mooeli_case {
    return Intl.message(
      'MOOELI',
      name: 'my_mooeli_case',
      desc: '',
      args: [],
    );
  }

  /// `无`
  String get none {
    return Intl.message(
      '无',
      name: 'none',
      desc: '',
      args: [],
    );
  }

  /// `病例列表`
  String get case_list {
    return Intl.message(
      '病例列表',
      name: 'case_list',
      desc: '',
      args: [],
    );
  }

  /// `没有找到“__”相关病例`
  String get cannot_find_case {
    return Intl.message(
      '没有找到“__”相关病例',
      name: 'cannot_find_case',
      desc: '',
      args: [],
    );
  }

  /// `暂无病例\n请登录`
  String get no_case_login {
    return Intl.message(
      '暂无病例\n请登录',
      name: 'no_case_login',
      desc: '',
      args: [],
    );
  }

  /// `网页端灵芽`
  String get lyoral_web {
    return Intl.message(
      '网页端灵芽',
      name: 'lyoral_web',
      desc: '',
      args: [],
    );
  }

  /// `创建`
  String get create_case {
    return Intl.message(
      '创建',
      name: 'create_case',
      desc: '',
      args: [],
    );
  }

  /// `创建`
  String get create {
    return Intl.message(
      '创建',
      name: 'create',
      desc: '',
      args: [],
    );
  }

  /// `暂无符合条件的病例\n请重置筛选条件`
  String get no_filter_case {
    return Intl.message(
      '暂无符合条件的病例\n请重置筛选条件',
      name: 'no_filter_case',
      desc: '',
      args: [],
    );
  }

  /// `暂时还没有消息记录`
  String get no_message {
    return Intl.message(
      '暂时还没有消息记录',
      name: 'no_message',
      desc: '',
      args: [],
    );
  }

  /// `筛选和排序`
  String get filter_title {
    return Intl.message(
      '筛选和排序',
      name: 'filter_title',
      desc: '',
      args: [],
    );
  }

  /// `筛选`
  String get filter {
    return Intl.message(
      '筛选',
      name: 'filter',
      desc: '',
      args: [],
    );
  }

  /// `颜色`
  String get color {
    return Intl.message(
      '颜色',
      name: 'color',
      desc: '',
      args: [],
    );
  }

  /// `时间`
  String get time {
    return Intl.message(
      '时间',
      name: 'time',
      desc: '',
      args: [],
    );
  }

  /// `开始时间`
  String get start_time {
    return Intl.message(
      '开始时间',
      name: 'start_time',
      desc: '',
      args: [],
    );
  }

  /// `结束时间`
  String get end_time {
    return Intl.message(
      '结束时间',
      name: 'end_time',
      desc: '',
      args: [],
    );
  }

  /// `标签`
  String get tag {
    return Intl.message(
      '标签',
      name: 'tag',
      desc: '',
      args: [],
    );
  }

  /// `性别`
  String get gender {
    return Intl.message(
      '性别',
      name: 'gender',
      desc: '',
      args: [],
    );
  }

  /// `男`
  String get gender_man {
    return Intl.message(
      '男',
      name: 'gender_man',
      desc: '',
      args: [],
    );
  }

  /// `女`
  String get gender_woman {
    return Intl.message(
      '女',
      name: 'gender_woman',
      desc: '',
      args: [],
    );
  }

  /// `排序`
  String get order {
    return Intl.message(
      '排序',
      name: 'order',
      desc: '',
      args: [],
    );
  }

  /// `更新时间`
  String get update_time {
    return Intl.message(
      '更新时间',
      name: 'update_time',
      desc: '',
      args: [],
    );
  }

  /// `创建时间`
  String get create_time {
    return Intl.message(
      '创建时间',
      name: 'create_time',
      desc: '',
      args: [],
    );
  }

  /// `姓名`
  String get name {
    return Intl.message(
      '姓名',
      name: 'name',
      desc: '',
      args: [],
    );
  }

  /// `降序`
  String get order_desc {
    return Intl.message(
      '降序',
      name: 'order_desc',
      desc: '',
      args: [],
    );
  }

  /// `升序`
  String get order_inc {
    return Intl.message(
      '升序',
      name: 'order_inc',
      desc: '',
      args: [],
    );
  }

  /// `重置`
  String get reset {
    return Intl.message(
      '重置',
      name: 'reset',
      desc: '',
      args: [],
    );
  }

  /// `确定`
  String get confirm {
    return Intl.message(
      '确定',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `收藏`
  String get favor {
    return Intl.message(
      '收藏',
      name: 'favor',
      desc: '',
      args: [],
    );
  }

  /// `未分类`
  String get not_sorted {
    return Intl.message(
      '未分类',
      name: 'not_sorted',
      desc: '',
      args: [],
    );
  }

  /// `初诊`
  String get first_treat {
    return Intl.message(
      '初诊',
      name: 'first_treat',
      desc: '',
      args: [],
    );
  }

  /// `治疗中`
  String get in_treating {
    return Intl.message(
      '治疗中',
      name: 'in_treating',
      desc: '',
      args: [],
    );
  }

  /// `已完成`
  String get complete_treat {
    return Intl.message(
      '已完成',
      name: 'complete_treat',
      desc: '',
      args: [],
    );
  }

  /// `%i条未读消息`
  String get unread_message_count {
    return Intl.message(
      '%i条未读消息',
      name: 'unread_message_count',
      desc: '',
      args: [],
    );
  }

  /// `全部已读`
  String get set_all_read {
    return Intl.message(
      '全部已读',
      name: 'set_all_read',
      desc: '',
      args: [],
    );
  }

  /// `安全设置`
  String get setting_secure {
    return Intl.message(
      '安全设置',
      name: 'setting_secure',
      desc: '',
      args: [],
    );
  }

  /// `消息通知设置`
  String get setting_message {
    return Intl.message(
      '消息通知设置',
      name: 'setting_message',
      desc: '',
      args: [],
    );
  }

  /// `语言设置`
  String get setting_language {
    return Intl.message(
      '语言设置',
      name: 'setting_language',
      desc: '',
      args: [],
    );
  }

  /// `关于乐齿拍`
  String get setting_about {
    return Intl.message(
      '关于乐齿拍',
      name: 'setting_about',
      desc: '',
      args: [],
    );
  }

  /// `联系我们`
  String get setting_contact {
    return Intl.message(
      '联系我们',
      name: 'setting_contact',
      desc: '',
      args: [],
    );
  }

  /// `若您在使用APP时遇到任何问题或有更好的建议，请扫描客服微信二维码，我们将一对一进行解答：`
  String get contact_way {
    return Intl.message(
      '若您在使用APP时遇到任何问题或有更好的建议，请扫描客服微信二维码，我们将一对一进行解答：',
      name: 'contact_way',
      desc: '',
      args: [],
    );
  }

  /// `尚未填写工作单位`
  String get no_organization {
    return Intl.message(
      '尚未填写工作单位',
      name: 'no_organization',
      desc: '',
      args: [],
    );
  }

  /// `退出登录`
  String get logout {
    return Intl.message(
      '退出登录',
      name: 'logout',
      desc: '',
      args: [],
    );
  }

  /// `疗程选择`
  String get select_contract {
    return Intl.message(
      '疗程选择',
      name: 'select_contract',
      desc: '',
      args: [],
    );
  }

  /// `共%i个疗程`
  String get total_contract_count {
    return Intl.message(
      '共%i个疗程',
      name: 'total_contract_count',
      desc: '',
      args: [],
    );
  }

  /// `资料库`
  String get contract_info {
    return Intl.message(
      '资料库',
      name: 'contract_info',
      desc: '',
      args: [],
    );
  }

  /// `病例档案`
  String get case_doc {
    return Intl.message(
      '病例档案',
      name: 'case_doc',
      desc: '',
      args: [],
    );
  }

  /// `病例档案`
  String get case_docs {
    return Intl.message(
      '病例档案',
      name: 'case_docs',
      desc: '',
      args: [],
    );
  }

  /// `备注`
  String get remark {
    return Intl.message(
      '备注',
      name: 'remark',
      desc: '',
      args: [],
    );
  }

  /// `面像与口内照片`
  String get face_mouth_photos {
    return Intl.message(
      '面像与口内照片',
      name: 'face_mouth_photos',
      desc: '',
      args: [],
    );
  }

  /// `面像照`
  String get face_photo {
    return Intl.message(
      '面像照',
      name: 'face_photo',
      desc: '',
      args: [],
    );
  }

  /// `选填`
  String get optional {
    return Intl.message(
      '选填',
      name: 'optional',
      desc: '',
      args: [],
    );
  }

  /// `X光片`
  String get x_photos {
    return Intl.message(
      'X光片',
      name: 'x_photos',
      desc: '',
      args: [],
    );
  }

  /// `牙颌模型`
  String get teeth_photos {
    return Intl.message(
      '牙颌模型',
      name: 'teeth_photos',
      desc: '',
      args: [],
    );
  }

  /// `CBCT`
  String get cbct_photos {
    return Intl.message(
      'CBCT',
      name: 'cbct_photos',
      desc: '',
      args: [],
    );
  }

  /// `暂无数据`
  String get no_record {
    return Intl.message(
      '暂无数据',
      name: 'no_record',
      desc: '',
      args: [],
    );
  }

  /// `正在努力加载中...`
  String get loading {
    return Intl.message(
      '正在努力加载中...',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `正面像`
  String get type_100 {
    return Intl.message(
      '正面像',
      name: 'type_100',
      desc: '',
      args: [],
    );
  }

  /// `正面照`
  String get type_100_1 {
    return Intl.message(
      '正面照',
      name: 'type_100_1',
      desc: '',
      args: [],
    );
  }

  /// `侧面像`
  String get type_101 {
    return Intl.message(
      '侧面像',
      name: 'type_101',
      desc: '',
      args: [],
    );
  }

  /// `侧面照`
  String get type_101_1 {
    return Intl.message(
      '侧面照',
      name: 'type_101_1',
      desc: '',
      args: [],
    );
  }

  /// `正面微笑像`
  String get type_102 {
    return Intl.message(
      '正面微笑像',
      name: 'type_102',
      desc: '',
      args: [],
    );
  }

  /// `微笑照`
  String get type_102_1 {
    return Intl.message(
      '微笑照',
      name: 'type_102_1',
      desc: '',
      args: [],
    );
  }

  /// `正面咬合像`
  String get type_103 {
    return Intl.message(
      '正面咬合像',
      name: 'type_103',
      desc: '',
      args: [],
    );
  }

  /// `正面咬合45°像`
  String get type_104 {
    return Intl.message(
      '正面咬合45°像',
      name: 'type_104',
      desc: '',
      args: [],
    );
  }

  /// `左侧咬合像`
  String get type_105 {
    return Intl.message(
      '左侧咬合像',
      name: 'type_105',
      desc: '',
      args: [],
    );
  }

  /// `下牙弓像`
  String get type_106 {
    return Intl.message(
      '下牙弓像',
      name: 'type_106',
      desc: '',
      args: [],
    );
  }

  /// `右侧咬合像`
  String get type_107 {
    return Intl.message(
      '右侧咬合像',
      name: 'type_107',
      desc: '',
      args: [],
    );
  }

  /// `上牙弓像`
  String get type_108 {
    return Intl.message(
      '上牙弓像',
      name: 'type_108',
      desc: '',
      args: [],
    );
  }

  /// `全颌曲面断层片`
  String get type_109 {
    return Intl.message(
      '全颌曲面断层片',
      name: 'type_109',
      desc: '',
      args: [],
    );
  }

  /// `头颅侧位定位片`
  String get type_110 {
    return Intl.message(
      '头颅侧位定位片',
      name: 'type_110',
      desc: '',
      args: [],
    );
  }

  /// `覆盖像`
  String get type_111 {
    return Intl.message(
      '覆盖像',
      name: 'type_111',
      desc: '',
      args: [],
    );
  }

  /// `侧面45度像`
  String get type_112 {
    return Intl.message(
      '侧面45度像',
      name: 'type_112',
      desc: '',
      args: [],
    );
  }

  /// `右侧45°`
  String get type_113 {
    return Intl.message(
      '右侧45°',
      name: 'type_113',
      desc: '',
      args: [],
    );
  }

  /// `右侧面微笑像`
  String get type_114 {
    return Intl.message(
      '右侧面微笑像',
      name: 'type_114',
      desc: '',
      args: [],
    );
  }

  /// `左45度微笑像`
  String get type_115 {
    return Intl.message(
      '左45度微笑像',
      name: 'type_115',
      desc: '',
      args: [],
    );
  }

  /// `左侧45°`
  String get type_116 {
    return Intl.message(
      '左侧45°',
      name: 'type_116',
      desc: '',
      args: [],
    );
  }

  /// `左侧面微笑像`
  String get type_117 {
    return Intl.message(
      '左侧面微笑像',
      name: 'type_117',
      desc: '',
      args: [],
    );
  }

  /// `左侧面像`
  String get type_118 {
    return Intl.message(
      '左侧面像',
      name: 'type_118',
      desc: '',
      args: [],
    );
  }

  /// `左侧90°`
  String get type_118_1 {
    return Intl.message(
      '左侧90°',
      name: 'type_118_1',
      desc: '',
      args: [],
    );
  }

  /// `右侧面像`
  String get type_119 {
    return Intl.message(
      '右侧面像',
      name: 'type_119',
      desc: '',
      args: [],
    );
  }

  /// `右侧90°`
  String get type_119_1 {
    return Intl.message(
      '右侧90°',
      name: 'type_119_1',
      desc: '',
      args: [],
    );
  }

  /// `右45度微笑像`
  String get type_120 {
    return Intl.message(
      '右45度微笑像',
      name: 'type_120',
      desc: '',
      args: [],
    );
  }

  /// `口内开颌正位像`
  String get type_151 {
    return Intl.message(
      '口内开颌正位像',
      name: 'type_151',
      desc: '',
      args: [],
    );
  }

  /// `口内开颌左位像`
  String get type_152 {
    return Intl.message(
      '口内开颌左位像',
      name: 'type_152',
      desc: '',
      args: [],
    );
  }

  /// `口内开颌右位像`
  String get type_153 {
    return Intl.message(
      '口内开颌右位像',
      name: 'type_153',
      desc: '',
      args: [],
    );
  }

  /// `口内牙套正位像`
  String get type_154 {
    return Intl.message(
      '口内牙套正位像',
      name: 'type_154',
      desc: '',
      args: [],
    );
  }

  /// `口内牙套左位像`
  String get type_155 {
    return Intl.message(
      '口内牙套左位像',
      name: 'type_155',
      desc: '',
      args: [],
    );
  }

  /// `口内牙套右位像`
  String get type_156 {
    return Intl.message(
      '口内牙套右位像',
      name: 'type_156',
      desc: '',
      args: [],
    );
  }

  /// `上颌模型`
  String get type_200 {
    return Intl.message(
      '上颌模型',
      name: 'type_200',
      desc: '',
      args: [],
    );
  }

  /// `下颌模型`
  String get type_201 {
    return Intl.message(
      '下颌模型',
      name: 'type_201',
      desc: '',
      args: [],
    );
  }

  /// `CBCT`
  String get type_300 {
    return Intl.message(
      'CBCT',
      name: 'type_300',
      desc: '',
      args: [],
    );
  }

  /// `全颌曲面断层片`
  String get analytics_5001 {
    return Intl.message(
      '全颌曲面断层片',
      name: 'analytics_5001',
      desc: '',
      args: [],
    );
  }

  /// `正面像分析报告`
  String get analytics_5002 {
    return Intl.message(
      '正面像分析报告',
      name: 'analytics_5002',
      desc: '',
      args: [],
    );
  }

  /// `侧面像分析`
  String get analytics_5003 {
    return Intl.message(
      '侧面像分析',
      name: 'analytics_5003',
      desc: '',
      args: [],
    );
  }

  /// `口内照分析`
  String get analytics_5004 {
    return Intl.message(
      '口内照分析',
      name: 'analytics_5004',
      desc: '',
      args: [],
    );
  }

  /// `头影分析`
  String get analytics_5021 {
    return Intl.message(
      '头影分析',
      name: 'analytics_5021',
      desc: '',
      args: [],
    );
  }

  /// `骨龄分析`
  String get analytics_5022 {
    return Intl.message(
      '骨龄分析',
      name: 'analytics_5022',
      desc: '',
      args: [],
    );
  }

  /// `气道分析`
  String get analytics_5023 {
    return Intl.message(
      '气道分析',
      name: 'analytics_5023',
      desc: '',
      args: [],
    );
  }

  /// `VTO分析`
  String get analytics_5024 {
    return Intl.message(
      'VTO分析',
      name: 'analytics_5024',
      desc: '',
      args: [],
    );
  }

  /// `微笑像分析`
  String get analytics_5031 {
    return Intl.message(
      '微笑像分析',
      name: 'analytics_5031',
      desc: '',
      args: [],
    );
  }

  /// `微笑模拟分析`
  String get analytics_5032 {
    return Intl.message(
      '微笑模拟分析',
      name: 'analytics_5032',
      desc: '',
      args: [],
    );
  }

  /// `原始模型分析`
  String get analytics_5101 {
    return Intl.message(
      '原始模型分析',
      name: 'analytics_5101',
      desc: '',
      args: [],
    );
  }

  /// `分割模型分析`
  String get analytics_5102 {
    return Intl.message(
      '分割模型分析',
      name: 'analytics_5102',
      desc: '',
      args: [],
    );
  }

  /// `根骨模型分析`
  String get analytics_5103 {
    return Intl.message(
      '根骨模型分析',
      name: 'analytics_5103',
      desc: '',
      args: [],
    );
  }

  /// `无约束排牙分析`
  String get analytics_5111 {
    return Intl.message(
      '无约束排牙分析',
      name: 'analytics_5111',
      desc: '',
      args: [],
    );
  }

  /// `工单排牙分析`
  String get analytics_5112 {
    return Intl.message(
      '工单排牙分析',
      name: 'analytics_5112',
      desc: '',
      args: [],
    );
  }

  /// `二维多平面重建（MPR）分析`
  String get analytics_5202 {
    return Intl.message(
      '二维多平面重建（MPR）分析',
      name: 'analytics_5202',
      desc: '',
      args: [],
    );
  }

  /// `曲面重建（CPR）分析`
  String get analytics_5203 {
    return Intl.message(
      '曲面重建（CPR）分析',
      name: 'analytics_5203',
      desc: '',
      args: [],
    );
  }

  /// `病例详情`
  String get case_detail {
    return Intl.message(
      '病例详情',
      name: 'case_detail',
      desc: '',
      args: [],
    );
  }

  /// `个人信息`
  String get user_profile {
    return Intl.message(
      '个人信息',
      name: 'user_profile',
      desc: '',
      args: [],
    );
  }

  /// `输入姓名`
  String get input_name {
    return Intl.message(
      '输入姓名',
      name: 'input_name',
      desc: '',
      args: [],
    );
  }

  /// `%s岁`
  String get year_old {
    return Intl.message(
      '%s岁',
      name: 'year_old',
      desc: '',
      args: [],
    );
  }

  /// `分类标签`
  String get sort_tags {
    return Intl.message(
      '分类标签',
      name: 'sort_tags',
      desc: '',
      args: [],
    );
  }

  /// `其他`
  String get other {
    return Intl.message(
      '其他',
      name: 'other',
      desc: '',
      args: [],
    );
  }

  /// `主诉`
  String get main_require {
    return Intl.message(
      '主诉',
      name: 'main_require',
      desc: '',
      args: [],
    );
  }

  /// `过敏史`
  String get history_allergy {
    return Intl.message(
      '过敏史',
      name: 'history_allergy',
      desc: '',
      args: [],
    );
  }

  /// `家族史`
  String get history_family {
    return Intl.message(
      '家族史',
      name: 'history_family',
      desc: '',
      args: [],
    );
  }

  /// `治疗史`
  String get history_treatment {
    return Intl.message(
      '治疗史',
      name: 'history_treatment',
      desc: '',
      args: [],
    );
  }

  /// `病例资料`
  String get case_info {
    return Intl.message(
      '病例资料',
      name: 'case_info',
      desc: '',
      args: [],
    );
  }

  /// `组`
  String get unit_team {
    return Intl.message(
      '组',
      name: 'unit_team',
      desc: '',
      args: [],
    );
  }

  /// `组`
  String get unit_teams {
    return Intl.message(
      '组',
      name: 'unit_teams',
      desc: '',
      args: [],
    );
  }

  /// `份`
  String get unit_count {
    return Intl.message(
      '份',
      name: 'unit_count',
      desc: '',
      args: [],
    );
  }

  /// `份`
  String get unit_counts {
    return Intl.message(
      '份',
      name: 'unit_counts',
      desc: '',
      args: [],
    );
  }

  /// `暂无疗程，请前往网页端灵芽创建`
  String get case_no_contract {
    return Intl.message(
      '暂无疗程，请前往网页端灵芽创建',
      name: 'case_no_contract',
      desc: '',
      args: [],
    );
  }

  /// `暂无病例档案，请前往网页端灵芽创建`
  String get case_no_doc {
    return Intl.message(
      '暂无病例档案，请前往网页端灵芽创建',
      name: 'case_no_doc',
      desc: '',
      args: [],
    );
  }

  /// `基本信息`
  String get doc_base_info {
    return Intl.message(
      '基本信息',
      name: 'doc_base_info',
      desc: '',
      args: [],
    );
  }

  /// `临床信息`
  String get doc_clinical_info {
    return Intl.message(
      '临床信息',
      name: 'doc_clinical_info',
      desc: '',
      args: [],
    );
  }

  /// `诊断结果`
  String get doc_diagnose_info {
    return Intl.message(
      '诊断结果',
      name: 'doc_diagnose_info',
      desc: '',
      args: [],
    );
  }

  /// `检查报告`
  String get doc_inspect_info {
    return Intl.message(
      '检查报告',
      name: 'doc_inspect_info',
      desc: '',
      args: [],
    );
  }

  /// `治疗方案及其他`
  String get doc_treatment_info {
    return Intl.message(
      '治疗方案及其他',
      name: 'doc_treatment_info',
      desc: '',
      args: [],
    );
  }

  /// `出生日期`
  String get birthday {
    return Intl.message(
      '出生日期',
      name: 'birthday',
      desc: '',
      args: [],
    );
  }

  /// `联系电话`
  String get phone {
    return Intl.message(
      '联系电话',
      name: 'phone',
      desc: '',
      args: [],
    );
  }

  /// `电话`
  String get mobile {
    return Intl.message(
      '电话',
      name: 'mobile',
      desc: '',
      args: [],
    );
  }

  /// `年龄`
  String get age {
    return Intl.message(
      '年龄',
      name: 'age',
      desc: '',
      args: [],
    );
  }

  /// `病例ID号`
  String get case_code {
    return Intl.message(
      '病例ID号',
      name: 'case_code',
      desc: '',
      args: [],
    );
  }

  /// `其他信息`
  String get other_info {
    return Intl.message(
      '其他信息',
      name: 'other_info',
      desc: '',
      args: [],
    );
  }

  /// `医生`
  String get doctor {
    return Intl.message(
      '医生',
      name: 'doctor',
      desc: '',
      args: [],
    );
  }

  /// `科室`
  String get office {
    return Intl.message(
      '科室',
      name: 'office',
      desc: '',
      args: [],
    );
  }

  /// `医疗机构`
  String get organization {
    return Intl.message(
      '医疗机构',
      name: 'organization',
      desc: '',
      args: [],
    );
  }

  /// `身高`
  String get height {
    return Intl.message(
      '身高',
      name: 'height',
      desc: '',
      args: [],
    );
  }

  /// `体重`
  String get weight {
    return Intl.message(
      '体重',
      name: 'weight',
      desc: '',
      args: [],
    );
  }

  /// `正畸开始时间`
  String get medical_start_time {
    return Intl.message(
      '正畸开始时间',
      name: 'medical_start_time',
      desc: '',
      args: [],
    );
  }

  /// `不良习惯`
  String get bad_habit {
    return Intl.message(
      '不良习惯',
      name: 'bad_habit',
      desc: '',
      args: [],
    );
  }

  /// `龋齿`
  String get dental_caries {
    return Intl.message(
      '龋齿',
      name: 'dental_caries',
      desc: '',
      args: [],
    );
  }

  /// `家族病史`
  String get family_history {
    return Intl.message(
      '家族病史',
      name: 'family_history',
      desc: '',
      args: [],
    );
  }

  /// `主诉`
  String get chief_complaint {
    return Intl.message(
      '主诉',
      name: 'chief_complaint',
      desc: '',
      args: [],
    );
  }

  /// `用药史`
  String get medication_history {
    return Intl.message(
      '用药史',
      name: 'medication_history',
      desc: '',
      args: [],
    );
  }

  /// `口腔外伤史`
  String get oralTrauma_history {
    return Intl.message(
      '口腔外伤史',
      name: 'oralTrauma_history',
      desc: '',
      args: [],
    );
  }

  /// `疾病性质与时间`
  String get disease_nature_time {
    return Intl.message(
      '疾病性质与时间',
      name: 'disease_nature_time',
      desc: '',
      args: [],
    );
  }

  /// `乳牙滞留`
  String get retention_baby_teeth {
    return Intl.message(
      '乳牙滞留',
      name: 'retention_baby_teeth',
      desc: '',
      args: [],
    );
  }

  /// `疾病分类`
  String get disease_classification {
    return Intl.message(
      '疾病分类',
      name: 'disease_classification',
      desc: '',
      args: [],
    );
  }

  /// `乳牙早失`
  String get premature_loss_baby_teeth {
    return Intl.message(
      '乳牙早失',
      name: 'premature_loss_baby_teeth',
      desc: '',
      args: [],
    );
  }

  /// `口腔拔牙史`
  String get oral_tooth_extraction_history {
    return Intl.message(
      '口腔拔牙史',
      name: 'oral_tooth_extraction_history',
      desc: '',
      args: [],
    );
  }

  /// `诊断`
  String get diagnosis {
    return Intl.message(
      '诊断',
      name: 'diagnosis',
      desc: '',
      args: [],
    );
  }

  /// `既往史`
  String get past_illness {
    return Intl.message(
      '既往史',
      name: 'past_illness',
      desc: '',
      args: [],
    );
  }

  /// `现病史`
  String get present_illness {
    return Intl.message(
      '现病史',
      name: 'present_illness',
      desc: '',
      args: [],
    );
  }

  /// `体格检查`
  String get physical_examination {
    return Intl.message(
      '体格检查',
      name: 'physical_examination',
      desc: '',
      args: [],
    );
  }

  /// `关节检查`
  String get joint_check {
    return Intl.message(
      '关节检查',
      name: 'joint_check',
      desc: '',
      args: [],
    );
  }

  /// `疼痛`
  String get pain {
    return Intl.message(
      '疼痛',
      name: 'pain',
      desc: '',
      args: [],
    );
  }

  /// `弹响`
  String get sound {
    return Intl.message(
      '弹响',
      name: 'sound',
      desc: '',
      args: [],
    );
  }

  /// `张口型`
  String get mouth_type {
    return Intl.message(
      '张口型',
      name: 'mouth_type',
      desc: '',
      args: [],
    );
  }

  /// `张口度`
  String get mouth_opening {
    return Intl.message(
      '张口度',
      name: 'mouth_opening',
      desc: '',
      args: [],
    );
  }

  /// `其他异常`
  String get other_exception {
    return Intl.message(
      '其他异常',
      name: 'other_exception',
      desc: '',
      args: [],
    );
  }

  /// `X线片分析`
  String get xray_analysis {
    return Intl.message(
      'X线片分析',
      name: 'xray_analysis',
      desc: '',
      args: [],
    );
  }

  /// `头颅侧位片`
  String get cranial {
    return Intl.message(
      '头颅侧位片',
      name: 'cranial',
      desc: '',
      args: [],
    );
  }

  /// `曲面断层片`
  String get surface_slices {
    return Intl.message(
      '曲面断层片',
      name: 'surface_slices',
      desc: '',
      args: [],
    );
  }

  /// `一般临床检查`
  String get base_clinical_check {
    return Intl.message(
      '一般临床检查',
      name: 'base_clinical_check',
      desc: '',
      args: [],
    );
  }

  /// `口腔卫生`
  String get oral_hygiene {
    return Intl.message(
      '口腔卫生',
      name: 'oral_hygiene',
      desc: '',
      args: [],
    );
  }

  /// `牙周组织`
  String get periodontium {
    return Intl.message(
      '牙周组织',
      name: 'periodontium',
      desc: '',
      args: [],
    );
  }

  /// `发育状况`
  String get development_status {
    return Intl.message(
      '发育状况',
      name: 'development_status',
      desc: '',
      args: [],
    );
  }

  /// `营养状况`
  String get nutritional_status {
    return Intl.message(
      '营养状况',
      name: 'nutritional_status',
      desc: '',
      args: [],
    );
  }

  /// `颜面检查`
  String get face_check {
    return Intl.message(
      '颜面检查',
      name: 'face_check',
      desc: '',
      args: [],
    );
  }

  /// `正面`
  String get front {
    return Intl.message(
      '正面',
      name: 'front',
      desc: '',
      args: [],
    );
  }

  /// `侧面`
  String get side {
    return Intl.message(
      '侧面',
      name: 'side',
      desc: '',
      args: [],
    );
  }

  /// `骨性`
  String get bony {
    return Intl.message(
      '骨性',
      name: 'bony',
      desc: '',
      args: [],
    );
  }

  /// `牙性`
  String get teeth {
    return Intl.message(
      '牙性',
      name: 'teeth',
      desc: '',
      args: [],
    );
  }

  /// `拥挤度`
  String get crowding {
    return Intl.message(
      '拥挤度',
      name: 'crowding',
      desc: '',
      args: [],
    );
  }

  /// `面型`
  String get face_type {
    return Intl.message(
      '面型',
      name: 'face_type',
      desc: '',
      args: [],
    );
  }

  /// `生长型`
  String get growth_type {
    return Intl.message(
      '生长型',
      name: 'growth_type',
      desc: '',
      args: [],
    );
  }

  /// `磨牙关系`
  String get molar_relationship {
    return Intl.message(
      '磨牙关系',
      name: 'molar_relationship',
      desc: '',
      args: [],
    );
  }

  /// `治疗方案`
  String get treatment {
    return Intl.message(
      '治疗方案',
      name: 'treatment',
      desc: '',
      args: [],
    );
  }

  /// `分析中...`
  String get in_analytic {
    return Intl.message(
      '分析中...',
      name: 'in_analytic',
      desc: '',
      args: [],
    );
  }

  /// `暂无AI分析结果\n请使用灵芽网页端开启分析`
  String get no_analytic_result {
    return Intl.message(
      '暂无AI分析结果\n请使用灵芽网页端开启分析',
      name: 'no_analytic_result',
      desc: '',
      args: [],
    );
  }

  /// `分析结果`
  String get analytic_result {
    return Intl.message(
      '分析结果',
      name: 'analytic_result',
      desc: '',
      args: [],
    );
  }

  /// `全部牙齿 %i 颗`
  String get all_teeth_count {
    return Intl.message(
      '全部牙齿 %i 颗',
      name: 'all_teeth_count',
      desc: '',
      args: [],
    );
  }

  /// `正常牙齿\n%i颗`
  String get normal_teeth_count {
    return Intl.message(
      '正常牙齿\n%i颗',
      name: 'normal_teeth_count',
      desc: '',
      args: [],
    );
  }

  /// `牙齿缺失\n%i颗`
  String get loss_teeth_count {
    return Intl.message(
      '牙齿缺失\n%i颗',
      name: 'loss_teeth_count',
      desc: '',
      args: [],
    );
  }

  /// `非正常牙齿\n%i颗`
  String get abnormal_teeth_count {
    return Intl.message(
      '非正常牙齿\n%i颗',
      name: 'abnormal_teeth_count',
      desc: '',
      args: [],
    );
  }

  /// `恒牙`
  String get permanent_tooth {
    return Intl.message(
      '恒牙',
      name: 'permanent_tooth',
      desc: '',
      args: [],
    );
  }

  /// `乳牙`
  String get baby_tooth {
    return Intl.message(
      '乳牙',
      name: 'baby_tooth',
      desc: '',
      args: [],
    );
  }

  /// `牙体、牙周`
  String get tooth_body {
    return Intl.message(
      '牙体、牙周',
      name: 'tooth_body',
      desc: '',
      args: [],
    );
  }

  /// `牙周`
  String get tooth_around {
    return Intl.message(
      '牙周',
      name: 'tooth_around',
      desc: '',
      args: [],
    );
  }

  /// `牙列`
  String get tooth_list {
    return Intl.message(
      '牙列',
      name: 'tooth_list',
      desc: '',
      args: [],
    );
  }

  /// `咬合关系`
  String get bat_relation {
    return Intl.message(
      '咬合关系',
      name: 'bat_relation',
      desc: '',
      args: [],
    );
  }

  /// `患者备注`
  String get patient_remark {
    return Intl.message(
      '患者备注',
      name: 'patient_remark',
      desc: '',
      args: [],
    );
  }

  /// `牙体缺失`
  String get teeth_lost {
    return Intl.message(
      '牙体缺失',
      name: 'teeth_lost',
      desc: '',
      args: [],
    );
  }

  /// `由灵芽智能口腔医疗平台生成，此报告仅供参考，不做最后诊断依据`
  String get declare_case {
    return Intl.message(
      '由灵芽智能口腔医疗平台生成，此报告仅供参考，不做最后诊断依据',
      name: 'declare_case',
      desc: '',
      args: [],
    );
  }

  /// `选择监控记录`
  String get select_record {
    return Intl.message(
      '选择监控记录',
      name: 'select_record',
      desc: '',
      args: [],
    );
  }

  /// `口腔全科观察`
  String get monitor_name_general {
    return Intl.message(
      '口腔全科观察',
      name: 'monitor_name_general',
      desc: '',
      args: [],
    );
  }

  /// `隐形矫正监控`
  String get monitor_name_clear {
    return Intl.message(
      '隐形矫正监控',
      name: 'monitor_name_clear',
      desc: '',
      args: [],
    );
  }

  /// `固定矫正监控`
  String get monitor_name_fixed {
    return Intl.message(
      '固定矫正监控',
      name: 'monitor_name_fixed',
      desc: '',
      args: [],
    );
  }

  /// `共%i条记录`
  String get total_record_count {
    return Intl.message(
      '共%i条记录',
      name: 'total_record_count',
      desc: '',
      args: [],
    );
  }

  /// `灵芽需要请求您的相机权限\n用于拍摄面像照`
  String get request_camera_smile {
    return Intl.message(
      '灵芽需要请求您的相机权限\n用于拍摄面像照',
      name: 'request_camera_smile',
      desc: '',
      args: [],
    );
  }

  /// `灵芽需要请求您的相机权限\n用于拍摄口腔图像`
  String get request_camera_scan {
    return Intl.message(
      '灵芽需要请求您的相机权限\n用于拍摄口腔图像',
      name: 'request_camera_scan',
      desc: '',
      args: [],
    );
  }

  /// `已保存到系统相册`
  String get save_to_gallery {
    return Intl.message(
      '已保存到系统相册',
      name: 'save_to_gallery',
      desc: '',
      args: [],
    );
  }

  /// `拍摄出了点问题，请重拍本段`
  String get zip_error {
    return Intl.message(
      '拍摄出了点问题，请重拍本段',
      name: 'zip_error',
      desc: '',
      args: [],
    );
  }

  /// `正常`
  String get status_normal {
    return Intl.message(
      '正常',
      name: 'status_normal',
      desc: '',
      args: [],
    );
  }

  /// `如需机构版的完整功能，请使用网页版灵芽`
  String get tip_use_web_lingya {
    return Intl.message(
      '如需机构版的完整功能，请使用网页版灵芽',
      name: 'tip_use_web_lingya',
      desc: '',
      args: [],
    );
  }

  /// `完成`
  String get complete {
    return Intl.message(
      '完成',
      name: 'complete',
      desc: '',
      args: [],
    );
  }

  /// `扫描录入`
  String get scan_to_record {
    return Intl.message(
      '扫描录入',
      name: 'scan_to_record',
      desc: '',
      args: [],
    );
  }

  /// `口腔数据采集`
  String get mouth_data_collect {
    return Intl.message(
      '口腔数据采集',
      name: 'mouth_data_collect',
      desc: '',
      args: [],
    );
  }

  /// `新建活动`
  String get add_collect {
    return Intl.message(
      '新建活动',
      name: 'add_collect',
      desc: '',
      args: [],
    );
  }

  /// `新建口腔数据采集`
  String get add_mouth_data_collect {
    return Intl.message(
      '新建口腔数据采集',
      name: 'add_mouth_data_collect',
      desc: '',
      args: [],
    );
  }

  /// `编辑口腔数据采集`
  String get edit_mouth_data_collect {
    return Intl.message(
      '编辑口腔数据采集',
      name: 'edit_mouth_data_collect',
      desc: '',
      args: [],
    );
  }

  /// `添加监控协议`
  String get add_mooeli_monitor {
    return Intl.message(
      '添加监控协议',
      name: 'add_mooeli_monitor',
      desc: '',
      args: [],
    );
  }

  /// `查看监控详情`
  String get view_mooeli_monitor {
    return Intl.message(
      '查看监控详情',
      name: 'view_mooeli_monitor',
      desc: '',
      args: [],
    );
  }

  /// `采集活动名称`
  String get collect_data_name {
    return Intl.message(
      '采集活动名称',
      name: 'collect_data_name',
      desc: '',
      args: [],
    );
  }

  /// `以下信息会自动生成在口腔报告的封面`
  String get data_in_cover {
    return Intl.message(
      '以下信息会自动生成在口腔报告的封面',
      name: 'data_in_cover',
      desc: '',
      args: [],
    );
  }

  /// `输入名称`
  String get input_collect_name {
    return Intl.message(
      '输入名称',
      name: 'input_collect_name',
      desc: '',
      args: [],
    );
  }

  /// `机构人员`
  String get tenant_worker {
    return Intl.message(
      '机构人员',
      name: 'tenant_worker',
      desc: '',
      args: [],
    );
  }

  /// `机构人员姓名：`
  String get tenant_worker_name {
    return Intl.message(
      '机构人员姓名：',
      name: 'tenant_worker_name',
      desc: '',
      args: [],
    );
  }

  /// `请输入姓名`
  String get input_tenant_worker_name {
    return Intl.message(
      '请输入姓名',
      name: 'input_tenant_worker_name',
      desc: '',
      args: [],
    );
  }

  /// `机构人员电话：`
  String get tenant_worker_phone {
    return Intl.message(
      '机构人员电话：',
      name: 'tenant_worker_phone',
      desc: '',
      args: [],
    );
  }

  /// `请输入电话号码`
  String get input_tenant_worker_phone {
    return Intl.message(
      '请输入电话号码',
      name: 'input_tenant_worker_phone',
      desc: '',
      args: [],
    );
  }

  /// `机构门诊地址：`
  String get tenant_worker_address {
    return Intl.message(
      '机构门诊地址：',
      name: 'tenant_worker_address',
      desc: '',
      args: [],
    );
  }

  /// `请输入机构门诊地址，最多50个字`
  String get input_tenant_worker_address {
    return Intl.message(
      '请输入机构门诊地址，最多50个字',
      name: 'input_tenant_worker_address',
      desc: '',
      args: [],
    );
  }

  /// `机构人员二维码：`
  String get tenant_worker_qrcode {
    return Intl.message(
      '机构人员二维码：',
      name: 'tenant_worker_qrcode',
      desc: '',
      args: [],
    );
  }

  /// `上传图片的长宽比最好接近1:1，且文件应小于2M`
  String get tenant_worker_qrcode_tip {
    return Intl.message(
      '上传图片的长宽比最好接近1:1，且文件应小于2M',
      name: 'tenant_worker_qrcode_tip',
      desc: '',
      args: [],
    );
  }

  /// `二维码说明：`
  String get tenant_worker_qrcode_desc {
    return Intl.message(
      '二维码说明：',
      name: 'tenant_worker_qrcode_desc',
      desc: '',
      args: [],
    );
  }

  /// `请输入二维码说明，最多9个字`
  String get input_tenant_worker_qrcode {
    return Intl.message(
      '请输入二维码说明，最多9个字',
      name: 'input_tenant_worker_qrcode',
      desc: '',
      args: [],
    );
  }

  /// `残根`
  String get disease_0 {
    return Intl.message(
      '残根',
      name: 'disease_0',
      desc: '',
      args: [],
    );
  }

  /// `残冠`
  String get disease_1 {
    return Intl.message(
      '残冠',
      name: 'disease_1',
      desc: '',
      args: [],
    );
  }

  /// `龋坏`
  String get disease_2 {
    return Intl.message(
      '龋坏',
      name: 'disease_2',
      desc: '',
      args: [],
    );
  }

  /// `脱矿`
  String get disease_3 {
    return Intl.message(
      '脱矿',
      name: 'disease_3',
      desc: '',
      args: [],
    );
  }

  /// `色素沉着`
  String get disease_4 {
    return Intl.message(
      '色素沉着',
      name: 'disease_4',
      desc: '',
      args: [],
    );
  }

  /// `楔状缺损`
  String get disease_5 {
    return Intl.message(
      '楔状缺损',
      name: 'disease_5',
      desc: '',
      args: [],
    );
  }

  /// `软垢`
  String get disease_6 {
    return Intl.message(
      '软垢',
      name: 'disease_6',
      desc: '',
      args: [],
    );
  }

  /// `牙周炎`
  String get disease_7 {
    return Intl.message(
      '牙周炎',
      name: 'disease_7',
      desc: '',
      args: [],
    );
  }

  /// `牙结石`
  String get disease_8 {
    return Intl.message(
      '牙结石',
      name: 'disease_8',
      desc: '',
      args: [],
    );
  }

  /// `磨损`
  String get disease_9 {
    return Intl.message(
      '磨损',
      name: 'disease_9',
      desc: '',
      args: [],
    );
  }

  /// `牙齿扭转`
  String get disease_10 {
    return Intl.message(
      '牙齿扭转',
      name: 'disease_10',
      desc: '',
      args: [],
    );
  }

  /// `牙颜色异常（牙髓病变）`
  String get disease_11 {
    return Intl.message(
      '牙颜色异常（牙髓病变）',
      name: 'disease_11',
      desc: '',
      args: [],
    );
  }

  /// `牙列不齐`
  String get disease_12 {
    return Intl.message(
      '牙列不齐',
      name: 'disease_12',
      desc: '',
      args: [],
    );
  }

  /// `牙列间隙`
  String get disease_13 {
    return Intl.message(
      '牙列间隙',
      name: 'disease_13',
      desc: '',
      args: [],
    );
  }

  /// `全冠`
  String get disease_14 {
    return Intl.message(
      '全冠',
      name: 'disease_14',
      desc: '',
      args: [],
    );
  }

  /// `AI分析结果`
  String get ai_analytics {
    return Intl.message(
      'AI分析结果',
      name: 'ai_analytics',
      desc: '',
      args: [],
    );
  }

  /// `正在进行AI分析......\n预计2小时内可查看结果`
  String get ai_status_analyzing {
    return Intl.message(
      '正在进行AI分析......\n预计2小时内可查看结果',
      name: 'ai_status_analyzing',
      desc: '',
      args: [],
    );
  }

  /// `口内照不规范，抱歉无法进行AI分析`
  String get ai_status_no_result {
    return Intl.message(
      '口内照不规范，抱歉无法进行AI分析',
      name: 'ai_status_no_result',
      desc: '',
      args: [],
    );
  }

  /// `潜在治疗方向`
  String get convert_project {
    return Intl.message(
      '潜在治疗方向',
      name: 'convert_project',
      desc: '',
      args: [],
    );
  }

  /// `该标签内容基于被采集人的AI分析结果自动生成推荐,旨在指出患者潜在治疗方向。\n仅供参考,最终治疗方案需由专业医生结合其实际情况综合判定。`
  String get convert_project_tip {
    return Intl.message(
      '该标签内容基于被采集人的AI分析结果自动生成推荐,旨在指出患者潜在治疗方向。\n仅供参考,最终治疗方案需由专业医生结合其实际情况综合判定。',
      name: 'convert_project_tip',
      desc: '',
      args: [],
    );
  }

  /// `跟进阶段`
  String get progress_stage {
    return Intl.message(
      '跟进阶段',
      name: 'progress_stage',
      desc: '',
      args: [],
    );
  }

  /// `保存并新建`
  String get save_and_add_next {
    return Intl.message(
      '保存并新建',
      name: 'save_and_add_next',
      desc: '',
      args: [],
    );
  }

  /// `您有必填项未填`
  String get request_data_lost {
    return Intl.message(
      '您有必填项未填',
      name: 'request_data_lost',
      desc: '',
      args: [],
    );
  }

  /// `您有图片被系统清理了，请确认`
  String get record_image_lost {
    return Intl.message(
      '您有图片被系统清理了，请确认',
      name: 'record_image_lost',
      desc: '',
      args: [],
    );
  }

  /// `已保存，可在待上传中查看`
  String get saved_to_upload_list {
    return Intl.message(
      '已保存，可在待上传中查看',
      name: 'saved_to_upload_list',
      desc: '',
      args: [],
    );
  }

  /// `待完善`
  String get status_recording {
    return Intl.message(
      '待完善',
      name: 'status_recording',
      desc: '',
      args: [],
    );
  }

  /// `待上传`
  String get status_waiting_upload {
    return Intl.message(
      '待上传',
      name: 'status_waiting_upload',
      desc: '',
      args: [],
    );
  }

  /// `正在上传`
  String get status_uploading {
    return Intl.message(
      '正在上传',
      name: 'status_uploading',
      desc: '',
      args: [],
    );
  }

  /// `已上传`
  String get status_complete_upload {
    return Intl.message(
      '已上传',
      name: 'status_complete_upload',
      desc: '',
      args: [],
    );
  }

  /// `上传失败`
  String get status_fail_upload {
    return Intl.message(
      '上传失败',
      name: 'status_fail_upload',
      desc: '',
      args: [],
    );
  }

  /// `已上传到云端`
  String get upload_to_cloud_success {
    return Intl.message(
      '已上传到云端',
      name: 'upload_to_cloud_success',
      desc: '',
      args: [],
    );
  }

  /// `正在上传中`
  String get upload_to_cloud_ing {
    return Intl.message(
      '正在上传中',
      name: 'upload_to_cloud_ing',
      desc: '',
      args: [],
    );
  }

  /// `全部上传`
  String get upload_all {
    return Intl.message(
      '全部上传',
      name: 'upload_all',
      desc: '',
      args: [],
    );
  }

  /// `正在上传: %i    已上传: %i`
  String get upload_list_count {
    return Intl.message(
      '正在上传: %i    已上传: %i',
      name: 'upload_list_count',
      desc: '',
      args: [],
    );
  }

  /// `新建患者扫描`
  String get create_new_scan {
    return Intl.message(
      '新建患者扫描',
      name: 'create_new_scan',
      desc: '',
      args: [],
    );
  }

  /// `如需制作报告，请使用网页版灵芽`
  String get get_report_in_web {
    return Intl.message(
      '如需制作报告，请使用网页版灵芽',
      name: 'get_report_in_web',
      desc: '',
      args: [],
    );
  }

  /// `为确保数据完整性，请先点击全部上传，然后再继续添加患者`
  String get upload_records_first {
    return Intl.message(
      '为确保数据完整性，请先点击全部上传，然后再继续添加患者',
      name: 'upload_records_first',
      desc: '',
      args: [],
    );
  }

  /// `注意`
  String get attention {
    return Intl.message(
      '注意',
      name: 'attention',
      desc: '',
      args: [],
    );
  }

  /// `您有%i条扫描数据异常所以无法上传，请先处理异常数据。`
  String get record_all_incomplete {
    return Intl.message(
      '您有%i条扫描数据异常所以无法上传，请先处理异常数据。',
      name: 'record_all_incomplete',
      desc: '',
      args: [],
    );
  }

  /// `您有%i条扫描数据异常所以无法上传。 您可以先上传其他完整数据，再去“待上传”列表处理异常数据。`
  String get record_part_incomplete {
    return Intl.message(
      '您有%i条扫描数据异常所以无法上传。 您可以先上传其他完整数据，再去“待上传”列表处理异常数据。',
      name: 'record_part_incomplete',
      desc: '',
      args: [],
    );
  }

  /// `先上传其他完整数据`
  String get upload_other_complete_data {
    return Intl.message(
      '先上传其他完整数据',
      name: 'upload_other_complete_data',
      desc: '',
      args: [],
    );
  }

  /// `我知道了`
  String get i_knew {
    return Intl.message(
      '我知道了',
      name: 'i_knew',
      desc: '',
      args: [],
    );
  }

  /// `重新上传`
  String get retry_upload {
    return Intl.message(
      '重新上传',
      name: 'retry_upload',
      desc: '',
      args: [],
    );
  }

  /// `每条数据上传约3秒，请耐心等待`
  String get upload_record_time_tip {
    return Intl.message(
      '每条数据上传约3秒，请耐心等待',
      name: 'upload_record_time_tip',
      desc: '',
      args: [],
    );
  }

  /// `如果有上传失败的扫描记录，请点击重新上传`
  String get retry_upload_failed_record {
    return Intl.message(
      '如果有上传失败的扫描记录，请点击重新上传',
      name: 'retry_upload_failed_record',
      desc: '',
      args: [],
    );
  }

  /// `删除活动`
  String get delete_activity {
    return Intl.message(
      '删除活动',
      name: 'delete_activity',
      desc: '',
      args: [],
    );
  }

  /// `确认删除本次口腔数据采集吗？`
  String get confirm_delete_activity {
    return Intl.message(
      '确认删除本次口腔数据采集吗？',
      name: 'confirm_delete_activity',
      desc: '',
      args: [],
    );
  }

  /// `删除所选活动后，数据将无法找回`
  String get delete_activity_not_recoverd {
    return Intl.message(
      '删除所选活动后，数据将无法找回',
      name: 'delete_activity_not_recoverd',
      desc: '',
      args: [],
    );
  }

  /// `删除活动`
  String get confirm_delete_activity_name {
    return Intl.message(
      '删除活动',
      name: 'confirm_delete_activity_name',
      desc: '',
      args: [],
    );
  }

  /// `患者详情`
  String get patient_detail {
    return Intl.message(
      '患者详情',
      name: 'patient_detail',
      desc: '',
      args: [],
    );
  }

  /// `拼命加载中\n预计30分钟内可查看图片`
  String get loading_images {
    return Intl.message(
      '拼命加载中\n预计30分钟内可查看图片',
      name: 'loading_images',
      desc: '',
      args: [],
    );
  }

  /// `图片加载失败`
  String get loading_image_error {
    return Intl.message(
      '图片加载失败',
      name: 'loading_image_error',
      desc: '',
      args: [],
    );
  }

  /// `请求系统存储权限失败，请手动授权`
  String get request_storage_permission {
    return Intl.message(
      '请求系统存储权限失败，请手动授权',
      name: 'request_storage_permission',
      desc: '',
      args: [],
    );
  }

  /// `暂存并返回`
  String get save_and_return {
    return Intl.message(
      '暂存并返回',
      name: 'save_and_return',
      desc: '',
      args: [],
    );
  }

  /// `请查看页面下方的待上传列表\n或新建患者扫描`
  String get record_empty_tip {
    return Intl.message(
      '请查看页面下方的待上传列表\n或新建患者扫描',
      name: 'record_empty_tip',
      desc: '',
      args: [],
    );
  }

  /// `密码长度8-20个字符，需包含大小写字母。`
  String get password_rule {
    return Intl.message(
      '密码长度8-20个字符，需包含大小写字母。',
      name: 'password_rule',
      desc: '',
      args: [],
    );
  }

  /// `当前账号:`
  String get current_account {
    return Intl.message(
      '当前账号:',
      name: 'current_account',
      desc: '',
      args: [],
    );
  }

  /// `删除成功`
  String get delete_success {
    return Intl.message(
      '删除成功',
      name: 'delete_success',
      desc: '',
      args: [],
    );
  }

  /// `手机号`
  String get collect_phone {
    return Intl.message(
      '手机号',
      name: 'collect_phone',
      desc: '',
      args: [],
    );
  }

  /// `请重拍缺失的口内照并提交，感谢您的理解与支持`
  String get cannot_find_mouth_scan {
    return Intl.message(
      '请重拍缺失的口内照并提交，感谢您的理解与支持',
      name: 'cannot_find_mouth_scan',
      desc: '',
      args: [],
    );
  }

  /// `提示：为确保数据完整，活动结束后请立刻上传`
  String get upload_today {
    return Intl.message(
      '提示：为确保数据完整，活动结束后请立刻上传',
      name: 'upload_today',
      desc: '',
      args: [],
    );
  }

  /// `灵芽个人版`
  String get personal_version {
    return Intl.message(
      '灵芽个人版',
      name: 'personal_version',
      desc: '',
      args: [],
    );
  }

  /// `开始扫描`
  String get start_scan {
    return Intl.message(
      '开始扫描',
      name: 'start_scan',
      desc: '',
      args: [],
    );
  }

  /// `全部`
  String get all {
    return Intl.message(
      '全部',
      name: 'all',
      desc: '',
      args: [],
    );
  }

  /// `完善个人信息`
  String get complete_user_profile {
    return Intl.message(
      '完善个人信息',
      name: 'complete_user_profile',
      desc: '',
      args: [],
    );
  }

  /// `您还没有加入任何疗程`
  String get you_have_no_treatment {
    return Intl.message(
      '您还没有加入任何疗程',
      name: 'you_have_no_treatment',
      desc: '',
      args: [],
    );
  }

  /// `您可以:`
  String get you_can {
    return Intl.message(
      '您可以:',
      name: 'you_can',
      desc: '',
      args: [],
    );
  }

  /// `未加载到数据，请尝试下拉刷新`
  String get no_data {
    return Intl.message(
      '未加载到数据，请尝试下拉刷新',
      name: 'no_data',
      desc: '',
      args: [],
    );
  }

  /// `账号设置`
  String get account_setting {
    return Intl.message(
      '账号设置',
      name: 'account_setting',
      desc: '',
      args: [],
    );
  }

  /// `关于灵芽`
  String get about_lyoral {
    return Intl.message(
      '关于灵芽',
      name: 'about_lyoral',
      desc: '',
      args: [],
    );
  }

  /// `常见问题`
  String get frequent_questions {
    return Intl.message(
      '常见问题',
      name: 'frequent_questions',
      desc: '',
      args: [],
    );
  }

  /// `常见问题`
  String get guide {
    return Intl.message(
      '常见问题',
      name: 'guide',
      desc: '',
      args: [],
    );
  }

  /// `头像`
  String get avatar {
    return Intl.message(
      '头像',
      name: 'avatar',
      desc: '',
      args: [],
    );
  }

  /// `昵称`
  String get nickname {
    return Intl.message(
      '昵称',
      name: 'nickname',
      desc: '',
      args: [],
    );
  }

  /// `请输入昵称`
  String get input_nickname {
    return Intl.message(
      '请输入昵称',
      name: 'input_nickname',
      desc: '',
      args: [],
    );
  }

  /// `点击设置`
  String get click_to_set {
    return Intl.message(
      '点击设置',
      name: 'click_to_set',
      desc: '',
      args: [],
    );
  }

  /// `拍照`
  String get take_photo {
    return Intl.message(
      '拍照',
      name: 'take_photo',
      desc: '',
      args: [],
    );
  }

  /// `从相册选取`
  String get choose_from_gallery {
    return Intl.message(
      '从相册选取',
      name: 'choose_from_gallery',
      desc: '',
      args: [],
    );
  }

  /// `保存`
  String get save {
    return Intl.message(
      '保存',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `保存成功`
  String get save_success {
    return Intl.message(
      '保存成功',
      name: 'save_success',
      desc: '',
      args: [],
    );
  }

  /// `注销账号`
  String get destroy_account {
    return Intl.message(
      '注销账号',
      name: 'destroy_account',
      desc: '',
      args: [],
    );
  }

  /// `提醒`
  String get warn {
    return Intl.message(
      '提醒',
      name: 'warn',
      desc: '',
      args: [],
    );
  }

  /// `确认退出登录吗？`
  String get confirm_logout {
    return Intl.message(
      '确认退出登录吗？',
      name: 'confirm_logout',
      desc: '',
      args: [],
    );
  }

  /// `当前为“离线状态”，当前账号数据已自动导出至本地，退出后请在联网状态下登录其他账号，否则将无法继续使用乐齿拍系统`
  String get confirm_logout_offline {
    return Intl.message(
      '当前为“离线状态”，当前账号数据已自动导出至本地，退出后请在联网状态下登录其他账号，否则将无法继续使用乐齿拍系统',
      name: 'confirm_logout_offline',
      desc: '',
      args: [],
    );
  }

  /// `版本更新`
  String get version_upgrade {
    return Intl.message(
      '版本更新',
      name: 'version_upgrade',
      desc: '',
      args: [],
    );
  }

  /// `新版本`
  String get new_version {
    return Intl.message(
      '新版本',
      name: 'new_version',
      desc: '',
      args: [],
    );
  }

  /// `当前版本`
  String get version {
    return Intl.message(
      '当前版本',
      name: 'version',
      desc: '',
      args: [],
    );
  }

  /// `扫描`
  String get scan {
    return Intl.message(
      '扫描',
      name: 'scan',
      desc: '',
      args: [],
    );
  }

  /// `提交`
  String get commit {
    return Intl.message(
      '提交',
      name: 'commit',
      desc: '',
      args: [],
    );
  }

  /// `取下牙套，张大嘴巴，扫描上颌`
  String get pickoff_mask_scan_up {
    return Intl.message(
      '取下牙套，张大嘴巴，扫描上颌',
      name: 'pickoff_mask_scan_up',
      desc: '',
      args: [],
    );
  }

  /// `取下牙套，自然咬合，从左向右扫描`
  String get pickoff_mask_natural_bite {
    return Intl.message(
      '取下牙套，自然咬合，从左向右扫描',
      name: 'pickoff_mask_natural_bite',
      desc: '',
      args: [],
    );
  }

  /// `取下牙套，双颌微张，从左向右扫描`
  String get pickoff_mask_open_bimaxillary {
    return Intl.message(
      '取下牙套，双颌微张，从左向右扫描',
      name: 'pickoff_mask_open_bimaxillary',
      desc: '',
      args: [],
    );
  }

  /// `戴上牙套，双颌微张，从左向右扫描`
  String get pickup_mask_open_bimaxillary {
    return Intl.message(
      '戴上牙套，双颌微张，从左向右扫描',
      name: 'pickup_mask_open_bimaxillary',
      desc: '',
      args: [],
    );
  }

  /// `取下牙套，张大嘴巴，扫描下颌`
  String get pickoff_mask_scan_down {
    return Intl.message(
      '取下牙套，张大嘴巴，扫描下颌',
      name: 'pickoff_mask_scan_down',
      desc: '',
      args: [],
    );
  }

  /// `张大嘴巴，扫描上颌`
  String get no_mask_scan_up {
    return Intl.message(
      '张大嘴巴，扫描上颌',
      name: 'no_mask_scan_up',
      desc: '',
      args: [],
    );
  }

  /// `自然咬合，从左向右扫描`
  String get no_mask_natural_bite {
    return Intl.message(
      '自然咬合，从左向右扫描',
      name: 'no_mask_natural_bite',
      desc: '',
      args: [],
    );
  }

  /// `双颌微张，从左向右扫描`
  String get no_mask_open_bimaxillary {
    return Intl.message(
      '双颌微张，从左向右扫描',
      name: 'no_mask_open_bimaxillary',
      desc: '',
      args: [],
    );
  }

  /// `张大嘴巴，扫描下颌`
  String get no_mask_scan_down {
    return Intl.message(
      '张大嘴巴，扫描下颌',
      name: 'no_mask_scan_down',
      desc: '',
      args: [],
    );
  }

  /// `重新拍摄`
  String get retake_photo {
    return Intl.message(
      '重新拍摄',
      name: 'retake_photo',
      desc: '',
      args: [],
    );
  }

  /// `重拍全部`
  String get retake_all_photos {
    return Intl.message(
      '重拍全部',
      name: 'retake_all_photos',
      desc: '',
      args: [],
    );
  }

  /// `重拍本段`
  String get retake_current_record {
    return Intl.message(
      '重拍本段',
      name: 'retake_current_record',
      desc: '',
      args: [],
    );
  }

  /// `删除`
  String get delete {
    return Intl.message(
      '删除',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `编辑`
  String get edit {
    return Intl.message(
      '编辑',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `确认要删除照片吗？`
  String get confirm_delete_smile_photo {
    return Intl.message(
      '确认要删除照片吗？',
      name: 'confirm_delete_smile_photo',
      desc: '',
      args: [],
    );
  }

  /// `扫描详情`
  String get scan_detail {
    return Intl.message(
      '扫描详情',
      name: 'scan_detail',
      desc: '',
      args: [],
    );
  }

  /// `您的扫描`
  String get your_scan_result {
    return Intl.message(
      '您的扫描',
      name: 'your_scan_result',
      desc: '',
      args: [],
    );
  }

  /// `标准扫描`
  String get standard_scan {
    return Intl.message(
      '标准扫描',
      name: 'standard_scan',
      desc: '',
      args: [],
    );
  }

  /// `拍摄技巧`
  String get scan_skill {
    return Intl.message(
      '拍摄技巧',
      name: 'scan_skill',
      desc: '',
      args: [],
    );
  }

  /// `示范`
  String get example {
    return Intl.message(
      '示范',
      name: 'example',
      desc: '',
      args: [],
    );
  }

  /// `扫描示范`
  String get scan_example {
    return Intl.message(
      '扫描示范',
      name: 'scan_example',
      desc: '',
      args: [],
    );
  }

  /// `重拍此段`
  String get retake_current_scan {
    return Intl.message(
      '重拍此段',
      name: 'retake_current_scan',
      desc: '',
      args: [],
    );
  }

  /// `扫描得很棒`
  String get scan_great {
    return Intl.message(
      '扫描得很棒',
      name: 'scan_great',
      desc: '',
      args: [],
    );
  }

  /// `未检测到牙齿或暴露出来的牙齿太少`
  String get tooth_expose_too_less {
    return Intl.message(
      '未检测到牙齿或暴露出来的牙齿太少',
      name: 'tooth_expose_too_less',
      desc: '',
      args: [],
    );
  }

  /// `将开口器置入口中并对准牙齿，摄像头不被遮挡`
  String get setup_mouth_prop_tip {
    return Intl.message(
      '将开口器置入口中并对准牙齿，摄像头不被遮挡',
      name: 'setup_mouth_prop_tip',
      desc: '',
      args: [],
    );
  }

  /// `扫描结果模糊`
  String get scan_result_blur {
    return Intl.message(
      '扫描结果模糊',
      name: 'scan_result_blur',
      desc: '',
      args: [],
    );
  }

  /// `画面模糊，建议使用适配器以便对焦`
  String get scan_blur_focus_fail {
    return Intl.message(
      '画面模糊，建议使用适配器以便对焦',
      name: 'scan_blur_focus_fail',
      desc: '',
      args: [],
    );
  }

  /// `1.建议安装适配器\n2.清洁手机摄像头`
  String get scan_blur_action {
    return Intl.message(
      '1.建议安装适配器\n2.清洁手机摄像头',
      name: 'scan_blur_action',
      desc: '',
      args: [],
    );
  }

  /// `扫描方向错误`
  String get scan_direction_error {
    return Intl.message(
      '扫描方向错误',
      name: 'scan_direction_error',
      desc: '',
      args: [],
    );
  }

  /// `方向错误，请跟随箭头方向移动开口器`
  String get move_mouth_prop_follow_arrow {
    return Intl.message(
      '方向错误，请跟随箭头方向移动开口器',
      name: 'move_mouth_prop_follow_arrow',
      desc: '',
      args: [],
    );
  }

  /// `开口器推向箭头所指方向，直到看清最后一颗牙齿`
  String get scan_left_to_right {
    return Intl.message(
      '开口器推向箭头所指方向，直到看清最后一颗牙齿',
      name: 'scan_left_to_right',
      desc: '',
      args: [],
    );
  }

  /// `当箭头移到中间时，门牙应该居中`
  String get scan_center_front {
    return Intl.message(
      '当箭头移到中间时，门牙应该居中',
      name: 'scan_center_front',
      desc: '',
      args: [],
    );
  }

  /// `方向错误，请扫描上牙的咬合面`
  String get scan_up_bat {
    return Intl.message(
      '方向错误，请扫描上牙的咬合面',
      name: 'scan_up_bat',
      desc: '',
      args: [],
    );
  }

  /// `嘴巴张到最大，将开口器深入口腔并完全向上`
  String get scan_iscan_into_mouth_up {
    return Intl.message(
      '嘴巴张到最大，将开口器深入口腔并完全向上',
      name: 'scan_iscan_into_mouth_up',
      desc: '',
      args: [],
    );
  }

  /// `方向错误，请扫描下牙的咬合面`
  String get scan_down_bat {
    return Intl.message(
      '方向错误，请扫描下牙的咬合面',
      name: 'scan_down_bat',
      desc: '',
      args: [],
    );
  }

  /// `嘴巴张到最大，将开口器深入口腔并完全向下`
  String get scan_iscan_into_mouth_down {
    return Intl.message(
      '嘴巴张到最大，将开口器深入口腔并完全向下',
      name: 'scan_iscan_into_mouth_down',
      desc: '',
      args: [],
    );
  }

  /// `未扫描到完整的后牙`
  String get scan_no_behind_tooth {
    return Intl.message(
      '未扫描到完整的后牙',
      name: 'scan_no_behind_tooth',
      desc: '',
      args: [],
    );
  }

  /// `扫描时，头向反方向转，更容易扫到后牙`
  String get scan_turn_head_behind_tooth {
    return Intl.message(
      '扫描时，头向反方向转，更容易扫到后牙',
      name: 'scan_turn_head_behind_tooth',
      desc: '',
      args: [],
    );
  }

  /// `牙弓角度不足，请将开口器尽可能朝上`
  String get scan_up_maxillary {
    return Intl.message(
      '牙弓角度不足，请将开口器尽可能朝上',
      name: 'scan_up_maxillary',
      desc: '',
      args: [],
    );
  }

  /// `扫描时稍微抬头，开口器尽可能朝上`
  String get scan_try_open_mouth_up {
    return Intl.message(
      '扫描时稍微抬头，开口器尽可能朝上',
      name: 'scan_try_open_mouth_up',
      desc: '',
      args: [],
    );
  }

  /// `牙弓角度不足，请将开口器尽可能朝下`
  String get scan_down_maxillary {
    return Intl.message(
      '牙弓角度不足，请将开口器尽可能朝下',
      name: 'scan_down_maxillary',
      desc: '',
      args: [],
    );
  }

  /// `扫描时稍微低头，开口器尽可能朝下`
  String get scan_try_open_mouth_down {
    return Intl.message(
      '扫描时稍微低头，开口器尽可能朝下',
      name: 'scan_try_open_mouth_down',
      desc: '',
      args: [],
    );
  }

  /// `请将门牙居中`
  String get put_front_tooth_center {
    return Intl.message(
      '请将门牙居中',
      name: 'put_front_tooth_center',
      desc: '',
      args: [],
    );
  }

  /// `当箭头移到中间时，门牙应该居中`
  String get scan_follow_progress_center {
    return Intl.message(
      '当箭头移到中间时，门牙应该居中',
      name: 'scan_follow_progress_center',
      desc: '',
      args: [],
    );
  }

  /// `上牙的咬合面未扫描完整`
  String get scan_whole_up_maxillary {
    return Intl.message(
      '上牙的咬合面未扫描完整',
      name: 'scan_whole_up_maxillary',
      desc: '',
      args: [],
    );
  }

  /// `下牙的咬合面未扫描完整`
  String get scan_while_down_maxillary {
    return Intl.message(
      '下牙的咬合面未扫描完整',
      name: 'scan_while_down_maxillary',
      desc: '',
      args: [],
    );
  }

  /// `未自然咬合`
  String get bite_back_teeth {
    return Intl.message(
      '未自然咬合',
      name: 'bite_back_teeth',
      desc: '',
      args: [],
    );
  }

  /// `咽口水，找到自然咬合的感觉，全程保持`
  String get scan_keep_bite_back_teeth {
    return Intl.message(
      '咽口水，找到自然咬合的感觉，全程保持',
      name: 'scan_keep_bite_back_teeth',
      desc: '',
      args: [],
    );
  }

  /// `牙齿张开幅度过小`
  String get gently_stretch_teeth {
    return Intl.message(
      '牙齿张开幅度过小',
      name: 'gently_stretch_teeth',
      desc: '',
      args: [],
    );
  }

  /// `正确的幅度如图所示，上下牙不能互相遮挡`
  String get scan_keep_gently_stretch_teeth {
    return Intl.message(
      '正确的幅度如图所示，上下牙不能互相遮挡',
      name: 'scan_keep_gently_stretch_teeth',
      desc: '',
      args: [],
    );
  }

  /// `牙齿张开幅度过大`
  String get gently_stretch_teeth_not_wild {
    return Intl.message(
      '牙齿张开幅度过大',
      name: 'gently_stretch_teeth_not_wild',
      desc: '',
      args: [],
    );
  }

  /// `正确的幅度如图所示，注意牙根不要被遮挡`
  String get scan_keep_gently_stretch_teeth_to_doctor {
    return Intl.message(
      '正确的幅度如图所示，注意牙根不要被遮挡',
      name: 'scan_keep_gently_stretch_teeth_to_doctor',
      desc: '',
      args: [],
    );
  }

  /// `画面不够亮`
  String get make_mouth_lighten {
    return Intl.message(
      '画面不够亮',
      name: 'make_mouth_lighten',
      desc: '',
      args: [],
    );
  }

  /// `调整手机或开口器位置，使闪光灯置于开口器取景框内`
  String get make_mouth_lighten_action {
    return Intl.message(
      '调整手机或开口器位置，使闪光灯置于开口器取景框内',
      name: 'make_mouth_lighten_action',
      desc: '',
      args: [],
    );
  }

  /// `未注册手机将自动为您注册账号`
  String get auto_register_for_new {
    return Intl.message(
      '未注册手机将自动为您注册账号',
      name: 'auto_register_for_new',
      desc: '',
      args: [],
    );
  }

  /// `登录`
  String get login {
    return Intl.message(
      '登录',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `获取验证码`
  String get get_verify_code {
    return Intl.message(
      '获取验证码',
      name: 'get_verify_code',
      desc: '',
      args: [],
    );
  }

  /// `重新获取`
  String get refetch_verify_code {
    return Intl.message(
      '重新获取',
      name: 'refetch_verify_code',
      desc: '',
      args: [],
    );
  }

  /// `请输入手机号`
  String get input_phone {
    return Intl.message(
      '请输入手机号',
      name: 'input_phone',
      desc: '',
      args: [],
    );
  }

  /// `验证码`
  String get input_verify_code {
    return Intl.message(
      '验证码',
      name: 'input_verify_code',
      desc: '',
      args: [],
    );
  }

  /// `和`
  String get and {
    return Intl.message(
      '和',
      name: 'and',
      desc: '',
      args: [],
    );
  }

  /// `请先勾选并同意《隐私政策》`
  String get please_read_and_agree {
    return Intl.message(
      '请先勾选并同意《隐私政策》',
      name: 'please_read_and_agree',
      desc: '',
      args: [],
    );
  }

  /// `同意并继续`
  String get agree_and_continue {
    return Intl.message(
      '同意并继续',
      name: 'agree_and_continue',
      desc: '',
      args: [],
    );
  }

  /// `请输入正确手机号`
  String get incorrect_phone {
    return Intl.message(
      '请输入正确手机号',
      name: 'incorrect_phone',
      desc: '',
      args: [],
    );
  }

  /// `验证码错误`
  String get incorrect_verify_code {
    return Intl.message(
      '验证码错误',
      name: 'incorrect_verify_code',
      desc: '',
      args: [],
    );
  }

  /// `发送失败,请重新获取验证码`
  String get send_verify_code_fail {
    return Intl.message(
      '发送失败,请重新获取验证码',
      name: 'send_verify_code_fail',
      desc: '',
      args: [],
    );
  }

  /// `前期准备`
  String get prepare_step {
    return Intl.message(
      '前期准备',
      name: 'prepare_step',
      desc: '',
      args: [],
    );
  }

  /// `第%i步`
  String get step_index {
    return Intl.message(
      '第%i步',
      name: 'step_index',
      desc: '',
      args: [],
    );
  }

  /// `请选择您如何进行扫描？`
  String get select_scan_mode {
    return Intl.message(
      '请选择您如何进行扫描？',
      name: 'select_scan_mode',
      desc: '',
      args: [],
    );
  }

  /// `1.音量调整至合适大小，保持网络通畅。`
  String get adjust_volume {
    return Intl.message(
      '1.音量调整至合适大小，保持网络通畅。',
      name: 'adjust_volume',
      desc: '',
      args: [],
    );
  }

  /// `下一步`
  String get next {
    return Intl.message(
      '下一步',
      name: 'next',
      desc: '',
      args: [],
    );
  }

  /// `组装好了，开始校准`
  String get setup_start_adjust {
    return Intl.message(
      '组装好了，开始校准',
      name: 'setup_start_adjust',
      desc: '',
      args: [],
    );
  }

  /// `打开声音，根据语音提示扫描，有助于更准确地完成扫描。`
  String get setup_volume_for_scan {
    return Intl.message(
      '打开声音，根据语音提示扫描，有助于更准确地完成扫描。',
      name: 'setup_volume_for_scan',
      desc: '',
      args: [],
    );
  }

  /// `请输入文字内容，最多100字`
  String get input_remark {
    return Intl.message(
      '请输入文字内容，最多100字',
      name: 'input_remark',
      desc: '',
      args: [],
    );
  }

  /// `暂无扫描记录`
  String get no_scan_record {
    return Intl.message(
      '暂无扫描记录',
      name: 'no_scan_record',
      desc: '',
      args: [],
    );
  }

  /// `取消`
  String get cancel {
    return Intl.message(
      '取消',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `去设置`
  String get go_setting {
    return Intl.message(
      '去设置',
      name: 'go_setting',
      desc: '',
      args: [],
    );
  }

  /// `隐私政策`
  String get privacy {
    return Intl.message(
      '隐私政策',
      name: 'privacy',
      desc: '',
      args: [],
    );
  }

  /// `确定`
  String get confirm_ok {
    return Intl.message(
      '确定',
      name: 'confirm_ok',
      desc: '',
      args: [],
    );
  }

  /// `前往应用商店`
  String get go_app_store {
    return Intl.message(
      '前往应用商店',
      name: 'go_app_store',
      desc: '',
      args: [],
    );
  }

  /// `前往应用宝`
  String get go_yingyongbao {
    return Intl.message(
      '前往应用宝',
      name: 'go_yingyongbao',
      desc: '',
      args: [],
    );
  }

  /// `发现新版本`
  String get find_newer_version {
    return Intl.message(
      '发现新版本',
      name: 'find_newer_version',
      desc: '',
      args: [],
    );
  }

  /// `去更新`
  String get go_upgrade {
    return Intl.message(
      '去更新',
      name: 'go_upgrade',
      desc: '',
      args: [],
    );
  }

  /// `退出灵芽`
  String get exit_app {
    return Intl.message(
      '退出灵芽',
      name: 'exit_app',
      desc: '',
      args: [],
    );
  }

  /// `当前已是最新版本`
  String get already_newest_version {
    return Intl.message(
      '当前已是最新版本',
      name: 'already_newest_version',
      desc: '',
      args: [],
    );
  }

  /// `上传失败，请重新尝试...`
  String get upload_fail {
    return Intl.message(
      '上传失败，请重新尝试...',
      name: 'upload_fail',
      desc: '',
      args: [],
    );
  }

  /// `网络不太顺畅，请检查网络设置`
  String get no_network {
    return Intl.message(
      '网络不太顺畅，请检查网络设置',
      name: 'no_network',
      desc: '',
      args: [],
    );
  }

  /// `网络不太顺畅，请连接可用的网络`
  String get need_network {
    return Intl.message(
      '网络不太顺畅，请连接可用的网络',
      name: 'need_network',
      desc: '',
      args: [],
    );
  }

  /// `倍速 快进中`
  String get speed_forward {
    return Intl.message(
      '倍速 快进中',
      name: 'speed_forward',
      desc: '',
      args: [],
    );
  }

  /// `正在加载视频...`
  String get loading_video {
    return Intl.message(
      '正在加载视频...',
      name: 'loading_video',
      desc: '',
      args: [],
    );
  }

  /// `填充`
  String get fill_in {
    return Intl.message(
      '填充',
      name: 'fill_in',
      desc: '',
      args: [],
    );
  }

  /// `默认`
  String get default_type {
    return Intl.message(
      '默认',
      name: 'default_type',
      desc: '',
      args: [],
    );
  }

  /// `拉伸`
  String get stretch {
    return Intl.message(
      '拉伸',
      name: 'stretch',
      desc: '',
      args: [],
    );
  }

  /// `%1s 年 %2s 月 %3s 日`
  String get date {
    return Intl.message(
      '%1s 年 %2s 月 %3s 日',
      name: 'date',
      desc: '',
      args: [],
    );
  }

  /// `申请注销账号`
  String get apply_destroy_account {
    return Intl.message(
      '申请注销账号',
      name: 'apply_destroy_account',
      desc: '',
      args: [],
    );
  }

  /// `注销代表`
  String get destroy_account_means {
    return Intl.message(
      '注销代表',
      name: 'destroy_account_means',
      desc: '',
      args: [],
    );
  }

  /// `• 所有您拥有的数据（包括病例信息、个人信息、临时文件）将被完全删除且不可恢复\n\n• 若您开通过%s监控，则所有疗程将被自动结束。所有的监控数据将被完全删除且不可恢复，但患者端仍可以查看历史提交与历史回复\n\n• 所有相关账号信息将被完全删除且不可恢复\n\n• 您在灵芽系统内的任何未到期的会员或资格、任何进行中的订单会自动于注销日结束\n\n• 注销后，您可以使用您的信息注册全新的灵芽账号`
  String get destroy_account_tip1 {
    return Intl.message(
      '• 所有您拥有的数据（包括病例信息、个人信息、临时文件）将被完全删除且不可恢复\n\n• 若您开通过%s监控，则所有疗程将被自动结束。所有的监控数据将被完全删除且不可恢复，但患者端仍可以查看历史提交与历史回复\n\n• 所有相关账号信息将被完全删除且不可恢复\n\n• 您在灵芽系统内的任何未到期的会员或资格、任何进行中的订单会自动于注销日结束\n\n• 注销后，您可以使用您的信息注册全新的灵芽账号',
      name: 'destroy_account_tip1',
      desc: '',
      args: [],
    );
  }

  /// `无法注销`
  String get cannot_destroy_account {
    return Intl.message(
      '无法注销',
      name: 'cannot_destroy_account',
      desc: '',
      args: [],
    );
  }

  /// `必须退出所有机构才可以注销，请先在网页端退出机构!`
  String get tenant_cannot_destroy_account {
    return Intl.message(
      '必须退出所有机构才可以注销，请先在网页端退出机构!',
      name: 'tenant_cannot_destroy_account',
      desc: '',
      args: [],
    );
  }

  /// `申请注销`
  String get apply_destroy {
    return Intl.message(
      '申请注销',
      name: 'apply_destroy',
      desc: '',
      args: [],
    );
  }

  /// `注销账号后不可恢复，请谨慎操作！`
  String get destroy_account_alert {
    return Intl.message(
      '注销账号后不可恢复，请谨慎操作！',
      name: 'destroy_account_alert',
      desc: '',
      args: [],
    );
  }

  /// `放弃`
  String get give_up {
    return Intl.message(
      '放弃',
      name: 'give_up',
      desc: '',
      args: [],
    );
  }

  /// `确认注销`
  String get confirm_destroy_account {
    return Intl.message(
      '确认注销',
      name: 'confirm_destroy_account',
      desc: '',
      args: [],
    );
  }

  /// `账号已成功注销`
  String get destroy_account_success {
    return Intl.message(
      '账号已成功注销',
      name: 'destroy_account_success',
      desc: '',
      args: [],
    );
  }

  /// `点击开始扫描`
  String get double_click_scan {
    return Intl.message(
      '点击开始扫描',
      name: 'double_click_scan',
      desc: '',
      args: [],
    );
  }

  /// `点击继续`
  String get double_click_continue {
    return Intl.message(
      '点击继续',
      name: 'double_click_continue',
      desc: '',
      args: [],
    );
  }

  /// `请戴上牙套，将开口器置入口中，然后继续`
  String get pickup_mask_ware_mouth_prop {
    return Intl.message(
      '请戴上牙套，将开口器置入口中，然后继续',
      name: 'pickup_mask_ware_mouth_prop',
      desc: '',
      args: [],
    );
  }

  /// `请取下牙套，将开口器置入口中，然后继续`
  String get pickoff_mask_ware_mouth_prop {
    return Intl.message(
      '请取下牙套，将开口器置入口中，然后继续',
      name: 'pickoff_mask_ware_mouth_prop',
      desc: '',
      args: [],
    );
  }

  /// `戴上牙套，将开口器置入口中，然后继续`
  String get pickup_mask_ware_mouth_prop2 {
    return Intl.message(
      '戴上牙套，将开口器置入口中，然后继续',
      name: 'pickup_mask_ware_mouth_prop2',
      desc: '',
      args: [],
    );
  }

  /// `取下牙套，将开口器置入口中，然后继续`
  String get pickoff_mask_ware_mouth_prop2 {
    return Intl.message(
      '取下牙套，将开口器置入口中，然后继续',
      name: 'pickoff_mask_ware_mouth_prop2',
      desc: '',
      args: [],
    );
  }

  /// `下一步`
  String get next_step {
    return Intl.message(
      '下一步',
      name: 'next_step',
      desc: '',
      args: [],
    );
  }

  /// `继续`
  String get continue_step {
    return Intl.message(
      '继续',
      name: 'continue_step',
      desc: '',
      args: [],
    );
  }

  /// `咽口水，自然咬合`
  String get natural_bite {
    return Intl.message(
      '咽口水，自然咬合',
      name: 'natural_bite',
      desc: '',
      args: [],
    );
  }

  /// `牙齿张开幅度如图所示`
  String get gently_open_maxillary {
    return Intl.message(
      '牙齿张开幅度如图所示',
      name: 'gently_open_maxillary',
      desc: '',
      args: [],
    );
  }

  /// `牙齿张开幅度如图所示`
  String get wear_mask_open_maxillary {
    return Intl.message(
      '牙齿张开幅度如图所示',
      name: 'wear_mask_open_maxillary',
      desc: '',
      args: [],
    );
  }

  /// `牙齿模糊`
  String get tooth_blur {
    return Intl.message(
      '牙齿模糊',
      name: 'tooth_blur',
      desc: '',
      args: [],
    );
  }

  /// `调整画面`
  String get adjust_view {
    return Intl.message(
      '调整画面',
      name: 'adjust_view',
      desc: '',
      args: [],
    );
  }

  /// `网络错误`
  String get network_error {
    return Intl.message(
      '网络错误',
      name: 'network_error',
      desc: '',
      args: [],
    );
  }

  /// `当前网络不太顺畅，请检查网络设置`
  String get network_weak {
    return Intl.message(
      '当前网络不太顺畅，请检查网络设置',
      name: 'network_weak',
      desc: '',
      args: [],
    );
  }

  /// `点击重试`
  String get click_retry {
    return Intl.message(
      '点击重试',
      name: 'click_retry',
      desc: '',
      args: [],
    );
  }

  /// `完成扫描`
  String get complete_scan {
    return Intl.message(
      '完成扫描',
      name: 'complete_scan',
      desc: '',
      args: [],
    );
  }

  /// `直接进入下一步`
  String get direct_to_next {
    return Intl.message(
      '直接进入下一步',
      name: 'direct_to_next',
      desc: '',
      args: [],
    );
  }

  /// `将头部置于白框内，露齿微笑`
  String get face_in_rect_smile {
    return Intl.message(
      '将头部置于白框内，露齿微笑',
      name: 'face_in_rect_smile',
      desc: '',
      args: [],
    );
  }

  /// `建议使用前置摄像头`
  String get suggest_front_camera {
    return Intl.message(
      '建议使用前置摄像头',
      name: 'suggest_front_camera',
      desc: '',
      args: [],
    );
  }

  /// `您可阅读`
  String get you_can_read {
    return Intl.message(
      '您可阅读',
      name: 'you_can_read',
      desc: '',
      args: [],
    );
  }

  /// `同意并接受`
  String get agree_and_accept {
    return Intl.message(
      '同意并接受',
      name: 'agree_and_accept',
      desc: '',
      args: [],
    );
  }

  /// `不同意`
  String get not_agree {
    return Intl.message(
      '不同意',
      name: 'not_agree',
      desc: '',
      args: [],
    );
  }

  /// `服务协议和隐私政策`
  String get protocal_and_privacy {
    return Intl.message(
      '服务协议和隐私政策',
      name: 'protocal_and_privacy',
      desc: '',
      args: [],
    );
  }

  /// `请您务必审慎阅读、充分理解"服务协议"和“隐私政策”各条款，包括但不限于：为了更好的向您提供服务，我们需要使用您的设备信息、使用情况数据和健康数据等。`
  String get please_read_privacy1 {
    return Intl.message(
      '请您务必审慎阅读、充分理解"服务协议"和“隐私政策”各条款，包括但不限于：为了更好的向您提供服务，我们需要使用您的设备信息、使用情况数据和健康数据等。',
      name: 'please_read_privacy1',
      desc: '',
      args: [],
    );
  }

  /// `了解详细信息。如果您同意，请点击下面按钮开始接受我们的服务。`
  String get please_read_privacy2 {
    return Intl.message(
      '了解详细信息。如果您同意，请点击下面按钮开始接受我们的服务。',
      name: 'please_read_privacy2',
      desc: '',
      args: [],
    );
  }

  /// `视频`
  String get video {
    return Intl.message(
      '视频',
      name: 'video',
      desc: '',
      args: [],
    );
  }

  /// `建议你选择合适的开口器，方便医生查看你的牙齿情况。`
  String get advice_bigger_iscan {
    return Intl.message(
      '建议你选择合适的开口器，方便医生查看你的牙齿情况。',
      name: 'advice_bigger_iscan',
      desc: '',
      args: [],
    );
  }

  /// `取下牙套`
  String get pickoff_mask {
    return Intl.message(
      '取下牙套',
      name: 'pickoff_mask',
      desc: '',
      args: [],
    );
  }

  /// `成人用-灰色`
  String get setup_iscan_tip1 {
    return Intl.message(
      '成人用-灰色',
      name: 'setup_iscan_tip1',
      desc: '',
      args: [],
    );
  }

  /// `儿童用-白色`
  String get setup_iscan_tip2 {
    return Intl.message(
      '儿童用-白色',
      name: 'setup_iscan_tip2',
      desc: '',
      args: [],
    );
  }

  /// `如有明显不适感，可以用-黄色`
  String get setup_iscan_tip3 {
    return Intl.message(
      '如有明显不适感，可以用-黄色',
      name: 'setup_iscan_tip3',
      desc: '',
      args: [],
    );
  }

  /// `成人用-中号开口器`
  String get setup_ultra_tip1 {
    return Intl.message(
      '成人用-中号开口器',
      name: 'setup_ultra_tip1',
      desc: '',
      args: [],
    );
  }

  /// `儿童用-小号开口器`
  String get setup_ultra_tip2 {
    return Intl.message(
      '儿童用-小号开口器',
      name: 'setup_ultra_tip2',
      desc: '',
      args: [],
    );
  }

  /// `装入方向`
  String get setup_direction {
    return Intl.message(
      '装入方向',
      name: 'setup_direction',
      desc: '',
      args: [],
    );
  }

  /// `长按电源键开机，`
  String get make_ultra_power_on {
    return Intl.message(
      '长按电源键开机，',
      name: 'make_ultra_power_on',
      desc: '',
      args: [],
    );
  }

  /// `确保指示灯已亮，将设备靠近手机`
  String get ensure_indicator_light {
    return Intl.message(
      '确保指示灯已亮，将设备靠近手机',
      name: 'ensure_indicator_light',
      desc: '',
      args: [],
    );
  }

  /// `请将设备靠近手机`
  String get ultra_near_phone {
    return Intl.message(
      '请将设备靠近手机',
      name: 'ultra_near_phone',
      desc: '',
      args: [],
    );
  }

  /// `搜不到设备？`
  String get scan_device_fail {
    return Intl.message(
      '搜不到设备？',
      name: 'scan_device_fail',
      desc: '',
      args: [],
    );
  }

  /// `连接`
  String get connect {
    return Intl.message(
      '连接',
      name: 'connect',
      desc: '',
      args: [],
    );
  }

  /// `搜索到`
  String get scan_result {
    return Intl.message(
      '搜索到',
      name: 'scan_result',
      desc: '',
      args: [],
    );
  }

  /// `连接中`
  String get connecting {
    return Intl.message(
      '连接中',
      name: 'connecting',
      desc: '',
      args: [],
    );
  }

  /// `连不上设备？`
  String get connect_device_fail {
    return Intl.message(
      '连不上设备？',
      name: 'connect_device_fail',
      desc: '',
      args: [],
    );
  }

  /// `请确保设备：`
  String get scan_ultra_tip_title1 {
    return Intl.message(
      '请确保设备：',
      name: 'scan_ultra_tip_title1',
      desc: '',
      args: [],
    );
  }

  /// `如果仍然搜不到，请尝试：`
  String get scan_ultra_tip_title2 {
    return Intl.message(
      '如果仍然搜不到，请尝试：',
      name: 'scan_ultra_tip_title2',
      desc: '',
      args: [],
    );
  }

  /// `如果以上方法均无效：`
  String get scan_ultra_tip_title3 {
    return Intl.message(
      '如果以上方法均无效：',
      name: 'scan_ultra_tip_title3',
      desc: '',
      args: [],
    );
  }

  /// `处于开机状态`
  String get scan_ultra_tip_content11 {
    return Intl.message(
      '处于开机状态',
      name: 'scan_ultra_tip_content11',
      desc: '',
      args: [],
    );
  }

  /// `不要离手机太远`
  String get scan_ultra_tip_content12 {
    return Intl.message(
      '不要离手机太远',
      name: 'scan_ultra_tip_content12',
      desc: '',
      args: [],
    );
  }

  /// `电量充足，指示灯为绿色\n（电量不足，指示灯为红\n色，建议充电后再使用）`
  String get scan_ultra_tip_content13 {
    return Intl.message(
      '电量充足，指示灯为绿色\n（电量不足，指示灯为红\n色，建议充电后再使用）',
      name: 'scan_ultra_tip_content13',
      desc: '',
      args: [],
    );
  }

  /// `长按关闭设备，再次长按开\n启并连接`
  String get scan_ultra_tip_content21 {
    return Intl.message(
      '长按关闭设备，再次长按开\n启并连接',
      name: 'scan_ultra_tip_content21',
      desc: '',
      args: [],
    );
  }

  /// `彻底关闭灵芽应用程序，\n再次打开`
  String get scan_ultra_tip_content22 {
    return Intl.message(
      '彻底关闭灵芽应用程序，\n再次打开',
      name: 'scan_ultra_tip_content22',
      desc: '',
      args: [],
    );
  }

  /// `如遇设备死机，请插入充电线\n再拔下即可恢复`
  String get scan_ultra_tip_content23 {
    return Intl.message(
      '如遇设备死机，请插入充电线\n再拔下即可恢复',
      name: 'scan_ultra_tip_content23',
      desc: '',
      args: [],
    );
  }

  /// `请联系客服：19957156823`
  String get scan_ultra_tip_content31 {
    return Intl.message(
      '请联系客服：19957156823',
      name: 'scan_ultra_tip_content31',
      desc: '',
      args: [],
    );
  }

  /// `请扫描客服微信二维码`
  String get scan_ultra_tip_content32 {
    return Intl.message(
      '请扫描客服微信二维码',
      name: 'scan_ultra_tip_content32',
      desc: '',
      args: [],
    );
  }

  /// `请尝试：`
  String get connect_ultra_tip_title1 {
    return Intl.message(
      '请尝试：',
      name: 'connect_ultra_tip_title1',
      desc: '',
      args: [],
    );
  }

  /// `长按关闭设备，`
  String get connect_ultra_tip_content11 {
    return Intl.message(
      '长按关闭设备，',
      name: 'connect_ultra_tip_content11',
      desc: '',
      args: [],
    );
  }

  /// `再次长按开启并连接`
  String get connect_ultra_tip_content12 {
    return Intl.message(
      '再次长按开启并连接',
      name: 'connect_ultra_tip_content12',
      desc: '',
      args: [],
    );
  }

  /// `给设备充电后，`
  String get connect_ultra_tip_content21 {
    return Intl.message(
      '给设备充电后，',
      name: 'connect_ultra_tip_content21',
      desc: '',
      args: [],
    );
  }

  /// `再次长按开启并连接`
  String get connect_ultra_tip_content22 {
    return Intl.message(
      '再次长按开启并连接',
      name: 'connect_ultra_tip_content22',
      desc: '',
      args: [],
    );
  }

  /// `彻底关闭灵芽应用程序，再次打开`
  String get connect_ultra_tip_content13 {
    return Intl.message(
      '彻底关闭灵芽应用程序，再次打开',
      name: 'connect_ultra_tip_content13',
      desc: '',
      args: [],
    );
  }

  /// `务必确保连接的是灵芽系列产品`
  String get connect_ultra_tip_content14 {
    return Intl.message(
      '务必确保连接的是灵芽系列产品',
      name: 'connect_ultra_tip_content14',
      desc: '',
      args: [],
    );
  }

  /// `请将电源键朝上，开口器放进嘴里`
  String get put_ultra_power_up {
    return Intl.message(
      '请将电源键朝上，开口器放进嘴里',
      name: 'put_ultra_power_up',
      desc: '',
      args: [],
    );
  }

  /// `请重试连接，或尝试：`
  String get connect_retry {
    return Intl.message(
      '请重试连接，或尝试：',
      name: 'connect_retry',
      desc: '',
      args: [],
    );
  }

  /// `连接中断`
  String get connect_interrupted {
    return Intl.message(
      '连接中断',
      name: 'connect_interrupted',
      desc: '',
      args: [],
    );
  }

  /// `放弃连接`
  String get giveup_connect {
    return Intl.message(
      '放弃连接',
      name: 'giveup_connect',
      desc: '',
      args: [],
    );
  }

  /// `重试连接`
  String get retry_connect {
    return Intl.message(
      '重试连接',
      name: 'retry_connect',
      desc: '',
      args: [],
    );
  }

  /// `找不到设备`
  String get scan_no_device {
    return Intl.message(
      '找不到设备',
      name: 'scan_no_device',
      desc: '',
      args: [],
    );
  }

  /// `电量充足，指示灯为绿色（电量不足，指\n示灯为红色，建议充电后再使用）`
  String get scan_ultra_tip_content4 {
    return Intl.message(
      '电量充足，指示灯为绿色（电量不足，指\n示灯为红色，建议充电后再使用）',
      name: 'scan_ultra_tip_content4',
      desc: '',
      args: [],
    );
  }

  /// `放弃搜索`
  String get giveup_scan {
    return Intl.message(
      '放弃搜索',
      name: 'giveup_scan',
      desc: '',
      args: [],
    );
  }

  /// `重新搜索`
  String get retry_scan {
    return Intl.message(
      '重新搜索',
      name: 'retry_scan',
      desc: '',
      args: [],
    );
  }

  /// `左牙`
  String get left_tooth {
    return Intl.message(
      '左牙',
      name: 'left_tooth',
      desc: '',
      args: [],
    );
  }

  /// `门牙`
  String get center_tooth {
    return Intl.message(
      '门牙',
      name: 'center_tooth',
      desc: '',
      args: [],
    );
  }

  /// `右牙`
  String get right_tooth {
    return Intl.message(
      '右牙',
      name: 'right_tooth',
      desc: '',
      args: [],
    );
  }

  /// `上颌`
  String get maxillary {
    return Intl.message(
      '上颌',
      name: 'maxillary',
      desc: '',
      args: [],
    );
  }

  /// `下颌`
  String get mandibular {
    return Intl.message(
      '下颌',
      name: 'mandibular',
      desc: '',
      args: [],
    );
  }

  /// `服务器有点忙，我们马上让它回来！请稍后再试`
  String get internal_error_retry {
    return Intl.message(
      '服务器有点忙，我们马上让它回来！请稍后再试',
      name: 'internal_error_retry',
      desc: '',
      args: [],
    );
  }

  /// `验证码已发送`
  String get verify_code_sent {
    return Intl.message(
      '验证码已发送',
      name: 'verify_code_sent',
      desc: '',
      args: [],
    );
  }

  /// `连接中......`
  String get connecting_dot {
    return Intl.message(
      '连接中......',
      name: 'connecting_dot',
      desc: '',
      args: [],
    );
  }

  /// `搜索`
  String get search {
    return Intl.message(
      '搜索',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `搜索中......`
  String get search_dot {
    return Intl.message(
      '搜索中......',
      name: 'search_dot',
      desc: '',
      args: [],
    );
  }

  /// `昵称不合规，请重新填写`
  String get invalid_nickname {
    return Intl.message(
      '昵称不合规，请重新填写',
      name: 'invalid_nickname',
      desc: '',
      args: [],
    );
  }

  /// `只能使用数字、字母、汉字和下划线`
  String get nickname_rule {
    return Intl.message(
      '只能使用数字、字母、汉字和下划线',
      name: 'nickname_rule',
      desc: '',
      args: [],
    );
  }

  /// `设备电量`
  String get device_battery {
    return Intl.message(
      '设备电量',
      name: 'device_battery',
      desc: '',
      args: [],
    );
  }

  /// `L号`
  String get l_size {
    return Intl.message(
      'L号',
      name: 'l_size',
      desc: '',
      args: [],
    );
  }

  /// `M号`
  String get m_size {
    return Intl.message(
      'M号',
      name: 'm_size',
      desc: '',
      args: [],
    );
  }

  /// `S号`
  String get s_size {
    return Intl.message(
      'S号',
      name: 's_size',
      desc: '',
      args: [],
    );
  }

  /// `长按设备的电源键开机，`
  String get pre_make_ultra_power_on {
    return Intl.message(
      '长按设备的电源键开机，',
      name: 'pre_make_ultra_power_on',
      desc: '',
      args: [],
    );
  }

  /// `确保指示灯已亮。`
  String get pre_ensure_indicator_light {
    return Intl.message(
      '确保指示灯已亮。',
      name: 'pre_ensure_indicator_light',
      desc: '',
      args: [],
    );
  }

  /// `画面模糊，请匀速扫描`
  String get scan_blur_keep_speed {
    return Intl.message(
      '画面模糊，请匀速扫描',
      name: 'scan_blur_keep_speed',
      desc: '',
      args: [],
    );
  }

  /// `跟随箭头匀速移动开口器，速度不要过快`
  String get scan_blur_action_ultra {
    return Intl.message(
      '跟随箭头匀速移动开口器，速度不要过快',
      name: 'scan_blur_action_ultra',
      desc: '',
      args: [],
    );
  }

  /// `解决办法`
  String get resolve_action {
    return Intl.message(
      '解决办法',
      name: 'resolve_action',
      desc: '',
      args: [],
    );
  }

  /// `打开手机Wi-Fi`
  String get turn_on_wifi_phone {
    return Intl.message(
      '打开手机Wi-Fi',
      name: 'turn_on_wifi_phone',
      desc: '',
      args: [],
    );
  }

  /// `开启Wi-Fi`
  String get turn_on_wifi {
    return Intl.message(
      '开启Wi-Fi',
      name: 'turn_on_wifi',
      desc: '',
      args: [],
    );
  }

  /// `将开口器安装在设备机身上。`
  String get setup_scanner_on_ultra {
    return Intl.message(
      '将开口器安装在设备机身上。',
      name: 'setup_scanner_on_ultra',
      desc: '',
      args: [],
    );
  }

  /// `遇到问题？`
  String get have_question {
    return Intl.message(
      '遇到问题？',
      name: 'have_question',
      desc: '',
      args: [],
    );
  }

  /// `遇到系统的网络提醒弹窗怎么办？`
  String get network_warn_question {
    return Intl.message(
      '遇到系统的网络提醒弹窗怎么办？',
      name: 'network_warn_question',
      desc: '',
      args: [],
    );
  }

  /// `请选择“继续使用无线网络”`
  String get network_warn_answer {
    return Intl.message(
      '请选择“继续使用无线网络”',
      name: 'network_warn_answer',
      desc: '',
      args: [],
    );
  }

  /// `遇到风险提醒弹窗怎么办？`
  String get network_risk_question {
    return Intl.message(
      '遇到风险提醒弹窗怎么办？',
      name: 'network_risk_question',
      desc: '',
      args: [],
    );
  }

  /// `请选择“信任此网络”`
  String get network_risk_answer {
    return Intl.message(
      '请选择“信任此网络”',
      name: 'network_risk_answer',
      desc: '',
      args: [],
    );
  }

  /// `设备无法关机怎么办？`
  String get device_dead_question {
    return Intl.message(
      '设备无法关机怎么办？',
      name: 'device_dead_question',
      desc: '',
      args: [],
    );
  }

  /// `请插入电源线再拔下，即可恢复`
  String get device_dead_answer {
    return Intl.message(
      '请插入电源线再拔下，即可恢复',
      name: 'device_dead_answer',
      desc: '',
      args: [],
    );
  }

  /// `摄像头画面卡住怎么办？`
  String get view_block_question {
    return Intl.message(
      '摄像头画面卡住怎么办？',
      name: 'view_block_question',
      desc: '',
      args: [],
    );
  }

  /// `请重启扫描设备`
  String get view_block_answer {
    return Intl.message(
      '请重启扫描设备',
      name: 'view_block_answer',
      desc: '',
      args: [],
    );
  }

  /// `其他问题：`
  String get other_question {
    return Intl.message(
      '其他问题：',
      name: 'other_question',
      desc: '',
      args: [],
    );
  }

  /// `昵称不能为空`
  String get empty_nickname {
    return Intl.message(
      '昵称不能为空',
      name: 'empty_nickname',
      desc: '',
      args: [],
    );
  }

  /// `新用户注册`
  String get new_user_register {
    return Intl.message(
      '新用户注册',
      name: 'new_user_register',
      desc: '',
      args: [],
    );
  }

  /// `密码登录`
  String get login_by_password {
    return Intl.message(
      '密码登录',
      name: 'login_by_password',
      desc: '',
      args: [],
    );
  }

  /// `验证码登录`
  String get login_by_code {
    return Intl.message(
      '验证码登录',
      name: 'login_by_code',
      desc: '',
      args: [],
    );
  }

  /// `已有账号，去登录`
  String get account_login {
    return Intl.message(
      '已有账号，去登录',
      name: 'account_login',
      desc: '',
      args: [],
    );
  }

  /// `注册并登录`
  String get register_and_login {
    return Intl.message(
      '注册并登录',
      name: 'register_and_login',
      desc: '',
      args: [],
    );
  }

  /// `忘记密码`
  String get forget_password {
    return Intl.message(
      '忘记密码',
      name: 'forget_password',
      desc: '',
      args: [],
    );
  }

  /// `请输入正确邮箱号`
  String get incorrect_mail {
    return Intl.message(
      '请输入正确邮箱号',
      name: 'incorrect_mail',
      desc: '',
      args: [],
    );
  }

  /// `密码错误`
  String get incorrect_password {
    return Intl.message(
      '密码错误',
      name: 'incorrect_password',
      desc: '',
      args: [],
    );
  }

  /// `8-20个字符`
  String get password_length_rule {
    return Intl.message(
      '8-20个字符',
      name: 'password_length_rule',
      desc: '',
      args: [],
    );
  }

  /// `大小写字母`
  String get password_content_rule {
    return Intl.message(
      '大小写字母',
      name: 'password_content_rule',
      desc: '',
      args: [],
    );
  }

  /// `登录邮箱`
  String get login_mail {
    return Intl.message(
      '登录邮箱',
      name: 'login_mail',
      desc: '',
      args: [],
    );
  }

  /// `更换绑定邮箱`
  String get change_mail {
    return Intl.message(
      '更换绑定邮箱',
      name: 'change_mail',
      desc: '',
      args: [],
    );
  }

  /// `请输入原邮箱`
  String get input_old_mail {
    return Intl.message(
      '请输入原邮箱',
      name: 'input_old_mail',
      desc: '',
      args: [],
    );
  }

  /// `请输入新邮箱`
  String get input_new_mail {
    return Intl.message(
      '请输入新邮箱',
      name: 'input_new_mail',
      desc: '',
      args: [],
    );
  }

  /// `新旧邮箱不能相同`
  String get use_different_mail {
    return Intl.message(
      '新旧邮箱不能相同',
      name: 'use_different_mail',
      desc: '',
      args: [],
    );
  }

  /// `邮箱更换成功`
  String get change_mail_success {
    return Intl.message(
      '邮箱更换成功',
      name: 'change_mail_success',
      desc: '',
      args: [],
    );
  }

  /// `邮箱未注册`
  String get mail_not_register {
    return Intl.message(
      '邮箱未注册',
      name: 'mail_not_register',
      desc: '',
      args: [],
    );
  }

  /// `邮箱已注册`
  String get mail_already_register {
    return Intl.message(
      '邮箱已注册',
      name: 'mail_already_register',
      desc: '',
      args: [],
    );
  }

  /// `验证码将会发送至您的注册邮箱`
  String get code_will_send_mail {
    return Intl.message(
      '验证码将会发送至您的注册邮箱',
      name: 'code_will_send_mail',
      desc: '',
      args: [],
    );
  }

  /// `已向%s发送验证码`
  String get code_has_sent_mail {
    return Intl.message(
      '已向%s发送验证码',
      name: 'code_has_sent_mail',
      desc: '',
      args: [],
    );
  }

  /// `设置新密码`
  String get set_new_password {
    return Intl.message(
      '设置新密码',
      name: 'set_new_password',
      desc: '',
      args: [],
    );
  }

  /// `输入原密码`
  String get input_old_password0 {
    return Intl.message(
      '输入原密码',
      name: 'input_old_password0',
      desc: '',
      args: [],
    );
  }

  /// `请输入原密码`
  String get input_old_password {
    return Intl.message(
      '请输入原密码',
      name: 'input_old_password',
      desc: '',
      args: [],
    );
  }

  /// `请输入新密码`
  String get input_new_password {
    return Intl.message(
      '请输入新密码',
      name: 'input_new_password',
      desc: '',
      args: [],
    );
  }

  /// `确认密码`
  String get confirm_password {
    return Intl.message(
      '确认密码',
      name: 'confirm_password',
      desc: '',
      args: [],
    );
  }

  /// `重置密码`
  String get reset_password {
    return Intl.message(
      '重置密码',
      name: 'reset_password',
      desc: '',
      args: [],
    );
  }

  /// `新旧密码不能相同`
  String get password_different_from_old {
    return Intl.message(
      '新旧密码不能相同',
      name: 'password_different_from_old',
      desc: '',
      args: [],
    );
  }

  /// `密码不一致`
  String get password_different {
    return Intl.message(
      '密码不一致',
      name: 'password_different',
      desc: '',
      args: [],
    );
  }

  /// `重置密码成功`
  String get reset_password_success {
    return Intl.message(
      '重置密码成功',
      name: 'reset_password_success',
      desc: '',
      args: [],
    );
  }

  /// `修改密码成功`
  String get modify_password_success {
    return Intl.message(
      '修改密码成功',
      name: 'modify_password_success',
      desc: '',
      args: [],
    );
  }

  /// `修改密码`
  String get modify_password {
    return Intl.message(
      '修改密码',
      name: 'modify_password',
      desc: '',
      args: [],
    );
  }

  /// `密码输入错误`
  String get input_password_error {
    return Intl.message(
      '密码输入错误',
      name: 'input_password_error',
      desc: '',
      args: [],
    );
  }

  /// `确认修改`
  String get confirm_modify {
    return Intl.message(
      '确认修改',
      name: 'confirm_modify',
      desc: '',
      args: [],
    );
  }

  /// `收不到验证码`
  String get cannot_receive_code {
    return Intl.message(
      '收不到验证码',
      name: 'cannot_receive_code',
      desc: '',
      args: [],
    );
  }

  /// `通用设置`
  String get general_setting {
    return Intl.message(
      '通用设置',
      name: 'general_setting',
      desc: '',
      args: [],
    );
  }

  /// `语言设置`
  String get language_setting {
    return Intl.message(
      '语言设置',
      name: 'language_setting',
      desc: '',
      args: [],
    );
  }

  /// `多语言`
  String get multiple_language {
    return Intl.message(
      '多语言',
      name: 'multiple_language',
      desc: '',
      args: [],
    );
  }

  /// `自动`
  String get language_auto {
    return Intl.message(
      '自动',
      name: 'language_auto',
      desc: '',
      args: [],
    );
  }

  /// `未找到可用的互联网`
  String get cannot_find_network {
    return Intl.message(
      '未找到可用的互联网',
      name: 'cannot_find_network',
      desc: '',
      args: [],
    );
  }

  /// `请前往系统的WIFI设置，或打开数据流量`
  String get go_setting_network {
    return Intl.message(
      '请前往系统的WIFI设置，或打开数据流量',
      name: 'go_setting_network',
      desc: '',
      args: [],
    );
  }

  /// `重试`
  String get retry {
    return Intl.message(
      '重试',
      name: 'retry',
      desc: '',
      args: [],
    );
  }

  /// `在提交扫描或退出之前，请连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机哦～`
  String get close_ultra_tip {
    return Intl.message(
      '在提交扫描或退出之前，请连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机哦～',
      name: 'close_ultra_tip',
      desc: '',
      args: [],
    );
  }

  /// `年`
  String get picker_year {
    return Intl.message(
      '年',
      name: 'picker_year',
      desc: '',
      args: [],
    );
  }

  /// `月`
  String get picker_month {
    return Intl.message(
      '月',
      name: 'picker_month',
      desc: '',
      args: [],
    );
  }

  /// `日`
  String get picker_day {
    return Intl.message(
      '日',
      name: 'picker_day',
      desc: '',
      args: [],
    );
  }

  /// `温馨提示`
  String get friendly_tip {
    return Intl.message(
      '温馨提示',
      name: 'friendly_tip',
      desc: '',
      args: [],
    );
  }

  /// `好的`
  String get ok {
    return Intl.message(
      '好的',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `请先连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机。`
  String get shut_off_ultra_exit {
    return Intl.message(
      '请先连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机。',
      name: 'shut_off_ultra_exit',
      desc: '',
      args: [],
    );
  }

  /// `请连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机，然后再上传`
  String get shut_off_ultra_submit {
    return Intl.message(
      '请连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机，然后再上传',
      name: 'shut_off_ultra_submit',
      desc: '',
      args: [],
    );
  }

  /// `使用遇到问题？`
  String get use_ultra_question {
    return Intl.message(
      '使用遇到问题？',
      name: 'use_ultra_question',
      desc: '',
      args: [],
    );
  }

  /// `查看wifi是否开启`
  String get ultra_faq_check_wifi {
    return Intl.message(
      '查看wifi是否开启',
      name: 'ultra_faq_check_wifi',
      desc: '',
      args: [],
    );
  }

  /// `检查设备指示灯是否为绿色闪烁状态`
  String get ultra_faq_check_light {
    return Intl.message(
      '检查设备指示灯是否为绿色闪烁状态',
      name: 'ultra_faq_check_light',
      desc: '',
      args: [],
    );
  }

  /// `若指示灯为红色，请充电`
  String get ultra_faq_light_red {
    return Intl.message(
      '若指示灯为红色，请充电',
      name: 'ultra_faq_light_red',
      desc: '',
      args: [],
    );
  }

  /// `若指示灯为常亮状态，长按关闭设备，再次长按开启并连接`
  String get ultra_faq_light_long {
    return Intl.message(
      '若指示灯为常亮状态，长按关闭设备，再次长按开启并连接',
      name: 'ultra_faq_light_long',
      desc: '',
      args: [],
    );
  }

  /// `若无法关闭设备，请插入充电线再拔下即可恢复`
  String get ultra_faq_charge {
    return Intl.message(
      '若无法关闭设备，请插入充电线再拔下即可恢复',
      name: 'ultra_faq_charge',
      desc: '',
      args: [],
    );
  }

  /// `若仍然无法连接，请至手机wifi列表手动连接%s（密码：Ultra001），并切换回灵芽App`
  String get ultra_faq_connect_wifi {
    return Intl.message(
      '若仍然无法连接，请至手机wifi列表手动连接%s（密码：Ultra001），并切换回灵芽App',
      name: 'ultra_faq_connect_wifi',
      desc: '',
      args: [],
    );
  }

  /// `卡在“连接中...”怎么办？`
  String get ultra_faq_block_connecting {
    return Intl.message(
      '卡在“连接中...”怎么办？',
      name: 'ultra_faq_block_connecting',
      desc: '',
      args: [],
    );
  }

  /// `退出连接，长按关闭设备，再次长按开启并连接`
  String get ultra_faq_restart_ultra {
    return Intl.message(
      '退出连接，长按关闭设备，再次长按开启并连接',
      name: 'ultra_faq_restart_ultra',
      desc: '',
      args: [],
    );
  }

  /// `若无法解决`
  String get ultra_faq_not_resolve {
    return Intl.message(
      '若无法解决',
      name: 'ultra_faq_not_resolve',
      desc: '',
      args: [],
    );
  }

  /// `确保双WLAN加速模式关闭`
  String get ultra_faq_two_wlan {
    return Intl.message(
      '确保双WLAN加速模式关闭',
      name: 'ultra_faq_two_wlan',
      desc: '',
      args: [],
    );
  }

  /// `在手机wifi列表中打开%s，并选择忽略此网络`
  String get ultra_faq_ignore_wifi {
    return Intl.message(
      '在手机wifi列表中打开%s，并选择忽略此网络',
      name: 'ultra_faq_ignore_wifi',
      desc: '',
      args: [],
    );
  }

  /// `重试第一步`
  String get ultra_faq_retry_first {
    return Intl.message(
      '重试第一步',
      name: 'ultra_faq_retry_first',
      desc: '',
      args: [],
    );
  }

  /// `断开VPN等代理连接`
  String get ultra_faq_stop_vpn {
    return Intl.message(
      '断开VPN等代理连接',
      name: 'ultra_faq_stop_vpn',
      desc: '',
      args: [],
    );
  }

  /// `查看wifi是否开启，且是否允许灵芽App访问本地网络（设置--隐私--本地网络--灵芽权限开启）`
  String get ultra_faq_check_wifi_ios {
    return Intl.message(
      '查看wifi是否开启，且是否允许灵芽App访问本地网络（设置--隐私--本地网络--灵芽权限开启）',
      name: 'ultra_faq_check_wifi_ios',
      desc: '',
      args: [],
    );
  }

  /// `若搜索不到，请至手机WiFi列表手动连接%s（密码：Ultra001 `
  String get ultra_handle_connect1 {
    return Intl.message(
      '若搜索不到，请至手机WiFi列表手动连接%s（密码：Ultra001 ',
      name: 'ultra_handle_connect1',
      desc: '',
      args: [],
    );
  }

  /// `），并切换回灵芽App`
  String get ultra_handle_connect2 {
    return Intl.message(
      '），并切换回灵芽App',
      name: 'ultra_handle_connect2',
      desc: '',
      args: [],
    );
  }

  /// `复制`
  String get copy {
    return Intl.message(
      '复制',
      name: 'copy',
      desc: '',
      args: [],
    );
  }

  /// `复制成功`
  String get copy_success {
    return Intl.message(
      '复制成功',
      name: 'copy_success',
      desc: '',
      args: [],
    );
  }

  /// `%s-%s-%s`
  String get simple_date {
    return Intl.message(
      '%s-%s-%s',
      name: 'simple_date',
      desc: '',
      args: [],
    );
  }

  /// `天`
  String get period_day {
    return Intl.message(
      '天',
      name: 'period_day',
      desc: '',
      args: [],
    );
  }

  /// `周`
  String get period_week {
    return Intl.message(
      '周',
      name: 'period_week',
      desc: '',
      args: [],
    );
  }

  /// `月`
  String get period_month {
    return Intl.message(
      '月',
      name: 'period_month',
      desc: '',
      args: [],
    );
  }

  /// `口内照`
  String get intraoral_photo {
    return Intl.message(
      '口内照',
      name: 'intraoral_photo',
      desc: '',
      args: [],
    );
  }

  /// `口内照录入`
  String get scan_intraoral_photo {
    return Intl.message(
      '口内照录入',
      name: 'scan_intraoral_photo',
      desc: '',
      args: [],
    );
  }

  /// `展开`
  String get expand {
    return Intl.message(
      '展开',
      name: 'expand',
      desc: '',
      args: [],
    );
  }

  /// `收起`
  String get collapse {
    return Intl.message(
      '收起',
      name: 'collapse',
      desc: '',
      args: [],
    );
  }

  /// `我已开启设备，下一步`
  String get setup_ultra_start {
    return Intl.message(
      '我已开启设备，下一步',
      name: 'setup_ultra_start',
      desc: '',
      args: [],
    );
  }

  /// `点击再次搜索`
  String get click_research {
    return Intl.message(
      '点击再次搜索',
      name: 'click_research',
      desc: '',
      args: [],
    );
  }

  /// `跳过`
  String get skip {
    return Intl.message(
      '跳过',
      name: 'skip',
      desc: '',
      args: [],
    );
  }

  /// `到底了`
  String get in_bottom {
    return Intl.message(
      '到底了',
      name: 'in_bottom',
      desc: '',
      args: [],
    );
  }

  /// `Light`
  String get light {
    return Intl.message(
      'Light',
      name: 'light',
      desc: '',
      args: [],
    );
  }

  /// `Wi-Fi牙菌斑扫描`
  String get light_intro {
    return Intl.message(
      'Wi-Fi牙菌斑扫描',
      name: 'light_intro',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑检测`
  String get scan_plaque {
    return Intl.message(
      '牙菌斑检测',
      name: 'scan_plaque',
      desc: '',
      args: [],
    );
  }

  /// `开始检测`
  String get start_detect {
    return Intl.message(
      '开始检测',
      name: 'start_detect',
      desc: '',
      args: [],
    );
  }

  /// `检测记录`
  String get detect_history {
    return Intl.message(
      '检测记录',
      name: 'detect_history',
      desc: '',
      args: [],
    );
  }

  /// `什么是牙菌斑？`
  String get what_is_plaque {
    return Intl.message(
      '什么是牙菌斑？',
      name: 'what_is_plaque',
      desc: '',
      args: [],
    );
  }

  /// `确定删除本次牙菌斑检测吗？`
  String get delete_plaque {
    return Intl.message(
      '确定删除本次牙菌斑检测吗？',
      name: 'delete_plaque',
      desc: '',
      args: [],
    );
  }

  /// `删除后，数据将无法找回`
  String get delete_not_recoverd {
    return Intl.message(
      '删除后，数据将无法找回',
      name: 'delete_not_recoverd',
      desc: '',
      args: [],
    );
  }

  /// `检测录入`
  String get detect_to_record {
    return Intl.message(
      '检测录入',
      name: 'detect_to_record',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑检测历史记录`
  String get plaque_detect_history {
    return Intl.message(
      '牙菌斑检测历史记录',
      name: 'plaque_detect_history',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑检测，张大嘴巴，扫描上颌`
  String get plaque_scan_up {
    return Intl.message(
      '牙菌斑检测，张大嘴巴，扫描上颌',
      name: 'plaque_scan_up',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑检测，双颌微张，从左向右扫描`
  String get plaque_open_bimaxillary {
    return Intl.message(
      '牙菌斑检测，双颌微张，从左向右扫描',
      name: 'plaque_open_bimaxillary',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑检测，张大嘴巴，扫描下颌`
  String get plaque_scan_down {
    return Intl.message(
      '牙菌斑检测，张大嘴巴，扫描下颌',
      name: 'plaque_scan_down',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑`
  String get plaque {
    return Intl.message(
      '牙菌斑',
      name: 'plaque',
      desc: '',
      args: [],
    );
  }

  /// `关于牙菌斑`
  String get about_plaque {
    return Intl.message(
      '关于牙菌斑',
      name: 'about_plaque',
      desc: '',
      args: [],
    );
  }

  /// `怎么看牙菌斑`
  String get how_observe_plaque {
    return Intl.message(
      '怎么看牙菌斑',
      name: 'how_observe_plaque',
      desc: '',
      args: [],
    );
  }

  /// `如何观察牙菌斑？`
  String get how_to_observe_plaque {
    return Intl.message(
      '如何观察牙菌斑？',
      name: 'how_to_observe_plaque',
      desc: '',
      args: [],
    );
  }

  /// `通过检测拍摄观察荧粉色的菌斑区域（常位于牙龈线、牙缝、牙齿舌侧、托槽周围等）`
  String get observe_plaque_pink_area {
    return Intl.message(
      '通过检测拍摄观察荧粉色的菌斑区域（常位于牙龈线、牙缝、牙齿舌侧、托槽周围等）',
      name: 'observe_plaque_pink_area',
      desc: '',
      args: [],
    );
  }

  /// `荧粉色`
  String get plaque_pink {
    return Intl.message(
      '荧粉色',
      name: 'plaque_pink',
      desc: '',
      args: [],
    );
  }

  /// `以下特征的都是牙菌斑`
  String get plaque_features {
    return Intl.message(
      '以下特征的都是牙菌斑',
      name: 'plaque_features',
      desc: '',
      args: [],
    );
  }

  /// `以下特征大概率不是牙菌斑，而是龋齿、色素沉着等。牙菌斑会呈现荧粉色哦~`
  String get plaque_exclude {
    return Intl.message(
      '以下特征大概率不是牙菌斑，而是龋齿、色素沉着等。牙菌斑会呈现荧粉色哦~',
      name: 'plaque_exclude',
      desc: '',
      args: [],
    );
  }

  /// `以下特征可能是龋齿、色素沉着的部位附着了牙菌斑`
  String get plaque_with_colors {
    return Intl.message(
      '以下特征可能是龋齿、色素沉着的部位附着了牙菌斑',
      name: 'plaque_with_colors',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑，也被称为牙斑或菌斑，是一种在牙齿表面形成的黏稠、无色的生物膜。它主要由在口腔中自然存在的细菌构成。`
  String get plaque_detail_introduction {
    return Intl.message(
      '牙菌斑，也被称为牙斑或菌斑，是一种在牙齿表面形成的黏稠、无色的生物膜。它主要由在口腔中自然存在的细菌构成。',
      name: 'plaque_detail_introduction',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑的形成：`
  String get plaque_generate {
    return Intl.message(
      '牙菌斑的形成：',
      name: 'plaque_generate',
      desc: '',
      args: [],
    );
  }

  /// `当食物残渣（特别是含糖食物）在口腔中残留时，它们会与口腔中的细菌结合。\n这些细菌利用食物残渣产生酸，随着时间的推移，这些酸会与唾液中的矿物质相结合，形成牙菌斑。\n牙菌斑通常首先在牙齿与牙龈接触的地方形成，随后扩散到整个牙齿表面。`
  String get plaque_generate_details {
    return Intl.message(
      '当食物残渣（特别是含糖食物）在口腔中残留时，它们会与口腔中的细菌结合。\n这些细菌利用食物残渣产生酸，随着时间的推移，这些酸会与唾液中的矿物质相结合，形成牙菌斑。\n牙菌斑通常首先在牙齿与牙龈接触的地方形成，随后扩散到整个牙齿表面。',
      name: 'plaque_generate_details',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑的危害：`
  String get plaque_danger {
    return Intl.message(
      '牙菌斑的危害：',
      name: 'plaque_danger',
      desc: '',
      args: [],
    );
  }

  /// `牙菌斑是牙周病和龋齿的主要原因。\n如果不及时清除，牙菌斑可以硬化成牙石，导致牙龈炎和牙周炎。\n长期的牙菌斑积累可能导致牙齿脱落、口腔异味和其他口腔健康问题。`
  String get plaque_danger_details {
    return Intl.message(
      '牙菌斑是牙周病和龋齿的主要原因。\n如果不及时清除，牙菌斑可以硬化成牙石，导致牙龈炎和牙周炎。\n长期的牙菌斑积累可能导致牙齿脱落、口腔异味和其他口腔健康问题。',
      name: 'plaque_danger_details',
      desc: '',
      args: [],
    );
  }

  /// `去除牙菌斑的方法：`
  String get plaque_clean {
    return Intl.message(
      '去除牙菌斑的方法：',
      name: 'plaque_clean',
      desc: '',
      args: [],
    );
  }

  /// `良好的口腔卫生：定期刷牙（每天至少两次），使用含氟牙膏，以及每日使用牙线清洁牙缝。\n定期牙科检查：定期去牙科诊所进行专业清洁，可以去除难以自行清除的牙菌斑和牙石。\n饮食控制：减少糖分和精制碳水化合物的摄入，这些食物容易促进牙菌斑的形成。`
  String get plaque_clean_details {
    return Intl.message(
      '良好的口腔卫生：定期刷牙（每天至少两次），使用含氟牙膏，以及每日使用牙线清洁牙缝。\n定期牙科检查：定期去牙科诊所进行专业清洁，可以去除难以自行清除的牙菌斑和牙石。\n饮食控制：减少糖分和精制碳水化合物的摄入，这些食物容易促进牙菌斑的形成。',
      name: 'plaque_clean_details',
      desc: '',
      args: [],
    );
  }

  /// `检测到您的设备是 Ultra Wi-Fi扫描装置，此设备无法进行牙菌斑检测哦~`
  String get check_your_device_ultra {
    return Intl.message(
      '检测到您的设备是 Ultra Wi-Fi扫描装置，此设备无法进行牙菌斑检测哦~',
      name: 'check_your_device_ultra',
      desc: '',
      args: [],
    );
  }

  /// `请您关闭当前设备，并开启一台 Light Wi-Fi扫描装置，然后才能进行牙菌斑检测`
  String get switch_device_to_light {
    return Intl.message(
      '请您关闭当前设备，并开启一台 Light Wi-Fi扫描装置，然后才能进行牙菌斑检测',
      name: 'switch_device_to_light',
      desc: '',
      args: [],
    );
  }

  /// `检测到您的设备是 Ultra Wi-Fi扫描装置，此设备无法进行牙菌斑检测哦~App将退回首页并帮您选择正确的设备。`
  String get check_your_device_ultra_home {
    return Intl.message(
      '检测到您的设备是 Ultra Wi-Fi扫描装置，此设备无法进行牙菌斑检测哦~App将退回首页并帮您选择正确的设备。',
      name: 'check_your_device_ultra_home',
      desc: '',
      args: [],
    );
  }

  /// `抱歉，您的MOOELI Ultra设备版本较低，无法连接灵芽App，详情可咨询客服。`
  String get ultra_device_not_support {
    return Intl.message(
      '抱歉，您的MOOELI Ultra设备版本较低，无法连接灵芽App，详情可咨询客服。',
      name: 'ultra_device_not_support',
      desc: '',
      args: [],
    );
  }

  /// `测量项目`
  String get measure_name {
    return Intl.message(
      '测量项目',
      name: 'measure_name',
      desc: '',
      args: [],
    );
  }

  /// `标准值`
  String get measure_norm {
    return Intl.message(
      '标准值',
      name: 'measure_norm',
      desc: '',
      args: [],
    );
  }

  /// `测量值`
  String get measure_value {
    return Intl.message(
      '测量值',
      name: 'measure_value',
      desc: '',
      args: [],
    );
  }

  /// `说明`
  String get measure_desc {
    return Intl.message(
      '说明',
      name: 'measure_desc',
      desc: '',
      args: [],
    );
  }

  /// `头影分析`
  String get xray_lateral {
    return Intl.message(
      '头影分析',
      name: 'xray_lateral',
      desc: '',
      args: [],
    );
  }

  /// `骨龄分析`
  String get xray_bone {
    return Intl.message(
      '骨龄分析',
      name: 'xray_bone',
      desc: '',
      args: [],
    );
  }

  /// `气道分析`
  String get xray_airway {
    return Intl.message(
      '气道分析',
      name: 'xray_airway',
      desc: '',
      args: [],
    );
  }

  /// `CS1起始期`
  String get bone_period_cs1 {
    return Intl.message(
      'CS1起始期',
      name: 'bone_period_cs1',
      desc: '',
      args: [],
    );
  }

  /// `CS2快速期`
  String get bone_period_cs2 {
    return Intl.message(
      'CS2快速期',
      name: 'bone_period_cs2',
      desc: '',
      args: [],
    );
  }

  /// `CS3过渡期`
  String get bone_period_cs3 {
    return Intl.message(
      'CS3过渡期',
      name: 'bone_period_cs3',
      desc: '',
      args: [],
    );
  }

  /// `CS4减速期`
  String get bone_period_cs4 {
    return Intl.message(
      'CS4减速期',
      name: 'bone_period_cs4',
      desc: '',
      args: [],
    );
  }

  /// `CS5成熟期`
  String get bone_period_cs5 {
    return Intl.message(
      'CS5成熟期',
      name: 'bone_period_cs5',
      desc: '',
      args: [],
    );
  }

  /// `CS6完成期`
  String get bone_period_cs6 {
    return Intl.message(
      'CS6完成期',
      name: 'bone_period_cs6',
      desc: '',
      args: [],
    );
  }

  /// `CS2快速期/CS3过渡期`
  String get bone_period_cs23 {
    return Intl.message(
      'CS2快速期/CS3过渡期',
      name: 'bone_period_cs23',
      desc: '',
      args: [],
    );
  }

  /// `CS3过渡期/CS4减速期`
  String get bone_period_cs34 {
    return Intl.message(
      'CS3过渡期/CS4减速期',
      name: 'bone_period_cs34',
      desc: '',
      args: [],
    );
  }

  /// `一般2年后，将达到下颌骨生长发育高峰。有80%~100%生长潜力。`
  String get bone_dl_cs1 {
    return Intl.message(
      '一般2年后，将达到下颌骨生长发育高峰。有80%~100%生长潜力。',
      name: 'bone_dl_cs1',
      desc: '',
      args: [],
    );
  }

  /// `一般1年后，将达到下颌骨生长发育高峰。有65%~85生长潜力。`
  String get bone_dl_cs2 {
    return Intl.message(
      '一般1年后，将达到下颌骨生长发育高峰。有65%~85生长潜力。',
      name: 'bone_dl_cs2',
      desc: '',
      args: [],
    );
  }

  /// `1年内，将达到下颌骨生长发育高峰。有25%~65%生长潜力。`
  String get bone_dl_cs3 {
    return Intl.message(
      '1年内，将达到下颌骨生长发育高峰。有25%~65%生长潜力。',
      name: 'bone_dl_cs3',
      desc: '',
      args: [],
    );
  }

  /// `1-2年前，已达到下颌骨生长发育高峰。有10%~25%生长潜力。`
  String get bone_dl_cs4 {
    return Intl.message(
      '1-2年前，已达到下颌骨生长发育高峰。有10%~25%生长潜力。',
      name: 'bone_dl_cs4',
      desc: '',
      args: [],
    );
  }

  /// `起码1年前，下颌骨生长发育高峰已结束。有5%~10%生长潜力。`
  String get bone_dl_cs5 {
    return Intl.message(
      '起码1年前，下颌骨生长发育高峰已结束。有5%~10%生长潜力。',
      name: 'bone_dl_cs5',
      desc: '',
      args: [],
    );
  }

  /// `起码2年前，下颌骨生长发育高峰已结束。无生长潜力。`
  String get bone_dl_cs6 {
    return Intl.message(
      '起码2年前，下颌骨生长发育高峰已结束。无生长潜力。',
      name: 'bone_dl_cs6',
      desc: '',
      args: [],
    );
  }

  /// `第Ⅰ期加速期`
  String get bone_ration_1 {
    return Intl.message(
      '第Ⅰ期加速期',
      name: 'bone_ration_1',
      desc: '',
      args: [],
    );
  }

  /// `第Ⅱ期高速期`
  String get bone_ration_2 {
    return Intl.message(
      '第Ⅱ期高速期',
      name: 'bone_ration_2',
      desc: '',
      args: [],
    );
  }

  /// `第Ⅲ期减速期`
  String get bone_ration_3 {
    return Intl.message(
      '第Ⅲ期减速期',
      name: 'bone_ration_3',
      desc: '',
      args: [],
    );
  }

  /// `第Ⅳ期完成期`
  String get bone_ration_4 {
    return Intl.message(
      '第Ⅳ期完成期',
      name: 'bone_ration_4',
      desc: '',
      args: [],
    );
  }

  /// `分期`
  String get stage {
    return Intl.message(
      '分期',
      name: 'stage',
      desc: '',
      args: [],
    );
  }

  /// `结论`
  String get conclusion {
    return Intl.message(
      '结论',
      name: 'conclusion',
      desc: '',
      args: [],
    );
  }

  /// `形态参考标准`
  String get stage_profile {
    return Intl.message(
      '形态参考标准',
      name: 'stage_profile',
      desc: '',
      args: [],
    );
  }

  /// `特征`
  String get features {
    return Intl.message(
      '特征',
      name: 'features',
      desc: '',
      args: [],
    );
  }

  /// `阶段名称`
  String get stage_phase {
    return Intl.message(
      '阶段名称',
      name: 'stage_phase',
      desc: '',
      args: [],
    );
  }

  /// `QCVM判断方法`
  String get qcvm_judge {
    return Intl.message(
      'QCVM判断方法',
      name: 'qcvm_judge',
      desc: '',
      args: [],
    );
  }

  /// `华西综合分析法`
  String get lateral_huaxi {
    return Intl.message(
      '华西综合分析法',
      name: 'lateral_huaxi',
      desc: '',
      args: [],
    );
  }

  /// `北京大学分析法`
  String get lateral_beijing {
    return Intl.message(
      '北京大学分析法',
      name: 'lateral_beijing',
      desc: '',
      args: [],
    );
  }

  /// `九院分析法`
  String get lateral_jiuyuan {
    return Intl.message(
      '九院分析法',
      name: 'lateral_jiuyuan',
      desc: '',
      args: [],
    );
  }

  /// `Downs分析法`
  String get lateral_downs {
    return Intl.message(
      'Downs分析法',
      name: 'lateral_downs',
      desc: '',
      args: [],
    );
  }

  /// `Jarabak分析法`
  String get lateral_jarabak {
    return Intl.message(
      'Jarabak分析法',
      name: 'lateral_jarabak',
      desc: '',
      args: [],
    );
  }

  /// `Wylie分析法`
  String get lateral_wylie {
    return Intl.message(
      'Wylie分析法',
      name: 'lateral_wylie',
      desc: '',
      args: [],
    );
  }

  /// `Steiner分析法`
  String get lateral_steiner {
    return Intl.message(
      'Steiner分析法',
      name: 'lateral_steiner',
      desc: '',
      args: [],
    );
  }

  /// `Ricketts分析法`
  String get lateral_ricketts {
    return Intl.message(
      'Ricketts分析法',
      name: 'lateral_ricketts',
      desc: '',
      args: [],
    );
  }

  /// `McNamara分析法`
  String get lateral_mcnamara {
    return Intl.message(
      'McNamara分析法',
      name: 'lateral_mcnamara',
      desc: '',
      args: [],
    );
  }

  /// `ABO分析法`
  String get lateral_abo {
    return Intl.message(
      'ABO分析法',
      name: 'lateral_abo',
      desc: '',
      args: [],
    );
  }

  /// `Tweed分析法`
  String get lateral_tweed {
    return Intl.message(
      'Tweed分析法',
      name: 'lateral_tweed',
      desc: '',
      args: [],
    );
  }

  /// `Holdaway分析法`
  String get lateral_holdway {
    return Intl.message(
      'Holdaway分析法',
      name: 'lateral_holdway',
      desc: '',
      args: [],
    );
  }

  /// `Burstone分析法`
  String get lateral_burstone {
    return Intl.message(
      'Burstone分析法',
      name: 'lateral_burstone',
      desc: '',
      args: [],
    );
  }

  /// `Riedel分析法`
  String get lateral_riedel {
    return Intl.message(
      'Riedel分析法',
      name: 'lateral_riedel',
      desc: '',
      args: [],
    );
  }

  /// `骨龄定量分析法`
  String get bone_quantitative {
    return Intl.message(
      '骨龄定量分析法',
      name: 'bone_quantitative',
      desc: '',
      args: [],
    );
  }

  /// `深度学习分析法`
  String get bone_learning {
    return Intl.message(
      '深度学习分析法',
      name: 'bone_learning',
      desc: '',
      args: [],
    );
  }

  /// `骨龄定性分析法`
  String get bone_qualitative {
    return Intl.message(
      '骨龄定性分析法',
      name: 'bone_qualitative',
      desc: '',
      args: [],
    );
  }

  /// `磨牙咬合关系`
  String get molar_relation {
    return Intl.message(
      '磨牙咬合关系',
      name: 'molar_relation',
      desc: '',
      args: [],
    );
  }

  /// `左侧`
  String get left_side {
    return Intl.message(
      '左侧',
      name: 'left_side',
      desc: '',
      args: [],
    );
  }

  /// `右侧`
  String get right_side {
    return Intl.message(
      '右侧',
      name: 'right_side',
      desc: '',
      args: [],
    );
  }

  /// `覆𬌗`
  String get tooth_overbite {
    return Intl.message(
      '覆𬌗',
      name: 'tooth_overbite',
      desc: '',
      args: [],
    );
  }

  /// `覆盖`
  String get tooth_overjet {
    return Intl.message(
      '覆盖',
      name: 'tooth_overjet',
      desc: '',
      args: [],
    );
  }

  /// `中线关系`
  String get midline_relation {
    return Intl.message(
      '中线关系',
      name: 'midline_relation',
      desc: '',
      args: [],
    );
  }

  /// `中线偏斜`
  String get midline_deviation {
    return Intl.message(
      '中线偏斜',
      name: 'midline_deviation',
      desc: '',
      args: [],
    );
  }

  /// ` %s 类`
  String get molar_class {
    return Intl.message(
      ' %s 类',
      name: 'molar_class',
      desc: '',
      args: [],
    );
  }

  /// `正常覆𬌗`
  String get overbite_normal {
    return Intl.message(
      '正常覆𬌗',
      name: 'overbite_normal',
      desc: '',
      args: [],
    );
  }

  /// `开𬌗 Ⅰ 度`
  String get overbite_open_1 {
    return Intl.message(
      '开𬌗 Ⅰ 度',
      name: 'overbite_open_1',
      desc: '',
      args: [],
    );
  }

  /// `开𬌗 Ⅱ 度`
  String get overbite_open_2 {
    return Intl.message(
      '开𬌗 Ⅱ 度',
      name: 'overbite_open_2',
      desc: '',
      args: [],
    );
  }

  /// `开𬌗 Ⅲ 度`
  String get overbite_open_3 {
    return Intl.message(
      '开𬌗 Ⅲ 度',
      name: 'overbite_open_3',
      desc: '',
      args: [],
    );
  }

  /// `轻度深覆𬌗 Ⅰ 度`
  String get overbite_deep_1 {
    return Intl.message(
      '轻度深覆𬌗 Ⅰ 度',
      name: 'overbite_deep_1',
      desc: '',
      args: [],
    );
  }

  /// `中度深覆𬌗 Ⅱ 度`
  String get overbite_deep_2 {
    return Intl.message(
      '中度深覆𬌗 Ⅱ 度',
      name: 'overbite_deep_2',
      desc: '',
      args: [],
    );
  }

  /// `重度深覆𬌗 Ⅲ 度`
  String get overbite_deep_3 {
    return Intl.message(
      '重度深覆𬌗 Ⅲ 度',
      name: 'overbite_deep_3',
      desc: '',
      args: [],
    );
  }

  /// `前牙反𬌗`
  String get front_negative_overjet {
    return Intl.message(
      '前牙反𬌗',
      name: 'front_negative_overjet',
      desc: '',
      args: [],
    );
  }

  /// `反𬌗`
  String get negative_overjet {
    return Intl.message(
      '反𬌗',
      name: 'negative_overjet',
      desc: '',
      args: [],
    );
  }

  /// `正常覆盖`
  String get overjet_normal {
    return Intl.message(
      '正常覆盖',
      name: 'overjet_normal',
      desc: '',
      args: [],
    );
  }

  /// `前牙深覆盖 Ⅰ 度`
  String get overjet_deep_1 {
    return Intl.message(
      '前牙深覆盖 Ⅰ 度',
      name: 'overjet_deep_1',
      desc: '',
      args: [],
    );
  }

  /// `前牙深覆盖 Ⅱ 度`
  String get overjet_deep_2 {
    return Intl.message(
      '前牙深覆盖 Ⅱ 度',
      name: 'overjet_deep_2',
      desc: '',
      args: [],
    );
  }

  /// `前牙深覆盖 Ⅲ 度`
  String get overjet_deep_3 {
    return Intl.message(
      '前牙深覆盖 Ⅲ 度',
      name: 'overjet_deep_3',
      desc: '',
      args: [],
    );
  }

  /// `对刃`
  String get overjet_edge {
    return Intl.message(
      '对刃',
      name: 'overjet_edge',
      desc: '',
      args: [],
    );
  }

  /// `至少有一颗磨牙不可见，无法判断磨牙关系`
  String get fail_determine_molar {
    return Intl.message(
      '至少有一颗磨牙不可见，无法判断磨牙关系',
      name: 'fail_determine_molar',
      desc: '',
      args: [],
    );
  }

  /// `至少有一颗尖牙不可见，无法判断尖牙关系`
  String get fail_determine_canine {
    return Intl.message(
      '至少有一颗尖牙不可见，无法判断尖牙关系',
      name: 'fail_determine_canine',
      desc: '',
      args: [],
    );
  }

  /// `至少有一颗1号牙不可见，无法判断中线关系`
  String get fail_determine_midline {
    return Intl.message(
      '至少有一颗1号牙不可见，无法判断中线关系',
      name: 'fail_determine_midline',
      desc: '',
      args: [],
    );
  }

  /// `下颌相对上颌中线对齐`
  String get midline_normal {
    return Intl.message(
      '下颌相对上颌中线对齐',
      name: 'midline_normal',
      desc: '',
      args: [],
    );
  }

  /// `下颌相对上颌中线偏左`
  String get midline_left {
    return Intl.message(
      '下颌相对上颌中线偏左',
      name: 'midline_left',
      desc: '',
      args: [],
    );
  }

  /// `下颌相对上颌中线偏右`
  String get midline_right {
    return Intl.message(
      '下颌相对上颌中线偏右',
      name: 'midline_right',
      desc: '',
      args: [],
    );
  }

  /// `颊间隙`
  String get buccal_corridors {
    return Intl.message(
      '颊间隙',
      name: 'buccal_corridors',
      desc: '',
      args: [],
    );
  }

  /// `颊间隙小，宽微笑，符合大众审美。`
  String get buccal_space_1 {
    return Intl.message(
      '颊间隙小，宽微笑，符合大众审美。',
      name: 'buccal_space_1',
      desc: '',
      args: [],
    );
  }

  /// `颊间隙较小，中宽微笑。`
  String get buccal_space_2 {
    return Intl.message(
      '颊间隙较小，中宽微笑。',
      name: 'buccal_space_2',
      desc: '',
      args: [],
    );
  }

  /// `颊间隙中等，中等微笑。`
  String get buccal_space_3 {
    return Intl.message(
      '颊间隙中等，中等微笑。',
      name: 'buccal_space_3',
      desc: '',
      args: [],
    );
  }

  /// `颊间隙较大，中窄微笑。`
  String get buccal_space_4 {
    return Intl.message(
      '颊间隙较大，中窄微笑。',
      name: 'buccal_space_4',
      desc: '',
      args: [],
    );
  }

  /// `颊间隙大，窄微笑。`
  String get buccal_space_5 {
    return Intl.message(
      '颊间隙大，窄微笑。',
      name: 'buccal_space_5',
      desc: '',
      args: [],
    );
  }

  /// `微笑宽度`
  String get smile_width {
    return Intl.message(
      '微笑宽度',
      name: 'smile_width',
      desc: '',
      args: [],
    );
  }

  /// `微笑丰满度`
  String get smile_fullness {
    return Intl.message(
      '微笑丰满度',
      name: 'smile_fullness',
      desc: '',
      args: [],
    );
  }

  /// `中线分析`
  String get midline_analysis {
    return Intl.message(
      '中线分析',
      name: 'midline_analysis',
      desc: '',
      args: [],
    );
  }

  /// `院内初诊`
  String get hospital_diagnose {
    return Intl.message(
      '院内初诊',
      name: 'hospital_diagnose',
      desc: '',
      args: [],
    );
  }

  /// `初诊`
  String get first_diagnose {
    return Intl.message(
      '初诊',
      name: 'first_diagnose',
      desc: '',
      args: [],
    );
  }

  /// `新建初诊`
  String get add_diagnose {
    return Intl.message(
      '新建初诊',
      name: 'add_diagnose',
      desc: '',
      args: [],
    );
  }

  /// `初诊详情`
  String get diagnose_detail {
    return Intl.message(
      '初诊详情',
      name: 'diagnose_detail',
      desc: '',
      args: [],
    );
  }

  /// `基本资料`
  String get base_info {
    return Intl.message(
      '基本资料',
      name: 'base_info',
      desc: '',
      args: [],
    );
  }

  /// `病例资料`
  String get diagnose_info {
    return Intl.message(
      '病例资料',
      name: 'diagnose_info',
      desc: '',
      args: [],
    );
  }

  /// `制作报告`
  String get make_report {
    return Intl.message(
      '制作报告',
      name: 'make_report',
      desc: '',
      args: [],
    );
  }

  /// `新建患者`
  String get create_patient {
    return Intl.message(
      '新建患者',
      name: 'create_patient',
      desc: '',
      args: [],
    );
  }

  /// `导入口采患者`
  String get import_scan {
    return Intl.message(
      '导入口采患者',
      name: 'import_scan',
      desc: '',
      args: [],
    );
  }

  /// `请输入患者姓名或手机号`
  String get search_scan_hint {
    return Intl.message(
      '请输入患者姓名或手机号',
      name: 'search_scan_hint',
      desc: '',
      args: [],
    );
  }

  /// `口采阶段治疗方向`
  String get scan_convert_project {
    return Intl.message(
      '口采阶段治疗方向',
      name: 'scan_convert_project',
      desc: '',
      args: [],
    );
  }

  /// `口采时间`
  String get scan_time {
    return Intl.message(
      '口采时间',
      name: 'scan_time',
      desc: '',
      args: [],
    );
  }

  /// `活动来源`
  String get scan_activity_source {
    return Intl.message(
      '活动来源',
      name: 'scan_activity_source',
      desc: '',
      args: [],
    );
  }

  /// `上一步`
  String get previous_step {
    return Intl.message(
      '上一步',
      name: 'previous_step',
      desc: '',
      args: [],
    );
  }

  /// `上传`
  String get upload {
    return Intl.message(
      '上传',
      name: 'upload',
      desc: '',
      args: [],
    );
  }

  /// `全科病例`
  String get case_general {
    return Intl.message(
      '全科病例',
      name: 'case_general',
      desc: '',
      args: [],
    );
  }

  /// `正畸病例`
  String get case_orthodontics {
    return Intl.message(
      '正畸病例',
      name: 'case_orthodontics',
      desc: '',
      args: [],
    );
  }

  /// `儿牙病历`
  String get case_children {
    return Intl.message(
      '儿牙病历',
      name: 'case_children',
      desc: '',
      args: [],
    );
  }

  /// `（儿科接诊专用）`
  String get case_children_desc {
    return Intl.message(
      '（儿科接诊专用）',
      name: 'case_children_desc',
      desc: '',
      args: [],
    );
  }

  /// `以下影像将用于制作报告`
  String get photos_for_report {
    return Intl.message(
      '以下影像将用于制作报告',
      name: 'photos_for_report',
      desc: '',
      args: [],
    );
  }

  /// `出生年份`
  String get birthday_year {
    return Intl.message(
      '出生年份',
      name: 'birthday_year',
      desc: '',
      args: [],
    );
  }

  /// `请点击选择出生日期`
  String get select_birthday {
    return Intl.message(
      '请点击选择出生日期',
      name: 'select_birthday',
      desc: '',
      args: [],
    );
  }

  /// `意向项目`
  String get deal_project {
    return Intl.message(
      '意向项目',
      name: 'deal_project',
      desc: '',
      args: [],
    );
  }

  /// `初诊日期`
  String get first_diagnose_Date {
    return Intl.message(
      '初诊日期',
      name: 'first_diagnose_Date',
      desc: '',
      args: [],
    );
  }

  /// `您确定要删除该初诊患者吗`
  String get delete_diagnose_title {
    return Intl.message(
      '您确定要删除该初诊患者吗',
      name: 'delete_diagnose_title',
      desc: '',
      args: [],
    );
  }

  /// `确认删除后，该初诊患者将在网页端同步移除，且数据一旦删除不可恢复`
  String get delete_diagnose_content {
    return Intl.message(
      '确认删除后，该初诊患者将在网页端同步移除，且数据一旦删除不可恢复',
      name: 'delete_diagnose_content',
      desc: '',
      args: [],
    );
  }

  /// `请输入主要症状、部位、时间，最多500字`
  String get input_appeal {
    return Intl.message(
      '请输入主要症状、部位、时间，最多500字',
      name: 'input_appeal',
      desc: '',
      args: [],
    );
  }

  /// `正在连接互联网……`
  String get connecting_network {
    return Intl.message(
      '正在连接互联网……',
      name: 'connecting_network',
      desc: '',
      args: [],
    );
  }

  /// `正在上传……`
  String get submitting_files {
    return Intl.message(
      '正在上传……',
      name: 'submitting_files',
      desc: '',
      args: [],
    );
  }

  /// `生成报告`
  String get generate_report {
    return Intl.message(
      '生成报告',
      name: 'generate_report',
      desc: '',
      args: [],
    );
  }

  /// `以下内容填写后将展示在报告中`
  String get diagnose_report_tip {
    return Intl.message(
      '以下内容填写后将展示在报告中',
      name: 'diagnose_report_tip',
      desc: '',
      args: [],
    );
  }

  /// `推荐治疗项目`
  String get recommend_treat_project {
    return Intl.message(
      '推荐治疗项目',
      name: 'recommend_treat_project',
      desc: '',
      args: [],
    );
  }

  /// `AI推荐`
  String get recommend_by_ai {
    return Intl.message(
      'AI推荐',
      name: 'recommend_by_ai',
      desc: '',
      args: [],
    );
  }

  /// `报告将根据AI分析自动生成推荐治疗项目`
  String get recommend_by_ai_desc {
    return Intl.message(
      '报告将根据AI分析自动生成推荐治疗项目',
      name: 'recommend_by_ai_desc',
      desc: '',
      args: [],
    );
  }

  /// `自主推荐`
  String get recommend_by_self {
    return Intl.message(
      '自主推荐',
      name: 'recommend_by_self',
      desc: '',
      args: [],
    );
  }

  /// `其他健康建议`
  String get other_health_recommend {
    return Intl.message(
      '其他健康建议',
      name: 'other_health_recommend',
      desc: '',
      args: [],
    );
  }

  /// `请输入内容，最多300字`
  String get other_health_recommend_hint {
    return Intl.message(
      '请输入内容，最多300字',
      name: 'other_health_recommend_hint',
      desc: '',
      args: [],
    );
  }

  /// `上传失败`
  String get upload_result_fail {
    return Intl.message(
      '上传失败',
      name: 'upload_result_fail',
      desc: '',
      args: [],
    );
  }

  /// `上传成功`
  String get upload_result_success {
    return Intl.message(
      '上传成功',
      name: 'upload_result_success',
      desc: '',
      args: [],
    );
  }

  /// `报告正在生成`
  String get report_generate_going {
    return Intl.message(
      '报告正在生成',
      name: 'report_generate_going',
      desc: '',
      args: [],
    );
  }

  /// `报告生成成功`
  String get report_generate_success {
    return Intl.message(
      '报告生成成功',
      name: 'report_generate_success',
      desc: '',
      args: [],
    );
  }

  /// `报告生成失败`
  String get report_generate_fail {
    return Intl.message(
      '报告生成失败',
      name: 'report_generate_fail',
      desc: '',
      args: [],
    );
  }

  /// `您可于【我的-我的文件】处查看并下载文件`
  String get view_download_report {
    return Intl.message(
      '您可于【我的-我的文件】处查看并下载文件',
      name: 'view_download_report',
      desc: '',
      args: [],
    );
  }

  /// `关闭`
  String get close {
    return Intl.message(
      '关闭',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `前往查看`
  String get go_view {
    return Intl.message(
      '前往查看',
      name: 'go_view',
      desc: '',
      args: [],
    );
  }

  /// `重新生成`
  String get remake {
    return Intl.message(
      '重新生成',
      name: 'remake',
      desc: '',
      args: [],
    );
  }

  /// `请稍后再试`
  String get try_again {
    return Intl.message(
      '请稍后再试',
      name: 'try_again',
      desc: '',
      args: [],
    );
  }

  /// `我的文件`
  String get my_file {
    return Intl.message(
      '我的文件',
      name: 'my_file',
      desc: '',
      args: [],
    );
  }

  /// `为节省文件存储空间，临时文件保存时限为48小时，48小时后系统将默认删除，请及时下载文件`
  String get my_file_tip {
    return Intl.message(
      '为节省文件存储空间，临时文件保存时限为48小时，48小时后系统将默认删除，请及时下载文件',
      name: 'my_file_tip',
      desc: '',
      args: [],
    );
  }

  /// `报告名称`
  String get report_name {
    return Intl.message(
      '报告名称',
      name: 'report_name',
      desc: '',
      args: [],
    );
  }

  /// `生成时间`
  String get generate_time {
    return Intl.message(
      '生成时间',
      name: 'generate_time',
      desc: '',
      args: [],
    );
  }

  /// `文件有效期: %d小时`
  String get file_effect_time {
    return Intl.message(
      '文件有效期: %d小时',
      name: 'file_effect_time',
      desc: '',
      args: [],
    );
  }

  /// `正在生成`
  String get report_status_making {
    return Intl.message(
      '正在生成',
      name: 'report_status_making',
      desc: '',
      args: [],
    );
  }

  /// `生成失败`
  String get report_status_fail {
    return Intl.message(
      '生成失败',
      name: 'report_status_fail',
      desc: '',
      args: [],
    );
  }

  /// `已过期`
  String get report_status_outdate {
    return Intl.message(
      '已过期',
      name: 'report_status_outdate',
      desc: '',
      args: [],
    );
  }

  /// `报告预览`
  String get report_preview {
    return Intl.message(
      '报告预览',
      name: 'report_preview',
      desc: '',
      args: [],
    );
  }

  /// `初诊报告`
  String get diagnose_report {
    return Intl.message(
      '初诊报告',
      name: 'diagnose_report',
      desc: '',
      args: [],
    );
  }

  /// `已逾期`
  String get status_overtime {
    return Intl.message(
      '已逾期',
      name: 'status_overtime',
      desc: '',
      args: [],
    );
  }

  /// `未绑定`
  String get status_not_binding {
    return Intl.message(
      '未绑定',
      name: 'status_not_binding',
      desc: '',
      args: [],
    );
  }

  /// `暂无记录`
  String get status_bound {
    return Intl.message(
      '暂无记录',
      name: 'status_bound',
      desc: '',
      args: [],
    );
  }

  /// `正常进行`
  String get status_processing {
    return Intl.message(
      '正常进行',
      name: 'status_processing',
      desc: '',
      args: [],
    );
  }

  /// `已归档`
  String get status_deleted {
    return Intl.message(
      '已归档',
      name: 'status_deleted',
      desc: '',
      args: [],
    );
  }

  /// `已注销`
  String get status_destroy {
    return Intl.message(
      '已注销',
      name: 'status_destroy',
      desc: '',
      args: [],
    );
  }

  /// `疑似`
  String get suspended {
    return Intl.message(
      '疑似',
      name: 'suspended',
      desc: '',
      args: [],
    );
  }

  /// `中线关系`
  String get midline_relationship {
    return Intl.message(
      '中线关系',
      name: 'midline_relationship',
      desc: '',
      args: [],
    );
  }

  /// `尖牙关系`
  String get canine_relationship {
    return Intl.message(
      '尖牙关系',
      name: 'canine_relationship',
      desc: '',
      args: [],
    );
  }

  /// `覆𬌗关系`
  String get overbite_relationship {
    return Intl.message(
      '覆𬌗关系',
      name: 'overbite_relationship',
      desc: '',
      args: [],
    );
  }

  /// `覆盖关系`
  String get overjet_relationship {
    return Intl.message(
      '覆盖关系',
      name: 'overjet_relationship',
      desc: '',
      args: [],
    );
  }

  /// `请输入患者姓名或病例ID号`
  String get input_name_or_id {
    return Intl.message(
      '请输入患者姓名或病例ID号',
      name: 'input_name_or_id',
      desc: '',
      args: [],
    );
  }

  /// `监控状态`
  String get monitor_status {
    return Intl.message(
      '监控状态',
      name: 'monitor_status',
      desc: '',
      args: [],
    );
  }

  /// `最近扫描`
  String get last_scan {
    return Intl.message(
      '最近扫描',
      name: 'last_scan',
      desc: '',
      args: [],
    );
  }

  /// `监控进度`
  String get monitor_process {
    return Intl.message(
      '监控进度',
      name: 'monitor_process',
      desc: '',
      args: [],
    );
  }

  /// `查看反馈`
  String get view_feedback {
    return Intl.message(
      '查看反馈',
      name: 'view_feedback',
      desc: '',
      args: [],
    );
  }

  /// `发送反馈`
  String get send_feedback {
    return Intl.message(
      '发送反馈',
      name: 'send_feedback',
      desc: '',
      args: [],
    );
  }

  /// `扫描记录`
  String get scan_record {
    return Intl.message(
      '扫描记录',
      name: 'scan_record',
      desc: '',
      args: [],
    );
  }

  /// `查看详情`
  String get view_detail {
    return Intl.message(
      '查看详情',
      name: 'view_detail',
      desc: '',
      args: [],
    );
  }

  /// `系统公告`
  String get system_announce {
    return Intl.message(
      '系统公告',
      name: 'system_announce',
      desc: '',
      args: [],
    );
  }

  /// `当前账号该功能使用次数已超限，如有疑问请咨询客服`
  String get buy_vip_tip {
    return Intl.message(
      '当前账号该功能使用次数已超限，如有疑问请咨询客服',
      name: 'buy_vip_tip',
      desc: '',
      args: [],
    );
  }

  /// `（长按保存二维码，微信扫码添加）`
  String get long_press_save_qrcode {
    return Intl.message(
      '（长按保存二维码，微信扫码添加）',
      name: 'long_press_save_qrcode',
      desc: '',
      args: [],
    );
  }

  /// `清除缓存`
  String get clear_cache {
    return Intl.message(
      '清除缓存',
      name: 'clear_cache',
      desc: '',
      args: [],
    );
  }

  /// `缓存文件已清除!`
  String get clear_cache_finish {
    return Intl.message(
      '缓存文件已清除!',
      name: 'clear_cache_finish',
      desc: '',
      args: [],
    );
  }

  /// `已为您自动保存`
  String get auto_saved {
    return Intl.message(
      '已为您自动保存',
      name: 'auto_saved',
      desc: '',
      args: [],
    );
  }

  /// `儿童不良习惯调查问卷`
  String get children_bad_habit_question {
    return Intl.message(
      '儿童不良习惯调查问卷',
      name: 'children_bad_habit_question',
      desc: '',
      args: [],
    );
  }

  /// `建议有早矫治疗意向的填写`
  String get suggest_treat_to_fill {
    return Intl.message(
      '建议有早矫治疗意向的填写',
      name: 'suggest_treat_to_fill',
      desc: '',
      args: [],
    );
  }

  /// `多选`
  String get multiple_select {
    return Intl.message(
      '多选',
      name: 'multiple_select',
      desc: '',
      args: [],
    );
  }

  /// `微笑模拟需保证上传高清正面微笑像`
  String get need_clear_smile_photo {
    return Intl.message(
      '微笑模拟需保证上传高清正面微笑像',
      name: 'need_clear_smile_photo',
      desc: '',
      args: [],
    );
  }

  /// `扫描绑定条形码`
  String get scan_bind_code {
    return Intl.message(
      '扫描绑定条形码',
      name: 'scan_bind_code',
      desc: '',
      args: [],
    );
  }

  /// `扫描条形码`
  String get scan_code {
    return Intl.message(
      '扫描条形码',
      name: 'scan_code',
      desc: '',
      args: [],
    );
  }

  /// `解除绑定`
  String get cancel_binding {
    return Intl.message(
      '解除绑定',
      name: 'cancel_binding',
      desc: '',
      args: [],
    );
  }

  /// `重新扫描`
  String get rescan_binding {
    return Intl.message(
      '重新扫描',
      name: 'rescan_binding',
      desc: '',
      args: [],
    );
  }

  /// `（条形码已被占用）`
  String get code_has_been_used {
    return Intl.message(
      '（条形码已被占用）',
      name: 'code_has_been_used',
      desc: '',
      args: [],
    );
  }

  /// `确认取消条形码绑定？`
  String get cancel_binding_confirm {
    return Intl.message(
      '确认取消条形码绑定？',
      name: 'cancel_binding_confirm',
      desc: '',
      args: [],
    );
  }

  /// `暂不解绑`
  String get not_cancel_binding {
    return Intl.message(
      '暂不解绑',
      name: 'not_cancel_binding',
      desc: '',
      args: [],
    );
  }

  /// `此码已被占用，请更换新码`
  String get code_has_been_bound {
    return Intl.message(
      '此码已被占用，请更换新码',
      name: 'code_has_been_bound',
      desc: '',
      args: [],
    );
  }

  /// `绑码异常`
  String get bind_code_error {
    return Intl.message(
      '绑码异常',
      name: 'bind_code_error',
      desc: '',
      args: [],
    );
  }

  /// `绑定成功`
  String get bind_code_success {
    return Intl.message(
      '绑定成功',
      name: 'bind_code_success',
      desc: '',
      args: [],
    );
  }

  /// `查看异常数据`
  String get view_error_data {
    return Intl.message(
      '查看异常数据',
      name: 'view_error_data',
      desc: '',
      args: [],
    );
  }

  /// `邮箱`
  String get mail {
    return Intl.message(
      '邮箱',
      name: 'mail',
      desc: '',
      args: [],
    );
  }

  /// `请输入邮箱`
  String get input_mail {
    return Intl.message(
      '请输入邮箱',
      name: 'input_mail',
      desc: '',
      args: [],
    );
  }

  /// `中国大陆`
  String get login_china {
    return Intl.message(
      '中国大陆',
      name: 'login_china',
      desc: '',
      args: [],
    );
  }

  /// `亚太地区`
  String get login_asian {
    return Intl.message(
      '亚太地区',
      name: 'login_asian',
      desc: '',
      args: [],
    );
  }

  /// `切换地区`
  String get switch_area {
    return Intl.message(
      '切换地区',
      name: 'switch_area',
      desc: '',
      args: [],
    );
  }

  /// `确定修改`
  String get confirm_modify_code {
    return Intl.message(
      '确定修改',
      name: 'confirm_modify_code',
      desc: '',
      args: [],
    );
  }

  /// `新密码`
  String get new_password {
    return Intl.message(
      '新密码',
      name: 'new_password',
      desc: '',
      args: [],
    );
  }

  /// `密码重置成功，请重新登录`
  String get reset_password_successful {
    return Intl.message(
      '密码重置成功，请重新登录',
      name: 'reset_password_successful',
      desc: '',
      args: [],
    );
  }

  /// `账号已在其他设备登录`
  String get account_login_other_device {
    return Intl.message(
      '账号已在其他设备登录',
      name: 'account_login_other_device',
      desc: '',
      args: [],
    );
  }

  /// `继续登录将导致账号在其他设备退出登录`
  String get login_push_other_device {
    return Intl.message(
      '继续登录将导致账号在其他设备退出登录',
      name: 'login_push_other_device',
      desc: '',
      args: [],
    );
  }

  /// `取消登录`
  String get cancel_login {
    return Intl.message(
      '取消登录',
      name: 'cancel_login',
      desc: '',
      args: [],
    );
  }

  /// `继续登录`
  String get continue_login {
    return Intl.message(
      '继续登录',
      name: 'continue_login',
      desc: '',
      args: [],
    );
  }

  /// `由于您长时间未进行操作或检测到账号已在其他设备登录，为了保护您的账户安全，系统已自动退出登录状态`
  String get login_exit_desc {
    return Intl.message(
      '由于您长时间未进行操作或检测到账号已在其他设备登录，为了保护您的账户安全，系统已自动退出登录状态',
      name: 'login_exit_desc',
      desc: '',
      args: [],
    );
  }

  /// `尊敬的用户，为了给您提供更优质的服务，我们已于10月24日凌晨更新版本，请重新登录以继续使用。感谢您的理解与支持！`
  String get login_v3_desc {
    return Intl.message(
      '尊敬的用户，为了给您提供更优质的服务，我们已于10月24日凌晨更新版本，请重新登录以继续使用。感谢您的理解与支持！',
      name: 'login_v3_desc',
      desc: '',
      args: [],
    );
  }

  /// `Logo设置：`
  String get logo_setting {
    return Intl.message(
      'Logo设置：',
      name: 'logo_setting',
      desc: '',
      args: [],
    );
  }

  /// `报告封面显示乐齿拍Logo`
  String get report_show_ts_logo {
    return Intl.message(
      '报告封面显示乐齿拍Logo',
      name: 'report_show_ts_logo',
      desc: '',
      args: [],
    );
  }

  /// `当前无上传失败数据`
  String get no_upload_fail_data {
    return Intl.message(
      '当前无上传失败数据',
      name: 'no_upload_fail_data',
      desc: '',
      args: [],
    );
  }

  /// `精准洞察，守护健康`
  String get login_slogan {
    return Intl.message(
      '精准洞察，守护健康',
      name: 'login_slogan',
      desc: '',
      args: [],
    );
  }

  /// `AI全方位智能识别与评估，为口腔健\n康保驾护航`
  String get login_desc {
    return Intl.message(
      'AI全方位智能识别与评估，为口腔健\n康保驾护航',
      name: 'login_desc',
      desc: '',
      args: [],
    );
  }

  /// `敬请期待`
  String get waiting_new_function {
    return Intl.message(
      '敬请期待',
      name: 'waiting_new_function',
      desc: '',
      args: [],
    );
  }

  /// `当前缓存: `
  String get current_cache {
    return Intl.message(
      '当前缓存: ',
      name: 'current_cache',
      desc: '',
      args: [],
    );
  }

  /// `点击清除`
  String get click_clear_cache {
    return Intl.message(
      '点击清除',
      name: 'click_clear_cache',
      desc: '',
      args: [],
    );
  }

  /// `活动列表`
  String get activity_list {
    return Intl.message(
      '活动列表',
      name: 'activity_list',
      desc: '',
      args: [],
    );
  }

  /// `口腔数据采集活动列表`
  String get scan_activity_list {
    return Intl.message(
      '口腔数据采集活动列表',
      name: 'scan_activity_list',
      desc: '',
      args: [],
    );
  }

  /// `编辑活动`
  String get edit_activity {
    return Intl.message(
      '编辑活动',
      name: 'edit_activity',
      desc: '',
      args: [],
    );
  }

  /// `创建成功`
  String get create_success {
    return Intl.message(
      '创建成功',
      name: 'create_success',
      desc: '',
      args: [],
    );
  }

  /// `去完善`
  String get go_to_complete {
    return Intl.message(
      '去完善',
      name: 'go_to_complete',
      desc: '',
      args: [],
    );
  }

  /// `编辑患者扫描`
  String get edit_scan_record {
    return Intl.message(
      '编辑患者扫描',
      name: 'edit_scan_record',
      desc: '',
      args: [],
    );
  }

  /// `一键重新上传`
  String get retry_upload_all {
    return Intl.message(
      '一键重新上传',
      name: 'retry_upload_all',
      desc: '',
      args: [],
    );
  }

  /// `切换机构`
  String get switch_tenant {
    return Intl.message(
      '切换机构',
      name: 'switch_tenant',
      desc: '',
      args: [],
    );
  }

  /// `切换成功`
  String get switch_success {
    return Intl.message(
      '切换成功',
      name: 'switch_success',
      desc: '',
      args: [],
    );
  }

  /// `设置中心`
  String get my_center {
    return Intl.message(
      '设置中心',
      name: 'my_center',
      desc: '',
      args: [],
    );
  }

  /// `点击进入`
  String get click_enter {
    return Intl.message(
      '点击进入',
      name: 'click_enter',
      desc: '',
      args: [],
    );
  }

  /// `条形码`
  String get record_sn {
    return Intl.message(
      '条形码',
      name: 'record_sn',
      desc: '',
      args: [],
    );
  }

  /// `文件大小不能超过2M`
  String get tenant_qrcode_size_limit {
    return Intl.message(
      '文件大小不能超过2M',
      name: 'tenant_qrcode_size_limit',
      desc: '',
      args: [],
    );
  }

  /// `暂无权限`
  String get no_permission {
    return Intl.message(
      '暂无权限',
      name: 'no_permission',
      desc: '',
      args: [],
    );
  }

  /// `口腔数据采集功能仅对机构版账户开放，请扫描下方二维码访问乐齿拍网站申请开通!`
  String get login_only_for_tenant {
    return Intl.message(
      '口腔数据采集功能仅对机构版账户开放，请扫描下方二维码访问乐齿拍网站申请开通!',
      name: 'login_only_for_tenant',
      desc: '',
      args: [],
    );
  }

  /// `正在下载...`
  String get downloading {
    return Intl.message(
      '正在下载...',
      name: 'downloading',
      desc: '',
      args: [],
    );
  }

  /// `下载完成`
  String get download_complete {
    return Intl.message(
      '下载完成',
      name: 'download_complete',
      desc: '',
      args: [],
    );
  }

  /// `灵芽需要请求您的相机权限，用于拍摄面像照，请前往手机系统设置打开相机权限`
  String get request_camera_permission {
    return Intl.message(
      '灵芽需要请求您的相机权限，用于拍摄面像照，请前往手机系统设置打开相机权限',
      name: 'request_camera_permission',
      desc: '',
      args: [],
    );
  }

  /// `灵芽需要请求您的相机权限\n用于扫描条形码`
  String get request_camera_qrcode {
    return Intl.message(
      '灵芽需要请求您的相机权限\n用于扫描条形码',
      name: 'request_camera_qrcode',
      desc: '',
      args: [],
    );
  }

  /// `灵芽需要请求您的存储权限\n用于保存图片`
  String get request_storage {
    return Intl.message(
      '灵芽需要请求您的存储权限\n用于保存图片',
      name: 'request_storage',
      desc: '',
      args: [],
    );
  }

  /// `设备的电量过低，可能出现卡顿或断连，请尽快充电哦！`
  String get low_battery_alert {
    return Intl.message(
      '设备的电量过低，可能出现卡顿或断连，请尽快充电哦！',
      name: 'low_battery_alert',
      desc: '',
      args: [],
    );
  }

  /// `请输入采集活动名称/归属人`
  String get input_activity_search {
    return Intl.message(
      '请输入采集活动名称/归属人',
      name: 'input_activity_search',
      desc: '',
      args: [],
    );
  }

  /// `请输入患者名称/手机号`
  String get input_record_search {
    return Intl.message(
      '请输入患者名称/手机号',
      name: 'input_record_search',
      desc: '',
      args: [],
    );
  }

  /// `没有找到“__”相关活动`
  String get cannot_find_activity {
    return Intl.message(
      '没有找到“__”相关活动',
      name: 'cannot_find_activity',
      desc: '',
      args: [],
    );
  }

  /// `没有找到“__”相关被采集人`
  String get cannot_find_record {
    return Intl.message(
      '没有找到“__”相关被采集人',
      name: 'cannot_find_record',
      desc: '',
      args: [],
    );
  }

  /// `AI分析超时，请点击重新分析`
  String get ai_status_overtime {
    return Intl.message(
      'AI分析超时，请点击重新分析',
      name: 'ai_status_overtime',
      desc: '',
      args: [],
    );
  }

  /// `抱歉AI分析失败，请点击重新分析，或联系客服`
  String get ai_status_fail {
    return Intl.message(
      '抱歉AI分析失败，请点击重新分析，或联系客服',
      name: 'ai_status_fail',
      desc: '',
      args: [],
    );
  }

  /// `重新分析`
  String get retry_analysis {
    return Intl.message(
      '重新分析',
      name: 'retry_analysis',
      desc: '',
      args: [],
    );
  }

  /// `AI分析失败`
  String get ai_analysis_fail {
    return Intl.message(
      'AI分析失败',
      name: 'ai_analysis_fail',
      desc: '',
      args: [],
    );
  }

  /// `正在进行AI分析`
  String get ai_analysis_process {
    return Intl.message(
      '正在进行AI分析',
      name: 'ai_analysis_process',
      desc: '',
      args: [],
    );
  }

  /// `AI分析超时`
  String get ai_analysis_overtime {
    return Intl.message(
      'AI分析超时',
      name: 'ai_analysis_overtime',
      desc: '',
      args: [],
    );
  }

  /// `哎呀，AI分析出了点岔子，不妨点击“重新分析”，或者咨询客服小伙伴哦！当然您也可以继续做报告～`
  String get ai_analysis_fail_report {
    return Intl.message(
      '哎呀，AI分析出了点岔子，不妨点击“重新分析”，或者咨询客服小伙伴哦！当然您也可以继续做报告～',
      name: 'ai_analysis_fail_report',
      desc: '',
      args: [],
    );
  }

  /// `预计一小时内就能见到AI分析结果啦，不妨稍后再来！当然您也可以继续做报告～`
  String get ai_analysis_process_report {
    return Intl.message(
      '预计一小时内就能见到AI分析结果啦，不妨稍后再来！当然您也可以继续做报告～',
      name: 'ai_analysis_process_report',
      desc: '',
      args: [],
    );
  }

  /// `继续做报告`
  String get continue_make_report {
    return Intl.message(
      '继续做报告',
      name: 'continue_make_report',
      desc: '',
      args: [],
    );
  }

  /// `稍后再来`
  String get come_on_later {
    return Intl.message(
      '稍后再来',
      name: 'come_on_later',
      desc: '',
      args: [],
    );
  }

  /// `选择`
  String get select {
    return Intl.message(
      '选择',
      name: 'select',
      desc: '',
      args: [],
    );
  }

  /// `删除患者数据`
  String get delete_user_record {
    return Intl.message(
      '删除患者数据',
      name: 'delete_user_record',
      desc: '',
      args: [],
    );
  }

  /// `确认删除这些患者数据吗？`
  String get delete_user_record_confirm {
    return Intl.message(
      '确认删除这些患者数据吗？',
      name: 'delete_user_record_confirm',
      desc: '',
      args: [],
    );
  }

  /// `存储空间`
  String get data_storage {
    return Intl.message(
      '存储空间',
      name: 'data_storage',
      desc: '',
      args: [],
    );
  }

  /// `口采数据导出备份`
  String get export_scan_data_file {
    return Intl.message(
      '口采数据导出备份',
      name: 'export_scan_data_file',
      desc: '',
      args: [],
    );
  }

  /// `缓存`
  String get cache {
    return Intl.message(
      '缓存',
      name: 'cache',
      desc: '',
      args: [],
    );
  }

  /// `清理`
  String get clear {
    return Intl.message(
      '清理',
      name: 'clear',
      desc: '',
      args: [],
    );
  }

  /// `缓存是使用灵芽过程中产生的临时数据，清理缓存不会影响灵芽的正常使用。`
  String get clear_cache_desc {
    return Intl.message(
      '缓存是使用灵芽过程中产生的临时数据，清理缓存不会影响灵芽的正常使用。',
      name: 'clear_cache_desc',
      desc: '',
      args: [],
    );
  }

  /// `口采数据文件`
  String get scan_data_file {
    return Intl.message(
      '口采数据文件',
      name: 'scan_data_file',
      desc: '',
      args: [],
    );
  }

  /// `这是口腔数据采集模块存在本地的备份，清理之后，`
  String get scan_data_file_desc1 {
    return Intl.message(
      '这是口腔数据采集模块存在本地的备份，清理之后，',
      name: 'scan_data_file_desc1',
      desc: '',
      args: [],
    );
  }

  /// `已上传的数据可以正常使用，待上传、上传中和上传失败的数据彻底被清空，不可恢复。\n`
  String get scan_data_file_desc2 {
    return Intl.message(
      '已上传的数据可以正常使用，待上传、上传中和上传失败的数据彻底被清空，不可恢复。\n',
      name: 'scan_data_file_desc2',
      desc: '',
      args: [],
    );
  }

  /// `请确保所有必要数据已上传成功之后再来清理。`
  String get scan_data_file_desc3 {
    return Intl.message(
      '请确保所有必要数据已上传成功之后再来清理。',
      name: 'scan_data_file_desc3',
      desc: '',
      args: [],
    );
  }

  /// `清理数据`
  String get clear_data {
    return Intl.message(
      '清理数据',
      name: 'clear_data',
      desc: '',
      args: [],
    );
  }

  /// `清理之后，待上传、上传中和上传失败的数据彻底被清空，不可恢复。`
  String get clear_data_desc1 {
    return Intl.message(
      '清理之后，待上传、上传中和上传失败的数据彻底被清空，不可恢复。',
      name: 'clear_data_desc1',
      desc: '',
      args: [],
    );
  }

  /// `请确保所有必要数据已上传成功之后再来清理。`
  String get clear_data_desc2 {
    return Intl.message(
      '请确保所有必要数据已上传成功之后再来清理。',
      name: 'clear_data_desc2',
      desc: '',
      args: [],
    );
  }

  /// `口采数据文件已清除！`
  String get clear_scan_data_finish {
    return Intl.message(
      '口采数据文件已清除！',
      name: 'clear_scan_data_finish',
      desc: '',
      args: [],
    );
  }

  /// `如果您有2024年3月1日~10月23日采集的患者数据，且还没有进行上传，请进行导出。如果不导出则数据会彻底丢失。`
  String get export_scan_data_desc {
    return Intl.message(
      '如果您有2024年3月1日~10月23日采集的患者数据，且还没有进行上传，请进行导出。如果不导出则数据会彻底丢失。',
      name: 'export_scan_data_desc',
      desc: '',
      args: [],
    );
  }

  /// `导出`
  String get export {
    return Intl.message(
      '导出',
      name: 'export',
      desc: '',
      args: [],
    );
  }

  /// `导出中...`
  String get export_ing {
    return Intl.message(
      '导出中...',
      name: 'export_ing',
      desc: '',
      args: [],
    );
  }

  /// `正在连接相机，请稍等...`
  String get conencting_camera {
    return Intl.message(
      '正在连接相机，请稍等...',
      name: 'conencting_camera',
      desc: '',
      args: [],
    );
  }

  /// `切换语言`
  String get change_language {
    return Intl.message(
      '切换语言',
      name: 'change_language',
      desc: '',
      args: [],
    );
  }

  /// `检测更新`
  String get check_version {
    return Intl.message(
      '检测更新',
      name: 'check_version',
      desc: '',
      args: [],
    );
  }

  /// `系统设置`
  String get setting_system {
    return Intl.message(
      '系统设置',
      name: 'setting_system',
      desc: '',
      args: [],
    );
  }

  /// `移动网络`
  String get mobile_wlan {
    return Intl.message(
      '移动网络',
      name: 'mobile_wlan',
      desc: '',
      args: [],
    );
  }

  /// `WLAN`
  String get wlan {
    return Intl.message(
      'WLAN',
      name: 'wlan',
      desc: '',
      args: [],
    );
  }

  /// `屏幕亮度`
  String get screen_light {
    return Intl.message(
      '屏幕亮度',
      name: 'screen_light',
      desc: '',
      args: [],
    );
  }

  /// `自动锁屏`
  String get auto_lock_screen {
    return Intl.message(
      '自动锁屏',
      name: 'auto_lock_screen',
      desc: '',
      args: [],
    );
  }

  /// `系统音量`
  String get system_audio {
    return Intl.message(
      '系统音量',
      name: 'system_audio',
      desc: '',
      args: [],
    );
  }

  /// `当前音量:`
  String get current_audio {
    return Intl.message(
      '当前音量:',
      name: 'current_audio',
      desc: '',
      args: [],
    );
  }

  /// `当前亮度:`
  String get current_brightness {
    return Intl.message(
      '当前亮度:',
      name: 'current_brightness',
      desc: '',
      args: [],
    );
  }

  /// `返回`
  String get back {
    return Intl.message(
      '返回',
      name: 'back',
      desc: '',
      args: [],
    );
  }

  /// `关注`
  String get follow {
    return Intl.message(
      '关注',
      name: 'follow',
      desc: '',
      args: [],
    );
  }

  /// `时间正序`
  String get time_sort {
    return Intl.message(
      '时间正序',
      name: 'time_sort',
      desc: '',
      args: [],
    );
  }

  /// `时间倒序`
  String get time_desc {
    return Intl.message(
      '时间倒序',
      name: 'time_desc',
      desc: '',
      args: [],
    );
  }

  /// `新建活动`
  String get create_activity {
    return Intl.message(
      '新建活动',
      name: 'create_activity',
      desc: '',
      args: [],
    );
  }

  /// `已上传：%d  待上传：%d`
  String get activity_upload_status {
    return Intl.message(
      '已上传：%d  待上传：%d',
      name: 'activity_upload_status',
      desc: '',
      args: [],
    );
  }

  /// `上传中`
  String get uploading {
    return Intl.message(
      '上传中',
      name: 'uploading',
      desc: '',
      args: [],
    );
  }

  /// `新建扫描`
  String get new_scan {
    return Intl.message(
      '新建扫描',
      name: 'new_scan',
      desc: '',
      args: [],
    );
  }

  /// `完善记录`
  String get complete_record {
    return Intl.message(
      '完善记录',
      name: 'complete_record',
      desc: '',
      args: [],
    );
  }

  /// `上传记录`
  String get upload_record {
    return Intl.message(
      '上传记录',
      name: 'upload_record',
      desc: '',
      args: [],
    );
  }

  /// `导出至U盘`
  String get export_u_card {
    return Intl.message(
      '导出至U盘',
      name: 'export_u_card',
      desc: '',
      args: [],
    );
  }

  /// `删除记录`
  String get delete_record {
    return Intl.message(
      '删除记录',
      name: 'delete_record',
      desc: '',
      args: [],
    );
  }

  /// `未识别到u盘，请确认u盘已经插好！`
  String get usb_recognized {
    return Intl.message(
      '未识别到u盘，请确认u盘已经插好！',
      name: 'usb_recognized',
      desc: '',
      args: [],
    );
  }

  /// `选择U盘保存`
  String get select_usb_save {
    return Intl.message(
      '选择U盘保存',
      name: 'select_usb_save',
      desc: '',
      args: [],
    );
  }

  /// `选拍`
  String get optional_scan {
    return Intl.message(
      '选拍',
      name: 'optional_scan',
      desc: '',
      args: [],
    );
  }

  /// `点击选择年龄`
  String get click_select_age {
    return Intl.message(
      '点击选择年龄',
      name: 'click_select_age',
      desc: '',
      args: [],
    );
  }

  /// `未登录`
  String get not_login {
    return Intl.message(
      '未登录',
      name: 'not_login',
      desc: '',
      args: [],
    );
  }

  /// `立即登录`
  String get login_now {
    return Intl.message(
      '立即登录',
      name: 'login_now',
      desc: '',
      args: [],
    );
  }

  /// `登录后显示机构`
  String get login_show_tenant {
    return Intl.message(
      '登录后显示机构',
      name: 'login_show_tenant',
      desc: '',
      args: [],
    );
  }

  /// `退出账号`
  String get logout_account {
    return Intl.message(
      '退出账号',
      name: 'logout_account',
      desc: '',
      args: [],
    );
  }

  /// `退出`
  String get exit {
    return Intl.message(
      '退出',
      name: 'exit',
      desc: '',
      args: [],
    );
  }

  /// `单人采集`
  String get scan_in_hospital {
    return Intl.message(
      '单人采集',
      name: 'scan_in_hospital',
      desc: '',
      args: [],
    );
  }

  /// `无需创建活动，适用于单个患者快速采集`
  String get scan_in_hospital_desc {
    return Intl.message(
      '无需创建活动，适用于单个患者快速采集',
      name: 'scan_in_hospital_desc',
      desc: '',
      args: [],
    );
  }

  /// `批量采集`
  String get scan_out_hospital {
    return Intl.message(
      '批量采集',
      name: 'scan_out_hospital',
      desc: '',
      args: [],
    );
  }

  /// `先创建活动再采集，适用于批量扫描活动`
  String get scan_out_hospital_desc {
    return Intl.message(
      '先创建活动再采集，适用于批量扫描活动',
      name: 'scan_out_hospital_desc',
      desc: '',
      args: [],
    );
  }

  /// `搜索活动名称/归属人/被采集人名字`
  String get search_activity_hint {
    return Intl.message(
      '搜索活动名称/归属人/被采集人名字',
      name: 'search_activity_hint',
      desc: '',
      args: [],
    );
  }

  /// `搜索活动名称/被采集人名字`
  String get offline_search_activity_hint {
    return Intl.message(
      '搜索活动名称/被采集人名字',
      name: 'offline_search_activity_hint',
      desc: '',
      args: [],
    );
  }

  /// `按活动名`
  String get search_by_activity_name {
    return Intl.message(
      '按活动名',
      name: 'search_by_activity_name',
      desc: '',
      args: [],
    );
  }

  /// `按归属人`
  String get search_by_person_name {
    return Intl.message(
      '按归属人',
      name: 'search_by_person_name',
      desc: '',
      args: [],
    );
  }

  /// `按被采集人`
  String get search_by_record_name {
    return Intl.message(
      '按被采集人',
      name: 'search_by_record_name',
      desc: '',
      args: [],
    );
  }

  /// `输入活动名称`
  String get input_activity_name {
    return Intl.message(
      '输入活动名称',
      name: 'input_activity_name',
      desc: '',
      args: [],
    );
  }

  /// `输入归属人名称`
  String get input_person_name {
    return Intl.message(
      '输入归属人名称',
      name: 'input_person_name',
      desc: '',
      args: [],
    );
  }

  /// `输入完整的被采集人名称或手机号`
  String get input_record_name {
    return Intl.message(
      '输入完整的被采集人名称或手机号',
      name: 'input_record_name',
      desc: '',
      args: [],
    );
  }

  /// `搜索“%s”中的被采集人`
  String get search_record_hint {
    return Intl.message(
      '搜索“%s”中的被采集人',
      name: 'search_record_hint',
      desc: '',
      args: [],
    );
  }

  /// `批量操作`
  String get batch_select_record {
    return Intl.message(
      '批量操作',
      name: 'batch_select_record',
      desc: '',
      args: [],
    );
  }

  /// `退出批量操作`
  String get exit_batch_select {
    return Intl.message(
      '退出批量操作',
      name: 'exit_batch_select',
      desc: '',
      args: [],
    );
  }

  /// `选择全部`
  String get batch_select_all_record {
    return Intl.message(
      '选择全部',
      name: 'batch_select_all_record',
      desc: '',
      args: [],
    );
  }

  /// `取消全选`
  String get cancel_select_all {
    return Intl.message(
      '取消全选',
      name: 'cancel_select_all',
      desc: '',
      args: [],
    );
  }

  /// `已选%d个`
  String get select_record_count {
    return Intl.message(
      '已选%d个',
      name: 'select_record_count',
      desc: '',
      args: [],
    );
  }

  /// `保存并退出`
  String get save_and_exit {
    return Intl.message(
      '保存并退出',
      name: 'save_and_exit',
      desc: '',
      args: [],
    );
  }

  /// `去上传`
  String get go_upload {
    return Intl.message(
      '去上传',
      name: 'go_upload',
      desc: '',
      args: [],
    );
  }

  /// `缺牙间隙`
  String get space_teeth {
    return Intl.message(
      '缺牙间隙',
      name: 'space_teeth',
      desc: '',
      args: [],
    );
  }

  /// `牙号缺失`
  String get lost_teeth {
    return Intl.message(
      '牙号缺失',
      name: 'lost_teeth',
      desc: '',
      args: [],
    );
  }

  /// `疑似无牙颌`
  String get lost_all_teeth {
    return Intl.message(
      '疑似无牙颌',
      name: 'lost_all_teeth',
      desc: '',
      args: [],
    );
  }

  /// `疑似上颌牙列缺失`
  String get lost_upper_teeth {
    return Intl.message(
      '疑似上颌牙列缺失',
      name: 'lost_upper_teeth',
      desc: '',
      args: [],
    );
  }

  /// `疑似下颌牙列缺失`
  String get lost_lower_teeth {
    return Intl.message(
      '疑似下颌牙列缺失',
      name: 'lost_lower_teeth',
      desc: '',
      args: [],
    );
  }

  /// `失败原因`
  String get fail_reason {
    return Intl.message(
      '失败原因',
      name: 'fail_reason',
      desc: '',
      args: [],
    );
  }

  /// `已修改AI结果`
  String get ai_result_modified {
    return Intl.message(
      '已修改AI结果',
      name: 'ai_result_modified',
      desc: '',
      args: [],
    );
  }

  /// `情况良好`
  String get teeth_good {
    return Intl.message(
      '情况良好',
      name: 'teeth_good',
      desc: '',
      args: [],
    );
  }

  /// `是否需要保存当前患者信息`
  String get save_info {
    return Intl.message(
      '是否需要保存当前患者信息',
      name: 'save_info',
      desc: '',
      args: [],
    );
  }

  /// `当前暂无网络，请联网后重试上传哦~`
  String get no_network_upload {
    return Intl.message(
      '当前暂无网络，请联网后重试上传哦~',
      name: 'no_network_upload',
      desc: '',
      args: [],
    );
  }

  /// `无法判断`
  String get cannot_judge {
    return Intl.message(
      '无法判断',
      name: 'cannot_judge',
      desc: '',
      args: [],
    );
  }

  /// `未发现明显牙列问题`
  String get teeth_list_good {
    return Intl.message(
      '未发现明显牙列问题',
      name: 'teeth_list_good',
      desc: '',
      args: [],
    );
  }

  /// `智齿`
  String get extract_teeth {
    return Intl.message(
      '智齿',
      name: 'extract_teeth',
      desc: '',
      args: [],
    );
  }

  /// `AI目前仅支持识别龋齿、牙结石、色素沉着、残根、残冠、楔状缺损、脱矿、软垢、牙周炎(包含牙龈退缩/牙龈红肿/牙龈出血三种表象)`
  String get tooth_body_ai_tip {
    return Intl.message(
      'AI目前仅支持识别龋齿、牙结石、色素沉着、残根、残冠、楔状缺损、脱矿、软垢、牙周炎(包含牙龈退缩/牙龈红肿/牙龈出血三种表象)',
      name: 'tooth_body_ai_tip',
      desc: '',
      args: [],
    );
  }

  /// `AI目前仅支持识别牙列不齐（颌面观的牙列流畅度不佳）、牙列间隙、牙号缺失、缺牙间隙`
  String get tooth_list_ai_tip {
    return Intl.message(
      'AI目前仅支持识别牙列不齐（颌面观的牙列流畅度不佳）、牙列间隙、牙号缺失、缺牙间隙',
      name: 'tooth_list_ai_tip',
      desc: '',
      args: [],
    );
  }

  /// `导出到U盘`
  String get export_to_usb {
    return Intl.message(
      '导出到U盘',
      name: 'export_to_usb',
      desc: '',
      args: [],
    );
  }

  /// `局域网共享`
  String get lan_sharing {
    return Intl.message(
      '局域网共享',
      name: 'lan_sharing',
      desc: '',
      args: [],
    );
  }

  /// `患者基本信息、图像`
  String get patient_info_and_image {
    return Intl.message(
      '患者基本信息、图像',
      name: 'patient_info_and_image',
      desc: '',
      args: [],
    );
  }

  /// `患者基本信息、图像和PDF报告`
  String get patient_info_and_pdf {
    return Intl.message(
      '患者基本信息、图像和PDF报告',
      name: 'patient_info_and_pdf',
      desc: '',
      args: [],
    );
  }

  /// `请选择导出到U盘的内容：`
  String get usb_export_content_tip {
    return Intl.message(
      '请选择导出到U盘的内容：',
      name: 'usb_export_content_tip',
      desc: '',
      args: [],
    );
  }

  /// `导出到U盘：`
  String get export_to_usb_tip {
    return Intl.message(
      '导出到U盘：',
      name: 'export_to_usb_tip',
      desc: '',
      args: [],
    );
  }

  /// `检测到U盘`
  String get detected_usb {
    return Intl.message(
      '检测到U盘',
      name: 'detected_usb',
      desc: '',
      args: [],
    );
  }

  /// `未检测到U盘`
  String get no_usb_detected {
    return Intl.message(
      '未检测到U盘',
      name: 'no_usb_detected',
      desc: '',
      args: [],
    );
  }

  /// `请先插入U盘，再使用此功能。`
  String get please_insert_usb {
    return Intl.message(
      '请先插入U盘，再使用此功能。',
      name: 'please_insert_usb',
      desc: '',
      args: [],
    );
  }

  /// `整体传输进度：已导出%s个，导出中%s个，导出失败%s个`
  String get usb_export_progress {
    return Intl.message(
      '整体传输进度：已导出%s个，导出中%s个，导出失败%s个',
      name: 'usb_export_progress',
      desc: '',
      args: [],
    );
  }

  /// `数据`
  String get data {
    return Intl.message(
      '数据',
      name: 'data',
      desc: '',
      args: [],
    );
  }

  /// `状态`
  String get status {
    return Intl.message(
      '状态',
      name: 'status',
      desc: '',
      args: [],
    );
  }

  /// `导出失败`
  String get export_fail {
    return Intl.message(
      '导出失败',
      name: 'export_fail',
      desc: '',
      args: [],
    );
  }

  /// `已导出`
  String get export_success {
    return Intl.message(
      '已导出',
      name: 'export_success',
      desc: '',
      args: [],
    );
  }

  /// `导出中`
  String get export_processing {
    return Intl.message(
      '导出中',
      name: 'export_processing',
      desc: '',
      args: [],
    );
  }

  /// `重新导出`
  String get export_retry {
    return Intl.message(
      '重新导出',
      name: 'export_retry',
      desc: '',
      args: [],
    );
  }

  /// `终止传输`
  String get terminate_export {
    return Intl.message(
      '终止传输',
      name: 'terminate_export',
      desc: '',
      args: [],
    );
  }

  /// `是否确认终止传输？`
  String get terminate_export_confirm {
    return Intl.message(
      '是否确认终止传输？',
      name: 'terminate_export_confirm',
      desc: '',
      args: [],
    );
  }

  /// `永不`
  String get auto_lock_screen_never {
    return Intl.message(
      '永不',
      name: 'auto_lock_screen_never',
      desc: '',
      args: [],
    );
  }

  /// `15秒`
  String get auto_lock_screen_15s {
    return Intl.message(
      '15秒',
      name: 'auto_lock_screen_15s',
      desc: '',
      args: [],
    );
  }

  /// `30秒`
  String get auto_lock_screen_30s {
    return Intl.message(
      '30秒',
      name: 'auto_lock_screen_30s',
      desc: '',
      args: [],
    );
  }

  /// `1分钟`
  String get auto_lock_screen_1m {
    return Intl.message(
      '1分钟',
      name: 'auto_lock_screen_1m',
      desc: '',
      args: [],
    );
  }

  /// `2分钟`
  String get auto_lock_screen_2m {
    return Intl.message(
      '2分钟',
      name: 'auto_lock_screen_2m',
      desc: '',
      args: [],
    );
  }

  /// `5分钟`
  String get auto_lock_screen_5m {
    return Intl.message(
      '5分钟',
      name: 'auto_lock_screen_5m',
      desc: '',
      args: [],
    );
  }

  /// `10分钟`
  String get auto_lock_screen_10m {
    return Intl.message(
      '10分钟',
      name: 'auto_lock_screen_10m',
      desc: '',
      args: [],
    );
  }

  /// `30分钟`
  String get auto_lock_screen_30m {
    return Intl.message(
      '30分钟',
      name: 'auto_lock_screen_30m',
      desc: '',
      args: [],
    );
  }

  /// `未输入名称`
  String get input_activity_name_error {
    return Intl.message(
      '未输入名称',
      name: 'input_activity_name_error',
      desc: '',
      args: [],
    );
  }

  /// `提示`
  String get tip {
    return Intl.message(
      '提示',
      name: 'tip',
      desc: '',
      args: [],
    );
  }

  /// `请先连接局域网，再使用此功能。`
  String get internet_not_connected {
    return Intl.message(
      '请先连接局域网，再使用此功能。',
      name: 'internet_not_connected',
      desc: '',
      args: [],
    );
  }

  /// `当前局域网已开启，请按照以下步骤，共享数据。`
  String get local_network_share_direction {
    return Intl.message(
      '当前局域网已开启，请按照以下步骤，共享数据。',
      name: 'local_network_share_direction',
      desc: '',
      args: [],
    );
  }

  /// `数据下载完成之前请不要结束共享，避免造成数据丢失。`
  String get local_network_share_tip {
    return Intl.message(
      '数据下载完成之前请不要结束共享，避免造成数据丢失。',
      name: 'local_network_share_tip',
      desc: '',
      args: [],
    );
  }

  /// `结束共享`
  String get finish_share {
    return Intl.message(
      '结束共享',
      name: 'finish_share',
      desc: '',
      args: [],
    );
  }

  /// `请将您的接收设备连接至同一网络`
  String get local_network_tip1 {
    return Intl.message(
      '请将您的接收设备连接至同一网络',
      name: 'local_network_tip1',
      desc: '',
      args: [],
    );
  }

  /// `在任意浏览器地址栏中输入：“%s”`
  String get local_network_tip2 {
    return Intl.message(
      '在任意浏览器地址栏中输入：“%s”',
      name: 'local_network_tip2',
      desc: '',
      args: [],
    );
  }

  /// `如果您的接收设备是windows10以下系统，请在任意浏览器地址栏中输入：“%s”`
  String get local_network_tip2_1 {
    return Intl.message(
      '如果您的接收设备是windows10以下系统，请在任意浏览器地址栏中输入：“%s”',
      name: 'local_network_tip2_1',
      desc: '',
      args: [],
    );
  }

  /// `点击下载后，病例文件会下载到接收设备中`
  String get local_network_tip3 {
    return Intl.message(
      '点击下载后，病例文件会下载到接收设备中',
      name: 'local_network_tip3',
      desc: '',
      args: [],
    );
  }

  /// `请确认您的网页端浏览器中所有数据已下载完成，再结束共享。`
  String get local_network_finish_share_confirm {
    return Intl.message(
      '请确认您的网页端浏览器中所有数据已下载完成，再结束共享。',
      name: 'local_network_finish_share_confirm',
      desc: '',
      args: [],
    );
  }

  /// `共享已结束`
  String get local_network_share_finished {
    return Intl.message(
      '共享已结束',
      name: 'local_network_share_finished',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'zh'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
