import 'package:flutter/widgets.dart';

class IconFont {
  static const String _family = 'iconfont';

  IconFont._();

  static const IconData icon_setting = IconData(0xe928, fontFamily: _family);
  static const IconData icon_Vector = IconData(0xe61a, fontFamily: _family);
  static const IconData icon_C4_5 = IconData(0xe927, fontFamily: _family);
  static const IconData icon_C3_4 = IconData(0xe926, fontFamily: _family);
  static const IconData icon_C4_4 = IconData(0xe925, fontFamily: _family);
  static const IconData icon_C3_5 = IconData(0xe924, fontFamily: _family);
  static const IconData icon_C3_1 = IconData(0xe91c, fontFamily: _family);
  static const IconData icon_C2_1 = IconData(0xe91d, fontFamily: _family);
  static const IconData icon_C3_2 = IconData(0xe91e, fontFamily: _family);
  static const IconData icon_C3_3 = IconData(0xe91f, fontFamily: _family);
  static const IconData icon_C4_1 = IconData(0xe920, fontFamily: _family);
  static const IconData icon_C2_2 = IconData(0xe921, fontFamily: _family);
  static const IconData icon_C4_2 = IconData(0xe922, fontFamily: _family);
  static const IconData icon_C4_3 = IconData(0xe923, fontFamily: _family);
  static const IconData icon_huidaodingbu = IconData(0xe917, fontFamily: _family);
  static const IconData icon_star = IconData(0xe618, fontFamily: _family);
  static const IconData icon_chexiao1 = IconData(0xe914, fontFamily: _family);
  static const IconData icon_huifu = IconData(0xe915, fontFamily: _family);
  static const IconData icon_zhongzhi1 = IconData(0xe916, fontFamily: _family);
  static const IconData icon_suoxiao2 = IconData(0xe913, fontFamily: _family);
  static const IconData icon_quanjufangda = IconData(0xe912, fontFamily: _family);
  static const IconData icon_zhaoxiangji1 = IconData(0xe911, fontFamily: _family);
  static const IconData icon_shoudong = IconData(0xe910, fontFamily: _family);
  static const IconData icon_fangda1 = IconData(0xe90a, fontFamily: _family);
  static const IconData icon_huidaoyuanwei = IconData(0xe90b, fontFamily: _family);
  static const IconData icon_xiazai2 = IconData(0xe90c, fontFamily: _family);
  static const IconData icon_suoxiao1 = IconData(0xe90d, fontFamily: _family);
  static const IconData icon_xianshiwenti = IconData(0xe90e, fontFamily: _family);
  static const IconData icon_yincangwenti = IconData(0xe90f, fontFamily: _family);
  static const IconData icon_touyingzhongdie = IconData(0xe617, fontFamily: _family);
  static const IconData icon_biaozhunzhi = IconData(0xe909, fontFamily: _family);
  static const IconData icon_weixiaomoni = IconData(0xe616, fontFamily: _family);
  static const IconData icon_xinxitishi = IconData(0xe908, fontFamily: _family);
  static const IconData icon_huidaoyuandian = IconData(0xe905, fontFamily: _family);
  static const IconData icon_suoxiao = IconData(0xe906, fontFamily: _family);
  static const IconData icon_fangda = IconData(0xe907, fontFamily: _family);
  static const IconData icon_xiaoxi = IconData(0xe904, fontFamily: _family);
  static const IconData icon_chongxinfenxi = IconData(0xe903, fontFamily: _family);
  static const IconData icon_xiazai1 = IconData(0xe66a, fontFamily: _family);
  static const IconData icon_quanjing = IconData(0xe6c9, fontFamily: _family);
  static const IconData icon_kouneizhao_01 = IconData(0xe902, fontFamily: _family);
  static const IconData icon_kouneizhao_moli = IconData(0xe901, fontFamily: _family);
  static const IconData icon_quanjingpian1 = IconData(0xe900, fontFamily: _family);
  static const IconData icon_a_Group1306 = IconData(0xe8fe, fontFamily: _family);
  static const IconData icon_sanjiao_shang = IconData(0xe615, fontFamily: _family);
  static const IconData icon_sanjiao_xia = IconData(0xe612, fontFamily: _family);
  static const IconData icon_xiangshang = IconData(0xe611, fontFamily: _family);
  static const IconData icon_xiangxia = IconData(0xe610, fontFamily: _family);
  static const IconData icon_Check = IconData(0xe6f8, fontFamily: _family);
  static const IconData icon_wujiaoxing_fill = IconData(0xe60f, fontFamily: _family);
  static const IconData icon_wujiaoxing = IconData(0xe60d, fontFamily: _family);
  static const IconData icon_jingshi1 = IconData(0xe8fd, fontFamily: _family);
  static const IconData icon_yousanjiao_tianchong = IconData(0xe600, fontFamily: _family);
  static const IconData icon_chunbufen = IconData(0xe8fc, fontFamily: _family);
  static const IconData icon_bibu = IconData(0xe8fb, fontFamily: _family);
  static const IconData icon_mianxing = IconData(0xe8fa, fontFamily: _family);
  static const IconData icon_cemaofenxi1 = IconData(0xe8f9, fontFamily: _family);
  static const IconData icon_chunhoudu = IconData(0xe8f8, fontFamily: _family);
  static const IconData icon_huangjinfenge = IconData(0xe8f7, fontFamily: _family);
  static const IconData icon_a_duicheng1 = IconData(0xe8f6, fontFamily: _family);
  static const IconData icon_santingwuyan = IconData(0xe8f5, fontFamily: _family);
  static const IconData icon_wenjian = IconData(0xe602, fontFamily: _family);
  static const IconData icon_ruya_61 = IconData(0xe8e4, fontFamily: _family);
  static const IconData icon_ruya_54 = IconData(0xe8e5, fontFamily: _family);
  static const IconData icon_ruya_55 = IconData(0xe8e6, fontFamily: _family);
  static const IconData icon_ruya_62 = IconData(0xe8e7, fontFamily: _family);
  static const IconData icon_ruya_64 = IconData(0xe8e8, fontFamily: _family);
  static const IconData icon_ruya_65 = IconData(0xe8e9, fontFamily: _family);
  static const IconData icon_ruya_73 = IconData(0xe8ea, fontFamily: _family);
  static const IconData icon_ruya_72 = IconData(0xe8eb, fontFamily: _family);
  static const IconData icon_ruya_63 = IconData(0xe8ec, fontFamily: _family);
  static const IconData icon_ruya_74 = IconData(0xe8ed, fontFamily: _family);
  static const IconData icon_ruya_71 = IconData(0xe8ee, fontFamily: _family);
  static const IconData icon_ruya_83 = IconData(0xe8ef, fontFamily: _family);
  static const IconData icon_ruya_75 = IconData(0xe8f0, fontFamily: _family);
  static const IconData icon_ruya_82 = IconData(0xe8f1, fontFamily: _family);
  static const IconData icon_ruya_85 = IconData(0xe8f2, fontFamily: _family);
  static const IconData icon_ruya_84 = IconData(0xe8f3, fontFamily: _family);
  static const IconData icon_ruya_81 = IconData(0xe8f4, fontFamily: _family);
  static const IconData icon_ruya_53 = IconData(0xe8e1, fontFamily: _family);
  static const IconData icon_ruya_52 = IconData(0xe8e2, fontFamily: _family);
  static const IconData icon_ruya_51 = IconData(0xe8e3, fontFamily: _family);
  static const IconData icon_d_occlusion = IconData(0xe60e, fontFamily: _family);
  static const IconData icon_hengya_15 = IconData(0xe8e0, fontFamily: _family);
  static const IconData icon_hengya_33 = IconData(0xe8d8, fontFamily: _family);
  static const IconData icon_hengya_34 = IconData(0xe8d9, fontFamily: _family);
  static const IconData icon_hengya_36 = IconData(0xe8da, fontFamily: _family);
  static const IconData icon_hengya_35 = IconData(0xe8db, fontFamily: _family);
  static const IconData icon_hengya_38 = IconData(0xe8dc, fontFamily: _family);
  static const IconData icon_hengya_37 = IconData(0xe8dd, fontFamily: _family);
  static const IconData icon_hengya_48 = IconData(0xe8de, fontFamily: _family);
  static const IconData icon_hengya_47 = IconData(0xe8df, fontFamily: _family);
  static const IconData icon_hengya_12 = IconData(0xe8c1, fontFamily: _family);
  static const IconData icon_hengya_11 = IconData(0xe8c2, fontFamily: _family);
  static const IconData icon_hengya_14 = IconData(0xe8c3, fontFamily: _family);
  static const IconData icon_hengya_16 = IconData(0xe8c4, fontFamily: _family);
  static const IconData icon_hengya_13 = IconData(0xe8c5, fontFamily: _family);
  static const IconData icon_hengya_21 = IconData(0xe8c6, fontFamily: _family);
  static const IconData icon_hengya_22 = IconData(0xe8c7, fontFamily: _family);
  static const IconData icon_hengya_23 = IconData(0xe8c8, fontFamily: _family);
  static const IconData icon_hengya_18 = IconData(0xe8c9, fontFamily: _family);
  static const IconData icon_hengya_24 = IconData(0xe8ca, fontFamily: _family);
  static const IconData icon_hengya_41 = IconData(0xe8cb, fontFamily: _family);
  static const IconData icon_hengya_28 = IconData(0xe8cc, fontFamily: _family);
  static const IconData icon_hengya_45 = IconData(0xe8cd, fontFamily: _family);
  static const IconData icon_hengya_17 = IconData(0xe8ce, fontFamily: _family);
  static const IconData icon_hengya_32 = IconData(0xe8cf, fontFamily: _family);
  static const IconData icon_hengya_42 = IconData(0xe8d0, fontFamily: _family);
  static const IconData icon_hengya_31 = IconData(0xe8d1, fontFamily: _family);
  static const IconData icon_hengya_25 = IconData(0xe8d2, fontFamily: _family);
  static const IconData icon_hengya_27 = IconData(0xe8d3, fontFamily: _family);
  static const IconData icon_hengya_46 = IconData(0xe8d4, fontFamily: _family);
  static const IconData icon_hengya_26 = IconData(0xe8d5, fontFamily: _family);
  static const IconData icon_hengya_43 = IconData(0xe8d6, fontFamily: _family);
  static const IconData icon_hengya_44 = IconData(0xe8d7, fontFamily: _family);
  static const IconData icon_d_left = IconData(0xe607, fontFamily: _family);
  static const IconData icon_d_yangshi = IconData(0xe608, fontFamily: _family);
  static const IconData icon_d_shezhi = IconData(0xe609, fontFamily: _family);
  static const IconData icon_d_xiahe = IconData(0xe60a, fontFamily: _family);
  static const IconData icon_d_right = IconData(0xe60c, fontFamily: _family);
  static const IconData icon_d_zhengmian = IconData(0xe606, fontFamily: _family);
  static const IconData icon_d_shanghe = IconData(0xe605, fontFamily: _family);
  static const IconData icon_d_houshi = IconData(0xe601, fontFamily: _family);
  static const IconData icon_xiangyou = IconData(0xe778, fontFamily: _family);
  static const IconData icon_xiangzuo = IconData(0xedcb, fontFamily: _family);
  static const IconData icon_CS6_C4 = IconData(0xe8bd, fontFamily: _family);
  static const IconData icon_CS6_C3 = IconData(0xe8bc, fontFamily: _family);
  static const IconData icon_CS5_C4 = IconData(0xe8bb, fontFamily: _family);
  static const IconData icon_CS5_C3 = IconData(0xe8b9, fontFamily: _family);
  static const IconData icon_CS4_C4 = IconData(0xe8b8, fontFamily: _family);
  static const IconData icon_CS6_C2 = IconData(0xe8b7, fontFamily: _family);
  static const IconData icon_CS5_C2 = IconData(0xe8b6, fontFamily: _family);
  static const IconData icon_CS4_C3 = IconData(0xe8b5, fontFamily: _family);
  static const IconData icon_CS4_C2 = IconData(0xe8b4, fontFamily: _family);
  static const IconData icon_CS3_C4 = IconData(0xe8b3, fontFamily: _family);
  static const IconData icon_CS3_C3 = IconData(0xe8b2, fontFamily: _family);
  static const IconData icon_CS3_C2 = IconData(0xe8b1, fontFamily: _family);
  static const IconData icon_CS2_C4 = IconData(0xe8b0, fontFamily: _family);
  static const IconData icon_CS2_C3 = IconData(0xe8af, fontFamily: _family);
  static const IconData icon_CS2_C2 = IconData(0xe8ae, fontFamily: _family);
  static const IconData icon_CS1_C4 = IconData(0xe8ad, fontFamily: _family);
  static const IconData icon_CS1_C3 = IconData(0xe8ac, fontFamily: _family);
  static const IconData icon_CS1_C2 = IconData(0xe8ab, fontFamily: _family);
  static const IconData icon_fangan = IconData(0xe85b, fontFamily: _family);
  static const IconData icon_xiazai = IconData(0xe674, fontFamily: _family);
  static const IconData icon_fenxiang = IconData(0xe630, fontFamily: _family);
  static const IconData icon_ceweipian = IconData(0xe826, fontFamily: _family);
  static const IconData icon_quanjingpian = IconData(0xe825, fontFamily: _family);
  static const IconData icon_zhuye = IconData(0xe824, fontFamily: _family);
  static const IconData icon_cemaofenxi = IconData(0xe823, fontFamily: _family);
  static const IconData icon_zhengmaofenxi = IconData(0xe822, fontFamily: _family);
  static const IconData icon_bingrenxiangqingye = IconData(0xe821, fontFamily: _family);
  static const IconData icon_a_3DStudio = IconData(0xe820, fontFamily: _family);
  static const IconData icon_CBCT = IconData(0xe81f, fontFamily: _family);
  static const IconData icon_yanjing_guan = IconData(0xe6fb, fontFamily: _family);
  static const IconData icon_yanjing_kai = IconData(0xe6f9, fontFamily: _family);
  static const IconData icon_jiahao = IconData(0xe61f, fontFamily: _family);
  static const IconData icon_dagou = IconData(0xe603, fontFamily: _family);
  static const IconData icon_yuanxingdacha = IconData(0xe712, fontFamily: _family);
  static const IconData icon_wenhao1 = IconData(0xe60b, fontFamily: _family);
  static const IconData icon_jianhao = IconData(0xeaf5, fontFamily: _family);
  static const IconData icon_dingwei = IconData(0xe619, fontFamily: _family);
  static const IconData icon_shezhi1 = IconData(0xe629, fontFamily: _family);
  static const IconData icon_zu = IconData(0xe613, fontFamily: _family);
  static const IconData icon_icon_measureTool = IconData(0xe64b, fontFamily: _family);
  static const IconData icon_ceweipian_01 = IconData(0xe604, fontFamily: _family);
  static const IconData icon_jiajiaojisuangongju = IconData(0xe68e, fontFamily: _family);
  static const IconData icon_zhixianjuli = IconData(0xedc9, fontFamily: _family);
  static const IconData icon_ico_qingchu_xuanzhong = IconData(0xe614, fontFamily: _family);
  static const IconData icon_sandianceliang = IconData(0xe621, fontFamily: _family);
  static const IconData icon_ziyuan = IconData(0xe622, fontFamily: _family);
  static const IconData icon_jiantou_xiangxia = IconData(0xeb0a, fontFamily: _family);
  static const IconData icon_jiantou_xiangshang = IconData(0xeb0b, fontFamily: _family);
  static const IconData icon_a_cloudyatnight = IconData(0xe754, fontFamily: _family);
  static const IconData icon_jiaoduceliang3dian = IconData(0xe81e, fontFamily: _family);
  static const IconData icon_jiaoduceliang = IconData(0xe81d, fontFamily: _family);
  static const IconData icon_jingshi = IconData(0xe81c, fontFamily: _family);
  static const IconData icon_jiazaizhong = IconData(0xe81a, fontFamily: _family);
  static const IconData icon_MPRmoshi = IconData(0xe819, fontFamily: _family);
  static const IconData icon_fuhemoshi = IconData(0xe818, fontFamily: _family);
  static const IconData icon_hemianmoshi = IconData(0xe817, fontFamily: _family);
  static const IconData icon_biaozhunmoshi = IconData(0xe814, fontFamily: _family);
  static const IconData icon_yaguanmoshi = IconData(0xe815, fontFamily: _family);
  static const IconData icon_genguangumoshi = IconData(0xe816, fontFamily: _family);
  static const IconData icon_xianshishezhi = IconData(0xe813, fontFamily: _family);
  static const IconData icon_ToTop = IconData(0xe80d, fontFamily: _family);
  static const IconData icon_a_tianjiada = IconData(0xe80e, fontFamily: _family);
  static const IconData icon_wendang = IconData(0xe80b, fontFamily: _family);
  static const IconData icon_tianjia = IconData(0xe805, fontFamily: _family);
  static const IconData icon_xiaolian = IconData(0xe806, fontFamily: _family);
  static const IconData icon_shibai = IconData(0xe807, fontFamily: _family);
  static const IconData icon_chenggong = IconData(0xe808, fontFamily: _family);
  static const IconData icon_fangzi = IconData(0xe809, fontFamily: _family);
  static const IconData icon_wenhao = IconData(0xe80a, fontFamily: _family);
  static const IconData icon_qingkongceliang = IconData(0xe804, fontFamily: _family);
  static const IconData icon_Boltonfenxi = IconData(0xe802, fontFamily: _family);
  static const IconData icon_chihaobiaoji = IconData(0xe801, fontFamily: _family);
  static const IconData icon_yaohejiechu = IconData(0xe800, fontFamily: _family);
  static const IconData icon_lishijilu = IconData(0xe7ff, fontFamily: _family);
  static const IconData icon_zhongzhi = IconData(0xe7fe, fontFamily: _family);
  static const IconData icon_chexiao = IconData(0xe7fd, fontFamily: _family);
  static const IconData icon_baocun = IconData(0xe7fc, fontFamily: _family);
  static const IconData icon_chakanbaogao = IconData(0xe7fb, fontFamily: _family);
  static const IconData icon_celiang = IconData(0xe7f9, fontFamily: _family);
  static const IconData icon_VTO = IconData(0xe7f8, fontFamily: _family);
  static const IconData icon_ceweifenxi1 = IconData(0xe7f5, fontFamily: _family);
  static const IconData icon_gulingfenxi1 = IconData(0xe7f4, fontFamily: _family);
  static const IconData icon_qidaofenxi = IconData(0xe7f3, fontFamily: _family);
  static const IconData icon_gulingfenxi = IconData(0xe7f2, fontFamily: _family);
  static const IconData icon_ceweifenxi = IconData(0xe7f1, fontFamily: _family);
  static const IconData icon_wenjianjia = IconData(0xe7f0, fontFamily: _family);
  static const IconData icon_guanbi = IconData(0xe7ef, fontFamily: _family);
  static const IconData icon_tuichu = IconData(0xe7ee, fontFamily: _family);
  static const IconData icon_shezhi = IconData(0xe7ed, fontFamily: _family);
  static const IconData icon_chahao = IconData(0xe7ec, fontFamily: _family);
  static const IconData icon_duihao = IconData(0xe7eb, fontFamily: _family);
  static const IconData icon_zhaoxiangji = IconData(0xe7ea, fontFamily: _family);
  static const IconData icon_lajitong = IconData(0xe7e9, fontFamily: _family);
  static const IconData icon_beizhu = IconData(0xe7e7, fontFamily: _family);
  static const IconData icon_bingli = IconData(0xe7e6, fontFamily: _family);
  static const IconData icon_zhaopian = IconData(0xe7e5, fontFamily: _family);
  static const IconData icon_shoucang = IconData(0xe7e4, fontFamily: _family);
  static const IconData icon_tupianzhanshi = IconData(0xe7e3, fontFamily: _family);
  static const IconData icon_liebiaozhanshi = IconData(0xe7e2, fontFamily: _family);
  static const IconData icon_gengduo = IconData(0xe7e1, fontFamily: _family);
  static const IconData icon_bofang = IconData(0xe7e0, fontFamily: _family);
  static const IconData icon_nansheng = IconData(0xe7df, fontFamily: _family);
  static const IconData icon_bianji = IconData(0xe7de, fontFamily: _family);
  static const IconData icon_yanjing = IconData(0xe7dd, fontFamily: _family);
  static const IconData icon_a_yanjingbi = IconData(0xe7dc, fontFamily: _family);
  static const IconData icon_shenfenzheng = IconData(0xe7da, fontFamily: _family);
  static const IconData icon_dianhua = IconData(0xe7d9, fontFamily: _family);
}
