import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:install_plugin/install_plugin.dart';
import 'package:intl/intl.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/pages/main_tabs.dart';
import 'package:mooeli/pages/scan/BrowerPhoto.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/video_player/HModalVideoPlayer.dart';
import 'package:mooeli/pages/web/webview_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli_ffi/bindings_ffi.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sprintf/sprintf.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import 'package:url_launcher/url_launcher.dart';

const bool disableMultiLang = true;
const bool disableAboardLogin = true;

String currentTempDir = "";

class Global {
  static bool offlineMode = true;
  static late dynamic deviceInfo = {};
  static String geTuiCid = "";
  static Map _scanDataTempCache = {}; //临时缓存
  static late SharedPreferences sharedPrefs;
  static final mainTabGlobalKey = GlobalKey<MainTabState>();
  static bool isHaveNewestAppVerAlert = false;
  static String appVersion = "0.0.0";
  static final globalNavigator = GlobalKey<NavigatorState>();

  //检测牙齿模型逻辑:
  // false = 佳晨的版本
  // true = 成龙的新版模型
  static const bool isGlobalUseNewModal = true; //模型版本

  static Map localStorageEnum = {
    "ANDROID_AGREEN_MODAL": "ANDROID_AGREEN_MODAL",
    "FIRST_OPEN_TOOTH_CAMERA": "FIRST_OPEN_TOOTH_CAMERA",
    "OPEN_TOOTH_CAMERA_LEFT": "OPEN_TOOTH_CAMERA_LEFT",
    "USER_DATAS": "USER_DATAS",
    "FACE_ID_LOGIN": "FACE_ID_LOGIN",
    "FINGRE_PRINT_LOGIN": "FINGRE_PRINT_LOGIN",
    "FIRST_BIND_INVITE_CODE": "FIRST_BIND_INVITE_CODE",
    "USER_SCAN_TEXT_IS_MIRROR": "USER_SCAN_TEXT_IS_MIRROR",
  };

  //用于初始化更早的数据与逻辑
  static Future<Map> beforeMainInit() async {
    WidgetsFlutterBinding.ensureInitialized();
    sharedPrefs = await SharedPreferences.getInstance();
    initAreaConfig(true);
    initTempDir();
    await HHttp.initHttp();
    bool needIntoLogin = await User.instance.init();

    return {
      "needIntoLogin": needIntoLogin,
      "globalNavigator": globalNavigator,
    };
  }

  static initTempDir() async {
    Directory tempDir = await getTemporaryDirectory();
    currentTempDir = tempDir.path;
  }

  static dynamic globalLoadingCancelFun;

  static showGlobalLoading({int seconds = 10}) {
    globalLoadingCancelFun = BotToast.showLoading(duration: Duration(seconds: seconds));
  }

  static hideGlobalLoading() {
    if (globalLoadingCancelFun != null) {
      globalLoadingCancelFun();
    }
  }

  static startInitGeTuiSDK() async {
    initGlobalAsync();
    UmengCommonSdk.initCommon("66271db5cac2a664de23005e", "66271e15cac2a664de230169", "lyoral");
    UmengCommonSdk.setPageCollectionModeAuto();
  }

  static initGlobalAsync() async {
    sharedPrefs = await SharedPreferences.getInstance();
    deviceInfo = await callMcByFunName("getDeviceInfo");
  }

  //存储临时文件地址和路径，退出程序即清掉
  static getTempScanCacheData(String dataTempKey) {
    if (_scanDataTempCache.containsKey(dataTempKey)) {
      return _scanDataTempCache[dataTempKey];
    }
    return null;
  }

  static setTempScanCacheDataAll(Map scanDataTempCache) {
    _scanDataTempCache = scanDataTempCache;
  }

  static setTempScanCacheData(String dataTempKey, dynamic dataTempValue) {
    _scanDataTempCache[dataTempKey] = dataTempValue;
  }

  static clearTempScanCacheData() {
    _scanDataTempCache = {};
  }

  //获取一个带毛玻璃效果的Widget
  static Widget getBlurWidget(Widget child, {double radius = 0}) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(radius),
          child: Container(
            decoration: BoxDecoration(color: Colors.black.withOpacity(0.4)),
            // decoration: BoxDecoration(color: Colors.transparent),
            child: child,
          ),
        ),
        // child,
      ],
    );
  }

  //获取路由参数
  static dynamic getRouterParams(BuildContext context) {
    return ModalRoute.of(context)?.settings.arguments;
  }

  static String getFileNameByPath(String filePath) {
    return filePath.substring(filePath.lastIndexOf("/") + 1, filePath.length);
  }

  //检查粘贴板邀请码
  static Future<String?> checkClipboardInviteCode() async {
    var clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null) {
      if (clipboardData.text!.trim().length == 10) {
        String inviteCode = clipboardData.text!.trim().toUpperCase();
        return inviteCode;
      }
    }
    return null;
  }

  //根据邀请码绘制
  static Widget getInviteWidgets(inviteCode, {int eachWidth = 30}) {
    List<Widget> inviteList = [];
    for (int i = 0; i < 10; i++) {
      inviteList.add(Container(
        width: eachWidth.sp,
        height: 48.sp,
        decoration: BoxDecoration(
          border: Border.all(color: color2B),
          borderRadius: BorderRadius.circular(8.sp),
        ),
        child: Center(
            child: Text(inviteCode.length > i ? inviteCode[i] : "",
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 20.sp, color: color2B))),
      ));
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: inviteList,
    );
  }

  //默认|| type=0 = 当前时间的 年-月-日
  //type=1 = 当前时间的 年.月.日
  //type=2 = 当前时间的 年月日

  static String getDateByTimestamp({int milliseconds = 0, int type = 0}) {
    DateTime date = milliseconds == 0 ? DateTime.now() : DateTime.fromMicrosecondsSinceEpoch(milliseconds * 1000);
    String result = "";
    if (type == 0) {
      result =
          "${date.year.toString()}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
    } else if (type == 1) {
      result =
          "${date.year.toString()}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}";
    } else if (type == 2) {
      result = "${date.year.toString()}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";
    }
    return result;
  }

  static String getShowTime({int milliseconds = 0, bool ignoreSameDay = true}) {
    DateTime date = milliseconds == 0 ? DateTime.now() : DateTime.fromMicrosecondsSinceEpoch(milliseconds * 1000);
    if (ignoreSameDay) {
      DateTime now = DateTime.now();
      if (date.year == now.year && date.month == now.month && date.day == now.day) {
        return DateFormat('HH:mm').format(date);
      }
    }
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  static String getLoggerTime({int milliseconds = 0, bool ignoreSameDay = true}) {
    DateTime date = milliseconds == 0 ? DateTime.now() : DateTime.fromMicrosecondsSinceEpoch(milliseconds * 1000);
    if (ignoreSameDay) {
      DateTime now = DateTime.now();
      if (date.year == now.year && date.month == now.month && date.day == now.day) {
        return DateFormat('HH:mm:ss').format(date);
      }
    }
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(date);
  }

  static int getAge(int ms) {
    DateTime now = DateTime.now();
    DateTime birthday = DateTime.fromMillisecondsSinceEpoch(ms);
    return (now.year * 10000 +
            now.month * 100 +
            now.day -
            birthday.year * 10000 -
            birthday.month * 100 -
            birthday.day) ~/
        10000;
  }

  static String getFormatDate(String date) {
    return sprintf(Lang.simple_date, [
      date.substring(0, 4),
      int.parse(date.substring(4, 6)),
      int.parse(date.substring(6, 8)),
    ]);
  }

  /// 重命名文件
  static Future<String> renameImgFile(String oldPathAndName, String newNameDontIncludeFileType) async {
    int lastFlagIndex = oldPathAndName.lastIndexOf("/");
    String oldPath = oldPathAndName.substring(0, lastFlagIndex);
    String oldName = oldPathAndName.substring(lastFlagIndex + 1, oldPathAndName.length);
    // String oldType = oldName.split('.')[1];
    File file = File(oldPathAndName);
    var newFile = await file.copy("$oldPath/$newNameDontIncludeFileType-$oldName");
    return newFile.path;
  }

  /// 解压zip
  static Future<List> unZip(String zipPathAndName, {Directory? dir}) async {
    List filePaths = [];
    Directory parentDir = dir ?? await getTemporaryDirectory();
    List<int> bytes = File(zipPathAndName).readAsBytesSync();
    Archive archive = ZipDecoder().decodeBytes(bytes);
    for (ArchiveFile file in archive) {
      if (file.isFile) {
        List<int> tempData = file.content;
        String fileName = file.name;
        //老的app上传的多个压缩包内图片有存在重复名字的处理
        // if (!fileName.contains("dver") && !fileName.contains("fver")) {
        //   fileName = "${file.name.split('.')[0]}-${DateTime.now().millisecondsSinceEpoch}.${file.name.split('.')[1]}";
        // }
        File f = File("${parentDir.path}/$fileName")
          ..createSync(recursive: true)
          ..writeAsBytesSync(tempData);
        filePaths.add(f.path);
      }
    }
    return filePaths;
  }

  static showBottomModal(BuildContext context, Widget widget,
      {double maxHeight = double.infinity, bool isTransparent = false, dynamic onDismiss}) {
    showModalBottomSheet(
        backgroundColor: isTransparent ? const Color(0x00000000) : Colors.white,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(16.sp), topRight: Radius.circular(16.sp))),
        constraints: BoxConstraints(
          minHeight: 100,
          maxHeight: maxHeight,
          minWidth: double.infinity,
          maxWidth: double.infinity,
        ),
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return widget;
        }).whenComplete(() {
      if (onDismiss != null) {
        onDismiss();
      }
    });
  }

  static toast(String message, {int showSeconds = 2, double marginBottom = 0}) {
    if (kDebugMode) {
      print("Toast Context:$message");
    }
    if (marginBottom == 0) {
      marginBottom = 0.4.sh;
    }
    BotToast.showCustomText(
        duration: Duration(seconds: showSeconds),
        toastBuilder: (_) => Container(
            constraints: BoxConstraints(maxWidth: 0.6.sw),
            padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 16.sp),
            margin: EdgeInsets.only(bottom: marginBottom),
            decoration: BoxDecoration(
              color: const Color(0x9A000000),
              borderRadius: BorderRadius.circular(16.sp),
            ),
            child: Text(
              message,
              style: TextStyle(
                fontSize: 24.sp,
                // fontWeight: FontWeight.w500,
                color: const Color(0xffffffff),
              ),
              textAlign: TextAlign.center,
            )));
  }

  static openUserAgreement(context, {Function? openedCallBack}) {
    showCustomDialog(
      Center(
        child: Container(
          width: 656.sp,
          height: 725.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 656.sp,
                height: 104.sp,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(width: 88.sp, height: 88.sp),
                    MyText(Lang.user_agreement, color2B, 28.sp, FontWeight.w500),
                    Click(
                      onTap: () {
                        BotToast.cleanAll();
                      },
                      child: Padding(
                        padding: EdgeInsets.all(32.sp),
                        child: Image.asset(
                          "res/imgs/icon_close.png",
                          width: 24.sp,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 620.sp,
                padding: EdgeInsets.all(32.sp),
                child: HWebView(
                  title: "",
                  url: "${HHttp.getOfficialHost()}lechipai_scan/user_agreement.html",
                  hideAppbar: true,
                  hideClose: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static openPrivacy(context, {Function? openedCallBack}) {
    showCustomDialog(
      Center(
        child: Container(
          width: 656.sp,
          height: 725.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 656.sp,
                height: 104.sp,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(width: 88.sp, height: 88.sp),
                    MyText(Lang.privacy, color2B, 28.sp, FontWeight.w500),
                    Click(
                      onTap: () {
                        BotToast.cleanAll();
                      },
                      child: Padding(
                        padding: EdgeInsets.all(32.sp),
                        child: Image.asset(
                          "res/imgs/icon_close.png",
                          width: 24.sp,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 620.sp,
                padding: EdgeInsets.all(32.sp),
                child: HWebView(
                  title: "",
                  url: "${HHttp.getOfficialHost()}lechipai_scan/privacy_agreement.html",
                  hideAppbar: true,
                  hideClose: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static openUrlByWebView(
    context,
    title,
    url, {
    Function? openedCallBack,
    bool readTitle = false,
    bool horizontal = false,
    bool hideAppbar = false,
    bool disableOriention = false, //不干涉屏幕方向
    bool hideClose = false,
  }) {
    logger(url, key: "openUrl");
    url = url.toString().replaceFirst("{host}/", HHttp.getH5Host()).replaceFirst("{host}", HHttp.getH5Host());
    Global.globalNavigator.currentState
        ?.push(MaterialPageRoute(
            builder: (context) => HWebView(
                  title: title,
                  url: url,
                  readTitle: readTitle,
                  horizontal: horizontal,
                  hideAppbar: hideAppbar,
                  disableOriention: disableOriention,
                  hideClose: hideClose,
                )))
        .then((_) {
      if (openedCallBack != null) {
        openedCallBack();
      }
    });
  }

  static CancelFunc showAlertDialog(String title, String content,
      {String okText = "",
      String subContent = "",
      String cancelText = "",
      bool clickOkDontHide = false,
      bool isHideCancelBtn = false,
      bool isBringXBtn = false,
      bool contentCenter = false,
      Color? okColor,
      double? cancelWidth,
      double? okWidth,
      TextStyle? contentStyle,
      TextStyle? subContentStyle,
      BackButtonBehavior backButtonBehavior = BackButtonBehavior.none,
      VoidCallback? cancelCallback,
      VoidCallback? okCallBack,
      VoidCallback? clickBgCallback}) {
    okText = (okText != "" ? okText : Lang.confirm_ok);
    cancelText = (cancelText != "" ? cancelText : Lang.cancel);
    return showCustomDialog(
      Center(
        child: Container(
          width: 640.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 56.sp,
                decoration: BoxDecoration(border: Border(bottom: BorderSide(color: colorE1))),
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 24.sp,
                    right: 24.sp,
                  ),
                  child: Center(
                    child: Row(
                      children: [
                        SizedBox(
                          width: 480.sp,
                          child: MyText(
                            title,
                            color2B,
                            24.sp,
                            FontWeight.w600,
                          ),
                        ),
                        Expanded(child: SizedBox()),
                        Container(
                          child: Click(
                            onTap: () {
                              BotToast.cleanAll();
                            },
                            child: Image.asset(
                              "res/imgs/icon_close.png",
                              width: 32.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              isEmpty(content)
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.all(
                        24.sp,
                      ),
                      child: MyText(content, color2B, 24.sp),
                    ),
              Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 16.sp),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    GestureDetector(
                      onTap: () {
                        BotToast.cleanAll();
                        if (cancelCallback != null) {
                          cancelCallback();
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 16.sp),
                        decoration: BoxDecoration(
                          color: colorPurpleLavender,
                          borderRadius: BorderRadius.circular(8.sp),
                        ),
                        child: MyText(cancelText, colorBlueDeep, 24.sp),
                      ),
                    ),
                    SizedBox(width: 24.sp),
                    GestureDetector(
                      onTap: () {
                        BotToast.cleanAll();
                        if (okCallBack != null) {
                          okCallBack();
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 16.sp),
                        decoration: BoxDecoration(
                          color: okColor ?? colorRed,
                          borderRadius: BorderRadius.circular(8.sp),
                        ),
                        child: MyText(okText, Colors.white, 24.sp),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static CancelFunc showTipDialog(String title, String content,
      {String okText = "",
      bool okRed = false,
      BackButtonBehavior backButtonBehavior = BackButtonBehavior.none,
      VoidCallback? okCallBack,
      VoidCallback? clickBgCallback}) {
    okText = (okText != "" ? okText : Lang.i_knew);
    return showCustomDialog(
      Center(
        child: Container(
          width: 640.sp,
          padding: EdgeInsets.all(32.sp),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset("res/icons/icon_alert.png", width: 32.sp),
                      SizedBox(width: 16.sp),
                      SizedBox(
                        width: 480.sp,
                        child: MyText(title, color2B, 28.sp, FontWeight.w600),
                      ),
                    ],
                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: Click(
                      onTap: () {
                        BotToast.cleanAll();
                      },
                      child: Image.asset(
                        "res/imgs/icon_close.png",
                        width: 24.sp,
                      ),
                    ),
                  ),
                ],
              ),
              isEmpty(content)
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.fromLTRB(48.sp, 8.sp, 0.sp, 0),
                      child: MyText(content, color2B, 24.sp),
                    ),
              SizedBox(height: 32.sp),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      BotToast.cleanAll();
                      if (okCallBack != null) {
                        okCallBack();
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.fromLTRB(64.sp, 16.sp, 64.sp, 16.sp),
                      decoration: BoxDecoration(
                        color: okRed ? colorRed : colorBrand,
                        borderRadius: BorderRadius.circular(60.sp),
                      ),
                      child: MyText(okText, Colors.white, 24.sp),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  static CancelFunc showCustomDialog(
    Widget customWidget, {
    bool isBringXBtn = false,
    dynamic cancelCallback,
    dynamic clickBgCallback,
    BackButtonBehavior backButtonBehavior = BackButtonBehavior.none,
  }) {
    return BotToast.showAnimationWidget(
        enableKeyboardSafeArea: false,
        clickClose: false,
        allowClick: false,
        onlyOne: true,
        crossPage: true,
        backButtonBehavior: backButtonBehavior,
        wrapToastAnimation: (controller, cancel, child) => Stack(
              children: <Widget>[
                Click(
                  onTap: () {
                    if (clickBgCallback != null) {
                      clickBgCallback();
                    } else {
                      cancel();
                    }
                  },
                  ms: 5000,
                  child: AnimatedBuilder(
                    builder: (_, child) => Opacity(
                      opacity: controller.value,
                      child: child,
                    ),
                    animation: controller,
                    child: const DecoratedBox(
                      decoration: BoxDecoration(color: Color(0xaa000000)),
                      child: SizedBox.expand(),
                    ),
                  ),
                ),
                CustomOffsetAnimation(
                  controller: controller,
                  child: child,
                )
              ],
            ),
        toastBuilder: (cancelFunc) {
          return isBringXBtn
              ? Stack(
                  children: [
                    customWidget,
                    isBringXBtn
                        ? Positioned(
                            top: 16.sp,
                            right: 12.sp,
                            child: GestureDetector(
                              onTap: () {
                                cancelFunc();
                              },
                              child: Padding(
                                padding: EdgeInsets.all(4.sp),
                                child: Image.asset(
                                  "res/imgs/icon_close.png",
                                  width: 16.sp,
                                  color: colorA4,
                                ),
                              ),
                            ))
                        : const SizedBox(),
                  ],
                )
              : customWidget;
        },
        animationDuration: const Duration(milliseconds: 180));
  }

  static int checkVersionTime = 0;

  // userCheck : true -> 资源版本号isResVerIsNewest有更新，且用户这里手动点击检查才会有弹窗
  // onlyBigAlert : 只有大版本号的时候才弹窗
  static checkNewestVersion({bool userCheck = false, bool onlyBigAlert = false, int retry = 0}) {
    if (Global.appVersion == "0.0.0") {
      return;
    }

    showVersionDialog(String version, String url) {
      SmartDialog.dismiss();
      GlobalKey buttonKey = GlobalKey();
      bool isBigVerIsNewest = false; //大版本号
      bool isSmallVerIsNewest = false; //小版本号
      bool isResVerIsNewest = false; //资源版本号

      List currentVerNums = appVersion.split('.');
      String targetVersion = version.contains("\n") ? version.split('\n')[0] : version.trim();

      List targetCheckNums = targetVersion.split('.');
      if (currentVerNums.length <= 1) {
        return;
      }
      if (double.parse("${targetCheckNums[0]}.${targetCheckNums[1]}") >
          double.parse("${currentVerNums[0]}.${currentVerNums[1]}")) {
        isHaveNewestAppVerAlert = true;
        if (int.parse(targetCheckNums[0]) > int.parse(currentVerNums[0])) {
          isBigVerIsNewest = true;
        } else {
          isSmallVerIsNewest = true;
        }
      } else if ("${targetCheckNums[0]}.${targetCheckNums[1]}" == "${currentVerNums[0]}.${currentVerNums[1]}" &&
          targetCheckNums.length >= 3) {
        if (int.parse(targetCheckNums[2]) > int.parse(currentVerNums.length >= 3 ? currentVerNums[2] : "0")) {
          isHaveNewestAppVerAlert = true;
          isResVerIsNewest = true;
        }
      }

      if (isBigVerIsNewest || isSmallVerIsNewest || (isResVerIsNewest && userCheck)) {
        if (!onlyBigAlert || (onlyBigAlert && isBigVerIsNewest)) {
          SmartDialog.dismiss();
          SmartDialog.show(
            alignment: Alignment.center,
            animationType: SmartAnimationType.centerScale_otherSlide,
            clickMaskDismiss: !isBigVerIsNewest,
            builder: (context) {
              return Container(
                width: 654.sp,
                padding: EdgeInsets.all(32.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.sp),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    MyText(Lang.find_newer_version, color2B, 40.sp, FontWeight.w600),
                    SizedBox(height: 20.sp),
                    MyText("V$targetVersion", color2B, 24.sp, FontWeight.w500),
                    Container(
                      margin: EdgeInsets.fromLTRB(0, 32.sp, 0, 16.sp),
                      width: 119.sp,
                      height: 119.sp,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(80.sp),
                        color: color2B.withOpacity(0.1),
                      ),
                      alignment: Alignment.center,
                      child: Image.asset("res/icons/icon_version_update.png", width: 160.sp, height: 160.sp),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 48.sp),
                      child: HCustomButton(
                        key: buttonKey,
                        width: 400.sp,
                        height: 76.sp,
                        fontSize: 28.sp,
                        text: Lang.go_upgrade,
                        onPress: () {
                          sendEventPoint("about_Mooeli", "update", {
                            "content": "update",
                            "value1": appVersion,
                            "value2": targetVersion,
                          });
                          (buttonKey.currentState as HCustomButtonState).setText(Lang.downloading);
                          HHttp.downFileWithProgress(
                            url,
                            (path) async {
                              (buttonKey.currentState as HCustomButtonState).setText(Lang.download_complete);
                              logger("start install: $path");
                              await InstallPlugin.install(path);
                            },
                            (progress) {
                              (buttonKey.currentState as HCustomButtonState).setText("${Lang.downloading}($progress%)");
                            },
                            // forceDownload: true,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        }
        if (userCheck) {
          sendEventPoint("about_Mooeli", "version", {
            "content": "not_latest",
            "value1": appVersion,
            "value2": targetVersion,
          });
        }
      } else {
        if (userCheck) {
          toast(Lang.already_newest_version);
          sendEventPoint("about_Mooeli", "version", {
            "content": "latest",
            "value1": appVersion,
            "value2": targetVersion,
          });
        }
      }
    }

    // //测试
    // showVersionDialog("4.0.0", "https://chohotech-public.oss-cn-hangzhou.aliyuncs.com/lyoral_3.1.0.apk");
    // return;

    HHttp.request(
      "${HHttp.getOfficialHost()}probox/config_json.txt",
      "GET",
      isFullUrl: true,
      isShowNetWorkLoading: false,
      isShowErrorToast: false,
      cbFullResponse: true,
      (content) {
        checkVersionTime = DateTime.now().millisecondsSinceEpoch;
        Map json = jsonDecode(content.toString().replaceAll("\n", "").replaceAll("\t", ""));
        String versionStr = json["version"];
        String url = json["url"];
        showVersionDialog(versionStr, url);
      },
      errCallBack: () {
        if (retry > 0) {
          checkNewestVersion(userCheck: userCheck, onlyBigAlert: onlyBigAlert, retry: retry - 1);
        } else {
          if (userCheck) {
            toast(Lang.already_newest_version);
          }
        }
      },
    );
  }

  static launchURL(String url) async {
    Uri uri = Uri.parse(url);
    launchUrl(uri, mode: LaunchMode.externalApplication);
  }

//检查上下颌是否微张
  static dynamic checkToothIsOpen(dynamic resultObj) {
    if (resultObj == null || !resultObj.containsKey("lastObjects")) {
      return {
        "toothIsOpen": false,
      };
    }
    Struct2LastTFLite showObject = resultObj["lastObjects"][0];
    Float32List results = pointerFloat2FloatList(showObject.rects, showObject.rectsLength);

    Map toothPair = {};
    int rectEleCount = showObject.rectEleCount;
    for (int i = 0; i < results.length / rectEleCount; i++) {
      double left = results[i * rectEleCount];
      if (left.isNaN) {
        left = 0;
      }
      double top = results[i * rectEleCount + 1];
      if (top.isNaN) {
        top = 0;
      }
      double right = results[i * rectEleCount + 2];
      if (right.isNaN) {
        right = 0;
      }
      double bottom = results[i * rectEleCount + 3];
      if (bottom.isNaN) {
        bottom = 0;
      }
      double tIndex = results[i * rectEleCount + (Global.isGlobalUseNewModal ? 6 : 5)];
      if (tIndex.isNaN) {
        tIndex = 0;
      }
      int index = tIndex.toInt();
      if (Global.isGlobalUseNewModal) {
        if (index >= 3) {
          index = index % 10;
        }
      } else {
        if (index >= 14) {
          index -= 13;
        } else if (index >= 6) {
          index -= 5;
        }
      }
      if (left < 0 || top < 0 || right < 0 || bottom < 0) {
        left = 0;
        top = 0;
        right = 0;
        bottom = 0;
      }
//成对装载
      if (tIndex.toInt() >= 6) {
        if (!toothPair.containsKey("$index")) {
          toothPair["$index"] = {
            "count": 0,
//上牙下边界
            "upTdownY": tIndex.toInt() < 14 && tIndex.toInt() >= 6 ? bottom : -1,
//下牙上边界
            "downTupY": tIndex.toInt() >= 14 ? top : -1
          };
        }
        toothPair["$index"]["count"] = toothPair["$index"]["count"] + 1;
// print("61检查: tIndex:$tIndex | index:$index \n left:$left | top:$top | right:$right | bottom:$bottom \n frontUpDownY:${toothPair["$index"]["upTdownY"]} | thisUpDownY:$bottom | frontDownUpY:${toothPair["$index"]["downTupY"]} |  thisDownUpY:$top");
        if (tIndex.toInt() < 14) {
//此牙号的最下坐标
          if (toothPair["$index"]["upTdownY"] == -1) {
            toothPair["$index"]["upTdownY"] = bottom;
          } else {
            toothPair["$index"]["upTdownY"] =
                bottom > toothPair["$index"]["upTdownY"] ? bottom : toothPair["$index"]["upTdownY"];
          }
        } else {
//此牙号的最上坐标
          if (toothPair["$index"]["downTupY"] == -1) {
            toothPair["$index"]["downTupY"] = top;
          } else {
            toothPair["$index"]["downTupY"] =
                top < toothPair["$index"]["downTupY"] ? top : toothPair["$index"]["downTupY"];
          }
        }
      }
    }
//从边界往里的前5个成对牙做判断,(检测出成对的牙号，且上下边界正常，且下牙上边界-上牙下边界<5 的数组对应的牙号)个数
    List normalPairList = []; //符合上下对边界且可以计算边界的牙齿
    List targetLogicPairToothList = [];

//为了提高准确率只先判断靠前的3个牙齿边界的距离
    List keys = toothPair.keys.toList();
    keys.sort((a, b) {
      return double.parse(a).compareTo(double.parse(b));
    });
    Map treeMap = {};
    for (var element in keys) {
      treeMap[element] = toothPair[element];
    }
    toothPair = treeMap;
    for (var key in toothPair.keys) {
//剔除没有成对有上下边界的错误对
      if (toothPair[key]["count"] >= 2 && toothPair[key]["upTdownY"] != -1 && toothPair[key]["downTupY"] != -1) {
        normalPairList.add(key);
        if (toothPair[key]["downTupY"] - toothPair[key]["upTdownY"] <= 5) {
          targetLogicPairToothList.add(key);
        }
//为了提高准确率只先判断靠前的3个牙齿边界的距离
        if (normalPairList.length >= 3) {
          break;
        }
      }
    }

//咬合所占可正常可计算上下边界成对牙齿的比例
    double yhToothRate = normalPairList.isEmpty ? 0 : (targetLogicPairToothList.length / normalPairList.length);

// print("61检查:$toothPair");
// print("61检查,咬合的牙齿:$targetLogicPairToothList");
// print("61检查:正常可判断的成对牙齿数:${normalPairList.length},咬合牙齿数占比为:$yhToothRate}");
    return {
//不再按照数量来判断，而是按照占比-> 当咬合牙齿数量占比>50%即可认为咬合，反之为微张
      "toothIsOpen": yhToothRate <= 0.5,
    };
  }

//检查5号牙齿存在情况与检测最大框体下标
  static dynamic check56TootchLogic(dynamic resultObj, DeviceOrientation currentOrientation,
      {int checkIndex = 0, bool isCheckBringDirLeft = false}) {
    if (resultObj == null || !resultObj.containsKey("lastObjects")) {
      return {
        "isHave5Tootch": false,
        "isHave6Tootch": false,
      };
    }
    Struct2LastTFLite showObject = resultObj["lastObjects"][checkIndex];
    Float32List results = pointerFloat2FloatList(showObject.rects, showObject.rectsLength);

//是否有5号牙齿
    bool isHave5Tootch = false;
//是否有6号牙齿
    bool isHave6Tootch = false;

    int rectEleCount = showObject.rectEleCount;

    if (Global.isGlobalUseNewModal && rectEleCount <= 6) {
      return {
        "isHave5Tootch": false,
        "isHave6Tootch": false,
      };
    }

    for (int i = 0; i < results.length / rectEleCount; i++) {
      double left = results[i * rectEleCount];
      if (left.isNaN) {
        left = 0;
      }
      double top = results[i * rectEleCount + 1];
      if (top.isNaN) {
        top = 0;
      }
      double right = results[i * rectEleCount + 2];
      if (right.isNaN) {
        right = 0;
      }
      double bottom = results[i * rectEleCount + 3];
      if (bottom.isNaN) {
        bottom = 0;
      }
      double tIndex = results[i * rectEleCount + (Global.isGlobalUseNewModal ? 6 : 5)];
      if (tIndex.isNaN) {
        tIndex = 0;
      }
      int index = tIndex.toInt();
      if (Global.isGlobalUseNewModal) {
        if (index >= 3) {
          index = index % 10;
        }
      } else {
        if (index >= 14) {
          index -= 13;
        } else if (index >= 6) {
          index -= 5;
        }
      }
      if (index == 5) {
        if (isCheckBringDirLeft) {
          if (Platform.isAndroid && currentOrientation == DeviceOrientation.landscapeLeft) {
            int rightIndex = resultObj["dirctionInfo"].rightIndex;
            if (rightIndex != -1) {
              isHave5Tootch = true;
            }
          } else {
            int leftIndex = resultObj["dirctionInfo"].leftIndex;
            if (leftIndex != -1) {
              isHave5Tootch = true;
            }
          }
        } else {
          isHave5Tootch = true;
        }
      }
      if (index == 6) {
        if (isCheckBringDirLeft) {
          if (Platform.isAndroid && currentOrientation == DeviceOrientation.landscapeLeft) {
            int rightIndex = resultObj["dirctionInfo"].rightIndex;
            if (rightIndex != -1) {
              isHave6Tootch = true;
            }
          } else {
            int leftIndex = resultObj["dirctionInfo"].leftIndex;
            if (leftIndex != -1) {
              isHave6Tootch = true;
            }
          }
        } else {
          isHave6Tootch = true;
        }
      }
    }
    return {
      "isHave5Tootch": isHave5Tootch,
      "isHave6Tootch": isHave6Tootch,
    };
  }

  ///根据扫描图片名字获取是否有错误信息
  static bool isHaveErrorInfoByScanImgPath(String imgPath) {
    if (imgPath.contains("dver") && imgPath.contains("fver")) {
      String imgName = imgPath.substring(imgPath.lastIndexOf("/") + 1, imgPath.length);
      if (imgName.split('-').length < 8) {
        return false;
      }
      try {
        int errorCode = int.parse(imgName.split('-')[3]);
        if (isUltraLightError(errorCode, imgName)) {
          errorCode = 0;
        }
        return errorCode != 0;
      } catch (ex) {
        return false;
      }
    } else {
      return false;
    }
  }

  ///根据obj模型结果推导
//上下牙齿各检测到了几颗
  static getUpDownHowManyNum(showObject) {
//记录牙齿相同号的出现次数
    Map toothMapCount = {};
    int upCount = 0;
    int downCount = 0;
    Float32List results = pointerFloat2FloatList(showObject.rects, showObject.rectsLength);
    int rectEleCount = showObject.rectEleCount;
    for (int i = 0; i < results.length / rectEleCount; i++) {
      double tIndex = results[i * rectEleCount + (Global.isGlobalUseNewModal ? 6 : 5)];
      if (tIndex.isNaN) {
        tIndex = 0;
      }
      int index = tIndex.toInt();
      if (Global.isGlobalUseNewModal) {
        if (index >= 19) {
          downCount++;
        } else if (index >= 3) {
          upCount++;
        }
      } else {
        if (index >= 14) {
          downCount++;
        } else if (index >= 6) {
          upCount++;
        }
      }
      if (Global.isGlobalUseNewModal) {
        if (index >= 3) {
          index = index % 10;
        }
      } else {
        if (index >= 14) {
          index -= 13;
        } else if (index >= 6) {
          index -= 5;
        }
      }
      if (toothMapCount.containsKey("$index")) {
        toothMapCount["$index"] = toothMapCount["$index"] + 1;
      } else {
        toothMapCount["$index"] = 1;
      }
    }
    return {
      "upCount": upCount,
      "downCount": downCount,
      "toothMapCount": toothMapCount,
    };
  }

//新版本的012框体信息以及其他
  static Map getNewModalRectInfo(Struct2LastTFLite showObject) {
    Float32List results = pointerFloat2FloatList(showObject.rects, showObject.rectsLength);
//上框
    List<double> upperRectRegInfo = [];
    double tempMaxUpperRectWidthHeight = 0;
//下框
    List<double> downRectRegInfo = [];
    double tempMaxDownRectWidthHeight = 0;
//上下总框体
    List<double> ulRegInfo = [];
    double tempMaxUlRectWidthHeight = 0;

    Map check11122122List = {"11": false, "12": false, "21": false, "22": false};
    Map check31324142 = {"31": false, "32": false, "41": false, "42": false};
    int tempCheckCountUp = 0;
    int tempCheckCountDown = 0;

    int rectEleCount = showObject.rectEleCount;
    logger("getNewModalRectInfo rectEleCount: $rectEleCount ${results.length}");
//异常保护
    if (results.isNotEmpty && results.length < rectEleCount) {
      if (kDebugMode) {
        print("${results.length} $rectEleCount");
        print("数据异常，开启异常保护逻辑了");
      }
      return {
        "upper": upperRectRegInfo,
        "lower": downRectRegInfo,
        "ul": ulRegInfo,
        "isHave11122122": false,
        "isHave31324142": false,
      };
    }
    for (int i = 0; i < results.length / rectEleCount; i++) {
      double left = results[i * rectEleCount];
      if (left.isNaN) {
        left = 0;
      }
      double top = results[i * rectEleCount + 1];
      if (top.isNaN) {
        top = 0;
      }
      double right = results[i * rectEleCount + 2];
      if (right.isNaN) {
        right = 0;
      }
      double bottom = results[i * rectEleCount + 3];
      if (bottom.isNaN) {
        bottom = 0;
      }
// double sorce = results[i*6+4];if(sorce.isNaN){sorce = 0;}
      double tIndex = results[i * rectEleCount + (Global.isGlobalUseNewModal ? 6 : 5)];
      if (tIndex.isNaN) {
        tIndex = 0;
      }
//
      String tempIndexStr = tIndex.toInt().toString();
      if (check11122122List.containsKey(tempIndexStr)) {
        check11122122List[tempIndexStr] = true;
      }
      if (check31324142.containsKey(tempIndexStr)) {
        check31324142[tempIndexStr] = true;
      }

      if (Global.isGlobalUseNewModal) {
//3/4 预留类别暂时不用
        if (tIndex == 3 || tIndex == 4) {
          continue;
        }
//只保留最大的 0/1/2/
        if (tIndex <= 2) {
          double tnWidth = right - left;
          double tnHeight = bottom - top;
          if (tIndex == 0) {
            if (tnWidth * tnHeight >= tempMaxUpperRectWidthHeight) {
              tempMaxUpperRectWidthHeight = tnWidth * tnHeight;
              upperRectRegInfo = results.sublist(i * rectEleCount + 7, i * rectEleCount + 13);
            } else {
//剔除较小的大框
              continue;
            }
          } else if (tIndex == 1) {
            if (tnWidth * tnHeight >= tempMaxDownRectWidthHeight) {
              tempMaxDownRectWidthHeight = tnWidth * tnHeight;
              downRectRegInfo = results.sublist(i * rectEleCount + 7, i * rectEleCount + 13);
            } else {
//剔除较小的大框
              continue;
            }
          } else if (tIndex == 2) {
            if (tnWidth * tnHeight >= tempMaxUlRectWidthHeight) {
              tempMaxUlRectWidthHeight = tnWidth * tnHeight;
              ulRegInfo = results.sublist(i * rectEleCount + 7, i * rectEleCount + 13);
            } else {
//剔除较小的大框
              continue;
            }
          }
        }
      }
    }
    for (var key in check11122122List.keys) {
      if (check11122122List[key]) {
        tempCheckCountUp++;
      }
    }
    for (var key in check31324142.keys) {
      if (check31324142[key]) {
        tempCheckCountDown++;
      }
    }

    logger(
        "getNewModalRectInfo result: $upperRectRegInfo $downRectRegInfo $ulRegInfo $tempCheckCountUp $tempCheckCountDown");
    return {
      "upper": upperRectRegInfo,
      "lower": downRectRegInfo,
      "ul": ulRegInfo,
      "isHave11122122": tempCheckCountUp >= 3, //辅助判断上颌
      "isHave31324142": tempCheckCountDown >= 3, //辅助判断下颌
    };
  }

  static bool checkIsBlurByNewModal({dynamic struct2lastTFLite, dynamic rectInfos}) {
// Map zeroOneTwoRectInfos;
// if (struct2lastTFLite != null) {
//   zeroOneTwoRectInfos = getNewModalRectInfo(struct2lastTFLite);
// } else {
//   zeroOneTwoRectInfos = rectInfos;
// }
// double minClearValue = -1;
// if (zeroOneTwoRectInfos["upper"].isNotEmpty) {
//   minClearValue = zeroOneTwoRectInfos["upper"][4];
// }
// if (zeroOneTwoRectInfos["lower"].isNotEmpty) {
//   if (minClearValue == -1) {
//     minClearValue = zeroOneTwoRectInfos["lower"][4];
//   } else {
//     if (zeroOneTwoRectInfos["lower"][4] <= minClearValue) {
//       minClearValue = zeroOneTwoRectInfos["lower"][4];
//     }
//   }
// }
// if (minClearValue < 0.2) {
//   return true;
// }
//   if (kDebugMode) {
//     print("是否模糊：${struct2lastTFLite.blurInfo.isBlur == 1?"模糊":"清晰"}");
//     print("Laplacian检测:X= ${struct2lastTFLite.blurInfo.imgLaplacianCount.toString().substring(0, struct2lastTFLite.blurInfo.imgLaplacianCount.toString().length >= 5 ? 5 : struct2lastTFLite.blurInfo.imgLaplacianCount.toString().length)} (${struct2lastTFLite.blurInfo.checkLapMin.toString().substring(0, struct2lastTFLite.blurInfo.checkLapMin.toString().length >= 5 ? 5 : struct2lastTFLite.blurInfo.checkLapMin.toString().length)} < X <${struct2lastTFLite.blurInfo.checkLapMax})");
//     print("Canny检测:${struct2lastTFLite.blurInfo.imgCannyCount.toStringAsFixed(2)} ( 模糊 < ${struct2lastTFLite.blurInfo.checkCannyCount.toStringAsFixed(2)} < 清晰)");
//   }
    if (struct2lastTFLite.blurInfo.isBlur == 1) {
      return true;
    }
    return false;
  }

//所有错误标识和文字均以1为下标开始的
//shouldDirect: 1=left 2=front 3=right 4=up 5=down
  static int getResultObjErrorInfo(
      dynamic resultObj, int index, int shouldDirect, currentOrientation, toothCameraStep, toothCameraType,
      {String checkImgPath = "", dynamic centerPoint}) {
    Struct2LastTFLite struct2lastTFLite = resultObj["lastObjects"][index];

//新模型计算用的
    Map zeroOneTwoRectInfos = {};
    if (Global.isGlobalUseNewModal) {
      zeroOneTwoRectInfos = getNewModalRectInfo(struct2lastTFLite);
    }

// print("zeroOneTwoRectInfos:$zeroOneTwoRectInfos \ncheckImgPath:$checkImgPath\n\n\n");

//没有检测到牙齿(设置为0是因为下面检测不清晰其实也是在找牙齿)
    if (Global.isGlobalUseNewModal) {
//因为新模型对类牙齿形状不太鲁棒，因此正常牙齿口扫图 包含012 大框中两个+至少暴露5颗牙齿
      if (struct2lastTFLite.rectsLength <= (7 * struct2lastTFLite.rectEleCount)) {
        return 11;
      }
    } else {
      if (struct2lastTFLite.rectsLength <= 0) {
        return 11;
      }
    }

//检测清晰
    if (checkIsBlurByNewModal(struct2lastTFLite: struct2lastTFLite)) {
      return 21;
    }

//扫描方向(312=标识：应该检测左->右，但是拍成了扫上颌)
//currentDirect:0=没检测到方向(无需说明缘由) 1=left 2=front 3=right 4=up 5=down
//6=应该扫左右，但是不满足最左最右，但是差不多的情况
//7=应该扫中间，但是不满足最中，但是差不多的情况
//8=应该微张，但是微幅度过大了
    if (Global.isGlobalUseNewModal) {
      int currentDirect = 0;
      if (zeroOneTwoRectInfos["upper"].isNotEmpty && zeroOneTwoRectInfos["lower"].isEmpty) {
        if (zeroOneTwoRectInfos["upper"][0] >= 0.4 &&
            zeroOneTwoRectInfos["upper"][2] >= 0.4 &&
            zeroOneTwoRectInfos["upper"][3] >= 0.4 &&
            zeroOneTwoRectInfos["isHave11122122"]) {
          currentDirect = 4;
        }
      } else if (zeroOneTwoRectInfos["lower"].isNotEmpty && zeroOneTwoRectInfos["upper"].isEmpty) {
        if (zeroOneTwoRectInfos["lower"][0] >= 0.4 &&
            zeroOneTwoRectInfos["lower"][2] >= 0.4 &&
            zeroOneTwoRectInfos["lower"][3] >= 0.4 &&
            zeroOneTwoRectInfos["isHave31324142"]) {
          currentDirect = 5;
        }
      } else {
        double meanCenterness = -1;
        meanCenterness = ((zeroOneTwoRectInfos["upper"].isNotEmpty ? zeroOneTwoRectInfos["upper"][1] : 0) +
                (zeroOneTwoRectInfos["lower"].isNotEmpty ? zeroOneTwoRectInfos["lower"][1] : 0)) /
            2;
        if (meanCenterness >= 0 && meanCenterness <= 0.25) {
          currentDirect = 1;
        } else if (meanCenterness >= 0.4 && meanCenterness <= 0.6) {
          currentDirect = 2;
        } else if (meanCenterness >= 0.75) {
          currentDirect = 3;
        } else {
//不符合最左最右最中标准,或者方向符合但是张开幅度过大
//6=应该扫左右，但是不满足最左最右，但是差不多的情况
//7=应该扫中间，但是不满足最中，但是差不多的情况
          if (toothCameraType == ToothCameraTypeEnum.openMaskOff ||
              toothCameraType == ToothCameraTypeEnum.openMaskOn ||
              toothCameraType == ToothCameraTypeEnum.occlusion) {
            if (shouldDirect == 1 || shouldDirect == 3) {
              currentDirect = 6;
            } else if (shouldDirect == 2) {
              currentDirect = 7;
            }
          }
        }
      }
      if (currentDirect != shouldDirect) {
        return int.parse("3$shouldDirect$currentDirect");
      }
    } else {
      Struct2TFLiteMCO struct2tfLiteMCO = resultObj["dirctionInfo"];
      int currentDirect = 0;
      if (struct2tfLiteMCO.leftIndex == index) {
        currentDirect = 1;
      } else if (struct2tfLiteMCO.frontIndex == index) {
        currentDirect = 2;
      } else if (struct2tfLiteMCO.rightIndex == index) {
        currentDirect = 3;
      } else if (struct2tfLiteMCO.upIndex == index) {
        currentDirect = 4;
      } else if (struct2tfLiteMCO.downIndex == index) {
        currentDirect = 5;
      }

      if (shouldDirect != currentDirect) {
//是否符合
        bool isSite = false;
//应该扫描上,通过上下牙齿的数量再次判断
        if (shouldDirect == 4) {
          Map result = getUpDownHowManyNum(resultObj["lastObjects"][index]);
//判断牙齿是否符合扫上颌的预期
          if (result["upCount"] >= 4 && result["downCount"] < 4) {
            isSite = true;
          }
        }
//如果模型返回的方向是下,通过上下牙齿数量再次判断
        if (shouldDirect == 5) {
          Map result = getUpDownHowManyNum(resultObj["lastObjects"][index]);
//判断模型检测错了
          if (result["downCount"] >= 4 && result["upCount"] < 4) {
            isSite = true;
          }
        }
//如果模型返回的方向是中间,通过上下牙齿成对的数量再次判断
        if (shouldDirect == 2) {
          Map result = getUpDownHowManyNum(resultObj["lastObjects"][index]);
//判断模型检测错了
          if ((result["toothMapCount"]["1"] >= 2 && result["toothMapCount"]["2"] >= 2) ||
              (result["toothMapCount"]["1"] >= 2 && result["toothMapCount"]["3"] >= 2) ||
              (result["toothMapCount"]["2"] >= 2 && result["toothMapCount"]["3"] >= 2)) {
            isSite = true;
          }
        }
        if (!isSite) {
          return int.parse("3$shouldDirect$currentDirect");
        }
      }
    }

//有没有5号牙
    if (shouldDirect != 2) {
      dynamic checkResult = Global.check56TootchLogic(resultObj, currentOrientation, checkIndex: index);
      if (!checkResult["isHave5Tootch"]) {
        return int.parse("4$shouldDirect");
      } else {
//有5号牙没有6号牙
        if (!checkResult["isHave6Tootch"]) {
          return int.parse("5$shouldDirect");
        }
      }
    }

//牙弓检测
    if (Global.isGlobalUseNewModal) {
      if (toothCameraType == ToothCameraTypeEnum.maxillary) {
        if (zeroOneTwoRectInfos["upper"].isNotEmpty && zeroOneTwoRectInfos["upper"][0] < 0.4) {
          return 81;
        }
      } else if (toothCameraType == ToothCameraTypeEnum.mandibular) {
        if (zeroOneTwoRectInfos["lower"].isNotEmpty && zeroOneTwoRectInfos["lower"][0] < 0.4) {
          return 82;
        }
      }
    }

//有没有微张
    if (shouldDirect == 1 || shouldDirect == 2 || shouldDirect == 3) {
      if (Global.isGlobalUseNewModal) {
        if (zeroOneTwoRectInfos["ul"].isNotEmpty) {
//应该咬合但是没有咬合
          if (toothCameraType == ToothCameraTypeEnum.occlusion && zeroOneTwoRectInfos["ul"][5] >= 0.08) {
            return int.parse("61");
          }
//应该微张未微张
          if ((toothCameraType == ToothCameraTypeEnum.openMaskOff ||
              toothCameraType == ToothCameraTypeEnum.openMaskOn)) {
            if (zeroOneTwoRectInfos["ul"][5] < 0.1) {
              return int.parse("62");
            }
            if (zeroOneTwoRectInfos["ul"][5] >= 0.5) {
              return int.parse("63");
            }
          }
        }
      } else {
        dynamic checkResult = Global.checkToothIsOpen(resultObj);
//应该咬合未咬合
        if (toothCameraType == ToothCameraTypeEnum.occlusion && checkResult["toothIsOpen"]) {
          return int.parse("61");
        }
//应该微张未微张
        if ((toothCameraType == ToothCameraTypeEnum.openMaskOff || toothCameraType == ToothCameraTypeEnum.openMaskOn) &&
            !checkResult["toothIsOpen"]) {
          return int.parse("62");
        }
      }
    }

//检查内壁的亮度
    if (checkImgPath != "" && centerPoint != null) {
      if (!ffiCheckImgHaveLightSync(checkImgPath)) {
        return 71;
      }
    }

    return 0;
  }

  ///根据错误代码获取信息
  ///dir:1=left 2=front 3=right 4=up 5=down
  static Map getAlertTextByScanErrorCode(int errorCode, String path) {
    String alertContent = "";
    String alertFun = "";
    if (errorCode == 0) {
      alertContent = Lang.scan_great;
    } else if (errorCode == 11) {
//1、牙齿<=5个
      alertContent = Lang.tooth_expose_too_less;
      alertFun = Lang.setup_mouth_prop_tip;
    } else if (errorCode == 21) {
//2、牙齿不清晰
//ultra扫描的，图片宽度传默认的-1
      if (isUltraImage(path)) {
        alertContent = Lang.scan_blur_keep_speed;
        alertFun = Lang.scan_blur_action_ultra;
      } else {
        alertContent = Lang.scan_blur_focus_fail;
        alertFun = Lang.scan_blur_action;
      }
    } else if (errorCode >= 300 && errorCode <= 399) {
//3、扫描方向错了
      int shouldCode = int.parse(("$errorCode").substring(1, 2));
      int currentCode = int.parse(("$errorCode").substring(2, 3));

      if (shouldCode == 1 || shouldCode == 3) {
//应该扫左/右，但扫的是其他方向
        alertContent = Lang.move_mouth_prop_follow_arrow;
        alertFun = Lang.scan_left_to_right;
      } else if (shouldCode == 2) {
//应该扫中，但扫的是其他方向
        alertContent = Lang.move_mouth_prop_follow_arrow;
        alertFun = Lang.scan_center_front;
      } else if (shouldCode == 4) {
//应该扫上颌但扫的不是上颌，且没露下牙
        alertContent = Lang.scan_up_bat;
        alertFun = Lang.scan_iscan_into_mouth_up;
      } else if (shouldCode == 5) {
//应该扫下颌但扫的不是下颌，且没露上牙
        alertContent = Lang.scan_down_bat;
        alertFun = Lang.scan_iscan_into_mouth_down;
      }

      if (currentCode == 6) {
//应该扫左/右但扫在临界区间
        alertContent = Lang.scan_no_behind_tooth;
        alertFun = Lang.scan_turn_head_behind_tooth;
      } else if (currentCode == 7) {
//应该扫中间但扫在临界区间
        alertContent = Lang.put_front_tooth_center;
        alertFun = Lang.scan_follow_progress_center;
      }
    } else if (errorCode >= 41 && errorCode <= 49) {
//4、没有露出5号牙
      if (errorCode == 41) {
//没露左5
        alertContent = Lang.scan_no_behind_tooth;
        alertFun = Lang.scan_turn_head_behind_tooth;
      } else if (errorCode == 43) {
//没露右5
        alertContent = Lang.scan_no_behind_tooth;
        alertFun = Lang.scan_turn_head_behind_tooth;
      } else if (errorCode == 44) {
//没露上5
        alertContent = Lang.scan_whole_up_maxillary;
        alertFun = Lang.scan_try_open_mouth_up;
      } else if (errorCode == 45) {
//没露下5
        alertContent = Lang.scan_while_down_maxillary;
        alertFun = Lang.scan_try_open_mouth_down;
      }
    } else if (errorCode >= 51 && errorCode <= 59) {
//5、露5没但露6号牙
      if (errorCode == 51) {
//没露左6
        alertContent = Lang.scan_no_behind_tooth;
        alertFun = Lang.scan_turn_head_behind_tooth;
      } else if (errorCode == 53) {
//没露右6
        alertContent = Lang.scan_no_behind_tooth;
        alertFun = Lang.scan_turn_head_behind_tooth;
      } else if (errorCode == 54) {
//没露上6
        alertContent = Lang.scan_whole_up_maxillary;
        alertFun = Lang.scan_try_open_mouth_up;
      } else if (errorCode == 55) {
//没露下6
        alertContent = Lang.scan_while_down_maxillary;
        alertFun = Lang.scan_try_open_mouth_down;
      }
    } else if (errorCode >= 61 && errorCode <= 69) {
//6、上下颌是否微张
      if (errorCode == 61) {
//该咬合但没咬合
        alertContent = Lang.bite_back_teeth;
        alertFun = Lang.scan_keep_bite_back_teeth;
      } else if (errorCode == 62) {
//该微张但没张开
        alertContent = Lang.gently_stretch_teeth;
        alertFun = Lang.scan_keep_gently_stretch_teeth;
      } else if (errorCode == 63) {
//该微张但张开幅度太大
        alertContent = Lang.gently_stretch_teeth_not_wild;
        alertFun = Lang.scan_keep_gently_stretch_teeth_to_doctor;
      }
    } else if (errorCode >= 71 && errorCode <= 79) {
//7、牙齿亮度不够
      if (errorCode == 71) {
        alertContent = Lang.make_mouth_lighten;
        alertFun = Lang.make_mouth_lighten_action;
      }
    } else if (errorCode >= 81 && errorCode <= 89) {
//8、上下颌弓形不够
      if (errorCode == 81) {
//上颌弓形不够
        alertContent = Lang.scan_up_maxillary;
        alertFun = Lang.scan_try_open_mouth_up;
      } else if (errorCode == 82) {
//下颌弓形不够
        alertContent = Lang.scan_down_maxillary;
        alertFun = Lang.scan_try_open_mouth_down;
      }
    }
    return {"content": alertContent, "fun": alertFun};
  }

  static showPhotoBrowerModal(
    BuildContext context, {
    required imgPathList,
    defaultShowIndex = 0,
    deleteCalllBack,
    title = "",
    initScale = 1.0,
    content = "",
    List titles = const [],
    List contents = const [],
    Widget lbWidget = const SizedBox(),
    Widget cbWidget = const SizedBox(),
    TypeCallback<int>? onPageChanged,
  }) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrowerPhoto(
            initScale: initScale,
            imgPathList: imgPathList,
            defaultShowIndex: defaultShowIndex,
            deleteCalllBack: deleteCalllBack,
            title: title,
            titles: titles,
            content: content,
            contents: contents,
            lbWidget: lbWidget,
            cbWidget: cbWidget,
            onPageChanged: onPageChanged,
          );
        });
  }

  static showModalVideoPlayer(BuildContext context, String path, String title) {
    showCustomDialog(
        HModalVideoPlayer(
            title: title,
            targetFilePathName: path,
            closeBotton: () {
              AudioPlayerUtil.stopSound();
              BotToast.cleanAll();
            }),
        isBringXBtn: false);
  }

  /// 获取 Android API 版本，错误状态返回0
  static Future<int> getAndroidApiVersion() async {
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      return androidInfo.version.sdkInt;
    } else {
      return 0;
    }
  }

  static Future<bool> checkStorePermission([bool showAlert = true]) async {
    try {
      Permission permission = Permission.storage;
      int apiVersion = await getAndroidApiVersion();
      logger("saveImage apiVersion: $apiVersion");
      if (apiVersion >= 33) {
        permission = Permission.manageExternalStorage;
      }
      PermissionStatus storageStatus = await permission.status;
      logger("saveImage $permission storageStatus: $storageStatus");
      if (storageStatus != PermissionStatus.granted) {
        logger("saveImage fail: no permission $permission");
        storageStatus = await permission.request();
        if (storageStatus != PermissionStatus.granted) {
          logger("saveImage $permission fail: denied");
          if (showAlert) {
            showAlertDialog(
              Lang.warn,
              Lang.request_storage_permission,
              okCallBack: openAppSettings,
            );
          }
          return false;
        }
      }
      return true;
    } catch (ex) {
      return false;
    }
  }

  static saveImageFileFromAssets(String assetsPath) async {
    // if (Platform.isAndroid) {
    //   Permission permission = Permission.storage;
    //   int apiVersion = await getAndroidApiVersion();
    //   if (apiVersion >= 33) {
    //     permission = Permission.manageExternalStorage;
    //   }
    //   PermissionStatus storageStatus = await permission.status;
    //   logger("saveImage apiVersion: $apiVersion $storageStatus");
    //   if (storageStatus != PermissionStatus.granted) {
    //     Global.showAlertDialog(
    //       Lang.warn,
    //       Lang.request_storage,
    //       okCallBack: () async {
    //         bool save = await _saveImageFileFromAssets(assetsPath);
    //         if (save) {
    //           Global.toast(Lang.save_to_gallery);
    //         }
    //       },
    //     );
    //     return;
    //   }
    // }
    //
    // bool save = await _saveImageFileFromAssets(assetsPath);
    // if (save) {
    //   Global.toast(Lang.save_to_gallery);
    // }
  }

  static Future<bool> _saveImageFileFromAssets(String assetsPath) async {
    try {
      if (await checkStorePermission()) {
        ByteData bytes = await rootBundle.load(assetsPath);
        Uint8List imageBytes = bytes.buffer.asUint8List();
        final result = await ImageGallerySaver.saveImage(imageBytes);
        logger("saveImage : $result");
        if (result != null && result != "") {
          return true;
        }
      }
    } catch (e) {
      //
    }
    return false;
  }

  static saveImageFileFromCache(String assetsPath) async {
    // if (Platform.isAndroid) {
    //   Permission permission = Permission.storage;
    //   int apiVersion = await getAndroidApiVersion();
    //   if (apiVersion >= 33) {
    //     permission = Permission.manageExternalStorage;
    //   }
    //   PermissionStatus storageStatus = await permission.status;
    //   logger("saveImage apiVersion: $apiVersion $storageStatus");
    //   if (storageStatus != PermissionStatus.granted) {
    //     Global.showAlertDialog(
    //       Lang.warn,
    //       Lang.request_storage,
    //       okCallBack: () async {
    //         bool save = await _saveImageFileFromCache(assetsPath);
    //         if (save) {
    //           Global.toast(Lang.save_to_gallery);
    //         }
    //       },
    //     );
    //     return;
    //   }
    // }
    //
    // bool save = await _saveImageFileFromCache(assetsPath);
    // if (save) {
    //   Global.toast(Lang.save_to_gallery);
    // }
  }

  static Future<bool> _saveImageFileFromCache(String filePath) async {
    try {
      filePath = getLocalPath(filePath);
      if (await checkStorePermission()) {
        String fileName = filePath.split("/").last;
        if (fileName.contains("?")) {
          fileName = fileName.substring(0, fileName.indexOf("?"));
        }
        final result = await ImageGallerySaver.saveFile(filePath, name: fileName);
        logger("saveImage : $result");
        if (result != null && result != "") {
          return true;
        }
      }
    } catch (e) {
      logger("saveImage error: $e");
    }
    return false;
  }

  static copyImageDocument(String filePath, String dirName, {bool save = false}) async {
    try {
      if (!save && File(filePath).existsSync()) {
        return;
      }
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String storagePath = appDocDir.path;

      String fileName = filePath.split("/").last;
      if (fileName.contains("?")) {
        fileName = fileName.substring(0, fileName.indexOf("?"));
      }
      if (fileName.contains(".")) {
        fileName = fileName.substring(0, fileName.lastIndexOf("."));
      }

      String newPath = '$storagePath/$fileName';
      if (save) {
//从缓存拷到document
        File file = await File(filePath).copy(newPath);
        logger("copyImageDocument $save $filePath -> $file");
      } else {
//从document拷到缓存
        File file = await File(newPath).copy(filePath);
        logger("copyImageDocument $save $newPath -> $file");
      }

      Directory externalDir = await getApplicationSupportDirectory();
      Directory saveDir = Directory('${externalDir!.path}/$dirName');
      if (!saveDir.existsSync()) {
        await saveDir.create(recursive: true);
      }
      String externalPath = '${saveDir!.path}/$fileName';
      if (save) {
//从缓存拷到document
        File file = await File(filePath).copy(externalPath);
        logger("copyImageDocument $save $filePath -> $file");
      } else {
//从document拷到缓存
        if (!File(filePath).existsSync()) {
          File file = await File(externalPath).copy(filePath);
          logger("copyImageDocument $save $externalPath -> $file");
        }
      }
    } catch (ex) {
      logger("copyImageDocument error $ex");
    }
  }

  static deleteExternalImages(String dirName) async {
    try {
      Directory externalDir = await getApplicationSupportDirectory();
      Directory saveDir = Directory('${externalDir.path}/$dirName');
      saveDir.delete(recursive: true);
    } catch (ex) {
//
    }
  }
}

class CustomOffsetAnimation extends StatefulWidget {
  final AnimationController? controller;
  final Widget? child;

  const CustomOffsetAnimation({Key? key, this.controller, this.child}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _CustomOffsetAnimationState();
}

class _CustomOffsetAnimationState extends State<CustomOffsetAnimation> {
  Tween<Offset>? tweenOffset;
  Tween<double>? tweenScale;

  Animation<double>? animation;

  @override
  void initState() {
    tweenOffset = Tween<Offset>(
      begin: const Offset(0.0, 0.0),
      end: Offset.zero,
    );
    tweenScale = Tween<double>(begin: 0.3, end: 1.0);
    animation = CurvedAnimation(parent: widget.controller!, curve: Curves.decelerate);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller!,
      builder: (BuildContext context, Widget? child) {
        return FractionalTranslation(
            translation: tweenOffset!.evaluate(animation!),
            child: ClipRect(
              child: Transform.scale(
                scale: tweenScale!.evaluate(animation!),
                child: Opacity(
                  opacity: animation!.value,
                  child: child,
                ),
              ),
            ));
      },
      child: widget.child,
    );
  }
}
