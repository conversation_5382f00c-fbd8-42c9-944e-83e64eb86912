import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/msgpack/msgpack_dart.dart';
import 'package:path_provider/path_provider.dart';

Map<String, List> localPhotos = {};

void copyLocalAssetZip() {
  copyAssetZip("la_down_use.zip");
  copyAssetZip("la_lr_close_use.zip");
  copyAssetZip("la_lr_open_use.zip");
  copyAssetZip("la_up_use.zip");

  readMsgPack();
}

void copyAssetZip(String fileName) async {
  Directory dir = await getApplicationDocumentsDirectory();
  ByteData bytes = await PlatformAssetBundle().load("res/local/$fileName");
  final File file = File("${dir.path}/$fileName");
  await file.writeAsBytes(bytes.buffer.asUint8List());
  List contents = await Global.unZip(file.path, dir: dir);
  localPhotos[fileName] = contents;
  logger("$fileName -> $contents", key: "copyAssetZip");
}

void readMsgPack() async{
  ByteData bytes = await PlatformAssetBundle().load("assets/front_analysis.msgpack");
  logger("readMsgPack bytes: ${bytes.lengthInBytes}");
  var decodedData = deserialize(bytes.buffer.asUint8List());
  logger("readMsgPack decodedData $decodedData");
  var jsonString = jsonEncode(decodedData);
  logger("readMsgPack jsonString: ${jsonString.length} $jsonString");
}