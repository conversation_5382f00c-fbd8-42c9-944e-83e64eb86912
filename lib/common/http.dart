import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/my/http_log.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/dio/dio.dart' as mydio;
import 'package:path_provider/path_provider.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

enum HttpEnv { dev, alpha, uat, uatAp, prod }

class HHttp {
  static HttpEnv httpEnv = HttpEnv.prod;
  static Map serverConfigs = {
    "dev": {
      'item-data': 'https://api.dev.iscanbot.com/lyoral-gateway',
      'h5': 'https://doctor-frontend.dev.iscanbot.com/',
      'config': 'https://api.iscanbot.com/configuration-center/',
      'official': 'https://official-website.uat.chohotech.com/static/',
      'trace': 'https://api.dev.iscanbot.com/trace-collector',
      'flag': "dev",
    },
    "alpha": {
      'item-data': 'https://api.alpha.iscanbot.com/lyoral-gateway',
      'h5': 'https://doctor-frontend.alpha.iscanbot.com/',
      'config': 'https://api.iscanbot.com/configuration-center/',
      'official': 'https://official-website.uat.chohotech.com/static/',
      'trace': 'https://api.dev.iscanbot.com/trace-collector-alpha',
      'flag': "alpha",
    },
    "uat": {
      'item-data': 'https://api.uat.iscanbot.com/lyoral-gateway',
      'h5': 'https://doctor-frontend.uat.iscanbot.com/',
      'config': 'https://api.iscanbot.com/configuration-center/',
      'official': 'https://official-website.uat.chohotech.com/static/',
      'trace': 'https://api.uat.iscanbot.com/trace-collector',
      'flag': "uat",
    },
    "uatAp": {
      'item-data': 'https://api.uat-sg.iscanbot.com/lyoral-gateway',
      'h5': 'https://doctor-frontend.uat-sg.iscanbot.com/',
      'config': 'https://api.iscanbot.com/configuration-center/',
      'official': 'https://official-website.uat.chohotech.com/static/',
      'trace': 'https://api.uat.iscanbot.com/trace-collector',
      'flag': "uatAp",
    },
    "prod": {
      'item-data': 'https://api.iscanbot.com/lyoral-gateway',
      'h5': 'https://cn.iscanbot.com/',
      'config': 'https://api.iscanbot.com/configuration-center/',
      'official': 'https://www.chohotech.com/static/',
      'trace': 'https://api.iscanbot.com/trace-collector',
      'flag': "",
    },
  };

  static Map _serverUrlsConfig = {};
  static Dio? _dio;
  static mydio.Dio? _uploadDio;
  static String cacheTempDirPath = "";
  static bool writeResponse4Txt = false;
  static bool isRefreshTokening = false;
  static int refreshTokenTime = 0;

  static Map errorCodeMap = {};

  static getHttpFlag() {
    return _serverUrlsConfig["flag"];
  }

  static getApiHost() {
    return _serverUrlsConfig["item-data"];
  }

  static getH5Host() {
    return _serverUrlsConfig["h5"];
  }

  static getConfigHost() {
    return _serverUrlsConfig["config"];
  }

  static getOfficialHost() {
    return _serverUrlsConfig["official"];
  }

  static getTraceHost() {
    return _serverUrlsConfig["trace"];
  }

  static setProxy(String proxy) {
    if (HHttp.httpEnv == HttpEnv.prod && isEmpty(Global.sharedPrefs.getString("http_env"))) {
      return;
    }
    (_dio!.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (HttpClient client) {
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      client.findProxy = (uri) {
        return "PROXY $proxy";
      };
      logger("setProxy $proxy");
    };
  }

  static initHttp() async {
    if (writeResponse4Txt) {
      Directory tempDir = await getTemporaryDirectory();
      cacheTempDirPath = "${tempDir.path}/http_response";
      Directory httpPathDir = Directory(cacheTempDirPath);
      if (!httpPathDir.existsSync()) {
        await httpPathDir.create();
      }
    }

    // try {
    //   // logger("HTTP-LOG initHttp area: ${getLoginArea()}");
    //   if (getLoginArea() != null) {
    //     int area = getLoginArea()!;
    //     // logger("HTTP-LOG initHttp loginAreasConfig: ${loginAreasConfig["$area"]}");
    //     if (isNotEmpty(loginAreasConfig["$area"])) {
    //       serverConfigs["prod"]["item-data"] = loginAreasConfig["$area"]["host"];
    //       // logger("HTTP-LOG initHttp fromArea: ${serverConfigs["prod"]["item-data"]}");
    //     }
    //   }
    // } catch (ex) {
    //   logger("HTTP-LOG initHttp err: $ex");
    // }

    _serverUrlsConfig = serverConfigs[httpEnv.name];
    // if (HHttp.httpEnv != HttpEnv.prod) {
    String? envName = Global.sharedPrefs.getString("http_env");
    if (isNotEmpty(envName)) {
      _serverUrlsConfig = serverConfigs[envName];
    }
    logger("HTTP-LOG initHttp: $_serverUrlsConfig");
    // }

    _dio = Dio(BaseOptions(
      headers: {
        'Content-type': 'application/json',
        "Accept": "application/json",
        "X-APP-NAME": "LingOral",
        // "Client-Type": "LingOralApp",
        "Language": lang,
        "Version": Global.appVersion,
      },
    ));
  }

  static setToken(token) {
    _dio!.options.headers = {
      'Content-type': 'application/json',
      "Accept": "application/json",
      "X-APP-NAME": "LingOral",
      // "Client-Type": "LingOralApp",
      "Language": lang,
      "Version": Global.appVersion,
    };
    if (isNotEmpty(token)) {
      _dio!.options.headers["X-ZH-TOKEN"] = token;
      // _dio!.options.headers["sessionId"] = token;
    }
    // if (kDebugMode) {
    //   // v4.0.6 网络代理设置办法
    //   // (_dio!.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //   //     (HttpClient client) {
    //   //   client.badCertificateCallback =
    //   //       (X509Certificate cert, String host, int port) => true;
    //   //   client.findProxy = (uri) {
    //   //     // 代理，这里localhost:888需要根据实际情况设置相应代理地址
    //   //     String proxy = 'PROXY *************:8888';
    //   //     return proxy;
    //   //   };
    //   // };
    //
    //   // v5.4.0 网络代理设置办法
    //   _dio!.httpClientAdapter = (IOHttpClientAdapter()..onHttpClientCreate = (client) {
    //     // Config the client.
    //     client.findProxy = (uri) {
    //       // Forward all request to proxy "localhost:8888".
    //       return 'PROXY *************:8888';
    //     };
    //     // You can also create a new HttpClient for Dio instead of returning,
    //     // but a client must being returned here.
    //     return client;
    //   });
    // }
  }

  static List _saveFrontReqList = [];

  static resetCallFrontSaveList() {
    _saveFrontReqList = [];
    User.instance.refreshTokenTime = 0;
    uploadingCaseIds.clear();
  }

  static reCallFrontSaveRequest() {
    for (int i = 0; i < _saveFrontReqList.length; i++) {
      dynamic eachSaveReqParmas = _saveFrontReqList[i];
      if (eachSaveReqParmas != null) {
        request(
          eachSaveReqParmas["reqPath"],
          eachSaveReqParmas["reqType"],
          eachSaveReqParmas["okCallBack"],
          params: eachSaveReqParmas["params"],
          addressType: eachSaveReqParmas["addressType"],
          isFullUrl: eachSaveReqParmas["isFullUrl"],
          reqOptions: eachSaveReqParmas["reqOptions"],
          errCallBack: eachSaveReqParmas["errCallBack"],
          isShowNetWorkLoading: eachSaveReqParmas["isShowNetWorkLoading"],
          isShowErrorToast: eachSaveReqParmas["isShowErrorToast"],
          cbFullResponse: eachSaveReqParmas["cbFullResponse"],
        );
      }
    }
    _saveFrontReqList.removeRange(0, _saveFrontReqList.length);
  }

  // static downImgZipAndUnZipByUrn(downZipUrn, timelineId, callBack) {
  //   String zipName = downZipUrn.substring(downZipUrn.lastIndexOf("/") + 1, downZipUrn.length);
  //   HHttp.reqUrnGetUrl(downZipUrn, (downZipUrl) {
  //     request(downZipUrl, "GET", (zipBytes) {
  //       getTemporaryDirectory().then((appDir) {
  //         String zipPath = '${appDir.path}/$timelineId/$zipName';
  //         File dir = File(zipPath);
  //         if (!dir.existsSync()) {
  //           dir.createSync(recursive: true);
  //         }
  //         File(zipPath).writeAsBytes(zipBytes, flush: true).then((value) {
  //           Global.unZip(zipPath).then((imgList) {
  //             try {
  //               callBack(imgList);
  //             } catch (e) {
  //               if (kDebugMode) {
  //                 print("页面已退出");
  //               }
  //             }
  //           });
  //         });
  //       });
  //     },
  //         cbFullResponse: true,
  //         isFullUrl: true,
  //         reqOptions: Options(
  //           responseType: ResponseType.bytes,
  //           followRedirects: false,
  //         ));
  //   });
  // }

  static getRemoteFileStream(String url, cb) async {
    return Future(() async {
      HttpClient httpClient = HttpClient();
      HttpClientRequest request = await httpClient.getUrl(Uri.parse(url));
      HttpClientResponse response = await request.close();
      logger("getRemoteFileStream $url");
      if (response.statusCode == HttpStatus.ok) {
        List<int> buffer = [];
        response.listen((data) {
          for (var e in data) {
            buffer.add(e);
          }
        }, onDone: () {
          logger("getRemoteFileStream onDone: ${buffer.length}");
          cb(buffer);
        }, onError: (e) {
          logger("getRemoteFileStream error: $e");
        });
      } else {
        logger('getRemoteFileStream request failed ${response.statusCode}');
      }
    });
  }

  static downImgAndGetTempLocalPath(String objId, String downImgUrl, okCallBack, {bool documentDir = false}) {
    String imgName = "";
    Uri uri = Uri.parse(downImgUrl);
    if (isNotEmpty(uri.queryParameters) && isNotEmpty(uri.queryParameters["response-content-disposition"])) {
      String desc = uri.queryParameters["response-content-disposition"]!;
      String fileKey = "filename=";
      if (desc.contains(fileKey)) {
        int start = desc.lastIndexOf(fileKey) + fileKey.length;
        int end = desc.length;
        if (desc.indexOf(";", start) > start) {
          end = desc.indexOf(";", start);
        }
        imgName = desc.substring(start, end);
      }
    }
    if (isEmpty(imgName)) {
      int start = downImgUrl.lastIndexOf("/") + 1;
      int end = downImgUrl.lastIndexOf("?");
      if (end < 0 || end < start) {
        end = downImgUrl.length;
      }
      imgName = downImgUrl.substring(start, end);
    }
    imgName = "${objId}_$imgName";
    logger("downImgAndGetTempLocalPath $imgName");
    getRemoteFileStream(downImgUrl, (imgBytes) {
      (documentDir ? getApplicationDocumentsDirectory() : getTemporaryDirectory()).then((appDir) {
        String path = '${appDir.path}/$imgName';
        File(path).writeAsBytes(imgBytes, flush: true).then((file) {
          okCallBack(file.path);
        });
      });
    });
  }

  static downFileIfNotExist(url, okCallBack, {bool quiet = true, bool forceDownload = false}) {
    String fileName = url.substring(url.lastIndexOf("/") + 1, url.length);
    if (fileName.contains("filename%3D")) {
      fileName = fileName.substring(fileName.indexOf("filename%3D") + "filename%3D".length);
    }
    if (fileName.contains("?")) {
      fileName = fileName.substring(0, fileName.indexOf("?"));
    }
    if (fileName.length > 100) {
      fileName = fileName.substring(fileName.length - 100);
    }
    getTemporaryDirectory().then((appDir) {
      String filePath = '${appDir.path}/$fileName';
      File file = File(filePath);
      logger("downFileIfNotExist $filePath");
      if (!forceDownload && file.existsSync()) {
        okCallBack(filePath);
        return;
      }
      HHttp.request(
        url,
        "GET",
        (imgBytes) {
          File(filePath).writeAsBytes(imgBytes, flush: true).then((file) {
            okCallBack(file.path);
          });
        },
        cbFullResponse: true,
        isFullUrl: true,
        isShowNetWorkLoading: !quiet,
        isShowErrorToast: false,
        reqOptions: Options(
          responseType: ResponseType.bytes,
          followRedirects: false,
        ),
      );
    });
  }

  static downFileWithProgress(url, okCallBack, onProgress) async {
    String fileName = url.substring(url.lastIndexOf("/") + 1, url.length);
    if (fileName.contains("filename%3D")) {
      fileName = fileName.substring(fileName.indexOf("filename%3D") + "filename%3D".length);
    }
    if (fileName.contains("?")) {
      fileName = fileName.substring(0, fileName.indexOf("?"));
    }
    int downloadProgress = 0; //0-100
    getTemporaryDirectory().then((appDir) async {
      String filePath = '${appDir.path}/$fileName';
      File file = File(filePath);
      logger("downFileIfNotExist $filePath");
      // if (file.existsSync()) {
      //   okCallBack(filePath);
      //   return;
      // }
      await _dio!.download(
        url,
        filePath,
        onReceiveProgress: (receivedBytes, totalBytes) {
          if (totalBytes != -1) {
            int progress = 100 * receivedBytes ~/ totalBytes;
            if (progress != downloadProgress) {
              downloadProgress = progress;
              onProgress(downloadProgress);
              if (downloadProgress >= 100) {
                okCallBack(filePath);
              }
            }
          }
        },
      );
    });
  }

  // static reqUrnGetUrl(String urn, okCallBack) {
  //   HHttp.request("/v2/file?urn=$urn&expiration=1800", "GET", (data) {
  //     okCallBack(data["Url"]);
  //   });
  // }

  static getSplicyBase64(String policyText) {
    //进行utf8编码
    List<int> policyText_utf8 = utf8.encode(policyText);
    //进行base64编码
    String policy_base64 = base64.encode(policyText_utf8);
    return policy_base64;
  }

// 使用 HMAC-SHA1 签名算法生成签名
  static String hmacSha1(String value, String key) {
    List<int> keyBytes = utf8.encode(key);
    List<int> valueBytes = utf8.encode(value);
    Hmac hmac = Hmac(sha1, keyBytes);
    Digest digest = hmac.convert(valueBytes);
    return base64.encode(digest.bytes);
  }

  /// 获取签名
  static String getOssSignature(String policyText, String ossAccessKeySecret) {
    List<int> policyText_utf8 = utf8.encode(policyText);
    String policy_base64 = base64.encode(policyText_utf8);
    List<int> policy = utf8.encode(policy_base64);
    List<int> key = utf8.encode(ossAccessKeySecret);
    List<int> signature_pre = Hmac(sha1, key).convert(policy).bytes;
    String signature = base64.encode(signature_pre);
    return signature;
  }

  static uploadFileByServer(
    String filePath,
    String sourceType,
    dynamic params, {
    Function? okCallback,
    Function? errCallback,
    bool showLoading = false,
  }) {
    onError(err) {
      if (errCallback != null) {
        try {
          errCallback(err);
        } catch (e) {
          errCallback();
        }
      }
    }

    if (!File(getLocalPath(filePath)).existsSync()) {
      onError({});
      return;
    }
    String fileName = Global.getFileNameByPath(filePath);

    HHttp.request(
      "/v3/file/getUploadPath?sourceType=$sourceType",
      "POST",
      (data) async {
        // logger("startUploadScanRecord $fileName get-upload-path");
        try {
          if (isNotEmpty(data["list"])) {
            String id = data["fileId"];
            List<String> urls = []; //已上传的url

            String url = data["list"][0]["url"];
            for (dynamic info in data["list"]) {
              if (info["fileType"] == 1) {
                // logger("startUploadScanRecord $fileName getFileType == 1 !");
                url = info["url"];
              }
            }

            checkUploadCount() {
              // if (urls.length == data["list"].length) {
              // logger("startUploadScanRecord $fileName confirm-upload-file");
              HHttp.request(
                "/v3/file/confirmWithCrop",
                "POST",
                (data) {
                  if (okCallback != null) {
                    okCallback(data);
                  }
                },
                errCallBack: onError,
                params: {
                  "fileId": id,
                  "sourceType": sourceType,
                },
              );
              // }
            }

            // for (dynamic info in data["list"]) {
            int time = 3;
            uploadFile(String url, okCallback, errCallback) {
              // logger("startUploadScanRecord uploadFile $fileName");
              HHttp.request(
                url,
                "PUT",
                cbFullResponse: true,
                isFullUrl: true,
                isShowErrorToast: false,
                useUploadDio: true,
                (data) {
                  okCallback();
                },
                errCallBack: (err) {
                  errCallback();
                },
                isShowNetWorkLoading: showLoading,
                params: File(getLocalPath(filePath)).readAsBytesSync(),
              );
            }

            uploadSingleFile(String url) {
              // logger("startUploadScanRecord uploadSingleFile $fileName");
              uploadFile(url, () {
                if (!urls.contains(url)) {
                  urls.add(url);
                  checkUploadCount();
                }
              }, () {
                time--;
                if (time >= 0) {
                  uploadSingleFile(url);
                } else {
                  if (!urls.contains(url)) {
                    urls.add(url);
                    checkUploadCount();
                  }
                }
              });
            }

            // String url = info["url"];
            uploadSingleFile(url);
            // }
          } else {
            onError({});
          }
        } catch (ex) {
          onError({});
        }
      },
      errCallBack: (err) {
        logger("startUploadScanRecord $fileName upload ${err["code"]}");
        if (err["code"] == "1_2_1x3_8") {
          //已经上传过了，直接成功回调
          if (okCallback != null) {
            okCallback(err);
          }
        } else {
          onError(err);
        }
      },
      params: params,
      isShowNetWorkLoading: showLoading,
    );
  }

  static deleteFileById(
    String fileId,
    String sourceType, {
    Function? okCallback,
    Function? errCallback,
  }) {
    onError(err) {
      if (errCallback != null) {
        try {
          errCallback(err);
        } catch (e) {
          errCallback();
        }
      }
    }

    HHttp.request(
      "/v3/file/delete?sourceType=$sourceType",
      "POST",
      okCallback,
      errCallBack: onError,
      params: {
        "fileId": fileId,
        // "sourceType": sourceType,
      },
      isShowNetWorkLoading: false,
    );
  }

  static Future<bool> checkInternetValid() async {
    try {
      //尝试访问百度，也可以是其他服务器
      final result = await InternetAddress.lookup('baidu.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }
    } on SocketException catch (_) {
      return false;
    }
    return false;
  }

  static Future<void> request(
    String reqPath,
    String reqType,
    dynamic okCallBack, {
    dynamic params,
    String addressType = "item-data",
    bool isFullUrl = false,
    Options? reqOptions,
    dynamic errCallBack,
    bool isShowNetWorkLoading = false,
    bool refreshFailLogin = true, //刷新token失败是否退回登录页
    bool isShowErrorToast = true,
    bool cbFullResponse = false, //直接返回http response 内容
    bool useUploadDio = false, //第三方请求，目前只用于PUT
    // bool noNetworkError = true, //没有网络时回调errCallBack
  }) async {
    if (!isFullUrl) {
      isShowErrorToast = false;
    }
    //检测是否有网络
    // bool isHaveNetWork = await checkInternetValid();
    // if (!isHaveNetWork) {
    //   if (isShowErrorToast) {
    //     Global.toast(Lang.no_network);
    //   }
    //   if (errCallBack != null) {
    //     try {
    //       errCallBack({});
    //     } catch (e) {
    //       errCallBack();
    //     }
    //   }
    //   hideNetWorkLoading();
    //   return;
    // }
    //检测是否有设置代理
    // callMcByFunName("checkProxy").then((isHaveProxy) async {
    //   if (isHaveProxy) {
    //     return Global.toast(Lang.httpToastPlsCloseProxy);
    //   } else {
    //
    //   }
    // });
    if (_dio == null) {
      Global.toast("Dio Http haven't init");
    }
    if (reqPath.substring(0, 1) != "/" && !isFullUrl) {
      Global.toast('Error:Reqest Url must have  "/" charset');
    }
    String serverAddress = _serverUrlsConfig[addressType];

    if (!isFullUrl) {
      serverAddress += reqPath;
    } else {
      serverAddress = reqPath;
    }
    if (useUploadDio) {
      _uploadDio ??= mydio.Dio(mydio.BaseOptions(
        contentType: '',
        // headers: {
        //   'Content-Type': 'application/octet-stream',
        // },
      ));
      loggerHttp(
          "_otherDio请求完整地址：$serverAddress\n"
          "请求类型：$reqType\n"
          "请求headers: ${_uploadDio!.options.headers}\n"
          "请求Options:${reqOptions == null ? "无" : "有"}\n"
          "请求内容：${params == null ? "无" : params.length}ㅤ",
          key: "HTTP-LOG");
    } else {
      loggerHttp(
          "请求完整地址：$serverAddress\n"
          "请求类型：$reqType\n"
          "请求headers: ${_dio!.options.headers}\n"
          "请求Options:${reqOptions == null ? "无" : "有"}\n"
          "请求内容：${params == null ? "无" : (params is Map ? json.encode(params) : params.toString())}ㅤ",
          key: "HTTP-LOG");
    }

    if (isShowNetWorkLoading) {
      showNetWorkLoading();
    }
    try {
      _dio!.options.headers["Language"] = lang;
      _dio!.options.headers["Version"] = Global.appVersion;
      if (HHttp.httpEnv == HttpEnv.prod && isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
        _dio!.options.headers.remove("Client-Type");
      }
      dynamic response;
      if (reqType == "PUT") {
        if (useUploadDio) {
          response = await _uploadDio!.put(serverAddress, data: params);
        } else {
          response = await _dio!.put(serverAddress, data: params, options: reqOptions);
        }
      } else if (reqType == "POST") {
        response = await _dio!.post(serverAddress, data: params, options: reqOptions);
      } else if (reqType == "PATCH") {
        response = await _dio!.patch(serverAddress, data: params, options: reqOptions);
      } else if (reqType == "DELETE") {
        response = await _dio!.delete(serverAddress, data: params, options: reqOptions);
      } else {
        response = await _dio!.get(serverAddress, options: reqOptions);
      }
      logger("HTTP-LOG response ${response.statusCode}");
      if (kDebugMode) {
        if (reqOptions != null && reqOptions.responseType == ResponseType.bytes ||
            reqPath.contains("aliyuncs.com") ||
            reqPath.startsWith(getConfigHost())) {
          logger("HTTP-LOG HTTP数据不展示 $reqPath");
        } else {
          if (reqPath.contains(".zip?")) {
            loggerHttp("HTTP-LOG response:$reqPath unzip...");
          } else {
            loggerHttp("HTTP-LOG response for $reqType $reqPath:");
            try {
              loggerJson(jsonEncode(response.data));
            } catch (e) {
              loggerHttp(e.toString(), key: "HTTP-LOG");
            }
          }
        }
      }
      if (cbFullResponse || response.data["code"] == "0") {
        if (okCallBack != null) {
          try {
            okCallBack(cbFullResponse ? response.data : response.data["data"]);
          } catch (e) {
            okCallBack();
          }
        }
      } else {
        if (!isFullUrl) {
          if (logText.split("时间").length > 500) {
            logText = "";
          }
          logText =
              "\n时间: ${Global.getLoggerTime(milliseconds: DateTime.now().millisecondsSinceEpoch, ignoreSameDay: false)}\n请求: $serverAddress  $reqType\n头: ${_dio!.options.headers}\n参数: $params\n响应: $response\n$logText";
        }
        //refresh token
        if (!isFullUrl &&
            (response.data["code"].toString().startsWith("1_2_4x20") ||
                response.data["code"].toString().startsWith("1_2_9x20"))) {
          if (reqPath != "/v3/auth/refreshToken") {
            _saveFrontReqList.add({
              "reqPath": reqPath,
              "reqType": reqType,
              "okCallBack": okCallBack,
              "params": params,
              "addressType": addressType,
              "isFullUrl": isFullUrl,
              "reqOptions": reqOptions,
              "errCallBack": errCallBack,
              "isShowNetWorkLoading": isShowNetWorkLoading,
              "isShowErrorToast": isShowErrorToast,
              "cbFullResponse": cbFullResponse
            });
            if (!isRefreshTokening || DateTime.now().millisecondsSinceEpoch - refreshTokenTime > 10000) {
              isRefreshTokening = true;
              refreshTokenTime = DateTime.now().millisecondsSinceEpoch;
              User.instance.refreshToken(
                okCallback: () {
                  isRefreshTokening = false;
                },
              );
            }
            // } else {
            //   User.instance.clearUserData(forceLogout: true);
          }
        } else if (!isFullUrl &&
            response.data["code"] == "1_2_4x3" &&
            isNotEmpty(currentUser.personName) &&
            isEmpty(currentUser.accessToken)) {
          //缺少必要数据或者数据格式不符合预期，如Header中没检测到sessionId或X-ZH-TOKEN
          User.instance.clearUserData(forceLogout: true);
        } else {
          if (errCallBack != null) {
            try {
              errCallBack(response.data);
            } catch (e) {
              errCallBack();
            }
          } else {
            if (isShowErrorToast) {
              toastError(response.data);
            }
          }
          if (!isFullUrl) {
            String path = reqPath;
            try {
              List<String> paths = path.split("/");
              path = "${paths[paths.length - 2]}_${paths.last}";
            } catch (ex) {
              path = reqPath.replaceAll("/", "_");
            }
            logger("UmengCommonSdk.onEvent: ${Global.sharedPrefs.getString("http_env") ?? httpEnv.name}_$path");
            UmengCommonSdk.onEvent(
              "${Global.sharedPrefs.getString("http_env") ?? httpEnv.name}_$path",
              {
                "http_request":
                    "version: ${Global.appVersion}, session_id: ${currentUser.accessToken}, params: $params, response: ${response.data}",
              },
            );
          }
        }
      }
    } catch (e) {
      if (isShowErrorToast) {
        bool isHaveNetWork = await checkInternetValid();
        if (!isHaveNetWork) {
          Global.toast(Lang.no_network);
        } else {
          Global.toast(Lang.internal_error_retry);
        }
      }
      if (errCallBack != null) {
        try {
          errCallBack({});
        } catch (e) {
          errCallBack();
        }
      }
      // if (e is DioError) {
      //   loggerHttp("exception: ${e.response}", key: "HTTP-LOG");
      // } else {
      loggerHttp("exception: $reqPath $e", key: "HTTP-LOG");
      // }
    } finally {
      hideNetWorkLoading();
    }
  }

  //Loading
  static dynamic networkLoadingCancelFun;
  static dynamic networkTimer;

  static showNetWorkLoading({second = 10}) {
    if (networkTimer != null) {
      networkTimer!.cancel();
      networkTimer = null;
    }
    //500ms 在显示
    networkTimer = Timer(const Duration(milliseconds: 500), () {
      showNetWorkLoadingUI(second: second);
    });
  }

  static showNetWorkLoadingUI({second = 10}) {
    try {
      if (networkLoadingCancelFun != null) {
        networkLoadingCancelFun();
      }
      networkLoadingCancelFun = BotToast.showLoading(duration: Duration(seconds: second));
      // BotToast.showCustomLoading(
      //     duration: Duration(seconds: second),
      //     enableKeyboardSafeArea: false,
      //     toastBuilder: (cancelFunc) {
      //       return Container(
      //         padding: const EdgeInsets.all(15),
      //         decoration: const BoxDecoration(
      //             color: Colors.black54,
      //             borderRadius: BorderRadius.all(Radius.circular(8))),
      //         child: const CircularProgressIndicator(
      //           backgroundColor: Colors.white,
      //         ),
      //       );
      //     });
    } catch (e) {
      if (kDebugMode) {
        print("Show NetWork Loading Erro :${e.toString()}");
      }
    }
  }

  static hideNetWorkLoading({second = 10}) {
    if (networkTimer != null) {
      networkTimer!.cancel();
      networkTimer = null;
    }
    if (networkLoadingCancelFun != null) {
      networkLoadingCancelFun();
    }
  }

  static String getMD5ValueByStr(String tStr) {
    Digest digest = md5.convert(utf8.encode(tStr));
    return digest.toString();
  }

  static void toastError(resp) {
    if (resp == {} || resp["code"].contains("2_2_1x20") || resp["code"].contains("3_2_1x20")) {
      //刷新token，不提示错误信息
      return;
    }
    Global.toast(getErrorMessage(resp));
  }

  static String getErrorMessage(resp) {
    try {
      String code = resp["code"];
      if (code.contains("x")) {
        List<String> codes = code.split("x");
        if (errorCodeMap[codes[0]][code] != null) {
          return errorCodeMap[codes[0]][code][getErrorLangKey()];
        } else {
          return errorCodeMap["common"][codes[1]][getErrorLangKey()];
        }
      }
    } catch (ex) {
      //
    }
    return Lang.internal_error_retry;
  }

  static bool isMailError(String code) {
    switch (code) {
      case "3_2_1x5_1":
      case "3_2_1x6_2":
        return true;
    }
    return false;
  }

  static bool isCodeError(String code) {
    if (code.contains("3_2_1x11")) {
      return true;
    }
    return false;
  }

  static bool isPasswordError(String code) {
    if (code.contains("3_2_1x17")) {
      return true;
    }
    return false;
  }
}
