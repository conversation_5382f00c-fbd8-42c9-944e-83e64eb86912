import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/xianzong_manager.dart';
import 'package:mooeli/model/user_info.dart';
import 'package:mooeli/pages/main_tabs.dart';
import 'package:mooeli/pages/my/http_log.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

UserInfo currentUser = UserInfo();

void saveCurrentToken(dynamic data) {
  if (isNotEmpty(getJsonString(data, "accessToken"))) {
    currentUser.accessToken = getJsonString(data, "accessToken");
  }
  if (isNotEmpty(getJsonString(data, "refreshToken"))) {
    currentUser.refreshToken = getJsonString(data, "refreshToken");
  }
  Global.sharedPrefs.setString("login_user", jsonEncode(currentUser));
  HHttp.setToken(currentUser.accessToken);
}

class User {
  static final User _user = User._internal();

  factory User() {
    return _user;
  }

  static User get instance => _user;

  User._internal();

  Future<bool> init() async {
    // Global.sharedPrefs.remove("USER_DATAS");
    // return true;

    final jsonStr = Global.sharedPrefs.getString("login_user");
    if (isNotEmpty(jsonStr)) {
      final json = jsonDecode(jsonStr!);
      currentUser = UserInfo.fromJson(json);
      HHttp.setToken(currentUser.accessToken);
      return false;
    }
    return true;
  }

  _clearUserDataTrue({bool showAlert = false, String? alertMsg}) {
    //清空可能保留在的协议队列（因为在refresh失败的情况下，这里的list会被保留，账号或其他参数等信息也是之前的）
    HHttp.resetCallFrontSaveList();
    //重置token
    HHttp.setToken("");
    currentUser = UserInfo();
    logger("UserInfo clear");
    Global.sharedPrefs.remove("login_user");
    Global.globalNavigator.currentState?.pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => MainTabs(key: Global.mainTabGlobalKey)), (router) => false);
    //清理缓存数据
    Global.clearTempScanCacheData();
    Global.sharedPrefs.remove("login_account");
    Global.sharedPrefs.remove("login_password");
    Global.sharedPrefs.remove("login_time");
    FlutterBugly.setUserId("");
    UmengCommonSdk.onProfileSignOff();
  }

  int logoutTime = 0;

  clearUserData({bool forceLogout = false, bool logoff = false, String? alertMsg}) {
    int time = DateTime.now().millisecondsSinceEpoch;
    if (time - logoutTime > 2000) {
      logoutTime = time;
      if (!logoff) {
        HHttp.request(
          "/v3/auth/logout",
          "POST",
          (resp) {},
          isShowErrorToast: false,
          params: {},
        );
      }
      Future.delayed(const Duration(milliseconds: 500), () {
        _clearUserDataTrue(showAlert: forceLogout, alertMsg: alertMsg);
      });
    }
  }

  String getDisplayName() {
    return currentUser.username;
  }

  getUserData(String key) {
    return "";
  }

  int refreshTokenTime = 0;

  refreshToken({dynamic okCallback}) {
    logger("UserInfo refreshToken 111: ${currentUser.refreshToken}");
    int time = DateTime.now().millisecondsSinceEpoch;
    if (time - refreshTokenTime < 5000) {
      if (okCallback != null) {
        okCallback();
      }
      return;
    }

    // HHttp.request("/v3/auth/usernameLogin", "POST", (data) {
    logger("UserInfo refreshToken 222: ${currentUser.refreshToken}");
    HHttp.request("/v3/auth/refreshToken", "POST", (data) {
      logger("UserInfo refreshToken: ${currentUser.refreshToken}\ndata:$data");
      logText =
          "\n时间: ${Global.getLoggerTime(milliseconds: DateTime.now().millisecondsSinceEpoch, ignoreSameDay: false)}\nrefreshToken: ${currentUser.refreshToken}\ndata:$data\n$logText";

      refreshTokenTime = DateTime.now().millisecondsSinceEpoch;
      modifyUserDataByKvs(data);
      saveCurrentToken(data);
      eventBus.fire(EventRelogin());
      Global.sharedPrefs.setInt("login_time", DateTime.now().millisecondsSinceEpoch);
      getXzSecretKey();
      //recall
      HHttp.reCallFrontSaveRequest();
      logger("UserInfo refreshToken ok");
      if (okCallback != null) {
        okCallback();
      }
    }, errCallBack: (err) {
      logText =
          "\n时间: ${Global.getLoggerTime(milliseconds: DateTime.now().millisecondsSinceEpoch, ignoreSameDay: false)}\nrefreshToken: ${currentUser.refreshToken}\nerror:$err\n$logText";

      logger("UserInfo refreshToken: ${currentUser.refreshToken} error:$err");
      if (err["code"].toString().startsWith("3_2_1x20_") ||
          err["code"].toString().startsWith("3_2_3x20_") ||
          err["code"].toString().startsWith("3_2_3x5_1") ||
          err["code"].toString().startsWith("1_2_9x20_")) {
        User.instance.clearUserData(forceLogout: true);
      } else {
        HHttp.toastError(err);
      }
    }, params: {
      "refreshToken": currentUser.refreshToken,
      // "username": account,
      // "password": password,
    });
  }

  //请求个人信息和手机号
  requestNewestUserInfoAndPhoneNum({dynamic getInfoSuccessCallBack}) {}

  Widget getUserAvatar(double size) {
    String localAvatarPath = currentUser.avatarPath;
    if (isNotEmpty(localAvatarPath)) {
      return ClipRRect(
          borderRadius: BorderRadius.circular(size),
          child: Image.file(File(localAvatarPath), height: size, width: size, fit: BoxFit.cover));
    } else {
      return ClipRRect(
          borderRadius: BorderRadius.circular(size),
          child: Image.asset("res/imgs/default_avatar.png", height: size, width: size));
    }
  }

  //请求服务器修改个人信息相关的统一操作，修改成功后自动修改本地缓存
  reqModifyUserDataByKvs(Map kvs, successCallBack) {}

  refreshUserTokens(Map data) {}

  modifyUserDataByKvs(Map modifyKvs) {}
}
