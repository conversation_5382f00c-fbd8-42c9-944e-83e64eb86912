//仙踪埋点工具类

import 'package:mooeli/user/user.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';

int sequenceCount = 0;
bool init = false;

initXianzong() async {
  sequenceCount = 0;
  getXzSecretKey();
}

getXzSecretKey({dynamic onComplete}) {
  HHttp.request(
    "/v3/track/traceLogin",
    "POST",
    (res) {
      init = true;
      if (onComplete != null) {
        onComplete();
      }
    },
    params: {},
    isShowErrorToast: false,
    isShowNetWorkLoading: false,
  );
}

addEventTrace(Map param) async {
  if (!init) {
    getXzSecretKey(onComplete: () {
      sendEventTrace(param);
    });
  } else {
    sendEventTrace(param);
  }
}

sendEventTrace(Map param) async {
  param["serverCode"] = "1_1_1";
  param["serverVersion"] = Global.appVersion;
  param["accessToken"] = currentUser.accessToken;
  param["user_id"] = currentUser.personId;
  param["timestamp"] = DateTime.now().millisecondsSinceEpoch;
  param["sequenceCount"] = sequenceCount++;
  bool isHaveNetWork = await HHttp.checkInternetValid();
  if (isHaveNetWork) {
    HHttp.request(
      "${HHttp.getTraceHost()}/v1/trace/send",
      "POST",
      (res) {},
      params: param,
      isFullUrl: true,
      isShowErrorToast: false,
      isShowNetWorkLoading: false,
    );
  }
}
