import 'dart:math' as math;

import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/calculate_data.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';

class CalculateTool100 {
  CalculateTool100._internal();

  static final CalculateTool100 instance = CalculateTool100._internal();

  double getNum(double num) {
    return double.parse(num.toStringAsFixed(2));
  }

  double getPositive_face(num) {
    if (num < 0) num = num * -1;
    return num;
  }

  double getSlope_face(p1, p2) {
    double slope = (p1[1] - p2[1]) / (p1[0] - p2[0]);
    return slope;
  }

  double getAngle_face(slope1, slope2) {
    double tan = (slope1 - slope2) / (1 + (slope1 * slope2));
    double angle = (math.atan(tan)) * 180 / math.pi;
    return angle;
  }

  getCommit_face(res) {
    var commit = 0;
    if (res > 4 || res < -4) {
      commit = 1;
    }
    return commit.toString();
  }

  getCalculationResult(int algorithm, List points, Target100 target, type, midLine) {
    switch (algorithm) {
      case 1:
        return calculation_face_1(points, target, type, midLine);
      case 2:
        return calculation_face_2(points, target, type, midLine);
      case 3:
        return calculation_face_3(points, target, type, midLine);
      case 4:
        return calculation_face_4(points, target, type, midLine);
      case 5:
        return calculation_face_5(points, target, type, midLine);
      case 6:
        return calculation_face_6(points, target, type, midLine);
      default:
        return null;
    }
  }

  // 大三庭计算+注释生成
  CalResult100 calculation_face_1(List pointArray, Target100 target, type, midLineSlope) {
    var Tr_G = pointArray[0][1] - pointArray[1][1];
    var G_Sn = pointArray[2][1] - pointArray[3][1];
    var Sn_Me = pointArray[4][1] - pointArray[5][1];
    var avg = (Tr_G + G_Sn + Sn_Me) / 3;
    var a = getPositive_face(Tr_G - avg);
    var b = getPositive_face(G_Sn - avg);
    var c = getPositive_face(Sn_Me - avg);
    var res = "";
    var commit = "";
    var max = target.max;
    var min = target.min;
    if (a < b && a < c) {
      res = "1:${getNum(G_Sn / Tr_G)}:${getNum(Sn_Me / Tr_G)}";
      var res1 = '';
      var res2 = '';
      var res3 = '';
      var res4 = '';
      if (getNum(G_Sn / Tr_G) > max) {
        res1 = target.describe3;
      }
      if (getNum(G_Sn / Tr_G) < min) {
        res2 = target.describe4;
      }
      if (getNum(Sn_Me / Tr_G) > max) {
        res3 = target.describe5;
      }
      if (getNum(Sn_Me / Tr_G) < min) {
        res4 = target.describe6;
      }
      commit = res1 + res2 + res3 + res4;
    }
    if (b < a && b < c) {
      res = "${getNum(Tr_G / G_Sn)}:1:${getNum(Sn_Me / G_Sn)}";
      var res1 = '';
      var res2 = '';
      var res3 = '';
      var res4 = '';
      if (getNum(Tr_G / G_Sn) > max) {
        res1 = target.describe1;
      }
      if (getNum(Tr_G / G_Sn) < min) {
        res2 = target.describe2;
      }
      if (getNum(Sn_Me / G_Sn) > max) {
        res3 = target.describe5;
      }
      if (getNum(Sn_Me / G_Sn) < min) {
        res4 = target.describe6;
      }
      commit = res1 + res2 + res3 + res4;
    }
    if (c < a && c < b) {
      res = "${getNum(Tr_G / Sn_Me)}:${getNum(G_Sn / Sn_Me)}:1";
      var res1 = '';
      var res2 = '';
      var res3 = '';
      var res4 = '';
      if (getNum(Tr_G / Sn_Me) > max) {
        res1 = target.describe1;
      }
      if (getNum(Tr_G / Sn_Me) < min) {
        res2 = target.describe2;
      }
      if (getNum(G_Sn / Sn_Me) > max) {
        res3 = target.describe3;
      }
      if (getNum(G_Sn / Sn_Me) < min) {
        res4 = target.describe4;
      }
      commit = res1 + res2 + res3 + res4;
    }
    return CalResult100(res: res, commit: commit);
  }

  // 小三庭&&黄金比例部分(不包括长宽比&&眼角距离口角距离)&&唇形 计算+注释生成
  CalResult100 calculation_face_2(List pointArray, Target100 target, type, midLineSlope) {
    var varTop = pointArray[0][1] - pointArray[1][1];
    var varBottom = pointArray[2][1] - pointArray[3][1];
    var max = target.max;
    var min = target.min;
    var temp = getNum(1 / (varTop / varBottom));
    var res = '';
    var commit = '';
    if (type == 1) {
      temp = getNum(varTop / varBottom);
      res = "$temp:1";
      if (temp > max) {
        commit = target.describe1;
      } else if (temp < min) {
        commit = target.describe2;
      } else {
        commit = '';
      }
    } else {
      temp = getNum(1 / (varTop / varBottom));
      res = "1:$temp";
      if ((1 / temp) > max) {
        commit = target.describe1;
      } else if ((1 / temp) < min) {
        commit = target.describe2;
      } else {
        commit = '';
      }
    }
    return CalResult100(res: res, commit: commit);
  }

  // 五眼计算+注释生成
  CalResult100 calculation_face_3(List pointArray, Target100 target, type, midLineSlope) {
    var ZyR_ExR = pointArray[0][0] - pointArray[1][0];
    var ExR_EnR = pointArray[2][0] - pointArray[3][0];
    var EnR_EnL = pointArray[4][0] - pointArray[5][0];
    var EnL_ExL = pointArray[6][0] - pointArray[7][0];
    var EnL_ZyL = pointArray[8][0] - pointArray[9][0];
    var avg2 = (ZyR_ExR + ExR_EnR + EnR_EnL + EnL_ExL + EnL_ZyL) / 5;
    var ZyR_ExR_var = getPositive_face(ZyR_ExR - avg2);
    var ExR_EnR_var = getPositive_face(ExR_EnR - avg2);
    var EnR_EnL_var = getPositive_face(EnR_EnL - avg2);
    var EnL_ExL_var = getPositive_face(EnL_ExL - avg2);
    var EnL_ZyL_var = getPositive_face(EnL_ZyL - avg2);
    var res = "";
    var commit = "";
    var max = target.max;
    var min = target.min;
    if (ZyR_ExR_var < ExR_EnR_var &&
        ZyR_ExR_var < EnR_EnL_var &&
        ZyR_ExR_var < EnL_ExL_var &&
        ZyR_ExR_var < EnL_ZyL_var) {
      res =
          "1:${getNum(ExR_EnR / ZyR_ExR)}:${getNum(EnR_EnL / ZyR_ExR)}:${getNum(EnL_ExL / ZyR_ExR)}:${getNum(EnL_ZyL / ZyR_ExR)}";

      var commit1 = '';
      var commit2 = '';
      var commit3 = '';
      var commit4 = '';
      var commit5 = '';
      var commit6 = '';
      var commit7 = '';
      var commit8 = '';
      if (getNum(ExR_EnR / ZyR_ExR) > max) {
        commit1 = target.describe3;
      }
      if (getNum(ExR_EnR / ZyR_ExR) < min) {
        commit2 = target.describe4;
      }
      if (getNum(EnR_EnL / ZyR_ExR) > max) {
        commit3 = target.describe5;
      }
      if (getNum(EnR_EnL / ZyR_ExR) < min) {
        commit4 = target.describe6;
      }
      if (getNum(EnL_ExL / ZyR_ExR) > max) {
        commit5 = target.describe7;
      }
      if (getNum(EnL_ExL / ZyR_ExR) < min) {
        commit6 = target.describe8;
      }
      if (getNum(EnL_ZyL / ZyR_ExR) > max) {
        commit7 = target.describe9;
      }
      if (getNum(EnL_ZyL / ZyR_ExR) < min) {
        commit8 = target.describe10;
      }
      commit = commit1 + commit2 + commit3 + commit4 + commit5 + commit6 + commit7 + commit8;
    }
    if (ExR_EnR_var < ZyR_ExR_var &&
        ExR_EnR_var < EnR_EnL_var &&
        ExR_EnR_var < EnL_ExL_var &&
        ExR_EnR_var < EnL_ZyL_var) {
      res =
          "${getNum(ZyR_ExR / ExR_EnR)}:1:${getNum(EnR_EnL / ExR_EnR)}:${getNum(EnL_ExL / ExR_EnR)}:${getNum(EnL_ZyL / ExR_EnR)}";
      var commit1 = '';
      var commit2 = '';
      var commit3 = '';
      var commit4 = '';
      var commit5 = '';
      var commit6 = '';
      var commit7 = '';
      var commit8 = '';
      if (getNum(ZyR_ExR / ExR_EnR) > max) {
        commit1 = target.describe1;
      }
      if (getNum(ZyR_ExR / ExR_EnR) < min) {
        commit2 = target.describe2;
      }
      if (getNum(EnR_EnL / ExR_EnR) > max) {
        commit3 = target.describe5;
      }
      if (getNum(EnR_EnL / ExR_EnR) < min) {
        commit4 = target.describe6;
      }
      if (getNum(EnL_ExL / ExR_EnR) > max) {
        commit5 = target.describe7;
      }
      if (getNum(EnL_ExL / ExR_EnR) < min) {
        commit6 = target.describe8;
      }
      if (getNum(EnL_ZyL / ExR_EnR) > max) {
        commit7 = target.describe9;
      }
      if (getNum(EnL_ZyL / ExR_EnR) < min) {
        commit8 = target.describe10;
      }
      commit = commit1 + commit2 + commit3 + commit4 + commit5 + commit6 + commit7 + commit8;
    }
    if (EnR_EnL_var < ZyR_ExR_var &&
        EnR_EnL_var < ExR_EnR_var &&
        EnR_EnL_var < EnL_ExL_var &&
        EnR_EnL_var < EnL_ZyL_var) {
      res =
          "${getNum(ZyR_ExR / EnR_EnL)}:${getNum(ExR_EnR / EnR_EnL)}:1:${getNum(EnL_ExL / EnR_EnL)}:${getNum(EnL_ZyL / EnR_EnL)}";
      var commit1 = '';
      var commit2 = '';
      var commit3 = '';
      var commit4 = '';
      var commit5 = '';
      var commit6 = '';
      var commit7 = '';
      var commit8 = '';
      if (getNum(ExR_EnR / EnR_EnL) > max) {
        commit1 = target.describe3;
      }
      if (getNum(ExR_EnR / EnR_EnL) < min) {
        commit2 = target.describe4;
      }
      if (getNum(ZyR_ExR / EnR_EnL) > max) {
        commit3 = target.describe1;
      }
      if (getNum(ZyR_ExR / EnR_EnL) < min) {
        commit4 = target.describe2;
      }
      if (getNum(EnL_ExL / EnR_EnL) > max) {
        commit5 = target.describe7;
      }
      if (getNum(EnL_ExL / EnR_EnL) < min) {
        commit6 = target.describe8;
      }
      if (getNum(EnL_ZyL / EnR_EnL) > max) {
        commit7 = target.describe9;
      }
      if (getNum(EnL_ZyL / EnR_EnL) < min) {
        commit8 = target.describe10;
      }
      commit = commit1 + commit2 + commit3 + commit4 + commit5 + commit6 + commit7 + commit8;
    }
    if (EnL_ExL_var < ZyR_ExR_var &&
        EnL_ExL_var < ExR_EnR_var &&
        EnL_ExL_var < EnR_EnL_var &&
        EnL_ExL_var < EnL_ZyL_var) {
      res =
          "${getNum(ZyR_ExR / EnL_ExL)}:${getNum(ExR_EnR / EnL_ExL)}:${getNum(EnR_EnL / EnL_ExL)}:1:${getNum(EnL_ZyL / EnL_ExL)}";
      var commit1 = '';
      var commit2 = '';
      var commit3 = '';
      var commit4 = '';
      var commit5 = '';
      var commit6 = '';
      var commit7 = '';
      var commit8 = '';
      if (getNum(ExR_EnR / EnL_ExL) > max) {
        commit1 = target.describe3;
      }
      if (getNum(ExR_EnR / EnL_ExL) < min) {
        commit2 = target.describe4;
      }
      if (getNum(ZyR_ExR / EnL_ExL) > max) {
        commit3 = target.describe1;
      }
      if (getNum(ZyR_ExR / EnL_ExL) < min) {
        commit4 = target.describe2;
      }
      if (getNum(EnR_EnL / EnL_ExL) > max) {
        commit5 = target.describe5;
      }
      if (getNum(EnR_EnL / EnL_ExL) < min) {
        commit6 = target.describe6;
      }
      if (getNum(EnL_ZyL / EnL_ExL) > max) {
        commit7 = target.describe9;
      }
      if (getNum(EnL_ZyL / EnL_ExL) < min) {
        commit8 = target.describe10;
      }
      commit = commit1 + commit2 + commit3 + commit4 + commit5 + commit6 + commit7 + commit8;
    }
    if (EnL_ZyL_var < ZyR_ExR_var &&
        EnL_ZyL_var < ExR_EnR_var &&
        EnL_ZyL_var < EnR_EnL_var &&
        EnL_ZyL_var < EnL_ExL_var) {
      res =
          "${getNum(ZyR_ExR / EnL_ExL)}:${getNum(ExR_EnR / EnL_ExL)}:${getNum(EnR_EnL / EnL_ExL)}:${getNum(EnL_ExL / EnL_ExL)}:1";
      var commit1 = '';
      var commit2 = '';
      var commit3 = '';
      var commit4 = '';
      var commit5 = '';
      var commit6 = '';
      var commit7 = '';
      var commit8 = '';
      if (getNum(ExR_EnR / EnL_ZyL) > max) {
        commit1 = target.describe3;
      }
      if (getNum(ExR_EnR / EnL_ZyL) < min) {
        commit2 = target.describe4;
      }
      if (getNum(ZyR_ExR / EnL_ZyL) > max) {
        commit3 = target.describe1;
      }
      if (getNum(ZyR_ExR / EnL_ZyL) < min) {
        commit4 = target.describe2;
      }
      if (getNum(EnR_EnL / EnL_ZyL) > max) {
        commit5 = target.describe5;
      }
      if (getNum(EnR_EnL / EnL_ZyL) < min) {
        commit6 = target.describe6;
      }
      if (getNum(EnL_ExL / EnL_ZyL) > max) {
        commit7 = target.describe7;
      }
      if (getNum(EnL_ExL / EnL_ZyL) < min) {
        commit8 = target.describe8;
      }
      commit = commit1 + commit2 + commit3 + commit4 + commit5 + commit6 + commit7 + commit8;
    }
    return CalResult100(res: res, commit: commit);
  }

  // 长宽比&&眼角口角
  CalResult100 calculation_face_4(List pointArray, Target100 target, type, midLineSlope) {
    var varTop = pointArray[0][0] - pointArray[1][0];
    var varBottom = pointArray[2][1] - pointArray[3][1];
    var temp = getNum(varTop / varBottom);
    var max = target.max;
    var min = target.min;
    var res = '';
    var commit = '';
    // 长宽比
    if (type == 1) {
      temp = getNum(varTop / varBottom);
      res = "$temp:1";
      commit = '';
      if (temp > max) {
        commit = target.describe1;
      } else if (temp < min) {
        commit = target.describe2;
      } else {
        commit = '';
      }
    } else {
      // 眼角口角
      varTop = pointArray[0][0] - pointArray[1][0];
      varBottom = pointArray[3][0] - pointArray[2][0];
      temp = getNum(1 / (varTop / varBottom));
      res = "1:$temp";
      if (getNum(1 / temp) > max) {
        commit = target.describe1;
      } else if (getNum(1 / temp) < min) {
        commit = target.describe2;
      } else {
        commit = '';
      }
    }
    return CalResult100(res: res, commit: commit);
  }

  // 对称性(除偏差)
  CalResult100 calculation_face_5(List pointArray, Target100 target, type, midLineSlope) {
    var midLineLevelSlope = -1 / midLineSlope;
    var targetSlope = getSlope_face(pointArray[0], pointArray[1]);
    var res = "";
    var commit = '';
    if (type == 1) {
      res = "${(getAngle_face(targetSlope, midLineLevelSlope)).toStringAsFixed(2)}°";
      // commit = getCommit_face(getAngle_face(targetSlope, midLineLevelSlope));
    } else {
      res = "${(getAngle_face(targetSlope, midLineSlope)).toStringAsFixed(2)}°";
      // commit = getCommit_face(getAngle_face(targetSlope, midLineSlope));
    }

    return CalResult100(res: res, commit: commit);
  }

  // 偏差
  CalResult100 calculation_face_6(List pointArray, Target100 target, type, midLineSlope) {
    var res = "";
    var commit = '';
    if (type == 1) {
      var angleLeft =
          getAngle_face(getSlope_face(pointArray[0], pointArray[1]), getSlope_face(pointArray[1], pointArray[2]));
      var angleRight =
          getAngle_face(getSlope_face(pointArray[3], pointArray[4]), getSlope_face(pointArray[4], pointArray[5]));
      res = "${-getNum(-angleLeft - angleRight)}°";
      // commit = getCommit_face(-getNum((-angleLeft - angleRight)));
    } else {
      var angleLeft = getAngle_face(getSlope_face(pointArray[1], pointArray[0]), midLineSlope);
      var angleRight = getAngle_face(getSlope_face(pointArray[3], pointArray[2]), midLineSlope);
      res = "${-getNum(-angleLeft - angleRight)}°";
      // commit = getCommit_face(-getNum((-angleLeft - angleRight)));
    }
    return CalResult100(res: res, commit: commit);
  }
}

class CalculateTool101 {
  CalculateTool101._internal();

  static final CalculateTool101 instance = CalculateTool101._internal();

  getCalculationResult(int algorithm, List pointArray, List target, type) {
    switch (algorithm) {
      case 1:
        return calculation_1(pointArray, target, type);
      case 2:
        return calculation_2(pointArray, target, type);
      case 3:
        return calculation_3(pointArray, target, type);
      default:
        return null;
    }
  }

//z角,软组织面角,上唇倾角,上唇突角,下唇倾角,下唇突角,颏沟倾角使用
//pointArray, Point列表， Point是List结构[x,y]
//target - 取值范围[最小值，最大值，标准值，偏差值]
//type - "algorithm": "2(1)"括号内的数字，默认为0
  CalResult101 calculation_1(List pointArray, List target, type) {
    double fhsLope = getSlope(pointArray[0], pointArray[1]);
    double angle = 0;
    int commit = 0;
    // 颏沟倾角, 下唇突角, 下唇倾角, ////////////////////////////
    if (type == 1) {
      double targetSlope = getSlope(pointArray[2], pointArray[3]);
      angle = getAngle(fhsLope, targetSlope);
      double resRotate = getAngle(targetSlope, 0);
      angle = (calcAngle_1(angle, resRotate)).angle;
      commit = getCommit(angle, target);
    }
    // 上唇突角, 上唇倾角, 软组织面角, z角
    else {
      double targetSlope = getSlope(pointArray[2], pointArray[3]);
      angle = calcAngle_1(getAngle(fhsLope, targetSlope), 0).angle;
      commit = getCommit(angle, target);
    }
    return CalResult101(angle: angle, commit: commit);
  }

//全面突角, 面突角, 鼻唇角使用
  CalResult101 calculation_2(List pointArray, target, type) {
    int commit = 0;
    double angle = 0;
    double slope1 = getSlope(pointArray[0], pointArray[1]);
    double slope2 = getSlope(pointArray[1], pointArray[2]);
    double tempAngle = getAngle(slope1, slope2);
    double rotate = getAngle(slope2, 0);
    // 鼻唇角
    if (type == 1) {
      rotate = getAngle(slope1, 0);
      angle = (calcAngle_4(slope1, slope2, tempAngle, rotate)).angle;
      commit = getCommit(angle, target);
    }
    // 全面突角, 面突角
    else {
      rotate = getAngle(slope2, 0);
      angle = (calcAngle_2(slope1, slope2, tempAngle, rotate)).angle;
      commit = getCommit(angle, target);
    }
    return CalResult101(angle: angle, commit: commit);
  }

// 上下唇突角, 上唇颏突角, 下唇颏突角
  CalResult101 calculation_3(List pointArray, target, type) {
    double angle = 0;
    double slope1 = getSlope(pointArray[0], pointArray[1]);
    double slope2 = getSlope(pointArray[1], pointArray[2]);
    // 上下唇突角 //////////////////////////////////////
    if (type == 1) {
      double rotate1 = getAngle(slope1, 0);
      double rotate2 = getAngle(slope2, 0);
      angle = getAngle(slope1, slope2);
      angle = (calcAngle_5(slope1, slope2, angle, rotate1, rotate2)).angle;
    }
    // 上唇颏突角, 下唇颏突角
    else {
      angle = getPositive(getAngle(slope1, slope2));
    }
    int commit = getCommit(angle, target);
    return CalResult101(angle: angle, commit: commit);
  }

  double getSlope(point1, point2) {
    double x1 = point1[0];
    double y1 = point1[1];
    double x2 = point2[0];
    double y2 = point2[1];
    double k = (y2 - y1) / (x2 - x1);
    return k;
  }

  double getAngle(slope1, slope2) {
    double tan = (slope1 - slope2) / (1 + (slope1 * slope2));
    double angle = (math.atan(tan)) * 180 / math.pi;
    return angle;
  }

  double getPositive(double num) {
    if (num < 0) {
      num = -num;
    }
    return num;
  }

  int getCommit(res, section) {
    double max = section[1];
    double min = section[0];
    int commit = 0;
    if (res > max) {
      commit = 1;
    } else if (res < min) {
      commit = 2;
    }
    return commit;
  }

//角度判定
//z角,软组织面角,上唇倾角,上唇突角,下唇倾角,下唇突角,颏沟倾角使用
  CalResult101 calcAngle_1(angle, rotate) {
    if (angle < 0) {
      angle = 180 + angle;
    }
    if (rotate > 0) {
    } else {
      rotate = rotate - 180.0;
    }
    return CalResult101(angle: angle, rotate: rotate);
  }

//全面突角,面突角使用
  CalResult101 calcAngle_2(slope1, slope2, angle, rotate) {
    if (slope1 > slope2) {
      if (slope1 > 0 && slope2 < 0) {
        rotate = rotate + 180;
        if (angle < 0) {
          angle = angle + 180;
        }
      }
      if (slope1 < 0 && slope2 < 0) {
        rotate = rotate + 180;
        if (angle > 0) {
          angle = angle + 180;
        }
      }
      if (slope1 > 0 && slope2 > 0) {
        if (angle > 0) {
          angle = 180 + angle;
        }
      }
    }
    if (slope2 > slope1) {
      if (slope2 > 0 && slope1 < 0) {
        if (angle > 0) {
          angle = angle + 180;
        }
      }
      if (slope2 < 0 && slope1 < 0) {
        if (angle < 0) {
          angle = 180 - angle;
          rotate = rotate + 180;
        }
        if (angle > 0) {
          angle = 360 - angle;
          rotate = rotate + 180;
        }
      }
      if (slope2 > 0 && slope1 > 0) {
        if (angle < 0) {
          angle = 180 + angle;
        }
      }
    }
    return CalResult101(angle: angle, rotate: rotate);
  }

//鼻唇角使用
  CalResult101 calcAngle_4(slope1, slope2, angle, rotate) {
    if (slope2 > slope1) {
      if (slope2 > 0 && slope1 < 0) {
        if (angle > 0) {
          angle = 180 - angle;
        }
        if (angle < 0) {
          angle = -angle;
        }
      }
      if (slope2 > 0 && slope1 > 0) {
        if (angle < 0) {
          angle = -angle;
        }
      }
      if (slope2 < 0 && slope1 < 0) {
        if (angle < 0) {
          angle = 180 - angle;
        }
      }
    }
    if (slope1 > slope2) {
      if (slope1 > 0 && slope2 < 0) {
        if (angle < 0) {
          angle = 180 + angle;
        }
        if (angle > 0) {
          angle = -angle;
          rotate = rotate + 180;
        }
      }
      if (slope1 < 0 && slope2 < 0) {
        if (angle > 0) {
          angle = 180 - angle;
        }
      }
      if (slope1 > 0 && slope2 > 0) {
        if (angle > 0) {
          angle = 180 - angle;
          rotate = rotate + 180;
        }
      }
    }
    return CalResult101(angle: angle, rotate: rotate);
  }

//上下唇突角使用
  CalResult101 calcAngle_5(slope1, slope2, angle, rotate, rotate2) {
    if (slope1 > 0 && slope2 < 0) {
      if (rotate > 0 && rotate2 < 0) {
        if (angle < 0) {
          angle = -angle;
        }
      }
    }
    if (slope1 > 0 && slope2 > 0) {
      if (slope2 > slope1) {
        if (rotate > 0 && rotate2 > 0) {
          if (rotate2 > rotate) {
            if (angle < 0) {
              angle = -angle;
            }
          }
        }
      }
      if (slope1 > slope2) {
        if (rotate > 0 && rotate2 > 0) {
          if (rotate > rotate2) {
            if (angle > 0) {
              rotate = rotate2;
            }
          }
        }
      }
    }
    if (slope1 < 0 && slope2 > 0) {
      if (rotate < 0 && rotate2 > 0) {
        if (angle < 0) {
          angle = -angle;
        } else {
          rotate = rotate2;
        }
      }
    }
    if (slope1 < 0 && slope2 < 0) {
      if (slope2 > slope1) {
        if (angle < 0) {
          angle = -angle;
        }
        rotate = rotate + 180;
      }
      if (slope1 > slope2) {
        rotate = rotate2 + 180;
      }
    }
    return CalResult101(angle: angle, rotate: rotate);
  }
}

class CalculateTool102 {
  CalculateTool102._internal();

  static final CalculateTool102 instance = CalculateTool102._internal();

  CalResult102 getGapSpaceRes(double rotate, Map pointsMap) {
    Map gapSpace = {
      'A': ['020011', '020012'],
      'B': ['020013', '020014'],
      'C': ['020007', '020008'],
      'D': ['020015', '020016']
    };
    Map res = {};
    for (dynamic key in gapSpace.keys) {
      var pointList = gapSpace[key];
      var pointPos = [pointsMap[pointList[0]], pointsMap[pointList[1]]];
      double length = (pointPos[1]["x"] -
              (pointPos[0]["x"] - (pointPos[0]["y"] - pointPos[1]["y"]) * math.tan(rotate / 180 * math.pi))) *
          math.cos(rotate / 180 * math.pi).abs();
      res[key] = length;
    }
    List<int> target = [
      int.parse(((res["B"] - res["A"]).abs() / res["B"] * 100).toStringAsFixed(0)),
      int.parse((res["C"] / res["D"] * 100).toStringAsFixed(0)),
      int.parse((res["A"] / res["B"] * 100).toStringAsFixed(0)),
    ];
    int gap = target[0];
    String summary = '';
    // res.quantitative = `颊间隙为${num[0]}%, 微笑宽度为${num[1]}%, 微笑丰满度为${num[2]}%。`
    if (gap > 100) {
      summary += '';
    } else if (gap >= 28) {
      summary += Lang.buccal_space_5;
    } else if (gap >= 22) {
      summary += Lang.buccal_space_4;
    } else if (gap >= 15) {
      summary += Lang.buccal_space_3;
    } else if (gap >= 10) {
      summary += Lang.buccal_space_2;
    } else {
      summary += Lang.buccal_space_1;
    }
    return CalResult102(target: target, summary: summary);
  }

  CalResult102 getMidSpaceRes(List faceMidPoint, List teethMidPoint) {
    double faceX = 0;
    double teethX = 0;
    double teethY = 0;

    for (var point in teethMidPoint) {
      teethX += point["x"];
      teethY += point["y"];
    }
    teethX /= 2;
    teethY /= 2;

    if (faceMidPoint[0]["x"] == faceMidPoint[1]["x"]) {
      faceX = faceMidPoint[1]["x"];
    } else {
      faceX = faceMidPoint[0]["x"] +
          (faceMidPoint[0]["y"] - teethY) /
              (faceMidPoint[0]["y"] - faceMidPoint[1]["y"]) *
              (faceMidPoint[1]["x"] - faceMidPoint[0]["x"]);
    }
    String summary = (faceX < teethX) ? '左偏' : '右偏';
    return CalResult102(teethX: teethX, faceX: faceX, summary: summary);
  }

  int calcFaceTeethMidlinePos(Map pointsMap) {
    // 计算方法 https://chohotech.feishu.cn/docx/UbBDdcL7WotF1xxsO2ocFXwcnkf
    // mid : {
    //   faceMid: {
    //     points: ['020100', '020101']
    //   },
    //   teethMid: {
    //     points: ['020009', '020010']
    //   }
    // }
    Point m1 = Point.fromJson(pointsMap["020009"]);
    Point m2 = Point.fromJson(pointsMap["020010"]);
    Point v1 = Point.fromJson(pointsMap["020100"]);
    Point v2 = Point.fromJson(pointsMap["020101"]);
    Point m3 = Point.mid(m1, m2);

    double m3v1v2_cross = (m3.y - v2.y) * (m3.x - v1.x) - (m3.y - v1.y) * (m3.x - v2.x);
    if (isEmpty(pointsMap["020202"]) || isEmpty(pointsMap["020203"])) {
      if (m3v1v2_cross < 1e-3) return -1; // m3 在 v1v2 右边，牙中线在面中线左边
      if (m3v1v2_cross > 1e-3) return 1; // m3 在 v1v2 左边，牙中线在面中线右边
      return 0; // 居中
    }

    Point sl2 = Point.fromJson(pointsMap["020202"]);
    Point sl3 = Point.fromJson(pointsMap["020203"]);
    double v1v2_norm = math.sqrt(math.pow(v1.y - v2.y, 2) + math.pow(v1.x - v2.x, 2));
    double dist_sl2sl3 = math.sqrt(math.pow(sl3.x - sl2.x, 2) + math.pow(sl3.y - sl2.y, 2));
    double dist_m3v1v2 = m3v1v2_cross.abs() / v1v2_norm;
    if (dist_m3v1v2 < 0.5 * dist_sl2sl3) return 0; // 居中
    if (m3v1v2_cross < 1e-3) return -1; // m3 在 v1v2 右边，牙中线在面中线左边
    if (m3v1v2_cross > 1e-3) return 1; // m3 在 v1v2 左边，牙中线在面中线右边
    return 0; // 居中
  }
}

class CalculateTool110 {
  CalculateTool110._internal();

  static final CalculateTool110 instance = CalculateTool110._internal();

  Map colorRangeMap = {
    '-3': '#EA605B',
    '-2': '#387CFF',
    '-1': '#76B283',
    0: '#2B2B2B',
    1: '#2B2B2B',
    2: '#76B283',
    3: '#387CFF',
    4: '#EA605B',
  };

  // 结果统一标记
  int getMeasureStatus(double value, List section) {
    if (section.length < 2) {
      return 0;
    }
    double min = section[0] * 1.0;
    double max = section[1] * 1.0;
    int status = 0;
    // 1 == 偏高，2 == 偏低，0 == 正常
    if (value < min) {
      status = 2;
    } else if (value > max) {
      status = 1;
    }
    return status;
  }

  Map getResColor(double value, Map item) {
    Map target = {};
    List section = item["section"] ?? [];
    if (section.length < 4) {
      return target;
    }
    double standard = section[2] * 1.0;
    double std = section[3] * 1.0;
    int status = 0;
    if (std == 0) {
      if (value == standard) {
        target["color"] = '#2B2B2B'; // 黑色
        return target;
      }
      target["color"] = '#EA605B'; // 红色
      target["status"] = 4;
      return target;
    }
    status = ((value - standard) / std).abs().ceil();
    // 0-1 == 正常，2 == 偏，3 == 中偏，4 == 高偏
    target["color"] = colorRangeMap[status];
    if (status >= 4) {
      target["color"] = '#EA605B'; // 红色
      status = 4;
    }
    target["status"] = status;
    return target;
  }

// 检查point的数据
  checkParams(pointArray, int count) {
    if (pointArray.length < count) {
      return false;
    }

    for (int index = 0; index < pointArray.length; index++) {
      var point = pointArray[index];
      if (point.length < 2) {
        return false;
      }
    }

    return true;
  }

  List<double> getMiddlePoint(point1, point2) {
    return [(point2[0] + point1[0]) / 2, (point2[1] + point1[1]) / 2];
  }

  double getSlope_lateral(point1, point2) {
    if (point1[0] - point2[0] == 0) {
      return 0;
    }
    return (point1[1] - point2[1]) / (point1[0] - point2[0]);
  }

  List<double> getVector(point1, point2) {
    return [point2[0] - point1[0], point2[1] - point1[1]];
  }

  double getVectorLength(vector) {
    return math.sqrt(vector[0] * vector[0] + vector[1] * vector[1]);
  }

  double getVectorDot(vector1, vector2) {
    return vector1[0] * vector2[0] + vector1[1] * vector2[1];
  }

  double getVectorAngle(vector1, vector2) {
    double dotValue = getVectorDot(vector1, vector2);

    double length1 = getVectorLength(vector1);
    double length2 = getVectorLength(vector2);

    double cosValue = dotValue / (length1 * length2);

    return (math.acos(cosValue) / math.pi) * 180;
  }

  double getVectorProj(vector1, vector2) {
    double length = getVectorLength(vector2);
    double dotValue = getVectorDot(vector1, vector2);

    return dotValue / length;
  }

  leastSquare(List pointArray, height) {
    double x_sum = 0, y_sum = 0, xy_sum = 0, xx_sum = 0, x = 0, y = 0;
    int count = pointArray.length;
    for (int i = 0; i < pointArray.length; i++) {
      x = pointArray[i][0];
      y = height - pointArray[i][1];
      x_sum += x;
      y_sum += y;
      xx_sum += x * x;
      xy_sum += x * y;
    }
    double m = (count * xy_sum - x_sum * y_sum) / (count * xx_sum - x_sum * x_sum);
    double b = y_sum / count - (m * x_sum) / count;
    return [m, b];
  }

  calArea(List pointsArray, ruler) {
    double sum_area = 0;

    for (int i = 0; i < pointsArray.length; i++) {
      double addX = pointsArray[i][0];
      double addY = pointsArray[i == pointsArray.length - 1 ? 0 : i + 1][1];
      double subX = pointsArray[i == pointsArray.length - 1 ? 0 : i + 1][0];
      double subY = pointsArray[i][1];

      sum_area += addX * addY * 0.5;
      sum_area -= subX * subY * 0.5;
    }
    return (sum_area / 100 / math.pow(ruler, 2)).abs();
  }

  getAirwayResult(int algorithm, List pointArray, double ruler, {double height = 1280}) {
    switch (algorithm) {
      case 1:
        return airway_1(pointArray, ruler);
      case 2:
        return airway_2(pointArray, ruler, height);
      case 3:
        return airway_3(pointArray, ruler);
      case 4:
        return airway_4(pointArray, ruler);
      case 5:
        return airway_5(pointArray, ruler);
      case 6:
        return airway_6(pointArray, ruler);
      case 7:
        return airway_7(pointArray, ruler);
      case 8:
        return airway_8(pointArray, ruler);
      default:
        return 0;
    }
  }

  getAlgorithmResult(int algorithm, List<List<double>> pointArray) {
    switch (algorithm) {
      case 1:
        return algorithm_1(pointArray);
      case 2:
        return algorithm_2(pointArray);
      case 3:
        return algorithm_3(pointArray);
      case 4:
        return algorithm_4(pointArray);
      case 5:
        return algorithm_5(pointArray);
      case 6:
        return algorithm_6(pointArray);
      case 7:
        return algorithm_7(pointArray);
      case 8:
        return algorithm_8(pointArray);
      case 9:
        return algorithm_9(pointArray);
      case 10:
        return algorithm_10(pointArray);
      case 11:
        return algorithm_11(pointArray);
      case 12:
        return algorithm_12(pointArray);
      case 13:
        return algorithm_13(pointArray);
      case 14:
        return algorithm_14(pointArray);
      case 15:
        return algorithm_15(pointArray);
      case 16:
        return algorithm_16(pointArray);
      case 21:
        return algorithm_21(pointArray);
      case 22:
        return algorithm_22(pointArray);
      case 23:
        return algorithm_23(pointArray);
      case 24:
        return algorithm_24(pointArray);
      case 25:
        return algorithm_25(pointArray);
      case 26:
        return algorithm_26(pointArray);
      case 27:
        return algorithm_27(pointArray);
      case 28:
        return algorithm_28(pointArray);
      case 29:
        return algorithm_29(pointArray);
      case 30:
        return algorithm_30(pointArray);
      case 31:
        return algorithm_31(pointArray);
      case 32:
        return algorithm_32(pointArray);
      default:
        return null;
    }
  }

  algorithm_1(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var vector1 = getVector(pointArray[0], pointArray[1]);
      var vector2 = getVector(pointArray[2], pointArray[3]);

      return getVectorAngle(vector1, vector2);
    }

    return null;
  }

  algorithm_2(List<List<double>> pointArray) {
    if (checkParams(pointArray, 3)) {
      var vector1 = getVector(pointArray[1], pointArray[0]);
      var vector2 = getVector(pointArray[1], pointArray[2]);

      return getVectorAngle(vector1, vector2);
    }

    return null;
  }

  algorithm_3(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var vector1 = getVector(pointArray[2], pointArray[3]);
      var vector2 = getVector(pointArray[2], pointArray[0]);
      var vector3 = getVector(pointArray[2], pointArray[1]);

      double project1 = getVectorProj(vector2, vector1);
      double project2 = getVectorProj(vector3, vector1);

      return (project1 - project2).abs();
    }

    return null;
  }

  algorithm_4(List<List<double>> pointArray) {
    if (checkParams(pointArray, 3)) {
      // ax+by+c = 0

      double a = 0;
      double b = 0;
      double c = 0;
      var k = getSlope_lateral(pointArray[1], pointArray[2]);
      if (k == null) {
        a = 1;
        b = 0;
      } else {
        a = -k;
        b = 1;
      }
      c = -(a * pointArray[1][0] + b * pointArray[1][1]);

      double dis = (a * pointArray[0][0] + b * pointArray[0][1] + c) / math.sqrt(a * a + b * b);
      return double.parse(dis.abs().toStringAsFixed(3));
    }

    return null;
  }

  algorithm_5(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var array1 = [pointArray[3], pointArray[1], pointArray[2]];
      var side1 = algorithm_4(array1);
      var array2 = [pointArray[0], pointArray[3], pointArray[1], pointArray[2]];
      var side2 = algorithm_3(array2);

      if (side1 != null && side2 != null) {
        return math.sqrt(side2 * side2 + side1 * side1);
      }
    }

    return null;
  }

  algorithm_6(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var array2 = [pointArray[0], pointArray[3], pointArray[1], pointArray[2]];
      return algorithm_3(array2);
    }

    return null;
  }

  algorithm_7(List<List<double>> pointArray) {
    if (checkParams(pointArray, 2)) {
      var vector = getVector(pointArray[1], pointArray[0]);
      return getVectorLength(vector);
    }

    return null;
  }

  algorithm_8(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var angle = algorithm_1(pointArray);
      if (angle > 90) return angle - 90;
    }

    return null;
  }

  algorithm_9(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var vector1 = getVector(pointArray[0], pointArray[1]);
      var vector2 = [
        pointArray[2][1] - pointArray[3][1],
        pointArray[3][0] - pointArray[2][0],
      ];
      var length = getVectorProj(vector1, vector2);

      return length.abs();
    }

    return null;
  }

  algorithm_10(List<List<double>> pointArray) {
    if (checkParams(pointArray, 5)) {
      var vector1 = getVector(pointArray[0], pointArray[1]);
      var vector2 = [
        pointArray[2][1] - pointArray[3][1],
        pointArray[3][0] - pointArray[2][0],
      ];
      var array = [pointArray[2], pointArray[3], pointArray[4]];
      var length2 = algorithm_4(array);
      var length1 = getVectorProj(vector1, vector2);

      return length1 / length2;
    }

    return null;
  }

  algorithm_11(List<List<double>> pointArray) {
    if (checkParams(pointArray, 6)) {
      var array1 = [pointArray[0], pointArray[1], pointArray[2], pointArray[3]];
      var length1 = algorithm_9(array1);
      var array2 = [pointArray[4], pointArray[5], pointArray[2], pointArray[3]];
      var length2 = algorithm_9(array2);

      return (length1 / length2) * 100;
    }

    return null;
  }

  algorithm_12(List<List<double>> pointArray) {
    if (checkParams(pointArray, 6)) {
      List<List<double>> array = [];
      array.add(getMiddlePoint(pointArray[2], pointArray[3]));
      array.add(getMiddlePoint(pointArray[0], pointArray[1]));

      array.add(pointArray[4]);
      array.add(pointArray[5]);

      return algorithm_1(array);
    }

    return null;
  }

  algorithm_13(List<List<double>> pointArray) {
    if (checkParams(pointArray, 3)) {
      var vector1 = getVector(pointArray[0], pointArray[1]);

      List<double> vector2;
      if (pointArray[1][1] > pointArray[2][1]) {
        vector2 = [
          pointArray[1][1] - pointArray[2][1],
          pointArray[2][0] - pointArray[1][0],
        ];
      } else {
        vector2 = [
          pointArray[2][1] - pointArray[1][1],
          pointArray[1][0] - pointArray[2][0],
        ];
      }
      var length = getVectorProj(vector1, vector2);

      return -length;
    }

    return null;
  }

  algorithm_14(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var vector1 = getVector(pointArray[0], pointArray[1]), vector2 = getVector(pointArray[2], pointArray[3]);
      var angle = getVectorAngle(vector1, vector2);
      if ((pointArray[3][0] - pointArray[2][0]) * (pointArray[1][1] - pointArray[0][1]) >
          (pointArray[2][1] - pointArray[3][1]) * (pointArray[0][0] - pointArray[1][0])) {
        return -angle;
      } else {
        return angle;
      }
    }

    return null;
  }

  algorithm_15(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var vector1 = getVector(pointArray[0], pointArray[3]);
      var vector2 = getVector(pointArray[1], pointArray[2]);
      var length = getVectorProj(vector1, vector2);

      return length;
    }

    return null;
  }

  algorithm_16(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var length1 = algorithm_4([pointArray[0], pointArray[1], pointArray[2]]);
      var length2 = algorithm_4([pointArray[3], pointArray[4], pointArray[5]]);
      var ratio = length1 / length2;

      return ratio;
    }

    return null;
  }

  algorithm_21(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var angle = algorithm_1(pointArray);
      var slope1 = getSlope_lateral(pointArray[0], pointArray[1]),
          slope2 = getSlope_lateral(pointArray[2], pointArray[3]);
      if (slope1 - slope2 < 0) {
        return -angle;
      } else {
        return angle;
      }
    }

    return null;
  }

  algorithm_22(List<List<double>> pointArray) {
    if (checkParams(pointArray, 6)) {
      var point1 = getMiddlePoint(pointArray[0], pointArray[1]), point2 = getMiddlePoint(pointArray[2], pointArray[3]);
      var array1 = [point1, point2, pointArray[4], pointArray[5]];
      return algorithm_3(array1);
    }

    return null;
  }

  algorithm_23(List<List<double>> pointArray) {
    if (checkParams(pointArray, 5)) {
      var length1 = getVectorLength(getVector(pointArray[0], pointArray[1])),
          array1 = [pointArray[2], pointArray[3], pointArray[4]];
      var length2 = algorithm_4(array1);
      return ((length1 / length2) * 100).round();
    }

    return null;
  }

  algorithm_24(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var Dis_a_to_bc = algorithm_4([pointArray[0], pointArray[1], pointArray[2]]);
      var verti_to_bc = [
            pointArray[1][1] - pointArray[2][1],
            pointArray[2][0] - pointArray[1][0],
          ],
          vector_de = getVector(pointArray[3], pointArray[4]);
      var length1 = getVectorLength(vector_de), length2 = getVectorProj(vector_de, verti_to_bc);
      return (Dis_a_to_bc * length1) / length2;
    }

    return null;
  }

  algorithm_25(List<List<double>> pointArray) {
    if (checkParams(pointArray, 5)) {
      var vector1 = getVector(pointArray[0], pointArray[1]);
      var vector2 = getVector(pointArray[2], pointArray[3]);
      var vector3 = getVector(pointArray[2], pointArray[4]);

      return math.max(getVectorAngle(vector1, vector2), getVectorAngle(vector1, vector3));
    }

    return null;
  }

  algorithm_26(List<List<double>> pointArray) {
    if (checkParams(pointArray, 9)) {
      var vector1 = getVector(pointArray[1], pointArray[0]);
      var vector2 = getVector(pointArray[1], pointArray[2]);
      var vector3 = getVector(pointArray[4], pointArray[3]);
      var vector4 = getVector(pointArray[4], pointArray[5]);
      var vector5 = getVector(pointArray[7], pointArray[6]);
      var vector6 = getVector(pointArray[7], pointArray[8]);

      return (getVectorAngle(vector1, vector2) + getVectorAngle(vector3, vector4) + getVectorAngle(vector5, vector6));
    }

    return null;
  }

  algorithm_27(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var vector1 = getVector(pointArray[0], pointArray[1]);
      var vector2 = getVector(pointArray[2], pointArray[3]);

      return ((getVectorLength(vector1) / getVectorLength(vector2)) * 100).round();
    }

    return null;
  }

  algorithm_28(List<List<double>> pointArray) {
    return -algorithm_15(pointArray);
  }

  algorithm_29(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var array1 = [pointArray[0], pointArray[2], pointArray[3]],
          array2 = [pointArray[1], pointArray[2], pointArray[3]];

      return (algorithm_4(array1) - algorithm_4(array2)).abs();
    }

    return null;
  }

  algorithm_30(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var a = pointArray[0], b = pointArray[1], c = pointArray[2], d = pointArray[3];
      var vector1 = [
        (a[0] + b[0] - c[0] - d[0]) / 2,
        (a[1] + b[1] - c[1] - d[1]) / 2,
      ];
      var vector2 = getVector(a, b);

      var length = getVectorProj(vector2, vector1);
      if (getVectorAngle(vector1, vector2) > 90) {
        return -length;
      }
      return length;
    }

    return null;
  }

  algorithm_31(List<List<double>> pointArray) {
    if (checkParams(pointArray, 4)) {
      var length = algorithm_30(pointArray);
      var vector1 = getVector(pointArray[0], pointArray[1]), vector2 = getVector(pointArray[2], pointArray[3]);
      var length_ab = getVectorLength(vector1);
      var res = math.sqrt(length_ab * length_ab - length * length);
      if (getVectorAngle(vector1, vector2) > 90) {
        return -res;
      }
      return res;
    }

    return null;
  }

  algorithm_32(List<List<double>> pointArray) {
    return -algorithm_14(pointArray);
  }

  getBuildLine(line) {
    var pointArrayTwo = [];
    for (var index = 0; index < line.length; index++) {
      pointArrayTwo.add(line[index][0]);
      pointArrayTwo.add(line[index][1]);
    }
    var pointArrayTri = [];
    var buildLine = buildBSpLine(pointArrayTwo);
    for (var i = 0; i < buildLine.length; i += 2) {
      pointArrayTri.add([buildLine[i], buildLine[i + 1]]);
    }
    return pointArrayTri;
  }

  buildBSpLine(array, {bool isClosed = false, int sampleCount = 10}) {
    if (array.length <= 6) return array;

    List<double> value = [];
    value.add(array[0]);
    value.add(array[1]);

    for (var index = 0; index < array.length; index += 2) {
      if (index + 1 >= array.length) break;

      if (value.length == 2 && (array[index] - value[0]).abs() < 0.2 && (array[index + 1] - value[1]).abs() < 0.2) {
        continue;
      }

      if (value.length > 2) {
        if ((array[index] - value[value.length - 2]).abs() < 0.2 &&
            (array[index + 1] - value[value.length - 1]).abs() < 0.2) continue;
        if ((array[index] - value[value.length - 4]).abs() < 0.2 &&
            (array[index + 1] - value[value.length - 3]).abs() < 0.2) continue;
      }

      value.add(array[index]);
      value.add(array[index + 1]);
    }

    if (value.length <= 6) return array;
    // logger("buildBSpLine $value $isClosed");
    return ffiBuildNurbsLine(value, isClosed);
  }

  getSecPoint(line, point, pointSlope, pointSlope2) {
    double slope = getSlope_lateral(pointSlope, pointSlope2), minSlope = 100;
    int minIndex = 0;
    for (int i = 0; i < line.length; i++) {
      double minusSlope = (slope - getSlope_lateral(line[i], point)).abs();
      if (minSlope > minusSlope) {
        minSlope = minusSlope;
        minIndex = i;
      }
    }
    return minIndex;
  }

  airway_1(List pointArray, double ruler) {
    if (checkParams(pointArray, 6)) {
      var pns = pointArray[0], po = pointArray[1], or = pointArray[2], line1 = pointArray[3];
      var buildLine1 = getBuildLine(line1);
      var pns_po_or_line1 = getSecPoint(buildLine1, pns, po, or);
      var vector = getVector(buildLine1[pns_po_or_line1], pns);
      return getVectorLength(vector).abs() / ruler;
    }
    return null;
  }

  airway_2(List pointArray, double ruler, double height) {
    if (checkParams(pointArray, 6)) {
      var pns = pointArray[0], h1 = pointArray[1], ata = pointArray[2], line1 = pointArray[3];
      var buildLine = getBuildLine(line1), longAxis = [];
      for (var idx = 0; idx < buildLine.length; idx++) {
        if (ata[1] < buildLine[idx][1]) {
          longAxis.add(buildLine[idx]);
        }
      }
      var longAxisPara = leastSquare(longAxis, height);

      var longAxisVector = [
        longAxis[0][0] - longAxis[50][0],
        (longAxis[0][1] - longAxis[50][1]) * longAxisPara[0],
      ];
      var pns_h1 = getVector(pns, h1);
      var res = getVectorProj(pns_h1, longAxisVector).abs() / ruler;
      return res;
    }
    return null;
  }

  airway_3(List pointArray, double ruler) {
    if (checkParams(pointArray, 5)) {
      var pns = pointArray[0], ata = pointArray[1], line1 = pointArray[2];
      var buildLine1 = getBuildLine(line1);
      var buildLine2 = getBuildLine([buildLine1[0], pns]);
      var ata_ata_pns_line1 = getSecPoint(buildLine1, ata, ata, pns);
      var buildLine3 = getBuildLine([pns, buildLine1[ata_ata_pns_line1]]);
      var pointArrayTri = [];
      for (var i = 0; i < buildLine3.length; i++) {
        pointArrayTri.add(buildLine3[i]);
      }
      for (var i = ata_ata_pns_line1; i > 0; i--) {
        pointArrayTri.add(buildLine1[i]);
      }
      for (var i = 0; i < buildLine2.length; i++) {
        pointArrayTri.add(buildLine2[i]);
      }
      var area = calArea(pointArrayTri, ruler);
      return area;
    }
    return null;
  }

  airway_4(List pointArray, double ruler) {
    if (checkParams(pointArray, 10)) {
      var pns = pointArray[0],
          ata = pointArray[1],
          et = pointArray[2],
          up = pointArray[3],
          po = pointArray[4],
          or = pointArray[5],
          h = pointArray[6],
          line1 = pointArray[7],
          line2 = pointArray[8],
          line3 = pointArray[9];
      var buildLine1 = getBuildLine(line1);

      var pns_ata_pns_line1 = getSecPoint(buildLine1, pns, ata, pns),
          h_po_or_line1 = getSecPoint(buildLine1, h, po, or);
      var line4 = [];
      // line4.add(buildLine1[pns_ata_pns_line1])
      var slope = getSlope_lateral(up, pns);
      for (var i = 0; i < line2.length - 1; i++) {
        if (slope <= getSlope_lateral(line2[i], pns)) {
          line4.add(line2[i]);
        }
      }
      for (var i = 0; i < line3.length; i++) {
        if (et[0] >= line3[i][0]) {
          line4.add(line3[i]);
        }
      }
      line4.add(buildLine1[h_po_or_line1]);
      var buildLine4 = getBuildLine(line4);
      for (var i = h_po_or_line1; i > pns_ata_pns_line1; i--) {
        buildLine4.add(buildLine1[i]);
      }
      var buildLine5 = getBuildLine([buildLine1[pns_ata_pns_line1], pns]);
      for (var i = 0; i < buildLine5.length; i++) {
        buildLine4.add(buildLine5[i]);
      }
      var area = calArea(buildLine4, ruler);
      return area;
    }
    return null;
  }

  airway_5(List pointArray, double ruler) {
    if (checkParams(pointArray, 9)) {
      var pns = pointArray[0],
          ata = pointArray[1],
          et = pointArray[2],
          up = pointArray[3],
          po = pointArray[4],
          or = pointArray[5],
          line1 = pointArray[6],
          line2 = pointArray[7],
          line3 = pointArray[8];
      var buildLine1 = getBuildLine(line1);

      var pns_ata_pns_line1 = getSecPoint(buildLine1, pns, ata, pns),
          et_po_or_line1 = getSecPoint(buildLine1, et, po, or);
      var line4 = [];
      // line4.add(buildLine1[pns_ata_pns_line1])
      var slope = getSlope_lateral(up, pns);
      for (var i = 0; i < line2.length - 1; i++) {
        if (slope <= getSlope_lateral(line2[i], pns)) {
          line4.add(line2[i]);
        }
      }
      for (var i = line3.length - 1; i > 0; i--) {
        if (et[0] > line3[i][0] && et[1] > line3[i][1]) {
          line4.add(line3[i]);
        }
      }
      line4.add(et);
      line4.add(buildLine1[et_po_or_line1]);
      var buildLine4 = getBuildLine(line4);
      for (var i = et_po_or_line1 - 1; i > pns_ata_pns_line1; i--) {
        buildLine4.add(buildLine1[i]);
      }
      var buildLine5 = getBuildLine([buildLine1[pns_ata_pns_line1], pns]);
      for (var i = 0; i < buildLine5.length; i++) {
        buildLine4.add(buildLine5[i]);
      }
      var area = calArea(buildLine4, ruler);
      return area;
    }
    return null;
  }

  airway_6(List pointArray, double ruler) {
    if (checkParams(pointArray, 5)) {
      var b = pointArray[0], go = pointArray[1], line1 = pointArray[2], line3 = pointArray[4];
      var buildLine1 = getBuildLine(line1), buildLine3 = getBuildLine(line3);
      var b_go_b_line1 = getSecPoint(buildLine1, b, go, b), b_go_b_line3 = getSecPoint(buildLine3, b, go, b);

      var vector = getVector(buildLine1[b_go_b_line1], buildLine3[b_go_b_line3]);
      return (getVectorLength(vector)).abs() / ruler;
    }
    return null;
  }

  airway_7(List pointArray, double ruler) {
    if (checkParams(pointArray, 6)) {
      var up = pointArray[0], po = pointArray[1], or = pointArray[2], line1 = pointArray[3];
      var buildLine1 = getBuildLine(line1);
      var up_po_or_line1 = getSecPoint(buildLine1, up, po, or);

      var vector = getVector(buildLine1[up_po_or_line1], up);
      return (getVectorLength(vector)).abs() / ruler;
    }
    return null;
  }

  airway_8(List pointArray, double ruler) {
    if (checkParams(pointArray, 6)) {
      var go = pointArray[0], po = pointArray[1], or = pointArray[2], line1 = pointArray[3], line3 = pointArray[5];
      var buildLine1 = getBuildLine(line1), buildLine3 = getBuildLine(line3);
      var go_po_or_line1 = getSecPoint(buildLine1, go, po, or), go_po_or_line3 = getSecPoint(buildLine3, go, po, or);

      var vector = getVector(buildLine1[go_po_or_line1], buildLine3[go_po_or_line3]);
      return (getVectorLength(vector)).abs() / ruler;
    }
    return null;
  }
}

class Point {
  double x;
  double y;
  double z;

  Point([this.x = 0, this.y = 0, this.z = 0]);

  factory Point.fromJson(json) {
    return Point(json["x"], json["y"]);
  }

  factory Point.mid(Point p1, Point p2) {
    return Point((p1.x + p2.x) / 2, (p2.y + p2.y) / 2);
  }

  @override
  String toString() {
    return "[$x, $y]";
  }
}
