import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';

//TODO chenxb
///0-中国  1-亚太  2-巴西  9999-其他
Map loginAreasConfig = {};

initAreaConfig([bool globalInit = false]) {
  loginAreasConfig = {
    "0": {
      "name": globalInit ? "china" : Lang.login_china,
      "host": "https://api.chohotech.com/lyoral-gateway",
    },
    "1": {
      "name": globalInit ? "asian" : Lang.login_asian,
      "host": "https://api.sg.chohotech.com/lyoral-gateway",
    },
    // "2": {
    //   "name": Lang.login_brazil,
    //   "host": "",
    // },
    // "9999": {
    //   "name": Lang.login_other,
    //   "host": "",
    // },
  };
}

bool checkPhone(String phone) {
  RegExp reg = RegExp(r'^1\d{10}$');
  return reg.hasMatch(phone);
}

bool checkCode(String code) {
  RegExp reg = RegExp(r'^\d{6}$');
  return reg.hasMatch(code);
}

bool checkMail(String mail) {
  RegExp reg = RegExp(r'^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$');
  return reg.hasMatch(mail);
}

bool checkPasswordLength(password) {
  return password.length >= 8 && password.length <= 20;
}

bool checkPasswordContent(password) {
  RegExp reg = RegExp(r'^(?=.*[a-z])(?=.*[A-Z]).+$');
  return reg.hasMatch(password);
}

bool checkPassword(password) {
  return checkPasswordLength(password) && checkPasswordContent(password);
}

bool isLoginChinaArea() {
  int area = Global.sharedPrefs.getInt("login_area_c") ?? 0;
  return area == 0 || disableAboardLogin;
}

int? getLoginArea() {
  return Global.sharedPrefs.getInt("login_area_c");
}

initLoginArea(bool isZh) {
  if (Global.sharedPrefs.getInt("login_area_c") == null) {
    setLoginArea(isZh ? 0 : 1);
  }
}

setLoginArea(int index) async {
  Global.sharedPrefs.setInt("login_area_c", index);
  if (index == 0) {
    if (Global.sharedPrefs.getString("http_env") == HttpEnv.uatAp.name) {
      Global.sharedPrefs.setString("http_env", HttpEnv.uat.name);
    }
  } else {
    if (Global.sharedPrefs.getString("http_env") == HttpEnv.uat.name) {
      Global.sharedPrefs.setString("http_env", HttpEnv.uatAp.name);
    }
  }

  await HHttp.initHttp();
  HHttp.serverConfigs["prod"]["item-data"] = loginAreasConfig["$index"]["host"];
}

setMailLogin(bool isMail) {
  Global.sharedPrefs.setBool("login_by_mail", isMail);
  if (isMail) {
    Global.sharedPrefs.setBool("logged_by_mail", true);
  }
}

//本次是否邮箱登录
bool isMailLogin() {
  return Global.sharedPrefs.getBool("login_by_mail") ?? false;
}

//是否用邮箱登录过
bool hasLoggedByMail() {
  return Global.sharedPrefs.getBool("logged_by_mail") ?? false;
}
