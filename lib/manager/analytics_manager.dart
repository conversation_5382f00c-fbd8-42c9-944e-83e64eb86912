import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/msgpack/msgpack_dart.dart';
import 'package:sprintf/sprintf.dart';

Map<String, String> monitorKeyMap = {};
Map<String, String> monitorNameMap = {};
Map sexMap = {};
Map faceMap = {};
Map<String, String> categoryMap = {};
Map<String, String> analyticsMap = {};
Map<String, String> healthMap = {};
// 牙齿病症map
Map<String, String> diseaseNameMap = {};
Map<String, IconData> teethIconMap = {};
Map<String, IconData> babyTeethIconMap = {};

initAnalyticsMaps() {
  monitorKeyMap = {
    "口腔全科观察": "General",
    "隐形矫正监控": "Invisible",
    "固定矫正监控": "Fixed",
  };

  monitorNameMap = {
    "口腔全科观察": Lang.monitor_name_general,
    "隐形矫正监控": Lang.monitor_name_clear,
    "固定矫正监控": Lang.monitor_name_fixed,
  };

  sexMap = {
    "male": "boy",
    "female": "girl",
  };

  faceMap = {
    "102": Lang.type_102_1,
    "100": Lang.type_100_1,
    "101": Lang.type_101_1,
    "118": Lang.type_118_1,
    "119": Lang.type_119_1,
    "116": Lang.type_116,
    "113": Lang.type_113,
  };

  categoryMap = {
    "100": Lang.type_100, //A
    "101": Lang.type_101, //B
    "102": Lang.type_102, //C
    "103": Lang.type_103, //D
    "104": Lang.type_104,
    "105": Lang.type_105, //D
    "106": Lang.type_106, //D
    "107": Lang.type_107, //D
    "108": Lang.type_108, //D
    "109": Lang.type_109, //F
    "110": Lang.type_110, //G
    "111": Lang.type_111,
    "112": Lang.type_112,
    "113": Lang.type_113,
    "114": Lang.type_114,
    "115": Lang.type_115,
    "116": Lang.type_116,
    "117": Lang.type_117,
    "118": Lang.type_118,
    "119": Lang.type_119,
    "120": Lang.type_120,
    "151": Lang.type_151,
    "152": Lang.type_152,
    "153": Lang.type_153,
    "154": Lang.type_154,
    "155": Lang.type_155,
    "156": Lang.type_156,
    "200": Lang.type_200,
    "201": Lang.type_201,
    "300": Lang.type_300,
  };
  analyticsMap = {
    "5001": Lang.analytics_5001,
    "5002": Lang.analytics_5002,
    "5003": Lang.analytics_5003,
    "5004": Lang.analytics_5004,
    "5021": Lang.analytics_5021,
    "5022": Lang.analytics_5022,
    "5023": Lang.analytics_5023,
    "5024": Lang.analytics_5024,
    "5031": Lang.analytics_5031,
    "5032": Lang.analytics_5032,
    "5101": Lang.analytics_5101,
    "5102": Lang.analytics_5102,
    "5103": Lang.analytics_5103,
    "5111": Lang.analytics_5111,
    "5112": Lang.analytics_5112,
    "5202": Lang.analytics_5202,
    "5203": Lang.analytics_5203,
  };
  healthMap = {
    "GOOD": "良好",
    "NORMAL": "一般",
    "WEAK": "需要改善",
  };
  diseaseNameMap = {
    '0': Lang.disease_0,
    '1': Lang.disease_1,
    '2': Lang.disease_2,
    '3': Lang.disease_3,
    '4': Lang.disease_4,
    '5': Lang.disease_5,
    '6': Lang.disease_6,
    '7': Lang.disease_7,
    '8': Lang.disease_8,
    '9': Lang.disease_9,
    '10': Lang.disease_10,
    '11': Lang.disease_11,
    '12': Lang.disease_12,
    '13': Lang.disease_13,
    // '14': Lang.disease_14,
  };
  teethIconMap = {
    "11": IconFont.icon_hengya_11,
    "12": IconFont.icon_hengya_12,
    "13": IconFont.icon_hengya_13,
    "14": IconFont.icon_hengya_14,
    "15": IconFont.icon_hengya_15,
    "16": IconFont.icon_hengya_16,
    "17": IconFont.icon_hengya_17,
    "18": IconFont.icon_hengya_18,
    "21": IconFont.icon_hengya_21,
    "22": IconFont.icon_hengya_22,
    "23": IconFont.icon_hengya_23,
    "24": IconFont.icon_hengya_24,
    "25": IconFont.icon_hengya_25,
    "26": IconFont.icon_hengya_26,
    "27": IconFont.icon_hengya_27,
    "28": IconFont.icon_hengya_28,
    "31": IconFont.icon_hengya_31,
    "32": IconFont.icon_hengya_32,
    "33": IconFont.icon_hengya_33,
    "34": IconFont.icon_hengya_34,
    "35": IconFont.icon_hengya_35,
    "36": IconFont.icon_hengya_36,
    "37": IconFont.icon_hengya_37,
    "38": IconFont.icon_hengya_38,
    "41": IconFont.icon_hengya_41,
    "42": IconFont.icon_hengya_42,
    "43": IconFont.icon_hengya_43,
    "44": IconFont.icon_hengya_44,
    "45": IconFont.icon_hengya_45,
    "46": IconFont.icon_hengya_46,
    "47": IconFont.icon_hengya_47,
    "48": IconFont.icon_hengya_48,
  };
  babyTeethIconMap = {
    "51": IconFont.icon_ruya_51,
    "52": IconFont.icon_ruya_52,
    "53": IconFont.icon_ruya_53,
    "54": IconFont.icon_ruya_54,
    "55": IconFont.icon_ruya_55,
    "61": IconFont.icon_ruya_61,
    "62": IconFont.icon_ruya_62,
    "63": IconFont.icon_ruya_63,
    "64": IconFont.icon_ruya_64,
    "65": IconFont.icon_ruya_65,
    "71": IconFont.icon_ruya_71,
    "72": IconFont.icon_ruya_72,
    "73": IconFont.icon_ruya_73,
    "74": IconFont.icon_ruya_74,
    "75": IconFont.icon_ruya_75,
    "81": IconFont.icon_ruya_81,
    "82": IconFont.icon_ruya_82,
    "83": IconFont.icon_ruya_83,
    "84": IconFont.icon_ruya_84,
    "85": IconFont.icon_ruya_85,
  };
}

// canine：true-尖牙，false-磨牙
String getMolarDesc(val, [bool canine = false]) {
  switch (val) {
    case 1:
      return sprintf(Lang.molar_class, ["I"]);
    case 2:
      return sprintf(Lang.molar_class, ["II"]);
    case 3:
      return sprintf(Lang.molar_class, ["III"]);
    default:
      return canine ? Lang.fail_determine_canine : Lang.fail_determine_molar;
  }
}

// 覆合
String getBiteDesc(val) {
  switch (val) {
    case 0:
      return Lang.overbite_normal;
    case 1:
      return Lang.overbite_deep_1;
    case 2:
      return Lang.overbite_deep_2;
    case 3:
      return Lang.overbite_deep_3;
    case -1:
      return Lang.overbite_open_1;
    case -2:
      return Lang.overbite_open_2;
    case -3:
      return Lang.overbite_open_3;
    case -4:
      return Lang.front_negative_overjet;
    default:
      return "-";
  }
}

// 覆盖
String getOverjetDesc(val) {
  switch (val) {
    case 0:
      return Lang.overjet_normal;
    case 1:
      return Lang.overjet_deep_1;
    case 2:
      return Lang.overjet_deep_2;
    case 3:
      return Lang.overjet_deep_3;
    case -1:
      return Lang.overjet_edge;
    case -2:
      return Lang.negative_overjet;
    default:
      return "-";
  }
}

// 中线
String getMidDesc(val) {
  switch (val) {
    case 0:
      return Lang.midline_normal;
    case 1:
      return Lang.midline_right;
    case -1:
      return Lang.midline_left;
    default:
      return Lang.fail_determine_midline;
  }
}

getAnalyticsInfo(String id, dynamic analyticsCb, {bool onlyShowStatus = false, dynamic onError}) {
  HHttp.request(
    "/v3/analysis/getAnalysisInfo",
    "POST",
    (data) {
      analyticsCb(data);
    },
    errCallBack: onError,
    params: {
      "id": id,
      "onlyShowStatus": onlyShowStatus,
    },
  );
}

getAlgorithmResult(String algorithmId, dynamic callback, {bool useJson = false}) {
  getDownloadUrl(algorithmId, 1, (url) {
    if (url.contains("filename%3D") && url.contains(".msgpack")) {
      int fileIndex = url.indexOf("filename%3D");
      int jsonIndex = url.indexOf(".msgpack");
      if (jsonIndex > fileIndex) {
        useJson = false;
      }
    } else if (url.contains("filename%3D") && url.contains(".json")) {
      int fileIndex = url.indexOf("filename%3D");
      int jsonIndex = url.indexOf(".json");
      if (jsonIndex > fileIndex) {
        useJson = true;
      }
    }
    if (!useJson) {
      HHttp.downFileIfNotExist(
        url,
            (path) async {
          try {
            File file = File(path);
            Uint8List uint8list = await file.readAsBytes();
            var json = deserialize(uint8list);
            logger("readMsgPack json: ${json.length} $json");
            // String jsonString = jsonEncode(decodedData);
            // dynamic json = jsonDecode(jsonString);
            callback(json);
          } catch (ex) {
            logger("readMsgPack error: $ex");
            HHttp.request(
              url,
              "GET",
              isFullUrl: true,
              isShowNetWorkLoading: false,
              isShowErrorToast: false,
              cbFullResponse: true,
                  (content) {
                dynamic json = jsonDecode(content.toString());
                callback(json);
              },
            );
          }
        },
        forceDownload: true,
      );
    } else {
      HHttp.request(
        url,
        "GET",
        isFullUrl: true,
        isShowNetWorkLoading: false,
        isShowErrorToast: false,
        cbFullResponse: true,
            (content) {
          dynamic json = jsonDecode(content.toString());
          callback(json);
        },
      );
    }
  });
}

getDraftContent(draft, callback) {
  logger("getDraftContent param: $draft");
  if (draft is Map) {
    callback(draft);
  } else if (isNotEmpty(draft)) {
    getAlgorithmResult(draft, callback, useJson: true);
  }
}

// "020000"[ 434.332763671875, 185.76861572265625 ]
//          ↓ ↓ ↓
// "020000": { "pointIndex": "020000", "x": 434.33276, "y": 185.76862, }
getDraftPoints(Map data, dynamic callback) {
  if (isNotEmpty(data["algorithmInfoMap"])) {
    logger("getAlgorithmResult getDraftPoints ${data["algorithmInfoMap"].length}");
    String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
    getAlgorithmResult(algorithmId, (data) {
      logger("getAlgorithmResult $algorithmId ${data.length}");
      if (isNotEmpty(data["kps"])) {
        Map pointsMap = {};
        for (String key in (data["kps"] as Map).keys) {
          pointsMap[key] = {
            "pointIndex": key,
            "x": data["kps"][key][0],
            "y": data["kps"][key][1],
          };
        }
        callback(pointsMap);
      }
    });
  }
}
