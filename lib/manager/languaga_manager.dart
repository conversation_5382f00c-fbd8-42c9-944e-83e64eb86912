import 'package:flutter/material.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/Languages/l10n.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';

S Lang = S.current;
Locale? currentLocale;

///多语言（中文 ”cn", 英文 "en", 繁体 "tw", 日语 ”jp", 韩语 "kr", 西语 "es", 德语 “de"， 俄语 ”ru", 葡萄牙语 ”pt", 法语 "fr", 越南语 "vi"）
String lang = "zh";
//用于key的后缀，通常形式为空或"-en"
String getLangStr([String joint = "-", bool needCn = false]) {
  if (lang == "zh" && !needCn) {
    return "";
  }
  return "$joint$lang";
}

//用于key，通常形式为"cn"或"en"
String getLangKey() {
  if (lang == "zh") {
    return "cn";
  }
  return lang;
}

//用于key，目前配置为"chinese"或"english"
String getErrorLangKey() {
  return lang;
}

Locale? getCurrentLocale() {
  String? language = Global.sharedPrefs.getString("locale_language");
  String? country = Global.sharedPrefs.getString("locale_country");
  if (language != null) {
    currentLocale = Locale(language, country);
  }
  return currentLocale;
}

Future<bool> setCurrentLocale(Locale locale) async {
  currentLocale = locale;
  await Global.sharedPrefs.setString("locale_language", locale.languageCode);
  await Global.sharedPrefs.setString("locale_country", locale.countryCode ?? "");
  return true;
}

resetLocale() {
  Global.sharedPrefs.remove("locale_language");
  Global.sharedPrefs.remove("locale_country");
  currentLocale = null;
}

loadSystmLocale(Locale locale) {
  currentLocale = getCurrentLocale();
  logger("loadSystemLocale locale: $locale, currentLocale: $currentLocale");
  //优先使用设置的语言
  if (currentLocale != null) {
    loadLocale(currentLocale!);
    return;
  }
  //若无设置则使用系统语言
  if (locale.languageCode.contains("zh")) {
    loadLocale(const Locale.fromSubtags(languageCode: 'zh'));
  } else {
    loadLocale(const Locale.fromSubtags(languageCode: 'en'));
  }
  if (locale.languageCode.contains("zh") ||
      locale.countryCode != null && locale.countryCode!.contains("CN") ||
      disableAboardLogin) {
    initLoginArea(true);
  } else {
    initLoginArea(false);
  }
}

loadLocale(Locale locale) async {
  if (disableMultiLang) {
    locale = const Locale.fromSubtags(languageCode: 'zh');
  }
  lang = locale.languageCode;
  Future.delayed(const Duration(milliseconds: 20), () async {
    logger("loadLocale: $locale");
    await S.load(locale);
    initAnalyticsMaps();
    HHttp.initHttp();
    HHttp.setToken(currentUser.accessToken);
    eventBus.fire(EventLanguage());
  });
}
