

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/utils/colors_utils.dart';

class Line {
  double x1 = 0, y1 = 0, x2 = 0, y2 = 0;

  Line(this.x1, this.y1, this.x2, this.y2);

  @override
  String toString() {
    return "[$x1, $y1] - [$x2, $y2]";
  }
}

class MyPainter extends CustomPainter {
  final _paint = Paint()
    ..isAntiAlias = true
    ..style = PaintingStyle.fill //填充
    ..strokeWidth = 1.sp
    ..color = colorBrand;

  List<Line> lines = [];
  List points = [];
  Line centerLine = Line(0, 0, 0, 0);

  @override
  void paint(Canvas canvas, Size size) {
    for (Line line in lines) {
      _paint.color = colorBrand;
      canvas.drawLine(
        Offset(line.x1, line.y1),
        Offset(line.x2, line.y2),
        _paint,
      );
    }
    for (var point in points) {
      _paint.color = Colors.white;
      canvas.drawCircle(Offset(point["x"], point["y"]), 2.sp, _paint);
    }
    if (centerLine.x1 != 0 && centerLine.x2 != 0) {
      canvas.drawDashLine(
        Offset(centerLine.x1, centerLine.y1),
        Offset(centerLine.x2, centerLine.y2),
        3.sp,
        2.sp,
        _paint,
      );
    }
  }

  // 返回false, 后面介绍
  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}


class DashLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;
  final Paint _paint;

  DashLinePainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 2.0,
    this.dashSpace = 2.0,
  }): _paint = Paint()
    ..color = color
    ..strokeWidth = strokeWidth
    ..style = PaintingStyle.stroke;

  @override
  void paint(Canvas canvas, Size size) {
    double x = 0.0;
    while (x < size.width) {
      final double endX = (x + dashWidth).clamp(0, size.width);
      canvas.drawLine(
        Offset(x, 0.0),
        Offset(endX, 0.0),
        _paint,
      );
      x += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}



extension CanvasExt on Canvas {
  ///绘制虚线
  ///[p1] 起点
  ///[p2] 终点
  ///[dashWidth] 实线宽度
  ///[spaceWidth] 空隙宽度
  void drawDashLine(
      Offset p1,
      Offset p2,
      double dashWidth,
      double spaceWidth,
      Paint paint,
      ) {
    assert(dashWidth > 0);
    assert(spaceWidth > 0);

    double radians;

    if (p1.dx == p2.dx) {
      radians = (p1.dy < p2.dy) ? pi / 2 : pi / -2;
    } else {
      radians = atan2(p2.dy - p1.dy, p2.dx - p1.dx);
    }

    save();
    translate(p1.dx, p1.dy);
    rotate(radians);

    var matrix = Matrix4.identity();
    matrix.translate(p1.dx, p1.dy);
    matrix.rotateZ(radians);
    matrix.invert();

    var endPoint = MatrixUtils.transformPoint(matrix, p2);

    double tmp = 0;
    double length = endPoint.dx;
    double delta;

    while (tmp < length) {
      delta = (tmp + dashWidth < length) ? dashWidth : length - tmp;
      drawLine(Offset(tmp, 0), Offset(tmp + delta, 0), paint);
      if (tmp + delta >= length) {
        break;
      }

      tmp = (tmp + dashWidth + spaceWidth < length) ? (tmp + dashWidth + spaceWidth) : (length);
    }

    restore();
  }
}