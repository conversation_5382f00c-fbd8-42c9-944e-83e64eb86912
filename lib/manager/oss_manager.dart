import 'dart:io';

import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/utils/common_utils.dart';

getDownloadUrl(String id, int fileType, Function(String) callback) {
  try {
    HHttp.request(
      "/v3/file/listDownloadUrl",
      "POST",
      (data) {
        if (isNotEmpty(data)) {
          for (int i = 0; i < data.length; i++) {
            String? foId = data[i]["fileId"];
            if (isEmpty(foId)) {
              continue;
            }
            if (isNotEmpty(data[i]["url"])) {
              callback(data[i]["url"]);
            }
          }
        }
      },
      params: {
        "fileIdList": [id],
        "fileType": 1,
      },
      isShowErrorToast: false,
      isShowNetWorkLoading: false,
    );
  } catch (ex) {
    logger("getDownloadUrl error :$ex");
  }
}

downloadBase64(String fileId, dynamic callback) {
  HHttp.request(
    "/v3/file/listDownloadUrl",
    "POST",
    (data) {
      if (isNotEmpty(data)) {
        for (int i = 0; i < data.length; i++) {
          String? foId = data[i]["fileId"];
          if (isEmpty(foId) || isEmpty(data[i]["url"])) {
            continue;
          }
          HHttp.request(
            data[i]["url"],
            "GET",
            isFullUrl: true,
            isShowNetWorkLoading: false,
            isShowErrorToast: false,
            cbFullResponse: true,
            (content) {
              callback(content);
            },
          );
        }
      }
    },
    params: {
      "fileIdList": [fileId],
      "fileType": 1,
    },
    isShowNetWorkLoading: false,
    isShowErrorToast: false,
  );
}

downloadOrigin(List<String> fieldIds, Function(String, String) callback, String sourceType) {
  downloadFile(fieldIds, 1, callback, sourceType);
}

downloadAnalytics(List<String> fieldIds, Function(String, String) callback, String sourceType) {
  downloadFile(fieldIds, 2, callback, sourceType, useCache: false);
}

downloadAvatar(List<String> fieldIds, Function(String, String) callback, String sourceType,
    {bool documentDir = false}) {
  downloadFile(
    fieldIds,
    4,
    callback,
    sourceType,
    documentDir: documentDir,
  );
}

downloadThumbnail(List<String> fieldIds, Function(String, String) callback, String sourceType) {
  downloadFile(fieldIds, 4, callback, sourceType);
}

downloadFile(List<String> fieldIds, int fileType, Function(String, String) callback, String sourceType,
    {bool documentDir = false, bool useCache = true}) {
  if (isEmpty(fieldIds)) {
    return;
  }
  try {
    HHttp.request(
      "/v3/file/listDownloadUrl?sourceType=$sourceType",
      "POST",
      (data) {
        if (isNotEmpty(data)) {
          for (int i = 0; i < data.length; i++) {
            logger("downloadFile data: ${data[i]}");
            String? foId = data[i]["fileId"];
            if (isEmpty(foId)) {
              continue;
            }
            if (!useCache ||
                isEmpty(Global.sharedPrefs.getString("file-$foId-$fileType")) ||
                !File(Global.sharedPrefs.getString("file-$foId-$fileType")!).existsSync()) {
              if (isNotEmpty(data[i]["url"])) {
                HHttp.downImgAndGetTempLocalPath(
                  foId!,
                  data[i]["url"],
                  (path) {
                    Global.sharedPrefs.setString("file-$foId-$fileType", path);
                    callback(data[i]["fileId"], path);
                  },
                  documentDir: documentDir,
                );
              }
            } else {
              callback(data[i]["fileId"], Global.sharedPrefs.getString("file-$foId-$fileType")!);
            }
          }
        }
      },
      params: {
        "fileIdList": fieldIds,
        "fileType": fileType,
        "sourceType": sourceType,
      },
      isShowErrorToast: false,
      isShowNetWorkLoading: false,
    );
  } catch (ex) {
    logger("downloadFile error :$ex");
  }
}
