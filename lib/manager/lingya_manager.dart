import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image/image.dart' as img;
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

getLingyaFileImage(LingyaFile file, double width, double height) {
  if (file.category == "200") {
    return Image.asset(
      "res/imgs/tooth_up.png",
      width: width,
      height: height,
      fit: BoxFit.contain,
    );
  } else if (file.category == "201") {
    return Image.asset(
      "res/imgs/tooth_down.png",
      width: width,
      height: height,
      fit: BoxFit.contain,
    );
  } else if (file.category == "300") {
    return Image.asset(
      "res/imgs/cbct_file.png",
      width: width,
      height: height,
      fit: BoxFit.fill,
    );
  } else if (isNotEmpty(file.imagePath)) {
    return Image.file(
      File(file.imagePath),
      width: width,
      height: height,
      fit: BoxFit.cover,
    );
  } else {
    return Image.asset(
      "res/imgs/file_default.jpg",
      width: width,
      height: height,
      fit: BoxFit.contain,
    );
  }
}

getDetailImageByHeight(LingyaFile file, double height) {
  if (isNotEmpty(file.imagePath)) {
    return Hero(
      tag: file.fileId,
      child: Image.file(
        File(file.imagePath),
        height: height,
        fit: BoxFit.cover,
      ),
    );
  } else {
    return Image.asset(
      "res/imgs/file_default.jpg",
      height: height,
      fit: BoxFit.cover,
    );
  }
}

getDetailImageSw(LingyaFile file, dynamic onTap) {
  if (isNotEmpty(file.imagePath)) {
    return GestureDetector(
      onTap: onTap,
      child: Hero(
        tag: file.fileId,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.sp),
          child: Image.file(
            File(file.imagePath),
            width: 335.sp,
            fit: BoxFit.fitWidth,
          ),
        ),
      ),
    );
  } else {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.sp),
      child: Image.asset(
        "res/imgs/file_default.jpg",
        width: 335.sp,
        fit: BoxFit.fitWidth,
      ),
    );
  }
}

getDetailImage(LingyaFile file, dynamic onTap) {
  if (isNotEmpty(file.imagePath)) {
    return GestureDetector(
      onTap: onTap,
      child: Hero(
        tag: file.fileId,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.sp),
          child: Image.file(
            File(file.imagePath),
            width: 335.sp,
            height: 200.sp,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  } else {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.sp),
      child: Image.asset(
        "res/imgs/file_default.jpg",
        width: 335.sp,
        height: 200.sp,
        fit: BoxFit.cover,
      ),
    );
  }
}

Color getValueColor(int star) {
  switch (star) {
    case 0:
      return color2B;
    case 1:
      return colorGreen;
    case 2:
      return colorBlue;
    default:
      return colorRed;
  }
}

getSloganView() {
  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      SizedBox(height: 60.sp),
      Align(
        alignment: Alignment.topCenter,
        child: Image.asset("res/imgs/icon_case_logo.png", height: 45.sp),
      ),
      Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 60.sp),
          child: MyText(Lang.declare_case, colorAB, 10.sp),
        ),
      ),
    ],
  );
}

getImageSize(String filePath, Function(int, int) callback) async {
  File imageFile = File(filePath);
  img.Image? image = img.decodeImage(imageFile.readAsBytesSync());
  if (image != null) {
    logger("getImageSize1 $filePath ${image!.width}x${image!.height}");
    callback(image!.width, image!.height);
  } else {
    var image = Image.file(File(filePath));
    Completer<ui.Image> completer = Completer<ui.Image>();
    image.image.resolve(const ImageConfiguration()).addListener(ImageStreamListener((ImageInfo info, bool _) {
      completer.complete(info.image);
    }));

    ui.Image info = await completer.future;
    callback(info.width, info.height);
    logger("getImageSize2 $filePath ${info.width}x${info.height}");
  }
}
