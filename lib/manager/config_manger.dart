import 'dart:convert';

import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/utils/common_utils.dart';

initRequestJson() {
  // requestMooeliConfig();
  // requestLingyaPointsConfig();
  // // requestBoneRuleConfig();
  // requestBoneDlConfig();
  // requestFaceProfileConfig();
  // requestFrontFaceConfig();
  // requestLateralConfig();
  // requestAirwayConfig();
  // requestBoneConfig();
  requestErrorCodeConfig();
  requestScanConfig();
  // requestDiagnoseConfig();
  // requestMessageConfig();
  // requestInitialErrorConfig();
  // requestAllCaseConfig();
  getErrorCodeConfigJson((json) {
    HHttp.errorCodeMap = json;
  });
}

requestNamespaces(List namespaceList, String key, [callback]) {
  HHttp.request(
    "${HHttp.getConfigHost()}json/jsonPatch",
    "POST",
    (data) {
      String str = jsonEncode(data);
      Global.sharedPrefs.setString(key, str);
      if (callback != null) {
        callback(jsonDecode(str));
      }
      for (String k in data.keys) {
        logger("requestNamespaces $key $k -> ${data[k].keys}");
      }
    },
    params: {"namespaceList": namespaceList},
    isFullUrl: true,
    isShowErrorToast: false,
  );
}

requestJson(String fileName, String key, [callback]) {
  HHttp.request(
    "${HHttp.getConfigHost()}$fileName",
    "GET",
    isFullUrl: true,
    isShowNetWorkLoading: false,
    isShowErrorToast: false,
    cbFullResponse: true,
    (content) {
      String str = jsonEncode(content);
      Global.sharedPrefs.setString(key, str);
      if (callback != null) {
        callback(jsonDecode(str));
      }
    },
  );
}

requestErrorCodeConfig({callback}) {
  requestJson("errorCodeV3.json", "error_code_json", callback);
}

getErrorCodeConfigJson(callback) {
  String? str = Global.sharedPrefs.getString("error_code_json");
  if (isEmpty(str)) {
    requestErrorCodeConfig(callback: callback);
  } else {
    callback(jsonDecode(str!));
  }
}

requestLingyaPointsConfig({callback}) {
  requestNamespaces(
    [
      "analysis_points_group",
      "analysis_points_line",
      "analysis_points_point",
      "analysis_points_xrayfront",
    ],
    "lingya_points_json",
    callback,
  );
}

getLingyaPointsConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("lingya_points_json");
  if (isEmpty(feedbackStr)) {
    requestLingyaPointsConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestBoneConfig({callback}) {
  requestNamespaces(
    [
      "bone_qualitative_analysis",
      "bone_quantification_analysis",
    ],
    "bone_json",
    callback,
  );
}

getBoneConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("bone_json");
  if (isEmpty(feedbackStr)) {
    requestBoneConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestBoneRuleConfig({callback}) {
  requestJson("bone_rule_analysis.json", "bone_rule_json", callback);
}

getBoneRuleConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("bone_rule_json");
  if (isEmpty(feedbackStr)) {
    requestBoneRuleConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestBoneDlConfig({callback}) {
  requestJson("bone_dl_analysis.json", "bone_dl_json", callback);
}

getBoneDlConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("bone_dl_json");
  if (isEmpty(feedbackStr)) {
    requestBoneDlConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestFaceProfileConfig({callback}) {
  requestJson("faceProfile_analysis.json", "face_profile_json", callback);
}

getFaceProfileConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("face_profile_json");
  if (isEmpty(feedbackStr)) {
    requestFaceProfileConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestFrontFaceConfig({callback}) {
  requestJson("faceFront_analysis.json", "front_face_json", callback);
}

getFrontFaceConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("front_face_json");
  if (isEmpty(feedbackStr)) {
    requestFrontFaceConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestLateralConfig({callback}) {
  requestJson("lateral_analysis.json", "lateral_json", callback);
}

getLateralConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("lateral_json");
  if (isEmpty(feedbackStr)) {
    requestLateralConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestAirwayConfig({callback}) {
  requestJson("airway_analysis.json", "airway_json", callback);
}

getAirwayConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("airway_json");
  if (isEmpty(feedbackStr)) {
    requestAirwayConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestMooeliConfig({callback}) {
  requestJson("monitor.json", "mooeli_config_json", callback);
}

getMooeliConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("mooeli_config_json");
  if (isEmpty(feedbackStr)) {
    requestMooeliConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestScanConfig({callback}) {
  requestNamespaces(
    ["quickScanV3_conversionProject", "quickScanV3_progressStage"],
    "scan_tags_json",
    callback,
  );
}

getScanConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("scan_tags_json");
  if (isEmpty(feedbackStr)) {
    requestScanConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestDiagnoseConfig({callback}) {
  requestJson("initial.json", "diagnose_tags_json", callback);
}

getDiagnoseConfigJson(callback) {
  String? feedbackStr = Global.sharedPrefs.getString("diagnose_tags_json");
  if (isEmpty(feedbackStr)) {
    requestDiagnoseConfig(callback: callback);
  } else {
    callback(jsonDecode(feedbackStr!));
  }
}

requestMessageConfig({callback}) {
  requestJson("message_and_config.json", "message_json", callback);
}

getMessageConfigJson(callback) {
  String? str = Global.sharedPrefs.getString("message_json");
  if (isEmpty(str)) {
    requestMessageConfig(callback: callback);
  } else {
    callback(jsonDecode(str!));
  }
}

requestAllCaseConfig({callback}) {
  requestJson("all_cases.json", "all_case_json", callback);
}

getAllCaseConfigJson(callback) {
  String? str = Global.sharedPrefs.getString("all_case_json");
  if (isEmpty(str)) {
    requestAllCaseConfig(callback: callback);
  } else {
    callback(jsonDecode(str!));
  }
}

requestInitialErrorConfig({callback}) {
  requestJson("initial_diagnosis_error_code.json", "initial_error_json", callback);
}

getInitialErrorConfigJson(callback) {
  String? str = Global.sharedPrefs.getString("initial_error_json");
  if (isEmpty(str)) {
    requestInitialErrorConfig(callback: callback);
  } else {
    callback(jsonDecode(str!));
  }
}
