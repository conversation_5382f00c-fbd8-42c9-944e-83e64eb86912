import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/first_into_app_check.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/login/login_page.dart';
import 'package:mooeli/pages/main_tabs.dart';
import 'package:mooeli/pages/my/about_lingya.dart';
import 'package:mooeli/pages/my/account_logoff.dart';
import 'package:mooeli/pages/my/account_setting.dart';
import 'package:mooeli/pages/my/contact_us.dart';
import 'package:mooeli/pages/my/general_setting.dart';
import 'package:mooeli/pages/my/http_log.dart';
import 'package:mooeli/pages/my/language_setting.dart';
import 'package:mooeli/pages/my/my_file.dart';
import 'package:mooeli/pages/my/my_page.dart';
import 'package:mooeli/pages/my/my_profile.dart';
import 'package:mooeli/pages/scan/AiFaceCameraView.dart';
import 'package:mooeli/pages/scan/EditUserComment.dart';
import 'package:mooeli/pages/scan/ScanInfoRecord.dart';
import 'package:mooeli/pages/scan/ScanPre.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/scan/diagnose_info_record.dart';
import 'package:mooeli/pages/scan/scan_code.dart';
import 'package:mooeli/utils/Languages/l10n.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:route_life/route_life.dart';

import 'utils/colors_utils.dart';

void main() async {
  Global.beforeMainInit().then((setting) async {
    FlutterBugly.postCatchedException(() {
      runApp(MyApp(initSetting: setting));
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
        ),
      );
    });
  });
}

RouteLifeObserver routeLifeObserver = RouteLifeObserver();

class MyApp extends BasePage {
  final Map initSetting;

  const MyApp({Key? key, required this.initSetting}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends BasePageState<MyApp> {
  @override
  Widget build(BuildContext context) {
    final botToastBuilder = BotToastInit();
    return ScreenUtilInit(
      // designSize: const Size(1255.2, 1255.2),
      designSize: const Size(1080, 1080),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            S.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate
          ],
          supportedLocales: S.delegate.supportedLocales,
          localeResolutionCallback: (locale, supportedLocales) {
            logger("localeResolutionCallback: $locale");
            if (locale != null) {
              loadSystmLocale(locale);
            }
            return;
          },
          localeListResolutionCallback: (locales, supportedLocales) {
            if (locales != null && locales.isNotEmpty) {
              // logger("localeListResolutionCallback: $locales");
            }
            return;
          },
          navigatorKey: widget.initSetting["globalNavigator"],
          title: "灵芽Pro",
          theme: ThemeData(
            fontFamily: "PingFang SC",
            colorScheme: const ColorScheme.light(primary: Colors.black),
            scaffoldBackgroundColor: colorBg,
            appBarTheme: AppBarTheme(
              centerTitle: true,
              iconTheme: IconThemeData(size: 48.sp, color: color2B),
              elevation: 0,
              backgroundColor: colorBg,
              titleTextStyle: TextStyle(
                fontSize: 36.sp,
                fontWeight: FontWeight.w600,
                color: color2B,
              ),
            ),
          ),
          routes: {
            "MainTabs": (context) => const MainTabs(),
            "My": (context) => const MyPage(),
            "Profile": (context) => const Profile(),
            "AccountSetting": (context) => const AccountSetting(),
            "GeneralSetting": (context) => const GeneralSetting(),
            "LanguageSetting": (context) => const LanguageSetting(),
            "Login": (context) => Login(),
            "AccountLogoff": (context) => const AccountLogoff(),
            "AboutLingya": (context) => const AboutLingya(),
            "ScanInfoRecord": (context) => ScanInfoRecord(activity: ScanActivity()),
            "DiagnoseInfoRecord": (context) => DiagnoseInfoRecord(),
            "AiFaceCameraView": (context) => const AiFaceCameraView(),
            "EditUserComment": (context) => const EditUserComment(),
            "ToothCameraView": (context) => ToothCameraView(),
            "ScanPre": (context) => const ScanPre(),
            "ScanCode": (context) => const ScanCode(),
            "ContactUs": (context) => const ContactUs(),
            "HttpLog": (context) => const HttpLog(),
            "MyFilePage": (context) => const MyFilePage(),
          },
          navigatorObservers: [routeLifeObserver],
          builder: (context, widget) {
            child = MediaQuery(
              data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
              child: widget!,
            );
            child = botToastBuilder(context, child);
            return FlutterSmartDialog(child: child!);
          },
          home: child,
        );
      },
      child: FirstInToAppCheck(initSetting: widget.initSetting),
    );
  }
}
