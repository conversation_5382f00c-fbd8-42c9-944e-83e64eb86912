import 'package:sqflite/sqflite.dart';

class TableScanActivity {

  static String tableName = 'scan_activity';
  // Dart 字段名映射
  static String colId = 'id';
  static String colCampaignId = 'campaignId';
  static String colCampaignName = 'campaignName';
  static String colContactName = 'contactName';
  static String colCreatorName = 'creatorName';
  static String colContactPhone = 'contactPhone';
  static String colContactAddress = 'contactAddress';
  static String colQrCodeFileId = 'qrCodeFileId';
  static String colQrcodePath = 'qrcodePath';
  static String colQrCodeDescription = 'qrCodeDescription';
  static String colCreateTime = 'createTime';
  static String colShowPdfLogo = 'showPdfLogo';
  static String colCounter = 'counter';

  static String createTableSql = '''
    CREATE TABLE $tableName (
      $colId INTEGER PRIMARY KEY AUTOINCREMENT,
      $colContactName TEXT,
      $colCreatorName TEXT,
      $colCampaignId TEXT,
      $colCampaignName TEXT,
      $colContactPhone TEXT,
      $colContactAddress TEXT,
      $colQrCodeFileId TEXT,
      $colQrcodePath TEXT,
      $colQrCodeDescription TEXT,
      $colCreateTime INTEGER,
      $colShowPdfLogo INTEGER,
      $colCounter INTEGER ); ''';


  // 根据 ID 获取记录的方法
  static Future<Map<String, dynamic>?> getByCampaignId(Database db, String campaignId) async {
    List<Map<String, dynamic>> results = await db.query(
      tableName,
      where: '$colCampaignId = ?',
      whereArgs: [campaignId],
    );

    if (results.isNotEmpty) {
      return results.first;
    }
    return null;
  }

  // 获取全部数据的方法
  static Future<List<Map<String, dynamic>>> getAll(Database db) async {
    return await db.query(tableName);
  }

  // 删除指定 ID 记录的方法
  static Future<int> deleteByIds(Database db, List<String> campaignIds) async {
    final ids = campaignIds.map((id) => "'$id'").join(',');
    return await db.delete(
      tableName,
      where: '$colCampaignId IN ($ids)',
    );
  }

  // 删除所有记录的方法
  static Future<int> deleteAll(Database db) async {
    return await db.delete(tableName);
  }

  // 插入记录的方法
  static Future<int> insert(Database db, Map<String, dynamic> data) async {
    return await db.insert(tableName, {
      colCampaignId: data[colCampaignId],
      colCampaignName: data[colCampaignName],
      colContactName: data[colContactName],
      colCreatorName: data[colCreatorName],
      colContactPhone: data[colContactPhone],
      colContactAddress: data[colContactAddress],
      colQrCodeFileId: data[colQrCodeFileId],
      colQrcodePath: data[colQrcodePath],
      colQrCodeDescription: data[colQrCodeDescription],
      colCreateTime: data[colCreateTime],
      colShowPdfLogo: data[colShowPdfLogo],
      colCounter: data[colCounter],
    });
  }

  // 更新记录的方法
  static Future<int> update(Database db, String campaignId, Map<String, dynamic> data) async {
    return await db.update(
      tableName,
      data,
      where: '$colCampaignId = ?',
      whereArgs: [campaignId],
    );
  }
}