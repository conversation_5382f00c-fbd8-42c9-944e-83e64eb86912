import 'dart:async';
import 'dart:io';

import 'package:mooeli/db/table_scan_activity.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

int LIMIT_COUNT = 30;

RecordDbHelper recordDbHelper = RecordDbHelper();

class RecordDbHelper {
  static RecordDbHelper? _databaseHelper; // Singleton DatabaseHelper
  static Database? _database; // Singleton Database

  int LAST_DB_VERSION = 1;
  int DB_VERSION = 2;

  RecordDbHelper._createInstance(); // Named constructor to create instance of DatabaseHelper

  factory RecordDbHelper() {
    _databaseHelper ??= RecordDbHelper._createInstance();
    return _databaseHelper!;
  }

  Future<Database> get database async {
    _database ??= await initializeDatabase();
    return _database!;
  }

  Future<Database> initializeDatabase() async {
    Directory directory = await getApplicationSupportDirectory();

    String path = '${directory.path}record_${currentUser.providerId}_${currentUser.personId}.db';
    logger("record db init $path");
    File file = File(path);
    try {
      if (!file.existsSync()) {
        file.createSync();
      }
    } catch (e) {
      logger("record db init create $e");
    }

    var recordDatabase = await openDatabase(path, version: DB_VERSION, onCreate: _createDb, onUpgrade: onUpgrade);
    return recordDatabase;
  }

  void onUpgrade(Database db, int oldVersion, int newVersion) {
    logger("record db onUpgrade $oldVersion to $newVersion");
    for (int migrateVersion = oldVersion + 1; migrateVersion <= newVersion; migrateVersion++) {
      upgrade(db, migrateVersion);
    }
  }

  void upgrade(Database db, int version) async {
    switch (version) {
      case 2:
        // await db.execute('ALTER TABLE $recordTable ADD COLUMN $colStatus INTEGER');】
        await db.execute(TableScanActivity.createTableSql);
        break;
    }
  }

  void clear() {
    _database = null;
    _databaseHelper = null;
  }

  void _createDb(Database db, int newVersion) async {
    await db.execute('CREATE TABLE $recordTable($colId INTEGER PRIMARY KEY AUTOINCREMENT, '
        '$colRecordId TEXT, $colRecordName TEXT, $colCampaignId TEXT, $colCampaignName TEXT, $colPatientPhone TEXT, $colGender TEXT, '
        '$colAge TEXT, $colExtra TEXT, $colCustomTime INTEGER, $colStatus INTEGER)');
    onUpgrade(db, LAST_DB_VERSION, newVersion);
  }

  ///### record model begin ###
  String recordTable = 'record_table';
  String colId = 'id';
  String colRecordId = 'scanRecordId';
  String colRecordName = 'name';
  String colCampaignId = 'campaignId';
  String colCampaignName = 'campaignName';
  String colPatientPhone = 'phone';
  String colGender = 'gender';
  String colExtra = 'extra';
  String colAge = 'age';
  String colCustomTime = 'recordCreateTime';
  String colStatus = 'status';

  Future<int> insertRecord(ScanInfo record) async {
    Database db = await database;
    logger("record db insert ready ${record.toDbJson()}");
    int result = await db.insert(recordTable, record.toDbJson());
    logger("record db insert ${record.recordName} $result");
    record.id = result;
    return result;
  }

  Future<int> updateRecord(ScanInfo record) async {
    var db = await database;
    var result =
        await db.update(recordTable, record.toDbJson(), where: '$colRecordId = ?', whereArgs: [record.recordId]);
    logger("record db update ${record.recordName} $result");
    return result;
  }

  Future<int> deleteRecord(String recordId) async {
    var db = await database;
    int result = await db.rawDelete('DELETE FROM $recordTable WHERE $colRecordId = ?', [recordId]);
    return result;
  }

  Future<int> getCount({String searchWord = "", int startTime = 0, int endTime = 0}) async {
    Database db = await database;

    String whereClause = "";
    List<Object?> whereArgs = [];
    if (searchWord.isNotEmpty) {
      whereClause += "$colRecordName LIKE ?";
      whereArgs.add('%$searchWord%');
    }
    if (startTime > 0) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colCustomTime >= ?";
      whereArgs.add(startTime);
    }
    if (endTime > 0) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colCustomTime <= ?";
      whereArgs.add(endTime);
    }

    final result = await db.rawQuery(
      "SELECT COUNT(*) as count FROM $recordTable${whereClause.isNotEmpty ? " WHERE $whereClause" : ""}",
      whereArgs,
    );

    // Extract the count from the query result
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<ScanInfo?> getRecordById(String recordId) async {
    Database db = await database;
    List<Map<String, Object?>> result = await db.query(recordTable, where: "$colRecordId = ?", whereArgs: [recordId]);
    if (isNotEmpty(result)) {
      return ScanInfo.fromJson(result.first);
    } else {
      return null;
    }
  }

  Future<List<ScanInfo>> getRecordList(String caseId,
      {bool includeComplete = false, int page = 0, String searchWord = "", int startTime = 0, int endTime = 0}) async {
    String whereClause = "";
    List<Object?> whereArgs = [];
    if (isNotEmpty(caseId)) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colCampaignId = ?";
      whereArgs.add(caseId);
    }
    if (!includeComplete) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colStatus != ?";
      whereArgs.add(RecordStatus.values.indexOf(RecordStatus.completeUpload));
    }
    if (searchWord.isNotEmpty) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colRecordName LIKE ?";
      whereArgs.add('%$searchWord%');
    }
    if (startTime > 0) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colCustomTime >= ?";
      whereArgs.add(startTime);
    }
    if (endTime > 0) {
      if (whereClause.isNotEmpty) whereClause += " AND ";
      whereClause += "$colCustomTime <= ?";
      whereArgs.add(endTime);
    }

    Database db = await database;
    List<Map<String, Object?>> result = await db.query(
      recordTable,
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: '$colId DESC',
      // offset: page * LIMIT_COUNT,
      // limit: LIMIT_COUNT,
    );
    logger("getRecordList:${result.length}");
    return result.map((data) => ScanInfo.fromJson(data)).toList();
  }


  // region scanActivity

  // 根据 ID 获取记录的方法
  Future<ScanActivity?> getScanActivityById(String campaignId) async {
    Database db = await database;
    Map<String, dynamic>? result = await TableScanActivity.getByCampaignId(db, campaignId);
    return result != null ? ScanActivity.fromDb(result) : null;
  }

  // 获取全部数据的方法
  Future<List<ScanActivity>> getScanActivityAll() async {
    Database db = await database;
    final result = await TableScanActivity.getAll(db);
    return result.map((e) => ScanActivity.fromDb(e)).toList();
  }

  // 删除指定 ID 记录的方法
  Future<int> deleteScanActivityByCampaignIds(List<String> campaignIds) async {
    Database db = await database;
    return await TableScanActivity.deleteByIds(db, campaignIds);
  }

  // 删除所有记录的方法
  Future<int> deleteScanActivityAll() async {
    Database db = await database;
    return await TableScanActivity.deleteAll(db);
  }

  Future<int> insertScanActivity(ScanActivity activity) async {
    Database db = await database;
    return await TableScanActivity.insert(db, activity.toDb());
  }

  Future<int> updateScanActivity(ScanActivity activity) async {
    Database db = await database;
    return await TableScanActivity.update(db, activity.campaignId, activity.toDb());
  }
  //end region
}
