import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';

class RefreshGrid extends StatefulWidget {
  final dynamic onRefresh;
  final List<Widget> childList;
  final String nullImgName;
  final String nullText;
  final dynamic onLoadMore;
  final Widget? nullWidget;
  final bool isLoading;
  final SliverGridDelegate gridDelegate;

  const RefreshGrid(
      {Key? key,
      required this.onRefresh,
      required this.childList,
      required this.isLoading,
      required this.gridDelegate,
      this.nullImgName = "res/icons/empty.png",
      this.nullText = "",
      this.nullWidget,
      this.onLoadMore})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => RefreshGridState();
}

class RefreshGridState extends State<RefreshGrid> {
  final ScrollController _scrollController = ScrollController();

  @override
  initState() {
    super.initState();
    initAsyncState();
    _scrollController.addListener(() {
      // logger("RefreshGrid _scrollController");
      FocusScope.of(context).requestFocus(FocusNode());
      if (widget.onLoadMore != null) {
        if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
          widget.onLoadMore();
        }
      }
    });
  }

  initAsyncState() async {}

  void scrollToPosition(double offset) {
    try {
      _scrollController.animateTo(
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (ex) {
      //
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      child: widget.childList.isNotEmpty
          ? GridView(
              padding: EdgeInsets.zero,
              physics: const AlwaysScrollableScrollPhysics(),
              gridDelegate: widget.gridDelegate,
              controller: _scrollController,
              children: widget.childList.toList(),
            )
          : Center(
              child: Padding(
                padding: EdgeInsets.only(top: 120.sp),
                child: Column(
                  children: [
                    widget.isLoading
                        ? const SizedBox()
                        : GestureDetector(
                            onTap: widget.onRefresh,
                            child: Image.asset(widget.nullImgName, width: 320.sp),
                          ),
                    SizedBox(height: 32.sp),
                    widget.isLoading
                        ? Text(Lang.loading,
                            textAlign: TextAlign.center, style: TextStyle(fontSize: 24.sp, color: colorAB, height: 1.5))
                        : widget.nullWidget ??
                            Text(isNotEmpty(widget.nullText) ? widget.nullText : Lang.no_data,
                                textAlign: TextAlign.center,
                                style: TextStyle(fontSize: 24.sp, color: colorAB, height: 1.5)),
                  ],
                ),
              ),
            ),
    );
  }
}
