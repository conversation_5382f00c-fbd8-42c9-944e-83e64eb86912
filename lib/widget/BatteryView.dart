import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';

class BatteryView extends StatefulWidget {
  int percent;

  BatteryView(
    this.percent, {
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return BatteryViewState();
  }
}

class BatteryViewState extends State<BatteryView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 46.sp,
              height: 24.sp,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0x14000000),
                borderRadius: BorderRadius.circular(6.sp),
                border: Border.all(color: const Color(0xff999999), width: 2.5.sp),
              ),
              padding: EdgeInsets.all(2.sp),
              child: <PERSON>ack(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      width: 37.sp * widget.percent / 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3.sp),
                        color: widget.percent <= 20
                            ? colorRed
                            : widget.percent <= 30
                                ? colorPink
                                : colorCyan,
                      ),
                    ),
                  ),
                  HHttp.httpEnv == HttpEnv.prod
                      ? const SizedBox()
                      : Center(
                          child: Text(
                            "${widget.percent}%",
                            style: TextStyle(fontSize: 12.sp, color: Colors.white, fontFamily: 'DINExp'),
                          ),
                        ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: 2.sp),
              width: 3.sp,
              height: 8.4.sp,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(topRight: Radius.circular(4.sp), bottomRight: Radius.circular(4.sp)),
                color: const Color(0xff999999),
              ),
            ),
          ],
        ),
        SizedBox(height: 4.sp),
        Text(
          Lang.device_battery,
          style: TextStyle(fontSize: 12.sp, color: color7C, fontWeight: FontWeight.w600),
        )
      ],
    );
  }
}
