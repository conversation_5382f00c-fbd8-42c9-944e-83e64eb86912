import 'package:flutter/material.dart';

List<Widget> addSpaces(List<Widget> list, double space, {axis = Axis.vertical}) {
  List<Widget> newList = [];
  double vSpace = axis == Axis.vertical ? space : 0;
  double hSpace = axis == Axis.vertical ? 0 : space;
  for (int i = 0; i < list.length; i++) {
    newList.add(list[i]);
    if (i < list.length - 1) {
      newList.add(SizedBox(
        width: hSpace,
        height: vSpace,
      ));
    }
  }
  return newList;
}