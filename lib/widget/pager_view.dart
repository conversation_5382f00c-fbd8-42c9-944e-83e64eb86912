import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mooeli/utils/common_utils.dart';

class MyPagerView extends StatefulWidget {
  final List<Widget> children;

  final Duration? switchDuration;

  TypeCallback<int>? clickBanner;

  TypeCallback<int>? onPageChanged;

  double viewportFraction;

  bool hideIndicator;

  bool circular;

  int initIndex = 0;

  MyPagerView(
      {this.children = const <Widget>[],
      this.switchDuration,
      this.clickBanner,
      this.onPageChanged,
      this.viewportFraction = 1.0,
      this.initIndex = 0,
      this.hideIndicator = false,
      this.circular = true,
      Key? key})
      : super(key: key);

  MyPagerViewState createState() => MyPagerViewState();
}

class MyPagerViewState extends State<MyPagerView> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  int _curPageIndex = 0;
  static const Duration animateDuration = Duration(milliseconds: 500);
  Timer? _timer;
  List<Widget> children = []; // 内部加两个页面  +B(A,B)+A

  @override
  void dispose() {
    _pageController.dispose();
    _tabController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  removeCurrentBanner() {
    setState(() {
      widget.children.removeAt(_curPageIndex);
      children.removeAt(_curPageIndex);
      if (_curPageIndex >= children.length) {
        _curPageIndex = children.length - 1;
      }
    });
    _pageController.animateToPage(_curPageIndex, duration: animateDuration, curve: Curves.linear);
  }

  int getCurrentIndex() {
    return _curPageIndex;
  }

  @override
  void initState() {
    super.initState();
    _curPageIndex = widget.initIndex;
    _tabController = TabController(length: widget.children.length, vsync: this);

    children.addAll(widget.children);

    /// 定时器完成自动翻页
    if (widget.children.length > 1) {
      if (widget.circular) {
        children.insert(0, widget.children.last);
        children.add(widget.children.first);

        ///如果大于一页，则会在前后都加一页， 初始页要是 1
        _curPageIndex = 1;
      }
      if (widget.switchDuration != null) {
        _timer = Timer.periodic(widget.switchDuration!, _nextBanner);
      }
    }

    ///初始页面 指定
    _pageController = PageController(
      initialPage: _curPageIndex,
      viewportFraction: widget.viewportFraction,
    );
  }

  void _nextBanner(Timer timer) {
    _curPageIndex++;
    _curPageIndex = _curPageIndex == children.length ? 0 : _curPageIndex;
    _pageController.animateToPage(_curPageIndex, duration: animateDuration, curve: Curves.linear);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Listener(
          onPointerDown: (_) {
            _timer?.cancel();
          },
          onPointerUp: (_) {
            if (widget.children.length > 1 && widget.switchDuration != null) {
              _timer = Timer.periodic(widget.switchDuration!, _nextBanner);
            }
          },
          child: NotificationListener(
            // ignore: missing_return
            onNotification: (notification) {
//              print(notification.runtimeType);
              if (notification is ScrollUpdateNotification) {
                //是一个完整页面的偏移
                if (notification.metrics.atEdge) {
                  if (widget.circular) {
                    if (_curPageIndex == children.length - 1) {
                      ///如果是最后一页 ，让pageview jump到第1页
                      _pageController.jumpToPage(1);
                    } else if (_curPageIndex == 0) {
                      ///第1页回滑， 滑到第0页。第0页的内容是倒数第二页，是所有真实页面的最后一页的内容
                      ///指示器 到 tab的最后一个
                      _pageController.jumpToPage(children.length - 2);
                    }
                  }
                }
              }
              return true;
            },
            child: PageView.builder(
              itemCount: children.length,
              itemBuilder: (context, index) {
                return InkWell(
                  child: children[index],
                  onTap: () {
                    if (widget.clickBanner != null) {
                      widget.clickBanner!(index);
                    }
                  },
                );
              },
              controller: _pageController,

              ///要到新页面的时候 把新页面的index给我们
              onPageChanged: (index) {
                // 需要更新下下标
                _curPageIndex = index;
                if (index == children.length - 1) {
                  ///如果是最后一页 ，让pageview jump到第1页
//                _pageController.jumpToPage(1);
                  _tabController.animateTo(0);
                } else if (index == 0) {
                  ///第1页回滑， 滑到第0页。第0页的内容是倒数第二页，是所有真实页面的最后一页的内容
                  ///指示器 到 tab的最后一个
//                _pageController.jumpToPage(children.length-2);
                  _tabController.animateTo(_tabController.length - 1);
                } else {
                  _tabController.animateTo(index - 1);
                }
                if (widget.onPageChanged != null) {
                  widget.onPageChanged!(index);
                }
              },
            ),
          ),
        ),
        children.length > 1 && !widget.hideIndicator
            ? Align(
                alignment: Alignment.bottomCenter,
                child: TabPageSelector(
                  indicatorSize: 8,
                  controller: _tabController,
                  color: Colors.white,
                  selectedColor: Colors.blue,
                ),
              )
            : Container()
      ],
    );
  }
}
