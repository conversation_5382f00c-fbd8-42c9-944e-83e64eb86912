import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/utils/colors_utils.dart';

class WaterRipple extends StatefulWidget {
  final int count;
  final Color color;

  const WaterRipple({Key? key, this.count = 3, this.color = const Color(0xFF6B57C7)}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _WaterRippleState();
}

class _WaterRippleState extends State<WaterRipple> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 1000))..repeat();
    final CurvedAnimation curve = CurvedAnimation(parent: _controller, curve: const Cubic(0.0, 0.0, 0.58, 1.0));
    animation = Tween(begin: 0.0, end: 1.0).animate(curve)
      ..addListener(() {
        setState(() {});
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // return AnimatedBuilder(
    //   animation: _controller,
    //   builder: (context, child) {
    return CustomPaint(
      painter: WaterRipplePainter(_controller.value, widget.count, widget.color),
    );
    //   },
    // );
  }
}

class WaterRipplePainter extends CustomPainter {
  final double progress;
  final int count;
  final Color color;
  final double strokeWidth = 25.sp;

  WaterRipplePainter(this.progress, this.count, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    double radius = min(size.width / 2, size.height / 2);
    Paint paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    final double opacity = (1.0 - progress) / 3.0;
    for (int i = count - 1; i >= 0; i--) {
      double r = radius * ((i + progress) / (count + 1));
      paint.shader = ui.Gradient.radial(
        Offset(size.width / 2, size.height / 2),
        r + strokeWidth,
        <Color>[color.withOpacity(0), color.withOpacity(opacity)],
        <double>[(r - strokeWidth / 2) / (r + strokeWidth / 2), 1.0],
      );

      canvas.drawArc(
          Rect.fromCircle(center: Offset(size.width / 2, size.height / 2), radius: r), 0, 2 * pi, false, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
