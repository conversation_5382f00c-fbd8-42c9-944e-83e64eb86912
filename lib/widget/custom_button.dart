import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/event_utils.dart';

class HCustomButton extends BasePage {
  final bool initVisible;
  final dynamic onPress;
  final double width;
  final double height;
  String text;
  final bool fixedBottom;
  final double fontSize;
  final Color? bgColor;
  final dynamic textColor;
  final dynamic borderWidth;
  final dynamic borderColor;
  final int ms; //点击间隔
  final double radius;
  final Widget? child;

  HCustomButton(
      {Key? key,
      required this.onPress,
      this.width = 0,
      this.height = 0,
      this.initVisible = true,
      this.fixedBottom = false,
      this.bgColor,
      this.textColor,
      this.fontSize = 0,
      this.borderWidth,
      this.borderColor,
      this.ms = 500,
      this.child,
      this.radius = 0,
      this.text = ""})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return HCustomButtonState();
  }
}

class HCustomButtonState extends BasePageState<HCustomButton> {
  bool isVisible = true;
  int clickTime = 0;
  String text = "";
  Color? bgColor;

  setVisible(bool visible) {
    setState(() {
      isVisible = visible;
    });
  }

  setText(String newtext) {
    setState(() {
      text = newtext;
    });
  }

  setBgColor(dynamic color) {
    setState(() {
      bgColor = color;
    });
  }

  @override
  void initState() {
    super.initState();
    isVisible = widget.initVisible;
    text = widget.text;
    bgColor = widget.bgColor;
  }

  @override
  Widget build(BuildContext context) {
    Widget button = ElevatedButton(
        onPressed: isVisible
            ? () {
                int time = DateTime.now().millisecondsSinceEpoch;
                if (time - clickTime >= widget.ms) {
                  clickTime = time;
                  widget.onPress();
                }
              }
            : null,
        style: ButtonStyle(
          side: MaterialStateProperty.all(
            BorderSide(
                style: widget.borderWidth != null ? BorderStyle.solid : BorderStyle.none,
                color: widget.borderColor ?? const Color(0xFFFFFFFF),
                width: widget.borderWidth ?? 0),
          ),
          elevation: MaterialStateProperty.all(0),
          //阴影范围
          backgroundColor: MaterialStateProperty.all((isVisible ? bgColor ?? colorBrand : colorE1)),
          shape: MaterialStateProperty.all(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(widget.radius > 0 ? widget.radius : 48.sp))),
          fixedSize: MaterialStateProperty.all(
            Size(widget.width == 0 ? 1.sw : widget.width, widget.height == 0 ? 48.sp : widget.height),
          ),
        ),
        child: widget.child ??
            Text(
              text,
              style: TextStyle(
                  fontSize: widget.fontSize == 0 ? 24.sp : widget.fontSize,
                  color: widget.textColor ?? const Color(0xFFFFFFFF)),
            ));

    if (widget.fixedBottom) {
      return Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            padding: EdgeInsets.fromLTRB(24.sp, 10.sp, 24.sp, 24.sp),
            child: button,
          ));
    }
    return button;
  }
}

class MainButton extends StatefulWidget {
  final dynamic onPress;
  final double width;
  final double height;
  String text;
  final double fontSize;
  final int ms; //点击间隔

  MainButton(
      {Key? key,
      required this.onPress,
      this.width = 0,
      this.height = 0,
      this.fontSize = 0,
      this.ms = 500,
      this.text = ""})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MainButtonState();
  }
}

class MainButtonState extends State<MainButton> {
  int clickTime = 0;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        int time = DateTime.now().millisecondsSinceEpoch;
        if (time - clickTime >= widget.ms) {
          clickTime = time;
          widget.onPress();
        }
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: color2B,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Text(
          widget.text,
          style: TextStyle(
            fontSize: widget.fontSize,
            color: const Color(0xFFFFFFFF),
          ),
        ),
      ),
    );
  }
}
