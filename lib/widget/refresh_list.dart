import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';

class RefreshList extends StatefulWidget {
  final dynamic onRefresh;
  final List<Widget> childList;
  final String nullImgName;
  final String nullText;
  final dynamic onLoadMore;
  final Widget? nullWidget;
  final bool isLoading;

  const RefreshList(
      {Key? key,
      required this.onRefresh,
      required this.childList,
      required this.isLoading,
      this.nullImgName = "res/icons/empty.png",
      this.nullText = "",
      this.nullWidget,
      this.onLoadMore})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => RefreshListState();
}

class RefreshListState extends State<RefreshList> {
  final ScrollController _scrollController = ScrollController();

  @override
  initState() {
    super.initState();
    initAsyncState();
    _scrollController.addListener(() {
      // logger("RefreshList _scrollController");
      // FocusScope.of(context).requestFocus(FocusNode());
      if (widget.onLoadMore != null) {
        if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
          widget.onLoadMore();
        }
      }
    });
  }

  initAsyncState() async {}

  void scrollToPosition(double offset) {
    try {
      _scrollController.animateTo(
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (ex) {
      //
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
        onRefresh: widget.onRefresh,
        child: ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          controller: _scrollController,
          children: widget.childList.isNotEmpty
              ? widget.childList.toList()
              : [
                  Center(
                    child: Padding(
                      padding: EdgeInsets.only(top: 160.sp),
                      child: Column(
                        children: [
                          widget.isLoading
                              ? const SizedBox()
                              : GestureDetector(
                                  onTap: widget.onRefresh,
                                  child: Image.asset(widget.nullImgName, width: 120.sp),
                                ),
                          SizedBox(height: 24.sp),
                          widget.isLoading
                              ? Text(Lang.loading,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(fontSize: 14.sp, color: colorAB, height: 1.5))
                              : widget.nullWidget ??
                                  Text(isNotEmpty(widget.nullText) ? widget.nullText : Lang.no_data,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(fontSize: 14.sp, color: colorAB, height: 1.5)),
                        ],
                      ),
                    ),
                  )
                ],
        ));
  }
}
