import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class SaveQrcodeView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return SaveQrcodeViewState();
  }
}

class SaveQrcodeViewState extends State<SaveQrcodeView> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  bool showSave = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: const Offset(0.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.ease,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Align(
        alignment: Alignment.center,
        child: Container(
          width: 335.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: GestureDetector(
            onLongPress: () {
              setState(() {
                showSave = true;
              });
              _controller.forward();
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 8.sp),
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          SizedBox(height: 2.sp),
                          MyText(Lang.friendly_tip, color2B, 16.sp, FontWeight.w500),
                          SizedBox(height: 16.sp),
                          HeightText(Lang.buy_vip_tip, color7C, 12.sp, 1.4, FontWeight.w400, TextAlign.center),
                          SizedBox(height: 16.sp),
                          Image.network(
                            wechatCodeImageUrl,
                            width: 236.sp,
                            height: 236.sp,
                          ),
                          SizedBox(height: 8.sp),
                          MyText(Lang.long_press_save_qrcode, colorA4, 10.sp),
                        ],
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: Click(
                          onTap: () {
                            BotToast.cleanAll();
                          },
                          child: Image.asset("res/imgs/icon_close.png", width: 20.sp, color: colorA4),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 1.sp,
                  width: double.infinity,
                  color: colorE1,
                ),
                Click(
                  onTap: () {
                    BotToast.cleanAll();
                  },
                  child: Container(
                    height: 46.sp,
                    width: double.infinity,
                    color: Colors.transparent,
                    alignment: Alignment.center,
                    child: MyText(Lang.i_knew, color2B, 14.sp, FontWeight.w500),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
      showSave
          ? Align(
              alignment: Alignment.bottomCenter,
              child: SlideTransition(
                position: _offsetAnimation,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Click(
                        child: Container(
                          width: 335.sp,
                          height: 48.sp,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16.sp),
                          ),
                          child: MyText(Lang.save, color2B, 14.sp, FontWeight.w500),
                        ),
                        onTap: () {
                          saveWechatCodeImage();
                        }),
                    SizedBox(height: 8.sp),
                    Click(
                        child: Container(
                          width: 335.sp,
                          height: 48.sp,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16.sp),
                          ),
                          child: MyText(Lang.cancel, colorRed, 14.sp, FontWeight.w500),
                        ),
                        onTap: () {
                          BotToast.cleanAll();
                        }),
                    SizedBox(height: 44.sp),
                  ],
                ),
              ),
            )
          : const SizedBox(),
    ]);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
