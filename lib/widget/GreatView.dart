import 'package:flutter/material.dart';
import 'package:flutter_gif/flutter_gif.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/common_utils.dart';

class GreatView extends BasePage {
  const GreatView({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _GreatViewState();
}

class _GreatViewState extends BasePageState<GreatView> with TickerProviderStateMixin {
  double _opacityLevel = 1.0;

  late AnimationController _controller;
  late Animation<double> _animation;
  late FlutterGifController _gifController;

  @override
  void initState() {
    logger("GreatView initState");
    super.initState();
    _opacityLevel = 1.0;

    _gifController = FlutterGifController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
      reverseDuration: const Duration(milliseconds: 200),
    );
    _gifController.value = 0;
    _gifController.animateTo(49, duration: const Duration(milliseconds: 2000));

    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 1000));
    _animation = Tween(begin: 1.0, end: 0.0).animate(_controller)
      ..addListener(() {
        setState(() {
          _opacityLevel = _animation.value;
        });
      });
    delay(3000, () {
      if (!isDisposed) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    isDisposed = true;
    _controller.dispose();
    _gifController.dispose();
    logger("GreatView dispose");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // logger("GreatView build $_opacityLevel");
    return _opacityLevel < 0.01
        ? const SizedBox()
        : Center(
            child: Container(
              margin: EdgeInsets.only(bottom: 70.sp),
              child: Opacity(
                opacity: _opacityLevel,
                child: GifImage(
                  controller: _gifController,
                  image: const AssetImage("res/scan/scan_great.webp"),
                  width: 909.sp,
                  height: 405.sp,
                ),
              ),
            ),
          );
  }
}
