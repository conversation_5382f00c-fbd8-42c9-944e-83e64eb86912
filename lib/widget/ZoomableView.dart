import 'package:flutter/material.dart';
import 'package:mooeli/utils/common_utils.dart';

class ZoomableView extends StatefulWidget {
  final Widget child;

  const ZoomableView({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  _ZoomableViewState createState() => _ZoomableViewState();
}

class _ZoomableViewState extends State<ZoomableView> {
  final TransformationController _transformationController = TransformationController();
  bool isZooming = false;
  int downTs = 0;
  int pointerCount = 0;

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (event) {
        logger("ZoomableView onPointerDown $pointerCount");
        pointerCount++;
        if (pointerCount == 1) {
          int ts = DateTime.now().millisecondsSinceEpoch;
          if (ts - downTs < 300) {
            logger("ZoomableView scale reset");
            dynamic trans = _transformationController.value.getTranslation();
            setState(() {
              _transformationController.value = Matrix4.identity()
                ..transform3(trans)
                ..scaled(1.0, 1.0, 1.0);
            });
          }
          downTs = ts;
        }
      },
      onPointerUp: (event) {
        logger("ZoomableView onPointerUp $pointerCount");
        pointerCount--;
      },
      onPointerMove: (event) {
        logger("ZoomableView onPointerMove $pointerCount");
        setState(() {
          isZooming = pointerCount == 2;
        });
      },
      onPointerCancel: (event) {
        logger("ZoomableView onPointerCancel $pointerCount");
        pointerCount = 0;
        setState(() {
          isZooming = false;
        });
      },
      child: InteractiveViewer(
        transformationController: _transformationController,
        minScale: 0.5,
        maxScale: 5.0,
        child: SingleChildScrollView(
          physics: isZooming ? NeverScrollableScrollPhysics() : AlwaysScrollableScrollPhysics(),
          child: widget.child,
        ),
      ),
    );
  }
}
