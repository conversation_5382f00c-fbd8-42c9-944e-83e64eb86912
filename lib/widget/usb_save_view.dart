import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/android_method.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class UsbPathScreen extends StatefulWidget {
  void Function(String path) onSave;

  UsbPathScreen(this.onSave, {super.key});

  @override
  _UsbPathScreenState createState() => _UsbPathScreenState();
}

class _UsbPathScreenState extends State<UsbPathScreen> {
  List<Map<String, String>> usbDevices = [];
  String? selectedPath;

  @override
  void initState() {
    super.initState();
    loadUsbPaths();
  }

  /// 获取所有 U 盘路径
  Future<void> loadUsbPaths() async {
    final paths = await getUsbDevices();
    setState(() {
      usbDevices = paths;
    });
  }

  getUsbPaths() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: usbDevices.map((device) {
        final name = device['name'] ?? 'Unknown Device'; // 设备名称
        final path = device['path']; // 设备路径
        bool isSelect = path == selectedPath;
        return Click(
            onTap: () {
              setState(() {
                selectedPath = path;
              });
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(32.sp, 40.sp, 32.sp, 32.sp),
              margin: EdgeInsets.only(top: usbDevices.indexOf(device) == 0 ? 0 : 16.sp),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.sp),
                border: isSelect ? Border.all(color: colorBrand) : null,
                color: isSelect ? colorPurpleLight : colorFA,
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 32.sp,
                    height: 32.sp,
                    child: Transform.scale(
                      scale: 1.4,
                      child: Material(
                        color: Colors.transparent,
                        child: Radio<String?>(
                          activeColor: colorBrand,
                          value: path,
                          onChanged: (value) {
                            setState(() {
                              selectedPath = value;
                            });
                          },
                          groupValue: selectedPath,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(16.sp, 0.sp, 0.sp, 0.sp),
                    child: MyText(name, isSelect ? colorBrand : color2B, 22.sp, FontWeight.w400),
                  ),
                ],
              ),
            ));
      }).toList(),
    );

    // final device = usbDevices[0];
    //     final name = device['name'] ?? 'Unknown Device'; // 设备名称
    //     final path = device['path']; // 设备路径
    //     bool isSelect = path == selectedPath;
    // return MyText(name,
    //     isSelect ? colorBrand : color2B, 22.sp, FontWeight.w400);
    // return IntrinsicHeight(
    //     child: ListView.separated(
    //   itemBuilder: (context, index) {
    //     final device = usbDevices[index];
    //     final name = device['name'] ?? 'Unknown Device'; // 设备名称
    //     final path = device['path']; // 设备路径
    //     bool isSelect = path == selectedPath;
    //     return Click(
    //         onTap: () {
    //           setState(() {
    //             selectedPath = path;
    //           });
    //         },
    //         child:Container(
    //           padding: EdgeInsets.fromLTRB(32.sp, 40.sp, 32.sp, 32.sp),
    //           decoration: BoxDecoration(
    //             borderRadius: BorderRadius.circular(16.sp),
    //             border: isSelect ? Border.all(color: colorBrand) : null,
    //             color: isSelect ? colorPurpleLight : colorFA,
    //           ),
    //           child: Row(
    //             children: [
    //               SizedBox(
    //                 width: 32.sp,
    //                 height: 32.sp,
    //                 child:
    //                 Material(
    //                   color: Colors.transparent,
    //                   child: Radio<String?>(
    //                     activeColor: colorBrand,
    //                     value: path,
    //                     onChanged: (value) {
    //                       setState(() {
    //                         selectedPath = value;
    //                       });
    //                     },
    //                     groupValue: selectedPath,
    //                   ),
    //                 ),
    //               ),
    //
    //               Container(
    //                 margin: EdgeInsets.fromLTRB(
    //                     16.sp, 0.sp, 0.sp, 0.sp),
    //                 child: MyText(device['name']!,
    //                     isSelect ? colorBrand : color2B, 22.sp, FontWeight.w400),
    //
    //               ),
    //
    //             ],
    //           ),
    //         ));
    //   }, separatorBuilder: (context, index) => SizedBox(height: 16.sp), itemCount: usbDevices.length,
    //
    // ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 0.4.sw,
      padding: EdgeInsets.all(32.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Text(Lang.select_usb_save,
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w500,
                    color: color2B,
                  )),
              const Spacer(),
              Click(
                  onTap: () {
                    SmartDialog.dismiss();
                  },
                  child: Image.asset("res/icons/icon_dialog_close.png", width: 44.sp)),
            ],
          ),
          Container(
              margin: EdgeInsets.fromLTRB(0.sp, 48.sp, 0.sp, 48.sp),
              child: usbDevices.isEmpty
                  ? Text(Lang.usb_recognized,
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w400,
                        color: color2B,
                      ))
                  : getUsbPaths()),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: GestureDetector(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: colorCA),
                        borderRadius: BorderRadius.circular(42.sp),
                      ),
                      padding: EdgeInsets.fromLTRB(16.sp, 24.sp, 16.sp, 24.sp),
                      child: Text(Lang.cancel,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w400,
                            color: color2B,
                          )),
                    )),
              ),
              SizedBox(width: 16.sp),
              Expanded(
                flex: 1,
                child: GestureDetector(
                    onTap: () {
                      // selectedPath = "test";
                      if (selectedPath != null && selectedPath!.isNotEmpty) {
                        widget.onSave(selectedPath!);
                      }
                      SmartDialog.dismiss();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: colorBrand,
                        borderRadius: BorderRadius.circular(44.sp),
                      ),
                      padding: EdgeInsets.fromLTRB(16.sp, 24.sp, 16.sp, 24.sp),
                      child: Text(Lang.confirm_ok,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 32.sp,
                            fontWeight: FontWeight.w400,
                            color: Colors.white,
                          )),
                    )),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
