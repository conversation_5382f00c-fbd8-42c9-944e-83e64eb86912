import 'package:flutter/material.dart';

class RotationWidget extends StatefulWidget {
  Widget child;

  RotationWidget(this.child, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _RotationWidgetState();
}

class _RotationWidgetState extends State<RotationWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 1000));
    _animation = Tween(begin: 0.0, end: 0.25).animate(_controller);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _animation,
      child: widget.child,
    );
  }
}
