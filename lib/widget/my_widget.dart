import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LingyaAppBar extends StatelessWidget implements PreferredSizeWidget {
  final double contentHeight; //从外部指定高度
  final Widget child;

  LingyaAppBar({
    required this.child,
    required this.contentHeight,
  }) : super();

  @override
  Size get preferredSize => Size.fromHeight(contentHeight);

  @override
  Widget build(BuildContext context) {
    return child;
  }
}

class Click extends StatelessWidget {
  Widget child;
  dynamic onTap;
  int ms;

  int clickTime = 0;

  Click({required this.child, required this.onTap, this.ms = 1000});

  @override
  Widget build(BuildContext context) {
    if (onTap == null) {
      return child;
    }
    return GestureDetector(
      child: child,
      onTap: () {
        int time = DateTime.now().millisecondsSinceEpoch;
        if (time - clickTime >= ms) {
          clickTime = time;
          onTap();
          return;
        }
      },
    );
  }
}

Text MyText(String text, Color color, double size, [FontWeight? weight]) {
  return Text(
    text,
    style: TextStyle(
      fontSize: size,
      color: color,
      fontWeight: weight ?? FontWeight.normal,
    ),
    softWrap: true,
  );
}

Text HeightText(String text, Color color, double size, double height, [FontWeight? weight, TextAlign? align]) {
  return Text(
    text,
    textAlign: align ?? TextAlign.start,
    style: TextStyle(
      fontSize: size,
      color: color,
      height: height,
      fontWeight: weight ?? FontWeight.normal,
    ),
    softWrap: true,
  );
}

Text SingleText(String text, Color color, double size, [FontWeight? weight]) {
  return Text(
    text,
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    style: TextStyle(
      fontSize: size,
      color: color,
      fontWeight: weight ?? FontWeight.normal,
    ),
  );
}

class NoOverScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(BuildContext context, Widget child, AxisDirection axisDirection) {
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
        return child;
      case TargetPlatform.android:
      case TargetPlatform.fuchsia:
        return GlowingOverscrollIndicator(
          child: child,
          // 不显示头部水波纹
          showLeading: false,
          // 不显示尾部水波纹
          showTrailing: false,
          axisDirection: axisDirection,
          color: Theme.of(context).accentColor,
        );
      case TargetPlatform.linux:
        break;
      case TargetPlatform.macOS:
        break;
      case TargetPlatform.windows:
        break;
    }
    return child;
  }
}

class TriangleView extends StatelessWidget {
  final double width, height;
  final Color color;

  TriangleView(this.width, this.height, this.color);

  @override
  Widget build(BuildContext context) {
    // logger("TriangleView build: $width x $height");
    return CustomPaint(
      size: Size(width, height),
      painter: TrianglePainter(width, height, color),
    );
  }
}

class TrianglePainter extends CustomPainter {
  final double width, height;
  final Color color;

  TrianglePainter(this.width, this.height, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    // logger("TriangleView paint: $size");
    Paint paint = Paint()
      ..color = color
      ..strokeWidth = 1.sp
      ..style = PaintingStyle.fill;

    Path path = Path();
    path.moveTo(width / 2, 0);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class DashLine extends StatelessWidget {
  final double height;
  final Color color;
  final double dashWidth;

  const DashLine({this.height = 1, this.color = Colors.black, this.dashWidth = 10.0});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(
                decoration: BoxDecoration(color: color),
              ),
            );
          }),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
        );
      },
    );
  }
}
