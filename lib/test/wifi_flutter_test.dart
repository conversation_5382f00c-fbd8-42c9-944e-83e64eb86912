import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:wifi_flutter/wifi_flutter.dart';

/// Example app for wifi_scan plugin.
class WifiFlutterTest extends BasePage {
  /// Default constructor for [WifiFlutterTest] widget.
  const WifiFlutterTest({Key? key}) : super(key: key);

  @override
  State<WifiFlutterTest> createState() => _WifiFlutterTestState();
}

class _WifiFlutterTestState extends BasePageState<WifiFlutterTest> {
  List<WifiNetwork> wifiList = [];

  @override
  void initState() {
    super.initState();
    getWifiList();
  }

  void getWifiList() async {
    wifiList = (await WifiFlutter.scanNetworks()).toList();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Flutter Wifi'),
        ),
        body: ListView.builder(
          itemCount: wifiList.length,
          itemBuilder: (context, index) {
            WifiNetwork wifi = wifiList[index];
            return Click(
              onTap: () {

              },
              child: Container(
                margin: EdgeInsets.all(16.sp),
                padding: EdgeInsets.all(8.sp),
                child: Text("wifi名称: ${wifi.ssid}\nwifi强度: ${wifi.rssi}\n需要密码: ${wifi.isSecure}"),
              ),
            );
          },
        ),
      ),
    );
  }
}
