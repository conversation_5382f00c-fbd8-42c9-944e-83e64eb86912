import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_uvc_camera/flutter_uvc_camera.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/common_utils.dart';

class CameraTestPage extends BasePage {
  int cameraId;

  CameraTestPage({super.key, this.cameraId = 1003});

  @override
  State<CameraTestPage> createState() => _CameraTestPageState();
}

class _CameraTestPageState extends BasePageState<CameraTestPage> {
  UVCCameraController? cameraController;
  String? imagePath = "";
  String logText = "";
  List<int> deviceIdList = [];
  int deviceIndex = 0;

  @override
  void initState() {
    super.initState();
    cameraController = UVCCameraController();
    cameraController?.cameraStateCallback = (state) {
      setState(() {
        logText += "State: $state\n";
      });
    };
    cameraController?.msgCallback = (msg) {
      setState(() {
        logText += "Msg: $msg\n";
      });
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('UVC Camera Example'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              children: [
                ElevatedButton(
                    onPressed: () async {
                      dynamic deviceList = await cameraController!.getDeviceIdList();
                      try {
                        List list = deviceList as List;
                        deviceIdList.clear();
                        for (dynamic id in list) {
                          deviceIdList.add(id);
                        }
                        await cameraController!.switchCameraId(deviceIdList[deviceIndex]);
                        setState(() {
                          logText += "$deviceIdList\n";
                        });
                      } catch (ex) {
                        setState(() {
                          logText += "$ex\n";
                        });
                      }
                    },
                    child: Text("相机列表")),
                SizedBox(width: 20.sp),
                ElevatedButton(
                    onPressed: () async {
                      replace(CameraTestPage(cameraId: 2007));
                    },
                    child: Text("切换相机")),
                SizedBox(width: 20.sp),
                ElevatedButton(
                    onPressed: () async {
                      // String? path = await cameraController!.takePicture();
                      // setState(() {
                      //   logText += "$path\n";
                      //   if (isNotEmpty(path)) {
                      //     imagePath = path;
                      //   }
                      // });
                    },
                    child: Text("拍照")),
              ],
            ),
            Row(
              children: [
                UVCCameraView(
                  cameraController: cameraController!,
                  cameraId: widget.cameraId,
                  width: 200.sp,
                  height: 200.sp,
                ),
                SizedBox(width: 10.sp),
                isEmpty(imagePath) ? const SizedBox() : Image.file(File(imagePath!), width: 200.sp),
                SizedBox(width: 10.sp),
                SizedBox(width: 1.sw - 420.sp, child: Text(logText)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
