import 'package:flutter/material.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/test/uvc_camera_list_page.dart';
import 'package:mooeli/test/uvc_camera_page.dart';
import 'package:mooeli/test/wifi_flutter_test.dart';
import 'package:mooeli/test/wifi_iot_test.dart';
import 'package:mooeli/test/wifi_scan_test.dart';

class TestListPage extends BasePage {
  const TestListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _TestListPagetate();
  }
}

class _TestListPagetate extends BasePageState<TestListPage> {
  @override
  void initState() {
    super.initState();
    setScreenVertiacl();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("测试功能"),
      ),
      body: Container(
        margin: const EdgeInsets.all(20),
        child: ListView(
          children: [
            ElevatedButton(
                onPressed: () {
                  push(CameraTestPage());
                },
                child: const Text("相机切换")),
            ElevatedButton(
                onPressed: () {
                  push(CameraListPage());
                },
                child: const Text("多相机")),
            ElevatedButton(
                onPressed: () {
                  push(WifiIotTest());
                },
                child: const Text("WifiIotTest")),
            ElevatedButton(
                onPressed: () {
                  push(WifiScanTest());
                },
                child: const Text("WifiScanTest")),
            ElevatedButton(
                onPressed: () {
                  push(WifiFlutterTest());
                },
                child: const Text("WifiFlutterTest")),
          ],
        ),
      ),
    );
  }
}
