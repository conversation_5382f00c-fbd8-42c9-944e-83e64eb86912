import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_uvc_camera/flutter_uvc_camera.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/common_utils.dart';

class CameraListPage extends BasePage {
  const CameraListPage({super.key});

  @override
  State<CameraListPage> createState() => _CameraListPageState();
}

class _CameraListPageState extends BasePageState<CameraListPage> {
  List<int> deviceIdList = [];
  List<UVCCameraController> controllerList = [];
  List<GlobalKey> keyList = [];

  String? imagePath = "";
  String logText = "";
  int deviceIndex = 0;

  late UVCCameraController controller;

  @override
  void initState() {
    super.initState();
    controller = UVCCameraController();
    controller.initChannel(0);
    // cameraController2?.cameraStateCallback = (state) {
    //   setState(() {
    //     logText += "${cameraController2?.getChannelName()} State: $state\n";
    //   });
    // };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('UVC Camera Example'),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    isEmpty(keyList)
                        ? const SizedBox()
                        : Padding(
                            padding: EdgeInsets.only(top: 0.5.sh - 337.5.sp, left: 360.sp),
                            child: Transform.scale(
                              scale: 0.5,
                              alignment: Alignment.topLeft,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(48.sp),
                                child: SizedBox(
                                  width: 2400.sp,
                                  height: 1350.sp,
                                  child: UVCCameraView(
                                    key: keyList[0],
                                    cameraController: controllerList[0],
                                    index: 0,
                                    cameraId: deviceIdList[0],
                                    width: 2400.sp,
                                    height: 1350.sp,
                                  ),
                                ),
                              ),
                            ),
                          ),
                    // SizedBox(
                    //   width: 1800.sp,
                    //   height: 1200.sp,
                    //   child: GridView(
                    //     physics: const NeverScrollableScrollPhysics(),
                    //     gridDelegate:
                    //         const SliverGridDelegateWithFixedCrossAxisCount(
                    //       crossAxisCount: 1,
                    //       childAspectRatio: 16 / 9,
                    //     ),
                    //     children: getCameraViews(),
                    //   ),
                    // ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            children: [
              Row(
                children: [
                  SizedBox(width: 20.sp),
                  ElevatedButton(
                      onPressed: () async {
                        dynamic deviceList = await controller.getDeviceIdList();
                        try {
                          List list = deviceList as List;
                          logText += "$list\n";
                          deviceIdList.clear();
                          controllerList.clear();
                          keyList.clear();
                          for (dynamic id in list) {
                            deviceIdList.add(id);

                            keyList.add(GlobalKey());

                            UVCCameraController cameraController = UVCCameraController();
                            cameraController.cameraStateCallback = (state) {
                              setState(() {
                                logText += "${cameraController.getChannelName()} State: $state\n";
                              });
                              if (state == UVCCameraState.opened) {
                                cameraController.updateResolution(PreviewSize(width: 2560, height: 1440));
                              }
                            };
                            controllerList.add(cameraController);
                          }
                          setState(() {});
                        } catch (ex) {
                          setState(() {
                            logText += "$ex\n";
                          });
                        }
                      },
                      child: Text("相机列表")),
                  SizedBox(width: 20.sp),
                  ElevatedButton(
                      onPressed: () async {
                        controllerList[0].takePicture(
                          onSuccess: (path) {
                            setState(() {
                              logText += path;
                              if (isNotEmpty(path)) {
                                imagePath = path;
                                logText += "  ${File(path!).lengthSync()}\n";
                              }
                            });
                          },
                          onError: (error) {
                            setState(() {
                              logText += error;
                            });
                          },
                        );
                      },
                      child: Text("拍照")),
                  SizedBox(width: 20.sp),
                  UVCCameraView(
                    cameraController: controller,
                    index: 5,
                    cameraId: 10086,
                    width: 10.sp,
                    height: 10.sp,
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  isEmpty(imagePath) ? const SizedBox() : Image.file(File(imagePath!), width: 400.sp),
                  SizedBox(width: 40.sp),
                  Text(logText),
                ],
              ),
              // Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   children: [
              //     isEmpty(keyList)
              //         ? const SizedBox()
              //         : Transform.scale(
              //             scale: 0.3,
              //             alignment: Alignment.topLeft,
              //             child: ClipRRect(
              //               borderRadius: BorderRadius.circular(48.sp),
              //               child: SizedBox(
              //                 width: 4000.sp,
              //                 height: 2250.sp,
              //                 child: UVCCameraView(
              //                   key: keyList[0],
              //                   cameraController: controllerList[0],
              //                   index: 0,
              //                   cameraId: deviceIdList[0],
              //                   width: 4000.sp,
              //                   height: 2250.sp,
              //                 ),
              //               ),
              //             ),
              //           ),
              //     // SizedBox(
              //     //   width: 1800.sp,
              //     //   height: 1200.sp,
              //     //   child: GridView(
              //     //     physics: const NeverScrollableScrollPhysics(),
              //     //     gridDelegate:
              //     //         const SliverGridDelegateWithFixedCrossAxisCount(
              //     //       crossAxisCount: 1,
              //     //       childAspectRatio: 16 / 9,
              //     //     ),
              //     //     children: getCameraViews(),
              //     //   ),
              //     // ),
              //   ],
              // ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> getCameraViews() {
    List<Widget> list = [];
    for (int i = 0; i < deviceIdList.length; i++) {
      list.add(
        ClipRRect(
          borderRadius: BorderRadius.circular(48.sp),
          child: SizedBox(
            width: 1200.sp,
            height: 675.sp,
            child: Transform.scale(
              scale: 1,
              child: UVCCameraView(
                key: keyList[i],
                cameraController: controllerList[i],
                index: i,
                cameraId: deviceIdList[i],
                width: 16000.sp,
                height: 9000.sp,
              ),
            ),
          ),
        ),
      );
    }
    return list;
  }
}
