import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/common_utils.dart';

// {
// "caseId": "1edc98c4e1d94b8aaecd09ebf31fa154",
// "caseName": "何玉振",
// "birthday": 849225600000,
// "latestScanTime": 1691128290836,
// "isPatientFeedback": true,
// "isProviderFeedback": false,
// "monitorStatusVo": {
// "monitorStatus": "OVERDUE",
// "overdueDays": -8
// },
// "timeNumber": 1,
// "timeUnit": "DAY",
// "gender": "female",
// "avatarFileId": "f15f5fc34fcb4e98a8fbac5170f87d86"
// }
class MooeliItem {
  String caseId = "";
  String caseName = "";
  int latestScanTime = 0;
  int birthday = 0;
  String monitorStatus = "";
  String avatarFileId = "";
  String avatarPath = "";
  String gender = "";

  MooeliItem({
    this.caseId = "",
    this.caseName = "",
    this.latestScanTime = 0,
    this.birthday = 0,
    this.monitorStatus = "",
    this.avatarFileId = "",
    this.avatarPath = "",
    this.gender = "",
  });

  MooeliItem.fromJson(Map<String, dynamic> json) {
    caseId = getJsonString(json, "caseId");
    caseName = getJsonString(json, "caseName");
    latestScanTime = getJsonInt(json, "latestScanTime");
    birthday = getJsonInt(json, "birthday");
    monitorStatus = getJsonString(getJsonMap(json, "monitorStatus"), "monitorStatus");
    gender = getJsonString(json, "gender");
    if (isNotEmpty(json["avatarFileId"])) {
      avatarFileId = getJsonString(json, "avatarFileId");
    }
  }

  getStatusText() {
    return getStatus(monitorStatus);
  }

  getGender() {
    switch (gender.toLowerCase()) {
      case "male":
        return Lang.gender_man;
      case "female":
        return Lang.gender_woman;
      default:
        return "-";
    }
  }
}

// "phaseId": "af53110532df4c06ad386d4d92df76b3",
// "phaseName": "口腔全科观察",
// "monitorStatus": "PROGRESS",
// "latestScanTime": 1691128290836,
// "createTime": 1687952313308,
// "modifyTime": 1693897200090,
// "extendedInfo": {}
class MooeliPhase {
  String phaseId = "";
  String phaseName = "";
  String monitorStatus = "";
  int latestScanTime = 0;
  int createTime = 0;
  int modifyTime = 0;

  MooeliPhase.fromJson(Map<String, dynamic> json) {
    phaseId = getJsonString(json, "monitorId");
    phaseName = getJsonString(json, "monitorName");
    monitorStatus = getJsonString(json, "monitorStatus");
    latestScanTime = getJsonInt(json, "latestScanTime");
    createTime = getJsonInt(json, "createTime");
    modifyTime = getJsonInt(json, "modifyTime");
  }

  getStatusText() {
    return getStatus(monitorStatus);
  }
}

getStatus(String status) {
  switch (status) {
    case "OVERDUE":
      return Lang.status_overtime;
    case "CREATE":
      return Lang.status_not_binding;
    case "PROGRESS":
    case "NORMAL":
      return Lang.status_processing;
    case "SEAL":
      return Lang.status_deleted;
    case "BIND":
      return Lang.status_bound;
    case "CANCEL":
      return Lang.status_destroy;
    default:
      return Lang.status_processing;
  }
}

// "recordId": "76f554ba127d4cfdba1a486c65a54da5",
// "scanTime": 1691128290836,
// "name": ""
class MooeliRecord {
  String recordId = "";
  String name = "";
  int scanTime = 0;
  String phaseName = "";

  MooeliRecord();

  MooeliRecord.fromJson(Map<String, dynamic> json) {
    recordId = getJsonString(json, "recordId");
    name = getJsonString(json, "name");
    scanTime = getJsonInt(json, "scanTime");
  }
}

// "attribute": "153",
// "id": "55373fafff1740029b4f0d1906d6d734",
// "status": 0.0,
// "type": 6000.0,
// "version": 2.0

class MooeliFile {
  String attribute = "";
  String id = "";
  String imagePath = "";

  MooeliFile();

  MooeliFile.fromJson(Map<String, dynamic> json, bool isScan) {
    attribute = isScan ? getJsonInt(json, "fileCategory").toString() : getJsonString(json, "attribute");
    id = isScan ? getJsonString(json, "fileId") : getJsonString(json, "id");
  }
}
