// {
// "caseId": "1e59eeb63a5541e3a602855e6d0880ae",
// "createTime": "1684406376588",
// "modifyTime": "1693481869045",
// "caseCode": "",
// "name": "458",
// "avatarFileId": "39f076e0dc494336922c1544e5ed618c",
// "gender": "male",
// "address": "",
// "contact": "",
// "birthday": 1186502400000,
// "colorTag": "#9DC7FF",
// "subscribe": false,
// "tagNameList": [
// {
// "tagId": "5fbe0da7e93049e5912b873ae56d2d27",
// "tagName": "缺牙"
// },
// {
// "tagId": "2db9e57d2fa64237899d688c51d43258",
// "tagName": "根尖周炎"
// },
// {
// "tagId": "aa0c80fda737413ead45dbb337e0cdda",
// "tagName": "龋齿"
// }
// ],
// "familyHistory": "",
// "allergyHistory": "",
// "treatmentHistory": "",
// "chiefComplaint": "",
// "comment": "",
// "statusTag": "0",
// "createFrom": 1,
// "externalSource": ""
// },

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class LingyaItem {
  String caseId = "";
  String name = "";
  int birthday = 0;
  int createTime = 0;
  String gender = "";
  String avatarFileId = "";
  String avatarPath = "";
  bool subscribe = false;
  String colorTag = "";
  String statusTag = "";
  List<String> tagList = [];

  String address = "";
  String caseCode = "";
  String contact = "";
  String familyHistory = "";
  String allergyHistory = "";
  String treatmentHistory = "";
  String chiefComplaint = "";
  String comment = "";

  LingyaItem.fromJson(Map<String, dynamic> json) {
    caseId = json["caseId"];
    name = json["name"];
    birthday = json["birthday"];
    createTime = json["createTime"];
    gender = json["gender"];
    subscribe = json["subscribe"];
    colorTag = getJsonString(json, "colorTag");
    statusTag = getJsonString(json, "statusTag");
    if (isNotEmpty(json["avatarFileId"])) {
      avatarFileId = json["avatarFileId"];
    }
    if (isNotEmpty(json["customTagList"])) {
      tagList = (json["customTagList"] as List).map((i) => "$i").toList();
    }
    caseCode = getJsonString(json, "caseCode");
    contact = getJsonString(json, "phone");
    address = getJsonString(json, "address");
    familyHistory = getJsonString(json, "familyHistory");
    allergyHistory = getJsonString(json, "allergyHistory");
    treatmentHistory = getJsonString(json, "treatmentHistory");
    chiefComplaint = getJsonString(json, "chiefComplaint");
    comment = getJsonString(json, "note");
  }

  getGender() {
    switch (gender.toLowerCase()) {
      case "male":
        return Lang.gender_man;
      case "female":
        return Lang.gender_woman;
      default:
        return "-";
    }
  }

  Color getTagColor() {
    return isEmpty(colorTag) ? Colors.white : string2Color(colorTag);
  }

  MooeliItem toMooeliItem() {
    return MooeliItem(
      caseId: caseId,
      caseName: name,
      birthday: birthday,
      avatarFileId: avatarFileId,
      avatarPath: avatarPath,
      gender: gender,
    );
  }
}

// "phaseId": "8f7e6e07cfdf4b069f80e200c6514966",
// "name": "正畸",
// "color": "",
// "document": {},
// "caseId": "1e59eeb63a5541e3a602855e6d0880ae",
// "createTime": "1684406376614",
// "modifyTime": "1684406376614"
class LingyaPhase {
  String phaseId = "";
  String name = "";
  String caseId = "";
  dynamic document = {};
  int createTime = 0;
  int modifyTime = 0;

  LingyaPhase.fromJson(Map<String, dynamic> json) {
    phaseId = getJsonString(json, "phaseId");
    name = getJsonString(json, "name");
    caseId = getJsonString(json, "caseId");
    document = json["document"];
    createTime = json["createTime"];
    modifyTime = json["modifyTime"];
  }

  bool hasDocument() {
    return isNotEmpty(document);
  }
}

// "recordId": "847303b39b8546a581e08034b8d3af43",
// "createTime": "1684406376797",
// "modifyTime": "1684406413468",
// "customTime": "1661356800000",
// "version": 2,
// "status": 0,
// "name": "初诊",
// "note": "",
// "metadata": null,
// "caseId": "1e59eeb63a5541e3a602855e6d0880ae",
// "phaseId": "8f7e6e07cfdf4b069f80e200c6514966",
// "phaseName": "正畸"
class LingyaRecord {
  String recordId = "";
  int createTime = 0;
  int modifyTime = 0;
  int customTime = 0;
  int version = 0;
  int status = 0;
  String name = "";
  String note = "";
  String caseId = "";
  String phaseId = "";
  String phaseName = "";

  LingyaRecord();

  LingyaRecord.fromJson(Map<String, dynamic> json) {
    recordId = getJsonString(json, "recordId");
    createTime = json["createTime"];
    modifyTime = json["modifyTime"];
    // customTime = json["customTime"];
    // version = json["version"];
    // status = json["status"];
    name = getJsonString(json, "name");
    note = getJsonString(json, "note");
    caseId = getJsonString(json, "caseId");
    phaseId = getJsonString(json, "phaseId");
    phaseName = getJsonString(json, "phaseName");
    if (isEmpty(note)) {
      note = Lang.none;
    }
  }
}

// "fileId": "39f076e0dc494336922c1544e5ed618c",
// "name": "img_0137副本.jpg",
// "category": "102",
// "confidence": "0.9153189659118652",
// "caseId": "1e59eeb63a5541e3a602855e6d0880ae",
// "recordId": "7224356e6bce4d159cec0c4bba7417a6",
// "createTime": "1693481863888",
// "modifyTime": "1693481868849",
// "phaseId": "8f7e6e07cfdf4b069f80e200c6514966",
// "version": 5,
// "status": 0,
// "analysisType": false,
// "fileType": 15,
// "subdivision": 0,
// "analysisInfoMap": {
// "5004": {
// "id": "37320c5a79a44de38ab0ef362e3681f0",
// "version": 1,
// "type": 5004,
// "attribute": null,
// "status": 5
// },
// "5031": {
// "id": "8d0c415a35454e3b87c639b4de9d9b95",
// "version": 1,
// "type": 5031,
// "attribute": null,
// "status": 5
// },
// "5032": {
// "id": "00e0ac13bd1d40108306a5d9d315ccdb",
// "version": 1,
// "type": 5032,
// "attribute": null,
// "status": 5
// }
// },
// "isAnalyzed": false,
// "fileChanged": true,
// "multiAnalysisInfoMap": {}
class LingyaFile {
  String fileId = "";
  String name = "";
  String category = "";
  String caseId = "";
  String recordId = "";
  String createTime = "";
  String modifyTime = "";
  String phaseId = "";
  int status = 0;
  bool analysisType = false;
  int fileType = 0;
  int subdivision = 0;
  bool isAnalyzed = false;
  dynamic analysisInfoMap;
  String imagePath = "";

  String getAnalyticsId() {
    if (isNotEmpty(analysisInfoMap) && analysisInfoMap is Map) {
      try {
        switch (category) {
          case "100":
            return analysisInfoMap["5002"]["id"];
          case "101":
            return analysisInfoMap["5003"]["id"];
          case "102":
            return analysisInfoMap["5031"]["id"];
          case "103": //正面咬合像
          case "105": //左侧咬合像
          case "106": //下牙弓像
          case "107": //右侧咬合像
          case "108": //上牙弓像
            return analysisInfoMap["5004"]["id"];
          case "109":
            return analysisInfoMap["5001"]["id"];
          case "110":
            return analysisInfoMap["5021"]["id"];
          default:
            return analysisInfoMap.values.first["id"];
        }
      } catch (ex) {
        //
      }
    }
    return "";
  }

  LingyaFile.fromJson(Map<String, dynamic> json) {
    fileId = getJsonString(json, "fileId");
    name = getJsonString(json, "name");
    category = getJsonInt(json, "category").toString();
    caseId = getJsonString(json, "caseId");
    recordId = getJsonString(json, "recordId");
    createTime = getJsonString(json, "createTime");
    modifyTime = getJsonString(json, "modifyTime");
    phaseId = getJsonString(json, "phaseId");
    status = getJsonInt(json, "status");
    analysisType = getJsonBool(json, "analysisType");
    fileType = getJsonInt(json, "fileType");
    subdivision = getJsonInt(json, "subdivision");
    isAnalyzed = getJsonBool(json, "isAnalyzed");
    analysisInfoMap = getJsonMap(json, "analysisInfoMap");
  }

  toJson() {
    return {
      "fileId": fileId,
      "name": name,
      "category": category,
      "caseId": caseId,
      "recordId": recordId,
      "phaseId": phaseId,
      "status": status,
      "fileType": fileType,
      "subdivision": subdivision,
      "isAnalyzed": isAnalyzed,
      "analysisInfoMap": analysisInfoMap,
    };
  }
}

class LingyaTag {
  String tagId = "";
  String tagName = "";

  LingyaTag(this.tagId, this.tagName);

  LingyaTag.fromJson(Map<String, dynamic> json) {
    tagId = json["tagId"];
    tagName = json["tagName"];
  }
}

class LingyaTenant {
  String id = "";
  String name = "";
  String status = ""; //NORMAL:正常  DELETE:正在被删除,显示为正常  FAILED:重新审核 REVIEW:审核中
  String avatarFileId = "";
  String departmentId = "";
  String departmentName = "";
  bool isTenant = false;

  LingyaTenant();

  LingyaTenant.fromJson(Map<String, dynamic> json, bool tenant) {
    id = getJsonString(json, "id");
    name = getJsonString(json, "name");
    status = getJsonString(json, "status");
    avatarFileId = getJsonString(json, "avatarFileId");
    departmentId = getJsonString(json, "departmentId");
    departmentName = getJsonString(json, "departmentName");
    isTenant = tenant;
  }

  String getTenantIcon() {
    if (!isTenant) {
      return "res/icons/icon_person.png";
    }
    int sum = 0;
    for (int i = 0; i < id.length; i++) {
      sum += id.codeUnitAt(i);
    }
    int index = sum % 5;
    return "res/icons/icon_tenant$index.png";
  }

  bool isNormal() {
    return status == "NORMAL" || status == "DELETE";
  }

  //NORMAL:正常  DELETE:正在被删除,显示为正常  FAILED:重新审核 REVIEW:审核中
  Widget getTenantStatus() {
    if (!isTenant) {
      return SingleText(Lang.personal_version, color7C, 24.sp);
    } else {
      return Container(
        height: 34.sp,
        padding: EdgeInsets.fromLTRB(8.sp, 0, 8.sp, 0),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: const Color(0x1A6FC9C3),
          borderRadius: BorderRadius.circular(4.sp),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset("res/icons/icon_normal.png", width: 20.sp),
            SizedBox(width: 4.sp),
            MyText(Lang.status_normal, colorCyan, 18.sp),
          ],
        ),
      );
    }
  }
}
