import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

enum RecordStatus {
  recording, //待完善
  waitingUpload, //待上传
  uploading, //上传中
  completeUpload,
  failUpload,
}

class ScanActivity {
  // 本地活动信息
  int id = 0;
  String campaignId = "";
  String campaignName = "";
  String creatorName = "";
  String contactName = "";
  String contactPhone = "";
  String contactAddress = "";
  String qrCodeFileId = "";
  String qrcodePath = "";
  String qrCodeDescription = "";
  bool showPdfLogo = true;
  int createTime = 0;
  bool singleCampaign = false;
  int counter = 0;

  bool get isLocalData => id != 0;

  ScanActivity();

  ScanActivity.fromJson(Map<String, dynamic> json) {
    campaignId = getJsonString(json, "campaignId");
    campaignName = getJsonString(json, "campaignName");
    contactName = getJsonString(json, "contactName");
    creatorName = getJsonString(json, "creatorName");
    contactPhone = getJsonString(json, "contactPhone");
    contactAddress = getJsonString(json, "contactAddress");
    qrCodeFileId = getJsonString(json, "qrCodeFileId");
    qrcodePath = getJsonString(json, "qrcodePath");
    showPdfLogo = getJsonBool(json, "showPdfLogo", defaultValue: true);
    qrCodeDescription = getJsonString(json, "qrCodeDescription");
    createTime = getJsonInt(json, "createTime");
    counter = getJsonInt(json, "counter");
  }

  Map<String, dynamic> toJson() {
    return {
      "campaignId": campaignId,
      "campaignName": campaignName,
      "contactName": contactName,
      "creatorName": creatorName,
      "contactPhone": contactPhone,
      "contactAddress": contactAddress,
      "qrCodeFileId": qrCodeFileId,
      "qrcodePath": qrcodePath,
      "qrCodeDescription": qrCodeDescription,
      "createTime": createTime,
      "showPdfLogo": showPdfLogo,
      "counter": counter,
    };
  }

  ScanActivity.fromDb(Map<String, dynamic> json) {
    id = getJsonInt(json, "id");
    campaignId = getJsonString(json, "campaignId");
    campaignName = getJsonString(json, "campaignName");
    contactName = getJsonString(json, "contactName");
    creatorName = getJsonString(json, "creatorName");
    contactPhone = getJsonString(json, "contactPhone");
    contactAddress = getJsonString(json, "contactAddress");
    qrCodeFileId = getJsonString(json, "qrCodeFileId");
    qrcodePath = getJsonString(json, "qrcodePath");
    showPdfLogo = getJsonInt(json, "showPdfLogo") == 1;
    qrCodeDescription = getJsonString(json, "qrCodeDescription");
    createTime = getJsonInt(json, "createTime");
    counter = getJsonInt(json, "counter");
  }

  Map<String, dynamic> toDb() {
    return {
      "campaignId": campaignId,
      "campaignName": campaignName,
      "contactName": contactName,
      "creatorName": creatorName,
      "contactPhone": contactPhone,
      "contactAddress": contactAddress,
      "qrCodeFileId": qrCodeFileId,
      "qrcodePath": qrcodePath,
      "qrCodeDescription": qrCodeDescription,
      "createTime": createTime,
      "showPdfLogo": showPdfLogo ? 1 : 0,
      "counter": counter,
    };
  }

  String getCampaignName([int length = 30]) {
    if (campaignName.length <= length) {
      return campaignName;
    } else {
      return "${campaignName.substring(0, length)}...";
    }
  }

  ScanActivity copy() {
    return ScanActivity.fromJson(toJson());
  }
}

class ScanInfo {
  int id = 0;
  String recordId = "";
  String phaseId = "";
  String campaignId = "";
  String recordName = "";
  String campaignName = "";
  String patientPhone = "";
  String gender = "";
  String age = "";
  String smileFileId = "";
  String smilePhoto = "";
  String frontPhoto = "";
  String left90Photo = "";
  String right90Photo = "";
  String left45Photo = "";
  String right45Photo = "";
  List upPhotos = [];
  List closePhotos = [];
  List openPhotos = [];
  List maskPhotos = [];
  List downPhotos = [];
  List upXPhotos = [];
  List closeXPhotos = [];
  List openXPhotos = [];
  List maskXPhotos = [];
  List downXPhotos = [];
  String note = "";
  List tagList = [];
  int customTime = 0;
  String progressStage = "NONE";
  String recordSn = "";
  bool repeatSn = false;
  RecordStatus status = RecordStatus.recording;

  String extra = "";

  ScanInfo();

  ScanActivity getScanActivity() {
    ScanActivity activity = ScanActivity();
    activity.campaignId = campaignId;
    activity.campaignName = campaignName;
    return activity;
  }

  setPhoto(String type, String path) {
    Global.copyImageDocument(path, "scan_$recordId", save: true);
    logger("setPhoto $type $path");
    switch (type) {
      case "100":
        frontPhoto = path;
        break;
      case "102":
        smilePhoto = path;
        break;
      case "118":
      case "101":
        left90Photo = path;
        break;
      case "119":
        right90Photo = path;
        break;
      case "116":
        left45Photo = path;
        break;
      case "113":
        right45Photo = path;
        break;
    }
  }

  String getGender() {
    return gender == "female" ? Lang.gender_woman : Lang.gender_man;
  }

  String getAge() {
    if (isEmpty(age)) {
      return "0";
    }
    if (age.endsWith(".0")) {
      return age.substring(0, age.indexOf("."));
    }
    return age;
  }

  String getPhoto(String type) {
    switch (type) {
      case "100":
        return frontPhoto;
      case "102":
        return smilePhoto;
      case "118":
      case "101":
        return left90Photo;
      case "119":
        return right90Photo;
      case "116":
        return left45Photo;
      case "113":
        return right45Photo;
      default:
        return "";
    }
  }

  Future<bool> checkPhotoLost({bool removeLost = false}) async {
    List files = getAllPhotos();
    for (String file in files) {
      await Global.copyImageDocument(file, "scan_$recordId", save: false);
    }
    if (removeLost) {
      return clearLostPhotos();
    } else {
      return false;
    }
  }

  bool clearLostPhotos() {
    bool lost = false;
    if (isNotEmpty(frontPhoto) && !File(getLocalPath(frontPhoto)).existsSync()) {
      frontPhoto = "";
      lost = true;
    }
    if (isNotEmpty(smilePhoto) && !File(getLocalPath(smilePhoto)).existsSync()) {
      smilePhoto = "";
      lost = true;
    }
    if (isNotEmpty(left90Photo) && !File(getLocalPath(left90Photo)).existsSync()) {
      left90Photo = "";
      lost = true;
    }
    if (isNotEmpty(right90Photo) && !File(getLocalPath(right90Photo)).existsSync()) {
      right90Photo = "";
      lost = true;
    }
    if (isNotEmpty(left45Photo) && !File(getLocalPath(left45Photo)).existsSync()) {
      left45Photo = "";
      lost = true;
    }
    if (isNotEmpty(right45Photo) && !File(getLocalPath(right45Photo)).existsSync()) {
      right45Photo = "";
      lost = true;
    }
    for (String path in upPhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        upPhotos.clear();
        lost = true;
        break;
      }
    }
    for (String path in closePhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        closePhotos.clear();
        lost = true;
        break;
      }
    }
    for (String path in openPhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        openPhotos.clear();
        lost = true;
        break;
      }
    }
    for (String path in downPhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        downPhotos.clear();
        lost = true;
        break;
      }
    }
    return lost;
  }

  bool hasAllRequest() {
    return allNotEmpty([
      recordName,
      gender,
      age,
      upPhotos,
      closePhotos,
      openPhotos,
      downPhotos,
    ]);
  }

  bool hasData() {
    return hasNotEmpty([
      recordName,
      age,
      patientPhone,
      gender,
      smilePhoto,
      frontPhoto,
      left90Photo,
      right90Photo,
      left45Photo,
      right45Photo,
      upPhotos,
      closePhotos,
      openPhotos,
      downPhotos,
      note,
      tagList
    ]);
  }

  bool hasScanPhoto() {
    return isNotEmpty(upPhotos) ||
        isNotEmpty(closePhotos) ||
        isNotEmpty(openPhotos) ||
        // isNotEmpty(maskPhotos) ||
        isNotEmpty(downPhotos);
  }

  List getAllPhotos() {
    List photos = [];
    if (isNotEmpty(frontPhoto)) {
      photos.add(frontPhoto);
    }
    if (isNotEmpty(smilePhoto)) {
      photos.add(smilePhoto);
    }
    if (isNotEmpty(left90Photo)) {
      photos.add(left90Photo);
    }
    if (isNotEmpty(right90Photo)) {
      photos.add(right90Photo);
    }
    if (isNotEmpty(left45Photo)) {
      photos.add(left45Photo);
    }
    if (isNotEmpty(right45Photo)) {
      photos.add(right45Photo);
    }
    if (isNotEmpty(upPhotos)) {
      photos.addAll(upPhotos);
    }
    if (isNotEmpty(closePhotos)) {
      photos.addAll(closePhotos);
    }
    if (isNotEmpty(openPhotos)) {
      photos.addAll(openPhotos);
    }
    // if (isNotEmpty(maskPhotos)) {
    //   photos.addAll(maskPhotos);
    // }
    if (isNotEmpty(downPhotos)) {
      photos.addAll(downPhotos);
    }
    // if (isNotEmpty(upXPhotos)) {
    //   photos.addAll(upXPhotos);
    // }
    // if (isNotEmpty(closeXPhotos)) {
    //   photos.addAll(closeXPhotos);
    // }
    // if (isNotEmpty(openXPhotos)) {
    //   photos.addAll(openXPhotos);
    // }
    // if (isNotEmpty(maskXPhotos)) {
    //   photos.addAll(maskXPhotos);
    // }
    // if (isNotEmpty(downXPhotos)) {
    //   photos.addAll(downXPhotos);
    // }

    return photos;
  }

  Widget getStatusView() {
    late String text;
    late String icon;
    late Color color;
    switch (status) {
      case RecordStatus.recording:
        text = Lang.status_recording;
        icon = "icon_upload_improved.png";
        color = colorPink;
        break;
      case RecordStatus.waitingUpload:
        text = Lang.status_waiting_upload;
        icon = "icon_upload_wait.png";
        color = color7C;
        break;
      case RecordStatus.uploading:
        text = Lang.status_uploading;
        icon = "icon_upload_ing.png";
        color = colorGreen2;
        break;
      case RecordStatus.completeUpload:
        text = Lang.status_complete_upload;
        icon = "icon_upload_complete.png";
        color = colorGreen2;
        break;
      case RecordStatus.failUpload:
        text = Lang.status_fail_upload;
        icon = "icon_upload_fail.png";
        color = colorRed;
        break;
    }
    if (repeatSn && status == RecordStatus.waitingUpload) {
      text = Lang.bind_code_error;
      icon = "icon_bind_code_error.png";
      color = colorPink;
    }
    if (repeatSn && status == RecordStatus.recording) {
      return Row(
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(8.sp, 5.sp, 8.sp, 5.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(8.sp)),
              color: color.withOpacity(0.1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset("res/icons/$icon", width: 24.sp),
                SizedBox(width: 4.sp),
                MyText(text, color, 20.sp),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(8.sp, 5.sp, 8.sp, 5.sp),
            margin: EdgeInsets.only(left: 8.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(8.sp)),
              color: colorPink.withOpacity(0.1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset("res/icons/icon_bind_code_error.png", width: 24.sp),
                SizedBox(width: 4.sp),
                MyText(Lang.bind_code_error, colorPink, 20.sp),
              ],
            ),
          )
        ],
      );
    } else {
      return Container(
        padding: EdgeInsets.fromLTRB(8.sp, 5.sp, 8.sp, 5.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.sp)),
          color: color.withOpacity(0.1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset("res/icons/$icon", width: 24.sp),
            SizedBox(width: 4.sp),
            MyText(text, color, 20.sp),
          ],
        ),
      );
    }
  }

  ScanInfo.fromJson(Map json, [bool completed = false]) {
    id = getJsonInt(json, "id");
    recordId = getJsonString(json, "scanRecordId");
    campaignId = getJsonString(json, "campaignId");
    phaseId = getJsonString(json, "phaseId");
    recordName = getJsonString(json, "name");
    campaignName = getJsonString(json, "campaignName");
    patientPhone = getJsonString(json, "phone");
    gender = getJsonString(json, "gender");
    try {
      dynamic ageNum = json["age"];
      age = ageNum.toString();
    } catch (ex) {
      age = getJsonString(json, "age");
    }
    smileFileId = getJsonString(json, "smileFileId");
    smilePhoto = getJsonString(json, "smilePhoto");
    frontPhoto = getJsonString(json, "frontPhoto");
    left90Photo = getJsonString(json, "sidePhoto");
    right90Photo = getJsonString(json, "side2Photo");
    left45Photo = getJsonString(json, "leftPhoto");
    right45Photo = getJsonString(json, "rightPhoto");
    upPhotos = getJsonList(json, "upPhotos");
    closePhotos = getJsonList(json, "closePhotos");
    openPhotos = getJsonList(json, "openPhotos");
    maskPhotos = getJsonList(json, "maskPhotos");
    downPhotos = getJsonList(json, "downPhotos");
    upXPhotos = getJsonList(json, "upXPhotos");
    closeXPhotos = getJsonList(json, "closeXPhotos");
    openXPhotos = getJsonList(json, "openXPhotos");
    maskXPhotos = getJsonList(json, "maskXPhotos");
    downXPhotos = getJsonList(json, "downXPhotos");
    note = getJsonString(json, "note");
    tagList = getJsonList(json, "tagList");
    customTime = getJsonInt(json, "recordCreateTime");
    progressStage = getJsonString(json, "progressStage");
    if (isEmpty(progressStage)) {
      progressStage = "NONE";
    }
    recordSn = getJsonString(json, "qrCodeId");
    repeatSn = getJsonBool(json, "repeatSn");
    status = completed ? RecordStatus.completeUpload : RecordStatus.values[getJsonInt(json, "status")];
    extra = getJsonString(json, "extra");
    if (isNotEmpty(extra)) {
      Map map = jsonDecode(extra);
      if (isEmpty(smileFileId)) {
        smileFileId = getJsonString(map, "smileFileId");
      }
      if (isEmpty(smilePhoto)) {
        smilePhoto = getJsonString(map, "smilePhoto");
      }
      if (isEmpty(frontPhoto)) {
        frontPhoto = getJsonString(map, "frontPhoto");
      }
      if (isEmpty(left90Photo)) {
        left90Photo = getJsonString(map, "sidePhoto");
      }
      if (isEmpty(right90Photo)) {
        right90Photo = getJsonString(map, "side2Photo");
      }
      if (isEmpty(left45Photo)) {
        left45Photo = getJsonString(map, "leftPhoto");
      }
      if (isEmpty(right45Photo)) {
        right45Photo = getJsonString(map, "rightPhoto");
      }
      if (isEmpty(upPhotos)) {
        upPhotos = getJsonList(map, "upPhotos");
      }
      if (isEmpty(closePhotos)) {
        closePhotos = getJsonList(map, "closePhotos");
      }
      if (isEmpty(openPhotos)) {
        openPhotos = getJsonList(map, "openPhotos");
      }
      if (isEmpty(maskPhotos)) {
        maskPhotos = getJsonList(map, "maskPhotos");
      }
      if (isEmpty(downPhotos)) {
        downPhotos = getJsonList(map, "downPhotos");
      }
      if (isEmpty(tagList)) {
        tagList = getJsonList(map, "tagList");
      }
      if (isEmpty(note)) {
        note = getJsonString(map, "note");
      }
      if (isEmpty(progressStage)) {
        progressStage = getJsonString(map, "progressStage");
      }
      if (isEmpty(recordSn)) {
        recordSn = getJsonString(map, "recordSn");
      }
      if (isEmpty(progressStage) || progressStage == "NONE") {
        progressStage = getJsonString(map, "progressStage");
      }
    }
  }

  Map<String, dynamic> toJson() {
    return {
      "scanRecordId": recordId,
      "campaignId": campaignId,
      "phaseId": phaseId,
      "name": recordName,
      "campaignName": campaignName,
      "phone": patientPhone,
      "gender": gender,
      "age": age,
      "smileFileId": smileFileId,
      "smilePhoto": smilePhoto,
      "frontPhoto": frontPhoto,
      "sidePhoto": left90Photo,
      "side2Photo": right90Photo,
      "leftPhoto": left45Photo,
      "rightPhoto": right45Photo,
      "upPhotos": upPhotos,
      "closePhotos": closePhotos,
      "openPhotos": openPhotos,
      "maskPhotos": maskPhotos,
      "downPhotos": downPhotos,
      "upXPhotos": upXPhotos,
      "closeXPhotos": closeXPhotos,
      "openXPhotos": openXPhotos,
      "maskXPhotos": maskXPhotos,
      "downXPhotos": downXPhotos,
      "note": note,
      "tagList": tagList,
      "recordCreateTime": customTime,
      "progressStage": progressStage,
      "qrCodeId": recordSn,
      "repeatSn": repeatSn,
      "status": RecordStatus.values.indexOf(status),
    };
  }

  Map<String, dynamic> toDbJson() {
    return {
      "scanRecordId": recordId,
      "name": recordName,
      "campaignId": campaignId,
      "campaignName": campaignName,
      "phone": patientPhone,
      "gender": gender,
      "age": age,
      "extra": jsonEncode(toJson()),
      "recordCreateTime": customTime,
      "status": RecordStatus.values.indexOf(status),
    };
  }
}
