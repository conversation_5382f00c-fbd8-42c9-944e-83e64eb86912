import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/common_utils.dart';

class Target100 {
  String describe1 = "";
  String describe2 = "";
  String describe3 = "";
  String describe4 = "";
  String describe5 = "";
  String describe6 = "";
  String describe7 = "";
  String describe8 = "";
  String describe9 = "";
  String describe10 = "";
  double min = 0.0;
  double max = 0.0;

  Target100.fromJson(Map json) {
    describe1 = getJsonString(json, "describe1${getLangStr()}");
    describe2 = getJsonString(json, "describe2${getLangStr()}");
    describe3 = getJsonString(json, "describe3${getLangStr()}");
    describe4 = getJsonString(json, "describe4${getLangStr()}");
    describe5 = getJsonString(json, "describe5${getLangStr()}");
    describe6 = getJsonString(json, "describe6${getLangStr()}");
    describe7 = getJsonString(json, "describe7${getLangStr()}");
    describe8 = getJsonString(json, "describe8${getLangStr()}");
    describe9 = getJsonString(json, "describe9${getLangStr()}");
    describe10 = getJsonString(json, "describe10${getLangStr()}");
    min = json["min"] * 1.0;
    max = json["max"] * 1.0;
  }
}

class CalResult100 {
  String commit;
  String res;

  CalResult100({this.commit = "", this.res = ""});
}

class CalResult101 {
  double angle;
  double rotate;
  int commit; //状态

  CalResult101({this.angle = 0, this.rotate = 0, this.commit = 0});
}

class CalResult102 {
  List<int> target;
  double teethX;
  double faceX;
  String summary;

  CalResult102({this.target = const [], this.teethX = 0, this.faceX = 0, this.summary = ""});

  @override
  String toString() {
    return "target: $target, summary: $summary";
  }
}
