import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/html/html_unescape.dart';

class MessageItem {
  String caseId = "";
  String createTime = "";
  int id = 0;
  int state = 0;
  String title = "";
  String message = "";
  String modifyTime = "";
  String recordId = "";
  String index = "";
  Map? metadata = {};

  MessageItem({
    this.caseId = "",
    this.createTime = "",
    this.id = 0,
    this.state = 0,
    this.title = "",
    this.message = "",
    this.modifyTime = "",
    this.recordId = "",
  });

  MessageItem.fromJson(Map<String, dynamic> json, configJson) {
    caseId = getJsonString(json, "caseId");
    createTime = getJsonInt(json, "createTime").toString();
    id = getJsonInt(json, "id");
    state = getJsonInt(json, "state");
    title = getJsonString(json, "title");
    message = getJsonString(json, "message");
    index = getJsonString(json, "index");
    modifyTime = getJsonInt(json, "modifyTime").toString();
    recordId = getJsonString(json, "recordId");
    metadata = json["metadata"];
    if (isNotEmpty(index) && configJson != null) {
      title = configJson[index]["title"][lang];
      message = configJson[index]["content"][lang];

      title = replacePlaceholders(title, metadata);
      message = replacePlaceholders(message, metadata);
    }
  }

  String replacePlaceholders(String template, Map? replacements) {
    if (isEmpty(replacements)) {
      return template;
    }
    final regex = RegExp(r'\{(\w+)\}');
    return template.replaceAllMapped(regex, (match) {
      String key = match[1]!;
      String result = match[0]!;
      if (replacements!.containsKey(key)) {
        result = replacements![key].toString();
      }
      return result;
    });
  }

  String getMessage() {
    if (message.contains("html") || message.contains("</")) {
      final unescape = HtmlUnescape();
      String plainText = unescape.convert(message);
      plainText = plainText.replaceAll(RegExp(r'<[^>]*>'), '').trim();
      return plainText;
    } else {
      return message;
    }
  }
}
