import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

import '../common/global.dart';

class FileInfo {
  String fileId = "";
  String recordName = "";
  String phaseName = "";
  String caseName = "";
  String gender = "";
  String avatarFileId = "";
  String avatarPhoto = "";
  int birthday = 0;
  String createTime = "";
  String analysisId = "";
  String category = "";
  String expire = "";
  int status = 0;

  FileInfo();

  String getGender() {
    return gender == "female" ? Lang.gender_woman : Lang.gender_man;
  }

  String getAge() {
    return Global.getAge(birthday).toString();
  }

  bool isMaking() {
    return status == 6;
  }

  bool isSuccess() {
    return status == 8 && !isOutdated();
  }

  bool isFail() {
    return status == 7;
  }

  bool isOutdated() {
    return int.parse(expire) < DateTime.now().millisecondsSinceEpoch;
  }

  int getEffectTime() {
    return (int.parse(expire) - DateTime.now().millisecondsSinceEpoch) / 1000 / 60 ~/ 60;
  }

  Widget getStatusView() {
    if (isSuccess()) {
      return Image.asset("res/icons/icon_right.png", width: 20.sp);
    }
    late String text;
    late String icon;
    late Color color;
    if (isMaking()) {
      text = Lang.report_status_making;
      icon = "icon_uploading.png";
      color = colorPink;
    } else if (isFail()) {
      text = Lang.report_status_fail;
      icon = "icon_upload_fail.png";
      color = colorRed;
    } else if (isOutdated()) {
      text = Lang.report_status_outdate;
      icon = "icon_outdated.png";
      color = color7C;
    } else {
      return const SizedBox();
    }
    return Container(
      padding: EdgeInsets.all(4.sp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4.sp)),
        color: color.withOpacity(0.1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset("res/icons/$icon", width: 12.sp),
          SizedBox(width: 2.sp),
          MyText(text, color, 12.sp),
        ],
      ),
    );
  }

  FileInfo.fromJson(Map json) {
    fileId = getJsonString(json, "fileId");
    caseName = getJsonString(json, "caseName");
    phaseName = getJsonString(json, "phaseName");
    recordName = getJsonString(json, "name");
    gender = getJsonString(json, "gender");
    birthday = getJsonInt(json, "birthday");
    avatarFileId = getJsonString(json, "avatarFileId");
    avatarPhoto = getJsonString(json, "avatarPhoto");
    createTime = getJsonInt(json, "createTime").toString();
    analysisId = getJsonString(json, "analysisId");
    category = getJsonInt(json, "category").toString();
    expire = getJsonInt(json, "expire").toString();
    status = getJsonInt(json, "status");
  }

  Map<String, dynamic> toJson() {
    return {
      "fileId": fileId,
      "caseName": caseName,
      "phaseName": phaseName,
      "name": recordName,
      "gender": gender,
      "birthday": birthday,
      "avatarFileId": avatarFileId,
      "avatarPhoto": avatarPhoto,
      "createTime": createTime,
      "analysisId": analysisId,
      "category": category,
      "expire": expire,
      "status": status,
    };
  }
}
