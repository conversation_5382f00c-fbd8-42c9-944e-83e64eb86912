import 'dart:convert';
import 'dart:io';

import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/diagnose_info_record.dart';
import 'package:mooeli/utils/common_utils.dart';

class DiagnoseInfo {
  String recordId = "";
  String name = "";
  String phone = "";
  String gender = "";
  String birthday = ""; //时间戳
  String avatarFileId = "";
  String smilePhoto = "";
  String avatarFilePath = "";
  String frontPhoto = "";
  String right90Photo = "";
  List upPhotos = [];
  List closePhotos = [];
  List downPhotos = [];
  String chiefComplaint = "";
  String note = "";
  List intentTagList = [];
  String category = ""; //病例类别（存枚举信息）
  String createTime = ""; //时间戳
  String modifyTime = ""; //时间戳

  String scanCreateTime = ""; //口采时间戳
  String scanCreateFrom = ""; //活动来源

  String scanRecordId = "";
  List scanTagList = []; //口采治疗方向
  int processStep = 0; //进行步骤
  bool recommendBySelf = false; //报告-自主推荐，默认false即AI推荐
  List recommendTagList = []; //报告-自选标签
  String recommdation = "auto"; //报告-自选标签
  String advice = ""; //报告-其他建议
  String questionaire_list = "";
  Map answerMap = {};

  DiagnoseInfo();

  setPhoto(String type, String path) {
    Global.copyImageDocument(path, "initial_$recordId", save: true);
    logger("currentRecord setPhoto $type $path");
    switch (type) {
      case "100":
        frontPhoto = path;
        break;
      case "102":
        smilePhoto = path;
        break;
      case "101":
        right90Photo = path;
        break;
    }
  }

  int getAge() {
    if (isEmpty(birthday)) {
      return 0;
    }
    int ms = int.parse(birthday);
    return Global.getAge(ms);
  }

  String getGender() {
    return gender == "female" ? Lang.gender_woman : Lang.gender_man;
  }

  String getPhoto(String type) {
    switch (type) {
      case "100":
        return frontPhoto;
      case "102":
        return smilePhoto;
      case "101":
        return right90Photo;
      default:
        return "";
    }
  }

  Future<bool> checkPhotos({bool removeLost = false}) async {
    List files = getAllPhotos();
    for (String file in files) {
      await Global.copyImageDocument(file, "initial_$recordId", save: false);
    }
    if (removeLost) {
      return clearLostPhotos();
    } else {
      return false;
    }
  }

  bool clearLostPhotos() {
    bool lost = false;
    if (isNotEmpty(frontPhoto) && !File(getLocalPath(frontPhoto)).existsSync()) {
      frontPhoto = "";
      lost = true;
    }
    if (isNotEmpty(smilePhoto) && !File(getLocalPath(smilePhoto)).existsSync()) {
      smilePhoto = "";
      lost = true;
    }
    if (isNotEmpty(right90Photo) && !File(getLocalPath(right90Photo)).existsSync()) {
      right90Photo = "";
      lost = true;
    }
    for (String path in upPhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        upPhotos.clear();
        lost = true;
        break;
      }
    }
    for (String path in closePhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        closePhotos.clear();
        lost = true;
        break;
      }
    }
    for (String path in downPhotos) {
      if (!File(getLocalPath(path)).existsSync()) {
        downPhotos.clear();
        lost = true;
        break;
      }
    }
    return lost;
  }

  bool hasAllBaseInfo() {
    return allNotEmpty([
      name,
      gender,
      birthday,
    ]);
  }

  bool hasAllFacePhotos() {
    return allNotEmpty([
      frontPhoto,
      smilePhoto,
      right90Photo,
    ]);
  }

  bool hasAllMouthPhotos() {
    return allNotEmpty([
      upPhotos,
      closePhotos,
      downPhotos,
    ]);
  }

  bool hasData() {
    return hasNotEmpty([
      name,
      gender,
      phone,
      gender,
      smilePhoto,
      frontPhoto,
      right90Photo,
      upPhotos,
      closePhotos,
      downPhotos,
      note,
      intentTagList
    ]);
  }

  bool hasScanPhoto() {
    return isNotEmpty(upPhotos) || isNotEmpty(closePhotos) || isNotEmpty(downPhotos);
  }

  List getAllPhotos() {
    List photos = [];
    if (isNotEmpty(frontPhoto)) {
      photos.add(frontPhoto);
    }
    if (isNotEmpty(smilePhoto)) {
      photos.add(smilePhoto);
    }
    if (isNotEmpty(right90Photo)) {
      photos.add(right90Photo);
    }
    if (isNotEmpty(upPhotos)) {
      photos.addAll(upPhotos);
    }
    if (isNotEmpty(closePhotos)) {
      photos.addAll(closePhotos);
    }
    if (isNotEmpty(downPhotos)) {
      photos.addAll(downPhotos);
    }
    return photos;
  }

  String getDate(String ts) {
    String date = ts;
    try {
      date = Global.getDateByTimestamp(milliseconds: int.parse(ts));
    } catch (ex) {
      //
    }
    return date;
  }

  bool isChild() {
    try {
      return Global.getAge(int.parse(birthday)) <= 15;
    } catch (ex) {
      return false;
    }
  }

  String getCategory() {
    CaseCategory category = isChild() ? CaseCategory.child_default : CaseCategory.adult_default;
    return category.name;
  }

  Map questionaireMap = {};

  void setProcessStep(int step, [bool updateMeta = false]) {
    if (jsonEncode(answerMap) != questionaire_list) {
      questionaire_list = jsonEncode(answerMap);
      updateMeta = true;
    }
    if (step > processStep) {
      processStep = step;
      updateMetadata();
    } else if (updateMeta) {
      updateMetadata();
    }
  }

  void updateMetadata() {
    if (isNotEmpty(recordId)) {
      HHttp.request(
        "/v3/doctor/initial/update",
        "POST",
        (data) {},
        params: {
          "recordId": recordId,
          "metadata": getMetaDataJson(),
        },
        isShowErrorToast: false,
      );
    }
  }

  bool hasBaseInfoChanged(DiagnoseInfo record) {
    return name != record.name ||
        phone != record.phone ||
        gender != record.gender ||
        birthday != record.birthday ||
        chiefComplaint != record.chiefComplaint ||
        note != record.note ||
        !listEqual(intentTagList, record.intentTagList);
  }

  bool hasPhotosChanged(DiagnoseInfo record) {
    return smilePhoto != record.smilePhoto ||
        frontPhoto != record.frontPhoto ||
        right90Photo != record.right90Photo ||
        !listEqual(upPhotos, record.upPhotos) ||
        !listEqual(closePhotos, record.closePhotos) ||
        !listEqual(downPhotos, record.downPhotos);
  }

  bool _isPhotoUpdated(String photo, String origin) {
    if (isEmpty(photo)) {
      return false;
    } else if (isNotEmpty(photo) && isEmpty(origin)) {
      return true;
    } else {
      return photo != origin;
    }
  }

  bool _isListUpdated(List photoList, List originList) {
    if (isEmpty(photoList)) {
      return false;
    } else if (isNotEmpty(photoList) && isEmpty(originList)) {
      return true;
    } else {
      return !listEqual(photoList, originList);
    }
  }

  bool _isPhotoDeleted(String photo, String origin) {
    return isEmpty(photo) && isNotEmpty(origin);
  }

  bool _isListDeleted(List photoList, List originList) {
    if (_isListUpdated(photoList, originList)) {
      return false;
    }
    return isEmpty(photoList) && isNotEmpty(originList);
  }

  List getUpdatePhotoList(DiagnoseInfo record) {
    List list = [];
    if (_isPhotoUpdated(smilePhoto, record.smilePhoto)) {
      list.add(smilePhoto);
    }
    if (_isPhotoUpdated(frontPhoto, record.frontPhoto)) {
      list.add(frontPhoto);
    }
    if (_isPhotoUpdated(right90Photo, record.right90Photo)) {
      list.add(right90Photo);
    }
    if (_isListUpdated(upPhotos, record.upPhotos)) {
      list.addAll(upPhotos);
    }
    if (_isListUpdated(closePhotos, record.closePhotos)) {
      list.addAll(closePhotos);
    }
    if (_isListUpdated(downPhotos, record.downPhotos)) {
      list.addAll(downPhotos);
    }
    return list;
  }

  List getDeletePhotoList(DiagnoseInfo record) {
    List list = [];
    if (_isPhotoDeleted(smilePhoto, record.smilePhoto)) {
      list.add(record.smilePhoto);
    }
    if (_isPhotoDeleted(frontPhoto, record.frontPhoto)) {
      list.add(record.frontPhoto);
    }
    if (_isPhotoDeleted(right90Photo, record.right90Photo)) {
      list.add(record.right90Photo);
    }
    if (_isListDeleted(upPhotos, record.upPhotos)) {
      list.addAll(record.upPhotos);
    }
    if (_isListDeleted(closePhotos, record.closePhotos)) {
      list.addAll(record.closePhotos);
    }
    if (_isListDeleted(downPhotos, record.downPhotos)) {
      list.addAll(record.downPhotos);
    }
    return list;
  }

  List<String> getAddTagList(DiagnoseInfo record) {
    List<String> tagList = [];
    for (String tag in intentTagList) {
      if (!record.intentTagList.contains(tag)) {
        tagList.add(tag);
      }
    }
    return tagList;
  }

  List<String> getDelTagList(DiagnoseInfo record) {
    List<String> tagList = [];
    for (String tag in record.intentTagList) {
      if (!intentTagList.contains(tag)) {
        tagList.add(tag);
      }
    }
    return tagList;
  }

  DiagnoseInfo.fromJson(Map json) {
    recordId = getJsonString(json, "recordId");
    name = getJsonString(json, "name");
    phone = getJsonString(json, "phone");
    gender = getJsonString(json, "gender");
    try {
      birthday = getJsonInt(json, "birthday").toString();
    } catch (ex) {
      birthday = getJsonString(json, "birthday");
    }
    avatarFileId = getJsonString(json, "avatarFileId");
    smilePhoto = getJsonString(json, "smilePhoto");
    frontPhoto = getJsonString(json, "frontPhoto");
    right90Photo = getJsonString(json, "side2Photo");
    upPhotos = getJsonList(json, "upPhotos");
    closePhotos = getJsonList(json, "closePhotos");
    downPhotos = getJsonList(json, "downPhotos");
    chiefComplaint = getJsonString(json, "chiefComplaint");
    note = getJsonString(json, "note");
    intentTagList = getJsonList(json, "intentTagList");
    try {
      createTime = getJsonInt(json, "createTime").toString();
    } catch (ex) {
      createTime = getJsonString(json, "createTime");
    }
    try {
      modifyTime = getJsonInt(json, "modifyTime").toString();
    } catch (ex) {
      modifyTime = getJsonString(json, "modifyTime");
    }
    category = getJsonString(json, "category");
    try {
      scanCreateTime = getJsonInt(json, "scanCreateTime").toString();
    } catch (ex) {
      scanCreateTime = getJsonString(json, "scanCreateTime");
    }
    scanCreateFrom = getJsonString(json, "scanCreateFrom");

    Map metaJson = getJsonMap(json, "metadata");
    scanRecordId = getJsonString(metaJson, "scanRecordId");
    scanTagList = getJsonList(metaJson, "scanTagList");
    processStep = getJsonInt(metaJson, "processStep");
    recommdation = getJsonString(metaJson, "recommdation");
    advice = getJsonString(metaJson, "advice");
    if (isEmpty(recommdation)) {
      recommdation = "auto";
    }
    recommendBySelf = recommdation != "auto";
    if (recommendBySelf) {
      recommendTagList.clear();
      Map tags = jsonDecode(recommdation);
      List keys = tags.keys.toList();
      for (dynamic key in keys) {
        if (isNotEmpty(tags[key])) {
          recommendTagList.addAll(tags[key]!);
        }
      }
    }
    questionaire_list = getJsonString(metaJson, "questionaire_list");
    if (isNotEmpty(questionaire_list)) {
      answerMap = jsonDecode(questionaire_list);
    }
  }

  Map<String, dynamic> toJson() {
    logger("DiagnoseInfo.toJson $gender $birthday");
    return {
      "recordId": recordId,
      "name": name,
      "phone": phone,
      "gender": gender,
      "birthday": birthday,
      "avatarFileId": avatarFileId,
      "smilePhoto": smilePhoto,
      "frontPhoto": frontPhoto,
      "side2Photo": right90Photo,
      "upPhotos": upPhotos,
      "closePhotos": closePhotos,
      "downPhotos": downPhotos,
      "chiefComplaint": chiefComplaint,
      "note": note,
      "intentTagList": intentTagList,
      "createTime": createTime,
      "modifyTime": modifyTime,
      "category": category,
      "scanCreateTime": scanCreateTime,
      "scanCreateFrom": scanCreateFrom,
      "metadata": getMetaDataJson(),
    };
  }

  Map<String, dynamic> getMetaDataJson() {
    return {
      "scanRecordId": scanRecordId,
      "scanTagList": scanTagList,
      "processStep": processStep,
      "recommdation": recommdation,
      "advice": advice,
      "questionaire_list": questionaire_list,
    };
  }

  DiagnoseInfo clone() {
    logger("currentRecord clone");
    return DiagnoseInfo.fromJson(toJson());
  }

  copyBaseInfo(DiagnoseInfo record) {
    name = record.name;
    phone = record.phone;
    gender = record.gender;
    birthday = record.birthday;
    chiefComplaint = record.chiefComplaint;
    note = record.note;
    intentTagList = record.intentTagList;
    modifyTime = record.modifyTime;
    category = record.category;
    scanCreateTime = record.scanCreateTime;
    scanCreateFrom = record.scanCreateFrom;
    scanRecordId = record.scanRecordId;
    scanTagList = record.scanTagList;
    logger("currentRecord copyBaseInfo");
  }

  copyPhotos(DiagnoseInfo record) {
    smilePhoto = record.smilePhoto;
    frontPhoto = record.frontPhoto;
    right90Photo = record.right90Photo;
    upPhotos = record.upPhotos;
    closePhotos = record.closePhotos;
    downPhotos = record.downPhotos;
    logger("currentRecord copyPhotos");
  }

  String getQuestionaireZh() {
    if (isNotEmpty(questionaireMap)) {
      String getZhText(String text, bool isTitle) {
        for (Map map in questionaireMap.values) {
          if (isTitle) {
            if (map["title"].values.contains(text)) {
              return map["title"]["zh"];
            }
          } else {
            dynamic projectNames = map["projectNames"];
            if (projectNames is List) {
              for (Map name in projectNames) {
                if (name.values.contains(text)) {
                  return name["zh"];
                }
              }
            } else if (projectNames is Map) {
              if (projectNames.values.contains(text)) {
                return projectNames["zh"];
              }
            }
          }
        }
        return text;
      }

      Map mapZh = {};
      for (String key in answerMap.keys) {
        List list = [];
        for (String value in answerMap[key]) {
          list.add(getZhText(value, false));
        }
        mapZh[getZhText(key, true)] = list;
        logger("getQuestionaireZh: ${getZhText(key, true)} -> $list");
      }
      return jsonEncode(mapZh);
    }
    return questionaire_list;
  }
}

class DiagnoseSearchInfo {
  String quickScanRecordId = "";
  String avatarFilePath = "";
  String name = "";
  double age = 0;
  String phone = "";
  String gender = "";
  String note = "";
  List tagList = [];
  int scanCreateTime = 0; //口采时间戳
  String scanCreateFrom = ""; //活动来源

  String getGender() {
    return gender == "female" ? Lang.gender_woman : Lang.gender_man;
  }

  String getDate(int ts) {
    return Global.getDateByTimestamp(milliseconds: ts);
  }

  DiagnoseSearchInfo.fromJson(Map json) {
    quickScanRecordId = getJsonString(json, "quickScanRecordId");
    name = getJsonString(json, "name");
    phone = getJsonString(json, "phone");
    gender = getJsonString(json, "gender");
    avatarFilePath = getJsonString(json, "avatarFilePath");
    note = getJsonString(json, "note");
    age = getJsonDouble(json, "age");
    tagList = getJsonList(json, "tagList");
    scanCreateTime = getJsonInt(json, "scanCreateTime");
    scanCreateFrom = getJsonString(json, "scanCreateFrom");
  }
}
