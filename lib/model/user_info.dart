import 'dart:convert';

import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/pages/my/http_log.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

// {
// "provider": {
// "personId": "c9800d4e7eef42edb7484926abaced8a",
// "name": "医生",
// "avatarId": "",
// "organization": "未知机构",
// "createTime": "1678807883315",
//
// "modifyTime": "1693464100824",
// "userUsedSize": 787580947,
// "analysisUsedSize": 192924316,
// "defaultCaseStatus": true,
// "externalSource": "",
// "monitorEnabled": true
// },
// "user": {
// "username": "ly_3823520",
// "phoneNumber": "19558113823",
// "wechatOpenidFlag": false
// },
// "sessionId": "sessionid_c4528fa0a01549b8a7dacf8c9485c498",
// "redirect": null
// }

class UserInfo {
  String personName = "";
  String username = "";
  String? phoneNumber = "";
  String accessToken = "";
  String refreshToken = "";
  String avatarId = "";
  String avatarPath = "";
  String organization = "";
  String personId = "";
  String roleId = "";
  String providerId = "";
  String providerName = "";
  List whitelistFeatures = [];
  dynamic memberInfo;

  UserInfo();

  UserInfo.fromJson(Map<String, dynamic> json) {
    logger(jsonEncode(json), key: "UserInfo");
    Global.sharedPrefs.setString("login_user", jsonEncode(json));
    username = getJsonString(json["user"], "username");
    phoneNumber = getJsonString(json["user"], "phoneNumber");
    personName = getJsonString(json["provider"], "personName");
    organization = getJsonString(json["provider"], "organization");
    personId = getJsonString(json["provider"], "personId");
    roleId = getJsonString(json["provider"], "roleId");
    whitelistFeatures = getJsonList(json["provider"], "whitelistFeatures");
    memberInfo = getJsonMap(json["provider"], "memberInfo");
    if (whitelistFeatures.contains("ULTRA")) {
      setUltraWifi(true);
    } else {
      setUltraWifi(false);
    }
    providerId = getJsonString(json["provider"], "providerId");
    providerName = getJsonString(json["provider"], "providerName");
    if (isNotEmpty(getJsonString(json, "accessToken"))) {
      accessToken = getJsonString(json, "accessToken");
    }
    if (isNotEmpty(getJsonString(json, "refreshToken"))) {
      refreshToken = getJsonString(json, "refreshToken");
      logText =
          "\n时间: ${Global.getLoggerTime(milliseconds: DateTime.now().millisecondsSinceEpoch, ignoreSameDay: false)}\naccessToken: $accessToken\nrefreshToken: $refreshToken\n$logText";
    }
    // logger("UserInfo.fromJson accessToken: $accessToken refreshToken: $refreshToken");
    if (isNotEmpty(json["provider"]) && isNotEmpty(json["provider"]["avatarId"])) {
      avatarId = json["provider"]["avatarId"];
      downloadAvatar(
        [avatarId],
        (id, path) {
          logger("downloadThumbnail $avatarId $id -> $path");
          if (id == avatarId) {
            avatarPath = path;
            logger("downloadThumbnail $avatarId $id -> $path");
            eventBus.fire(EventRefreshUserInfo());
          }
        },
        "PROVIDER",
        documentDir: true,
      );
    }
    FlutterBugly.setUserId("${username}__$phoneNumber");
    UmengCommonSdk.onProfileSignIn("${username}__$phoneNumber");
  }

  Map<String, dynamic> toJson() {
    return {
      "user": {
        "username": username,
        "phoneNumber": phoneNumber,
      },
      "provider": {
        "avatarId": avatarId,
        "personName": personName,
        "organization": organization,
        "personId": personId,
        "roleId": roleId,
        "providerId": providerId,
        "providerName": providerName,
        "whitelistFeatures": whitelistFeatures,
      },
      "accessToken": accessToken,
      "refreshToken": refreshToken,
    };
  }

  bool canMakeDiagnosePdf() {
    //TODO chenxb 会员
    return true;
    // try {
    //   String key = memberInfo["membership"]["key"];
    //   // institutional_standard, institutional_standard_plus,
    //   // institutional_commercial, institutional_ultimate,
    //   // personal_standard, personal_vip, personal_svip,
    //   // unimplemented
    //   logger("canMakeDiagnosePdf key: $key");
    //   return key == "institutional_commercial" || key == "institutional_ultimate";
    // } catch (ex) {
    //   logger("canMakeDiagnosePdf error $ex");
    // }
    // return false;
  }

  bool isVip() {
    return true; //TODO chenxb
  }

  bool isTenantManager() {
    return roleId == "300" || roleId.startsWith("2");
  }

  String getTenantName() {
    if (isNotEmpty(providerName)) {
      return providerName;
      // } else if (isNotEmpty(organization)) {
      //   return organization;
    } else {
      return Lang.personal_version;
    }
  }
}
