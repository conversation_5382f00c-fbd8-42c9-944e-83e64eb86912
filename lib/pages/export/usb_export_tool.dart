import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/utils/android_method.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/widget_utils.dart';
import 'package:sprintf/sprintf.dart';

import 'export_config.dart';

// 显示usb

void showUsbExportSelectView(void Function(int type, String path) onSave) {
  showCustomDialog(UsbExportSelectView(onSave), clickBackDismiss: false);
}

class UsbExportSelectView extends StatefulWidget {
  final void Function(int type, String path) onSave;

  const UsbExportSelectView(this.onSave, {super.key});

  @override
  State<UsbExportSelectView> createState() => _UsbExportSelectViewState();
}

class _UsbExportSelectViewState extends State<UsbExportSelectView> {
  List<Map<String, String>> usbDevices = [];
  String? selectedPath;

  int _selectedExportType = -1;

  @override
  void initState() {
    super.initState();
    loadUsbPaths();
  }

  @override
  Widget build(BuildContext context) {
    bool hasUsb = usbDevices.isNotEmpty;
    bool prepared = selectedPath != null && _selectedExportType != -1;
    return Container(
      width: hasUsb ? 720.sp : 520.sp,
      height: hasUsb ? 680.sp : null,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 24.sp),
            child: Row(
              children: [
                Text(hasUsb ? Lang.detected_usb : Lang.no_usb_detected,
                    style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        color: color2B)),
                const Spacer(),
                Click(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Image.asset(
                      "res/icons/icon_close_purple.png",
                      width: 24.sp,
                      height: 24.sp,
                      color: color2B,
                    )),
              ],
            ),
          ),
          Divider(
            thickness: 1.sp,
            height: 1.sp,
            color: colorE1,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(24.sp),
                child: hasUsb ? _buildSelectUsbAndContent() : _buildEmptyUsb(),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 16.sp),
            child: !hasUsb
                ? Align(
              alignment: Alignment.centerRight,
              child: HCustomButton(
                width: 144.sp,
                height: 56.sp,
                fontSize: 24.sp,
                radius: 8.sp,
                onPress: () {
                  SmartDialog.dismiss();
                },
                bgColor: colorBlueDeep,
                child: MyText(Lang.i_knew, Colors.white, 24.sp),
              ),
            )
                : Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  HCustomButton(
                    width: 126.sp,
                    height: 56.sp,
                    fontSize: 24.sp,
                    radius: 8.sp,
                    onPress: () {
                      SmartDialog.dismiss();
                    },
                    bgColor: colorPurpleLavender,
                    child: MyText(Lang.cancel, colorBlueDeep, 24.sp),
                  ),
                  SizedBox(
                    width: 24.sp,
                  ),
                  HCustomButton(
                    key: UniqueKey(),
                    width: 126.sp,
                    height: 56.sp,
                    fontSize: 24.sp,
                    radius: 8.sp,
                    onPress: () {
                      if (!prepared) return;
                      SmartDialog.dismiss();
                      //打开传入dialog
                      widget.onSave(_selectedExportType, selectedPath!);
                    },
                    bgColor:
                    colorBlueDeep.withOpacity(prepared ? 1 : 0.6),
                    child: MyText(Lang.confirm, Colors.white, 24.sp),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 获取所有 U 盘路径
  Future<void> loadUsbPaths() async {
    final paths = await getUsbDevices();
    setState(() {
      // usbDevices = paths;
      usbDevices = [
        {
          'name': 'U盘1',
          'path':
          '/storage/emulated/0/Android/data/com.example.lingmay_pro/files/U盘1'
        },
        {
          'name': 'U盘1',
          'path':
          '/storage/emulated/0/Android/data/com.example.lingmay_pro/files/U盘1'
        }
      ];
    });
  }

  Widget _buildEmptyUsb() {
    return Text(
      Lang.please_insert_usb,
      style: TextStyle(fontSize: 24.sp, color: color2B),
    );
  }

  Widget _buildSelectUsbAndContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.usb_export_content_tip,
          style: TextStyle(fontSize: 24.sp, color: color2B),
        ),
        SizedBox(
          height: 16.sp,
        ),
        ...addSpaces(
            exportType
                .map((e) =>
                GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedExportType = e.type;
                      });
                    },
                    child: buildExportSelectItem(e.title, _selectedExportType == e.type)))
                .toList(),
            16.sp),
        SizedBox(
          height: 16.sp,
        ),
        Text(
          Lang.export_to_usb_tip,
          style: TextStyle(fontSize: 24.sp, color: color2B),
        ),
        SizedBox(
          height: 16.sp,
        ),
        ...addSpaces(
            usbDevices.map((device) {
              final name = device['name'] ?? 'Unknown Device'; // 设备名称
              final path = device['path']; // 设备路径
              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedPath = path;
                  });
                },
                child: buildExportSelectItem(name, path == selectedPath),
              );
            }).toList(),
            16.sp),
      ],
    );
  }


}

//显示传输进度
void showUsbExportProgressDialog(List<ScanInfo> scanRecords) {
  showCustomDialog(UsbExportProgressView(scanRecords), clickBackDismiss: false);
}

class UsbExportProgressView extends StatefulWidget {
  final List<ScanInfo> scanRecords;

  const UsbExportProgressView(this.scanRecords, {super.key});

  @override
  State<UsbExportProgressView> createState() => _UsbExportProgressViewState();
}

class _UsbExportProgressViewState extends State<UsbExportProgressView> {
  final Map<int, ExportStatus> _exportStatus = {};
  final List<ScanInfo> _exportList = [];

  @override
  void initState() {
    super.initState();
    for (var record in widget.scanRecords) {
      _exportStatus[record.id] = ExportStatus.processing;
    }
    _exportList.addAll(widget.scanRecords);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1200.sp,
      height: 756.sp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        children: [
          Padding(
              padding: EdgeInsets.symmetric(vertical: 10.sp),
              child: Text(
                "正在导出USB文件",
                style: TextStyle(
                    fontSize: 24.sp,
                    color: color2B,
                    fontWeight: FontWeight.w600),
              )),
          Divider(
            height: 1.sp,
            thickness: 1.sp,
            color: colorE1,
          ),
          Expanded(
            child: Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 0),
                child: Column(
                  children: [
                    Text(
                      sprintf(Lang.usb_export_progress, [10, 10, 10]),
                      style: TextStyle(fontSize: 20.sp, color: color7C),
                    ),
                    SizedBox(
                      height: 8.sp,
                    ),
                    //进度条
                    Container(
                      width: double.infinity,
                      height: 12.sp,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.sp),
                        color: colorE1,
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: 0.6, // 进度值 0.0-1.0
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20.sp),
                            // 内部进度条也是圆角
                            color: colorGreen,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20.sp,
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                          vertical: 10.sp, horizontal: 16.sp),
                      decoration: BoxDecoration(
                        color: const Color(0xFFEBEDF0),
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(8.sp),
                            topRight: Radius.circular(8.sp)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                              child: Text(
                                Lang.data,
                                style: TextStyle(
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.w500,
                                    color: color2B),
                              )),
                          Expanded(
                              child: Text(
                                Lang.create_time,
                                style: TextStyle(
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.w500,
                                    color: color2B),
                              )),
                          Expanded(
                              child: Text(
                                Lang.status,
                                style: TextStyle(
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.w500,
                                    color: color2B),
                              )),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(itemBuilder: (context, index) {
                        final item = _exportList[index];
                        return _buildExportItem(item, _exportStatus[item.id]!, (){
                          //TODO重新导出
                        });
                      }),
                    )
                  ],
                )),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: HCustomButton(
              width: 126.sp,
              height: 56.sp,
              fontSize: 24.sp,
              radius: 8.sp,
              onPress: () {
                CancelFunc? cancelFunc;
                cancelFunc = Global.showAlertDialog(
                    Lang.terminate_export, Lang.terminate_export_confirm,
                    cancelCallback: () {
                      cancelFunc?.call();
                    }, okCallBack: () {
                  //TODO 终止传输
                });
              },
              bgColor: colorBlueDeep,
              child: MyText(Lang.terminate_export, Colors.white, 24.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportItem(ScanInfo scanInfo, ExportStatus status,
      VoidCallback? retry) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
      child: Row(
        children: [
          Expanded(
              child: Row(
                children: [
                  Flexible(
                      child: Text(scanInfo.recordName,
                          style: TextStyle(
                              fontSize: 20.sp,
                              color: color2B,
                              fontWeight: FontWeight.w500))),
                  SizedBox(width: 12.sp),
                  Text(scanInfo.gender,
                      style: TextStyle(
                          fontSize: 20.sp,
                          color: color7C,
                          fontWeight: FontWeight.w400)),
                  SizedBox(width: 12.sp),
                  Text(scanInfo.age,
                      style: TextStyle(
                          fontSize: 20.sp,
                          color: color7C,
                          fontWeight: FontWeight.w400)),
                ],
              )),
          Expanded(
              child: SingleText(
                  Global.getShowTime(
                      milliseconds: scanInfo.customTime, ignoreSameDay: false),
                  color2B,
                  20.sp)),
          Expanded(child: _buildExportStatus(status, retry))
        ],
      ),
    );
  }

  Widget _buildExportStatus(ExportStatus status, VoidCallback? retry) {
    switch (status) {
      case ExportStatus.processing:
        return _buildProcessing();
      case ExportStatus.success:
        return _buildSuccess();
      case ExportStatus.fail:
        return _buildFail(retry!);
    }
  }

  Widget _buildExportStatusItem(String image, String text, Color textColor,
      Color backgroundColor) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 2.sp),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Row(
        children: [
          Image.asset(
            image,
            width: 24.sp,
          ),
          SizedBox(
            width: 4.sp,
          ),
          Text(
            text,
            style: TextStyle(fontSize: 18.sp, color: textColor),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessing() {
    return _buildExportStatusItem('res/icons/icon_upload_ing.png',
        Lang.export_processing, colorGreen2, const Color(0x1A76B283));
  }

  Widget _buildSuccess() {
    return _buildExportStatusItem('res/icons/icon_upload_complete.png',
        Lang.export_success, colorGreen2, const Color(0x1A76B283));
  }

  Widget _buildFail(VoidCallback retry) {
    return Row(
      children: [
        _buildExportStatusItem('res/icons/icon_upload_fail_select.png',
            Lang.export_success, colorGreen2, const Color(0x1A76B283)),
        SizedBox(
          width: 12.w,
        ),
        Click(
          onTap: retry,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'res/icons/icon_retry_brand.png',
                width: 24.sp,
              ),
              SizedBox(
                width: 4.w,
              ),
              Text(
                Lang.export_retry,
                style: TextStyle(fontSize: 20.sp, color: colorBrand),
              ),
            ],
          ),
        )
      ],
    );
  }
}

enum ExportStatus {
  success,
  fail,
  processing,
}
