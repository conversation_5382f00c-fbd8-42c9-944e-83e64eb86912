import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';

final List<ExportType> exportType = [
  ExportType(ExportType.basicInfo, Lang.patient_info_and_image),
  ExportType(ExportType.basicInfoAndPdf, Lang.patient_info_and_pdf),
];


Widget buildExportSelectItem(String title, bool selected) {
  Widget selectedView() {
    return Container(
      width: 32.sp,
      height: 32.sp,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: colorBrand, width: 2.sp),
      ),
      child: Center(
        child: CircleAvatar(
          radius: 8.sp,
          backgroundColor: colorBrand,
        ),
      ),
    );
  }

  Widget unSelectedView() {
    return Container(
      width: 32.sp,
      height: 32.sp,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: colorE1, width: 2.sp),
      ),
    );
  }

  return Container(
    decoration: BoxDecoration(
      border: Border.all(color: selected ? colorBrand : colorE1, width: 1.sp),
      borderRadius: BorderRadius.all(Radius.circular(16.sp)),
    ),
    padding: EdgeInsets.all(24.sp),
    child: Row(
      children: [
        selected ? selectedView() : unSelectedView(),
        SizedBox(
          width: 16.sp,
        ),
        Text(
          title,
          style: TextStyle(fontSize: 24.sp, color: Colors.black),
        )
      ],
    ),
  );
}

class ExportType {

  static const int basicInfo = 1;
  static const int basicInfoAndPdf = 2;

  final int type;
  final String title;

  ExportType(this.type, this.title);
}