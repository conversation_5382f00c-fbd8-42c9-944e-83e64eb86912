import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/widget_utils.dart';

import 'export_config.dart';

const String _webExportSelectTag = "webExportSelect";

void selectWebExportDialog() async {
  final internetValid = await HHttp.checkInternetValid();
  if (!internetValid) {
    Global.showAlertDialog(Lang.tip, Lang.internet_not_connected,
        okText: Lang.i_knew, okCallBack: () {});
  } else {
    showCustomDialog(
      WebShareSelectView(onClose: (){
      },), tag: _webExportSelectTag
    );
  }
}

class WebShareSelectView extends StatefulWidget {
  final VoidCallback onClose;
  const WebShareSelectView({super.key, required this.onClose});

  @override
  State<WebShareSelectView> createState() => _WebShareSelectViewState();
}

class _WebShareSelectViewState extends State<WebShareSelectView> {
  int _selectedExportType = -1;

  @override
  Widget build(BuildContext context) {
    bool prepared = _selectedExportType != -1;
    return Container(
        width: 720.sp,
        height: 428.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding:
                  EdgeInsets.symmetric(vertical: 10.sp, horizontal: 24.sp),
              child: Row(
                children: [
                  Text(Lang.lan_sharing,
                      style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w600,
                          color: color2B)),
                  const Spacer(),
                  Click(
                      onTap: () {
                        widget.onClose.call();
                      },
                      child: Image.asset(
                        "res/icons/icon_close_purple.png",
                        width: 24.sp,
                        height: 24.sp,
                        color: color2B,
                      )),
                ],
              ),
            ),
            Divider(
              thickness: 1.sp,
              height: 1.sp,
              color: colorE1,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(24.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Lang.usb_export_content_tip,
                        style: TextStyle(fontSize: 24.sp, color: color2B),
                      ),
                      SizedBox(
                        height: 16.sp,
                      ),
                      ...addSpaces(
                          exportType
                              .map((e) => GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedExportType = e.type;
                                    });
                                  },
                                  child: buildExportSelectItem(e.title,
                                      _selectedExportType == e.type)))
                              .toList(),
                          16.sp),
                      SizedBox(
                        height: 16.sp,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: 24.sp, vertical: 16.sp),
              child: Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    HCustomButton(
                      width: 126.sp,
                      height: 56.sp,
                      fontSize: 24.sp,
                      radius: 8.sp,
                      onPress: () {
                        widget.onClose.call();
                      },
                      bgColor: colorPurpleLavender,
                      child: MyText(Lang.cancel, colorBlueDeep, 24.sp),
                    ),
                    SizedBox(
                      width: 24.sp,
                    ),
                    HCustomButton(
                      key: UniqueKey(),
                      width: 126.sp,
                      height: 56.sp,
                      fontSize: 24.sp,
                      radius: 8.sp,
                      onPress: () {
                        if (!prepared) return;
                        widget.onClose.call();
                        showWebShareExportDialog();
                      },
                      bgColor: prepared
                          ? colorBlueDeep
                          : colorBlueDeep.withOpacity(0.6),
                      child: MyText(Lang.confirm, Colors.white, 24.sp),
                    ),
                  ],
                ),
              ),
            )
          ],
        ));
  }
}


void showWebShareExportDialog() {
  Global.showCustomDialog(const WebShareExportView());
}

class WebShareExportView extends StatefulWidget {
  const WebShareExportView({super.key});

  @override
  State<WebShareExportView> createState() => _WebShareExportViewState();
}

class _WebShareExportViewState extends State<WebShareExportView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 760.sp,
      height: 580.sp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 24.sp),
            child: Text(Lang.no_usb_detected,
                style: TextStyle(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w600,
                    color: color2B)),
          ),
          Divider(
            thickness: 1.sp,
            height: 1.sp,
            color: colorE1,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 8.sp),
            decoration: const BoxDecoration(
              color: Color(0x1AEF8666),
            ),
            child: Text(
              Lang.local_network_share_direction,
              style: TextStyle(fontSize: 14.sp, color: colorPink),
            ),
          ),
          Expanded(
              child: Padding(
            padding: EdgeInsets.all(24.sp),
            child: Column(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildDirectionItem(1, Lang.local_network_tip1, false),
                ),
                Expanded(
                  flex: 3,
                  child: _buildDirectionItem(1, Lang.local_network_tip2, false,
                      child: Text(Lang.local_network_tip3,
                          style: TextStyle(fontSize: 24.sp, color: color7C))),
                ),
                Expanded(
                  flex: 2,
                  child: _buildDirectionItem(1, Lang.local_network_tip3, true),
                ),
              ],
            ),
          )),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 16.sp),
            child: Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    Lang.local_network_share_tip,
                    style: TextStyle(fontSize: 20.sp, color: color7C),
                  ),
                  SizedBox(
                    width: 24.sp,
                  ),
                  HCustomButton(
                    width: 126.sp,
                    height: 56.sp,
                    fontSize: 24.sp,
                    radius: 8.sp,
                    onPress: () {
                      Global.showAlertDialog(Lang.finish_share,
                          Lang.local_network_finish_share_confirm,
                          isBringXBtn: true,
                          okText: Lang.finish_share, okCallBack: () {
                        //todo 取消共享
                        SmartDialog.dismiss();
                        Global.toast(Lang.local_network_share_finished);
                      });
                    },
                    bgColor: colorBlueDeep,
                    child: MyText(Lang.finish_share, Colors.white, 24.sp),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildDirectionItem(int index, String title, bool isEnd,
      {Widget? child}) {
    final titleWidget =
        Text(title, style: TextStyle(fontSize: 24.sp, color: color2B));
    return Row(
      children: [
        Column(
          children: [
            Badge.count(count: index),
            if (!isEnd)
              SizedBox(
                height: double.infinity,
                child: VerticalDivider(
                  color: color2B,
                  thickness: 1.5,
                ),
              ),
          ],
        ),
        Expanded(
            child: child != null
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      titleWidget,
                      child,
                    ],
                  )
                : titleWidget)
      ],
    );
  }
}
