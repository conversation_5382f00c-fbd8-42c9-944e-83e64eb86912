import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/web/web_report_page.dart';
import 'package:mooeli/utils/common_utils.dart';

String schema = "lyoral://";

bool startWithSchema(String url) {
  return url.startsWith("lyoral://") || url.startsWith("mooeli://");
}

void navigatorLyoral(BasePageState page, String url) async {
  if (url.startsWith(schema)) {
    Uri uri = Uri.parse(url);
    logger("WebViewPage navigator ${uri.host} ${uri.queryParameters}");
    Map<String, dynamic> param = uri.queryParameters;
    switch (uri.host.toLowerCase()) {
      case "closepage":
        page.pop();
        break;
      case "savecode":
        saveWechatCodeImage();
        break;
      case "toast":
        toast(param["msg"]);
        break;
      case "selectage":
        if (page is WebReportPageState) {
          page.selectAge(int.parse(param["age"]));
        }
        break;
      case "openpdfurl":
        if (page is WebReportPageState) {
          page.loadPdfUrl(Uri(queryParameters: param).query);
        }
        break;
      case "printnetworkerror":
        if (page is WebReportPageState) {
          page.printPdfInfo("PrintNetworkError");
        }
        break;
      case "printerror":
        if (page is WebReportPageState) {
          page.printPdfInfo("PrintError");
        }
        break;
    }
  }
}
