// import 'dart:convert';
// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:mooeli/common/local_account.dart';
// import 'package:mooeli/pages/base_page.dart';
// import 'package:mooeli/pages/viewer/pdf_preview.dart';
// import 'package:mooeli/pages/web/web_manager.dart';
// import 'package:mooeli/utils/common_utils.dart';
// import 'package:path_provider/path_provider.dart';
//
// ///inapp webview，支持下载pdf
// class WebReportPage extends BasePage {
//   String url;
//   String title;
//   String fileName;
//   String param;
//   bool readTitle;
//   bool horizontal;
//   bool hideAppbar;
//   bool disableOriention;
//   bool hideClose;
//
//   WebReportPage(
//       this.url,
//       this.title,
//       this.fileName, {
//         this.param = "",
//         this.readTitle = false,
//         this.horizontal = false,
//         this.hideAppbar = false,
//         this.disableOriention = false,
//         this.hideClose = false,
//       });
//
//   @override
//   _WebViewPageState createState() => _WebViewPageState();
// }
//
// class _WebViewPageState extends BasePageState<WebReportPage> {
//   InAppWebViewController? _webViewController;
//   bool hasFinished = false;
//
//   @override
//   void initState() {
//     super.initState();
//     widget.url = "assets/webPdf/h_appPdf.html";
//     logger("WebViewPage url:${widget.url}");
//     if (widget.horizontal) {
//       setScreenHorizontal();
//     }
//   }
//
//   Future<void> _downloadBlobFile(String blobUrl) async {
//     // Inject JavaScript to convert blob to base64
//     String script = '''
//     (async function() {
//       const response = await fetch("$blobUrl");
//       const blob = await response.blob();
//       const reader = new FileReader();
//       reader.onloadend = function() {
//         window.flutter_inappwebview.callHandler('blobData', reader.result);
//       };
//       reader.readAsDataURL(blob);
//     })();
//     ''';
//     _webViewController!.evaluateJavascript(source: script);
//   }
//
//   Future<void> _saveBlobFile(Uint8List bytes, String filename) async {
//     logger("WebViewPage downloadFile bytes ${bytes.length}");
//     final directory = await getTemporaryDirectory();
//     final filePath = '${directory.path}/$filename';
//     final file = File(filePath);
//     await file.writeAsBytes(bytes);
//     logger("WebViewPage downloadFile $filePath ${file.lengthSync()}");
//     // OpenFile.open(filePath);
//     push(PdfViewerPagePage(path: filePath));
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () async {
//         // callPostMessage(jsonEncode({"type": "webview_close", "data": ""}), "*");
//         return true;
//       },
//       child: Scaffold(
//         appBar: widget.hideAppbar
//             ? null
//             : AppBar(
//           title: Text(widget.title),
//         ),
//         body: Stack(
//           children: [
//             InAppWebView(
//               initialFile: widget.url,
//               ///加载本地html
//               // initialUrlRequest: URLRequest(url: WebUri(widget.url)),
//               initialSettings: InAppWebViewSettings(
//                 useOnDownloadStart: true,
//                 javaScriptEnabled: true,
//                 useHybridComposition: true,
//                 allowsInlineMediaPlayback: true,
//                 allowFileAccessFromFileURLs: true,
//                 allowUniversalAccessFromFileURLs: true,
//                 allowFileAccess: true,
//                 javaScriptCanOpenWindowsAutomatically: true,
//               ),
//               onConsoleMessage: (controller, message) {
//                 logger("WebViewPage onConsoleMessage: $message");
//               },
//               shouldOverrideUrlLoading: (controller, navigationAction) async {
//                 final url = navigationAction.request.url;
//                 logger("WebViewPage shouldOverrideUrlLoading: $url");
//                 if (url != null && url.toString().startsWith(schema)) {
//                   navigatorLyoral(this, url.toString());
//                   return NavigationActionPolicy.CANCEL;
//                 }
//                 return NavigationActionPolicy.ALLOW;
//               },
//               onWebViewCreated: (InAppWebViewController controller) {
//                 _webViewController = controller;
//                 _webViewController!.addJavaScriptHandler(
//                     handlerName: 'blobData',
//                     callback: (args) async {
//                       String originData = args[0];
//
//                       logger("WebViewPage base64data: ${originData.split(',')[0]}");
//                       String base64data = originData.split(',')[1];
//
//                       Uint8List bytes = base64.decode(base64data);
//                       await _saveBlobFile(bytes, '${widget.fileName}.pdf');
//                     });
//               },
//               onLoadStart: (controller, url) {
//                 logger("WebViewPage onLoadStart: ${url?.rawValue}");
//               },
//
//               onReceivedError: (controller, request, error) {
//                 logger("WebViewPage onReceivedError: ${error.description}");
//               },
//               onLoadStop: (controller, url) async {
//                 logger("WebViewPage onLoadStop: ${url?.rawValue}");
//                 if (widget.horizontal) {
//                   setScreenHorizontal();
//                 }
//                 if (widget.readTitle) {
//                   String webTitle = await _webViewController!.getTitle() ?? widget.title;
//                   setState(() {
//                     widget.title = webTitle;
//                   });
//                 }
//                 if (!hasFinished && isNotEmpty(widget.param)) {
//                   hasFinished = true;
//                   callPostMessage(widget.param, "*");
//                   // List fontTexts = [
//                   //   "PingFang-Heavy-normal",
//                   //   "PingFang-Bold-normal",
//                   //   "DIN-normal",
//                   //   "PingFang",
//                   // ];
//                   // for (int i = 0; i < fontTexts.length; i++) {
//                   //   delay(i * 500, () async {
//                   //     String text = fontTexts[i];
//                   //     String content = await rootBundle.loadString("assets/$text.txt");
//                   //     callPostMessage(jsonEncode({"type": text, "data": content}), "*");
//                   //   });
//                   // }
//                 }
//               },
//               onDownloadStartRequest: (controller, request) async {
//                 String url = request.url.rawValue;
//                 logger("WebViewPage onDownloadStartRequest: $url");
//                 if (url.startsWith("blob")) {
//                   await _downloadBlobFile(url);
//                 } else {
//                   String base64data = url.split(',')[1];
//                   Uint8List bytes = base64.decode(base64data);
//                   await _saveBlobFile(bytes, '${widget.fileName}.pdf');
//                 }
//               },
//             ),
//             widget.hideAppbar && !widget.hideClose
//                 ? Positioned(
//               top: 12.sp,
//               right: 24.sp,
//               child: GestureDetector(
//                 onTap: () {
//                   setScreenVertiacl();
//                   Navigator.pop(context);
//                 },
//                 child: Padding(
//                     padding: EdgeInsets.only(top: 24.sp, right: 36.sp),
//                     child: Image.asset("res/icons/modal_close.png", width: 42.sp, height: 42.sp)),
//               ),
//             )
//                 : const SizedBox(),
//           ],
//         ),
//       ),
//     );
//   }
//
//   WebStorageManager webStorageManager = WebStorageManager.instance();
//
//   @override
//   void dispose() {
//     if (!widget.disableOriention) {
//       setScreenVertiacl();
//     }
//     webStorageManager.deleteAllData();
//     InAppWebViewController.clearAllCache();
//     _webViewController?.dispose();
//     super.dispose();
//   }
//
//   void callPostMessage(String param1, String param2) {
//     String jsCode = "postMessage('$param1', '$param2');";
//     _webViewController!.evaluateJavascript(source: jsCode);
//     try {
//       logger("WebViewPage callPostMessage: ${param1}");
//     } catch (ex) {
//       //
//     }
//   }
//
//   void callLoadImage() {
//     ///通过path加载，经测试只支持Android
//     String script = 'loadImage("file:///${widget.fileName}")';
//     logger("WebViewPage call js: $script");
//     _webViewController!.evaluateJavascript(source: script);
//
//     ///通过base64加载，经测试Android、iOS都支持
//     for (int i = 0; i < 20; i++) {
//       for (List list in localPhotos.values) {
//         for (String path in list) {
//           final bytes = File(path).readAsBytesSync();
//           String base64 = base64Encode(bytes);
//           String script2 = 'loadBase64("$base64")';
//           logger("WebViewPage call js: $script2");
//           _webViewController!.evaluateJavascript(source: script2);
//         }
//       }
//     }
//   }
// }
