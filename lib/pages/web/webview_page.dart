import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/web/web_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';

/*
* title: Appbar顶部标题
* url:打开的网址
*
*
* */
class HWebView extends BasePage {
  String title;
  String url;
  bool readTitle;
  bool horizontal;
  bool hideAppbar;
  bool disableOriention;
  bool hideClose;

  HWebView({
    required this.url,
    this.title = "",
    this.readTitle = false,
    this.horizontal = false,
    this.hideAppbar = false,
    this.disableOriention = false,
    this.hideClose = false,
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _HWebViewState();
  }
}

class _HWebViewState extends BasePageState<HWebView> {
  double progressRatio = 0;
  bool isFinished = false;
  bool isDispose = false;

  late WebViewController webViewController;

  Offset dragStart = const Offset(0, 0), dragEnd = const Offset(0, 0);

  @override
  void initState() {
    super.initState();
    logger("Open webview url:${widget.url}");
    if (widget.horizontal) {
      setScreenHorizontal();
    }
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0xFFF6F6F6))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // logger("On webview progress:$progress");
            if (!isDispose) {
              setState(() {
                progressRatio = progress / 100;
              });
            }
          },
          onPageStarted: (String url) {
            logger("On webview start $url");
          },
          onPageFinished: (String url) async {
            logger("On webview finish");
            if (!isDispose) {
              setState(() {
                progressRatio = 0;
                isFinished = true;
              });

              if (widget.horizontal) {
                setScreenHorizontal();
              }
              if (widget.readTitle) {
                String webTitle = await webViewController.getTitle() ?? widget.title;
                setState(() {
                  widget.title = webTitle;
                });
              }
            }
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            logger("On webview onNavigationRequest ${request.url}");
            if (isNotEmpty(request.url) && request.url.startsWith(schema)) {
              navigatorLyoral(this, request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));

    // OrientationPlugin.forceOrientation(DeviceOrientation.landscapeRight);
  }

  @override
  void dispose() {
    isDispose = true;
    if (!widget.disableOriention) {
      setScreenVertiacl();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.hideAppbar
          ? null
          : AppBar(
              title: Text(widget.title),
            ),
      body: Stack(
        children: [
          WebViewWidget(
            controller: webViewController,
          ),
          Container(height: 2.sp, width: progressRatio.sw, color: Colors.green),
          isFinished
              ? const SizedBox()
              : Align(
                  alignment: Alignment.topCenter,
                  child: Padding(padding: EdgeInsets.only(top: 100.sp), child: MyText(Lang.loading, color2B, 14.sp))),
          widget.hideAppbar && !widget.hideClose
              ? Positioned(
                  top: 12.sp,
                  right: 24.sp,
                  child: GestureDetector(
                    onTap: () {
                      setScreenVertiacl();
                      Navigator.pop(context);
                    },
                    child: Padding(
                        padding: EdgeInsets.only(top: 24.sp, right: 36.sp),
                        child: Image.asset("res/icons/modal_close.png", width: 42.sp, height: 42.sp)),
                  ),
                )
              : const SizedBox(),
        ],
      ),
    );
  }
}
