import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/default_style.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/viewer/pdf_preview.dart';
import 'package:mooeli/pages/web/web_manager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebReportPage extends BasePage {
  ScanInfo record;

  WebReportPage(this.record);

  @override
  State<StatefulWidget> createState() {
    return WebReportPageState();
  }
}

class WebReportPageState extends BasePageState<WebReportPage> with WidgetsBindingObserver {
  double progressRatio = 0;
  bool isFinished = false;
  bool isDispose = false;

  late WebViewController webViewController;
  late WebViewController pdfController;

  Offset dragStart = const Offset(0, 0), dragEnd = const Offset(0, 0);

  String url = "assets/webPdf/h_boxPDF_$lang.html";

  @override
  void initState() {
    super.initState();
    addEventListener<EventClosePdf>((event) {
      webViewController.loadFlutterAsset(url);
    });
    WidgetsBinding.instance.addObserver(this);
    String fileName =
        "${widget.record.recordName}-${Global.getDateByTimestamp(milliseconds: widget.record.customTime)}";
    String param = jsonEncode({
      "type": "http_param",
      "data": {
        "accessToken": currentUser.accessToken,
        "refreshToken": currentUser.refreshToken,
        "url": HHttp.getApiHost(),
        "json_url": HHttp.getApiHost().toString().substring(0, HHttp.getApiHost().toString().lastIndexOf("/")),
        "recordId": widget.record.recordId,
        "region": getLoginArea() != 0 ? "Overseas" : "China",
        "lang": lang,
      },
    });
    logger("Open webview url:$url");
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0xFFF6F6F6))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // logger("WebViewPage progress:$progress");
            if (!isDispose) {
              setState(() {
                progressRatio = progress / 100;
              });
            }
          },
          onPageStarted: (String url) {
            logger("WebViewPage start $url");
          },
          onPageFinished: (String url) async {
            logger("WebViewPage finish");
            if (!isDispose) {
              setState(() {
                progressRatio = 0;
                isFinished = true;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            logger("WebViewPage onWebResourceError ${error.description}");
          },
          onNavigationRequest: (NavigationRequest request) async {
            logger("WebViewPage onNavigationRequest ${request.url}");
            if (isNotEmpty(request.url) && startWithSchema(request.url)) {
              navigatorLyoral(this, request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..addJavaScriptChannel('setToken', onMessageReceived: (args) {
        logger("WebViewPage onJavaScriptChannel setToken");
        if (isNotEmpty(param)) {
          callPostMessage(param, "*");
        }
      })
      ..addJavaScriptChannel('blobData', onMessageReceived: (args) async {
        logger("WebViewPage onJavaScriptChannel blobData ${args.message.length} ${args.message.substring(0, 40)}");
        String base64data = args.message.split(',')[1];
        Uint8List bytes = base64.decode(base64data);
        await _saveBlobFile(bytes, '$fileName.pdf');
      })
      // ..loadRequest(Uri.parse(url"));
      ..loadFlutterAsset(url);

    pdfController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0xFFF6F6F6))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            logger("WebViewPage pdf start $url");
          },
          onPageFinished: (String url) async {
            logger("WebViewPage pdf finish");
          },
          onWebResourceError: (WebResourceError error) {
            logger("WebViewPage pdf onWebResourceError ${error.description}");
          },
          onNavigationRequest: (NavigationRequest request) async {
            logger("WebViewPage pdf onNavigationRequest ${request.url}");
            if (isNotEmpty(request.url) && startWithSchema(request.url)) {
              navigatorLyoral(this, request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..addJavaScriptChannel('setToken', onMessageReceived: (args) {
        logger("WebViewPage onJavaScriptChannel setToken");
        if (isNotEmpty(param)) {
          String jsCode = "postMessage('$param', '*');";
          pdfController.runJavaScript(jsCode);
        }
      })
      ..addJavaScriptChannel('printPDF', onMessageReceived: (args) {
        logger("WebViewPage onJavaScriptChannel printPDF");
        delay(2000, () async {
          final directory = await getTemporaryDirectory();
          final filePath = '${directory.path}/${fileName}_${DateTime.now().millisecondsSinceEpoch}.pdf';
          final path = await pdfController.runJavaScriptReturningResult("lyapp('exportPdf','$filePath');");
          logger("WebViewPage exportPdf: $path");
          push(PdfViewerPagePage(path: filePath));
          printPdfInfo("PrintFinished");
        });
      });
  }

  void loadPdfUrl(String param) {
    pdfController.loadFlutterAsset("assets/quickPdf/index.html");
  }

  void printPdfInfo(String type) {
    callPostMessage(jsonEncode({"type": type}), "*");
  }

  Future<void> _saveBlobFile(Uint8List bytes, String filename) async {
    logger("WebViewPage downloadFile bytes ${bytes.length}");
    final directory = await getTemporaryDirectory();
    final filePath = '${directory.path}/$filename';
    final file = File(filePath);
    await file.writeAsBytes(bytes);
    logger("WebViewPage downloadFile $filePath ${file.lengthSync()}");
    // OpenFile.open(filePath);
    push(PdfViewerPagePage(path: filePath));
  }

  void callPostMessage(String param1, String param2) {
    String jsCode = "postMessage('$param1', '$param2');";
    webViewController.runJavaScript(jsCode);
    try {
      logger("WebViewPage callPostMessage: $param1");
    } catch (ex) {
      //
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    logger("WebViewPage didChangeAppLifecycleState: $state");
    switch (state) {
      case AppLifecycleState.resumed:
        if (!isPaused) {
          webViewController.loadFlutterAsset(url);
        }
        break;
      default:
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    isDispose = true;
    webViewController.clearCache();
    webViewController.clearLocalStorage();
    pdfController.clearCache();
    pdfController.clearLocalStorage();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          WebViewWidget(
            controller: pdfController,
          ),
          WebViewWidget(
            controller: webViewController,
          ),
          Container(height: 2.sp, width: progressRatio.sw, color: colorBrand),
        ],
      ),
    );
  }

  void selectAge(int age) {
    PickerStyle pickerStyle = DefaultPickerStyle();
    pickerStyle.commitButton = Container(
      alignment: Alignment.center,
      padding: EdgeInsets.only(left: 32.sp, right: 32.sp),
      child: Text(Lang.confirm, style: TextStyle(color: colorBlueDeep, fontSize: 32.sp)),
    );
    pickerStyle.cancelButton = Container(
      alignment: Alignment.center,
      padding: EdgeInsets.only(left: 32.sp, right: 32.sp),
      child: Text(Lang.cancel, style: TextStyle(color: Theme.of(context!).unselectedWidgetColor, fontSize: 32.sp)),
    );
    pickerStyle.textSize = 28.sp;

    List<int> singleData = List.generate(101, (index) => index);
    Pickers.showSinglePicker(
      context,
      data: singleData,
      selectData: age,
      pickerStyle: pickerStyle,
      onConfirm: (var data, int position) {
        callPostMessage(
            jsonEncode({
              "type": "onSelectAge",
              "data": {
                "age": position,
              },
            }),
            "*");
      },
    );
  }
}
