import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/test/convert_pdf_page.dart';
import 'package:mooeli/pages/test/draw_image_page.dart';
import 'package:mooeli/widget/flushbar/flushbar.dart';
import 'package:mooeli/widget/my_widget.dart';

class TestListPage extends BasePage {
  const TestListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _TestListPagetate();
  }
}

class _TestListPagetate extends BasePageState<TestListPage> {
  late Flushbar flushbar;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Test List"),
      ),
      body: Container(
        margin: const EdgeInsets.all(20),
        child: ListView(
          children: [
            ElevatedButton(
                onPressed: () {
                  push(DrawImagePage());
                },
                child: const Text("绘图测试")),
            ElevatedButton(
                onPressed: () {
                  push(ConvertPdfPage());
                },
                child: const Text("WebToPdf")),
            ElevatedButton(
                onPressed: () {
                  flushbar = Flushbar(
                      flushbarPosition: FlushbarPosition.TOP,
                      animationDuration: const Duration(milliseconds: 300),
                      duration: const Duration(seconds: 3),
                      margin: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 0),
                      padding: EdgeInsets.all(16.sp),
                      icon: Container(
                        padding: EdgeInsets.only(left: 16.sp, right: 8.sp),
                        child: SizedBox(
                          width: 24.sp,
                          height: 24.sp,
                          child: Image.asset(
                            "res/scan/low_battery_yellow.png",
                            width: 24.sp,
                          ),
                        ),
                      ),
                      borderRadius: BorderRadius.circular(8.sp),
                      backgroundGradient: const LinearGradient(
                        colors: [
                          Color(0xFFF3D684),
                          Color(0xFFEF8666),
                          Color(0xFFFFA286),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      messageText: MyText("口腔观察仪的电量过低！", Colors.white, 16.sp),
                      mainButton: IconButton(
                        icon: Icon(
                          Icons.close,
                          size: 20.sp,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          flushbar.dismiss();
                        },
                      ));
                  flushbar.show(context);
                },
                child: const Text("SnackBar")),
          ],
        ),
      ),
    );
  }
}
