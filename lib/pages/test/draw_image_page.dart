import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class DrawImagePage extends BasePage {
  @override
  _DrawImagePageState createState() => _DrawImagePageState();
}

class _DrawImagePageState extends BasePageState<DrawImagePage> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text("DrawImage")),
        body: Center(
          child: ImagePathOverlay(
            imagePath: 'res/scan/print_scan_close_mid.png', // 请将路径更改为您的图片路径
            paths: [
              // 曲线1 (左上牙齿)
              [
                Offset(60, 150),
                Offset(50, 120),
                Offset(70, 100),
                Offset(90, 130),
                Offset(110, 110),
                Offset(130, 100),
                Offset(150, 130),
                Offset(170, 120),
                Offset(160, 150),
                Offset(60, 150), // Close the path
              ],
              // 曲线2 (右上牙齿，x 增加 100)
              [
                Offset(320, 150), // 270 + 50
                Offset(340, 120), // 290 + 50
                Offset(320, 100), // 270 + 50
                Offset(300, 130), // 250 + 50
                Offset(280, 110), // 230 + 50
                Offset(260, 100), // 210 + 50
                Offset(240, 130), // 190 + 50
                Offset(220, 120), // 170 + 50
                Offset(230, 150), // 180 + 50
                Offset(320, 150), // Close the path
              ],

              // 曲线3 (左下牙齿)
              [
                Offset(60, 90),
                Offset(50, 70),
                Offset(70, 50),
                Offset(90, 80),
                Offset(110, 60),
                Offset(130, 50),
                Offset(150, 80),
                Offset(170, 70),
                Offset(160, 90),
                Offset(60, 90), // Close the path
              ],

              // 曲线4 (右下牙齿，x 增加 100)
              [
                Offset(320, 90), // 270 + 50
                Offset(340, 70), // 290 + 50
                Offset(320, 50), // 270 + 50
                Offset(300, 80), // 250 + 50
                Offset(280, 60), // 230 + 50
                Offset(260, 50), // 210 + 50
                Offset(240, 80), // 190 + 50
                Offset(220, 70), // 170 + 50
                Offset(230, 90), // 180 + 50
                Offset(320, 90), // Close the path
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class ImagePathOverlay extends StatefulWidget {
  final String imagePath;
  final List<List<Offset>> paths;

  const ImagePathOverlay({Key? key, required this.imagePath, required this.paths}) : super(key: key);

  @override
  _ImagePathOverlayState createState() => _ImagePathOverlayState();
}

class _ImagePathOverlayState extends State<ImagePathOverlay> {
  Offset offset = Offset.zero;
  double rotation = 0.0;
  double scale = 1.0;
  int? highlightedPathIndex;

  Offset? lastFocalPoint;
  double lastScale = 1.0;
  double lastRotation = 0.0;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onScaleStart: (details) {
        lastFocalPoint = details.focalPoint; // 记录起始焦点位置
        lastScale = scale; // 初始缩放比例
        lastRotation = rotation; // 初始旋转角度
      },
      onScaleUpdate: (details) {
        setState(() {
          // 计算缩放
          scale = lastScale * details.scale;

          // 限制缩放比例
          if (scale < 0.5)
            scale = 0.5; // 最小缩放比例
          else if (scale > 3.0) scale = 3.0; // 最大缩放比例

          // 计算旋转
          rotation = lastRotation + details.rotation;

          // 平移操作
          if (details.pointerCount == 1) {
            if (lastFocalPoint != null) {
              // 直接使用手指的位置进行平移
              offset += details.focalPoint - lastFocalPoint!;
            }
          }

          lastFocalPoint = details.focalPoint; // 更新最后的焦点位置
        });
      },
      onTapUp: (details) {
        // 计算变换后的点
        final point = details.localPosition;

        // 逆变换
        final transformedPoint = inverseTransform(point);

        for (int i = 0; i < widget.paths.length; i++) {
          if (isPointInPath(transformedPoint, widget.paths[i])) {
            setState(() {
              highlightedPathIndex = i;
            });

            double width = 200.sp, height = 100.sp;
            double left =
                (details.localPosition.dx < 1.sw - width - 20.sp) ? details.localPosition.dx : (1.sw - width - 20.sp);
            double top =
                (details.localPosition.dy < 1.sh - height - 20.sp) ? details.localPosition.dy : (1.sh - height - 20.sp);
            showCustomDialog(
              SizedBox(
                width: 1.sw,
                height: 1.sh,
                child: Stack(
                  children: [
                    SizedBox(width: 1.sw, height: 1.sh),
                    Positioned(
                      left: left,
                      top: top,
                      child: Container(
                        // width: width,
                        // height: height,
                        constraints: BoxConstraints(minHeight: height, maxWidth: width),
                        margin: EdgeInsets.all(20.sp),
                        padding: EdgeInsets.all(20.sp),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(16.sp)),
                          color: Colors.white,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            MyText("${details.localPosition}\ntop: $top, left: $left\n${1.sw}, ${1.sh}", color2B, 14.sp)
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
            break;
          }
        }
      },
      child: Transform(
        transform: Matrix4.identity()
          ..translate(offset.dx, offset.dy)
          ..rotateZ(rotation)
          ..scale(scale),
        child: Stack(
          children: [
            Image.asset(widget.imagePath),
            CustomPaint(
              painter: PathPainter(paths: widget.paths, highlightedPathIndex: highlightedPathIndex),
              child: Container(),
            ),
          ],
        ),
      ),
    );
  }

  // 计算逆变换后的坐标
  Offset inverseTransform(Offset point) {
    // 先应用缩放和旋转的逆变换
    final double scaledX = (point.dx - offset.dx) / scale;
    final double scaledY = (point.dy - offset.dy) / scale;

    // 计算逆旋转
    final double angle = -rotation;
    final double cosTheta = math.cos(angle);
    final double sinTheta = math.sin(angle);

    final double transformedX = scaledX * cosTheta - scaledY * sinTheta;
    final double transformedY = scaledX * sinTheta + scaledY * cosTheta;

    return Offset(transformedX, transformedY);
  }

  // 判断点是否在路径内部
  bool isPointInPath(Offset point, List<Offset> pathPoints) {
    // 创建路径
    final Path path = Path();
    path.moveTo(pathPoints[0].dx, pathPoints[0].dy);

    for (var point in pathPoints) {
      path.lineTo(point.dx, point.dy);
    }
    path.close();

    // 先检查点是否在路径的边界框内，以减少不必要的计算
    final Rect bounds = path.getBounds();
    if (!bounds.contains(point)) {
      return false;
    }

    // 通过路径的contains方法判断点是否在路径内
    return path.contains(point);
  }
}

// 自定义绘制闭合路径的 Painter
class PathPainter extends CustomPainter {
  final List<List<Offset>> paths;
  final int? highlightedPathIndex;

  PathPainter({required this.paths, required this.highlightedPathIndex});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    for (int i = 0; i < paths.length; i++) {
      paint.color = (i == highlightedPathIndex) ? Colors.red : Colors.blue;

      final Path path = Path();
      path.moveTo(paths[i][0].dx, paths[i][0].dy);
      for (var point in paths[i]) {
        path.lineTo(point.dx, point.dy);
      }
      path.close();

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
