import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/viewer/pdf_preview.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:webcontent_converter/webcontent_converter.dart';

class ConvertPdfPage extends BasePage {
  @override
  _ConvertPdfPageState createState() => _ConvertPdfPageState();
}

class _ConvertPdfPageState extends BasePageState<ConvertPdfPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Convert Pdf"),
      ),
      body: Container(
        margin: const EdgeInsets.all(20),
        child: ListView(
          children: [
            ElevatedButton(
              onPressed: () {
                convertPdf(1);
              },
              child: const Text("Url"),
            ),
            ElevatedButton(
              onPressed: () {
                convertPdf(2);
              },
              child: const Text("Assets不带参数"),
            ),
            ElevatedButton(
              onPressed: () {
                convertPdf(3);
              },
              child: const Text("Assets带参数"),
            ),
            ElevatedButton(
              onPressed: () {
                convertPdf(4);
              },
              child: const Text("File"),
            ),
          ],
        ),
      ),
    );
  }

  convertPdf(int type) async {
    final directory = await getExternalStorageDirectory();
    final savedPath = '${directory!.path}/TestPdf-${DateTime.now().millisecondsSinceEpoch}.pdf';
    logger("WebViewPage savedPath $savedPath");
    var result;
    switch (type) {
      case 1: //Url
        result = await WebcontentConverter.webUriToPdf(
          uri: "https://www.chohotech.com",
          duration: 5000,
          savedPath: savedPath,
          format: PaperFormat.a4,
          margins: PdfMargins.px(top: 35, bottom: 35, right: 35, left: 35),
        );
        break;
      case 2: //Assets不带参数
        result = await WebcontentConverter.filePathToPdf(
          path: "assets/test_pdf.html",
          duration: 5000,
          savedPath: savedPath,
          format: PaperFormat.a4,
          margins: PdfMargins.px(top: 35, bottom: 35, right: 35, left: 35),
        );
        break;
      case 3: //Assets带参数
        result = await WebcontentConverter.filePathToPdf(
          path: "assets/test_pdf.html?param=123",
          duration: 5000,
          savedPath: savedPath,
          format: PaperFormat.a4,
          margins: PdfMargins.px(top: 35, bottom: 35, right: 35, left: 35),
        );
        break;
      case 4: //本地文件path
        final ByteData data = await rootBundle.load('assets/test_pdf.html');
        final List<int> bytes = data.buffer.asUint8List();
        // 将文件保存到缓存目录
        final File file = File("${directory.path}/test_pdf_${DateTime.now().millisecondsSinceEpoch}.html");
        await file.writeAsBytes(bytes);
        logger("WebViewPage copy ${file.path}");
        result = await WebcontentConverter.filePathToPdf(
          path: file.path,
          duration: 5000,
          savedPath: savedPath,
          format: PaperFormat.a4,
          margins: PdfMargins.px(top: 35, bottom: 35, right: 35, left: 35),
        );
        if (result == null) {
          logger("PDF conversion failed");
        } else {
          logger("PDF saved at: $result");
        }
        break;
    }
    logger("WebViewPage webUriToPdf $result");
    push(PdfViewerPagePage(path: result));
  }
}
