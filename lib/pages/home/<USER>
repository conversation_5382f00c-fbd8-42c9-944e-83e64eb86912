import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/model/user_info.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/login/login_page.dart';
import 'package:mooeli/pages/my/my_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/pages/scan/scan_activity_list_page.dart';
import 'package:mooeli/pages/scan/scan_record_list_page.dart';
import 'package:mooeli/test/test_list_page.dart';
import 'package:mooeli/test/uvc_camera_list_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class HomePage extends BasePage {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return HomePageState();
  }
}

class HomePageState extends BasePageState<HomePage> {
  bool showLogin = false;

  @override
  void initState() {
    super.initState();
    addEventListener<EventSwitchTenant>((event) {
      setState(() {
        currentUser.providerName = event.selectTenant.name;
      });
    });
    addEventListener<EventRefreshUserInfo>((event) {
      setState(() {});
    });
    addEventListener<EventLogin>((event) {
      setState(() {});
    });
    addEventListener<EventLoging>((event) {
      showLogin = true;
    });
    initAnalyticsMaps();
    getUserInfo();
    disconnectUltra(disconnectWifi: true);
  }

  getUserInfo() {
    HHttp.request("/v3/provider/getProviderUserInfo", "POST", (data) {
      setState(() {
        String accessToken = currentUser.accessToken;
        String refreshToken = currentUser.refreshToken;
        currentUser = UserInfo.fromJson(data);
        currentUser.accessToken = accessToken;
        currentUser.refreshToken = refreshToken;
        logger("get UserInfo refreshToken: ${currentUser.refreshToken}");
        Global.sharedPrefs.setString("login_user", jsonEncode(currentUser.toJson()));
        HHttp.setToken(accessToken);
        eventBus.fire(EventRelogin());
      });
    });
  }

  void getAnnounceList() {
    // try {
    //   Map json = getStaticConfigJson();
    //   List list = json["announces"];
    //   announceList.clear();
    //   if (isNotEmpty(list)) {
    //     setState(() {
    //       showAnnounce = Global.sharedPrefs.getBool("show_announce") ?? true;
    //       for (dynamic announce in list) {
    //         if (isNotEmpty(announce["title"])) {
    //           announceList.add(announce);
    //         }
    //       }
    //     });
    //   }
    // } catch (ex) {
    //   //
    // }
  }

  int clickEnv = 0;

  @override
  Widget build(BuildContext context) {
    bool isLogin = isNotEmpty(currentUser.accessToken);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: Container(
        padding: EdgeInsets.all(80.sp),
        child: Row(
          children: [
            Flexible(
              flex: 679,
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    height: 312.sp,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(32.sp),
                      image: const DecorationImage(
                        image: AssetImage("res/imgs/home_my_bg.png"),
                        fit: BoxFit.cover,
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(48.sp, 48.sp, 48.sp, 0),
                      child: isLogin
                          ? Column(
                              children: [
                                Row(
                                  children: [
                                    GestureDetector(
                                      child: User.instance.getUserAvatar(88.sp),
                                      onTap: () {
                                        if (kDebugMode || isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
                                          push(const TestListPage());
                                        }
                                      },
                                    ),
                                    SizedBox(width: 24.sp),
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: 420.sp,
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: SingleText(
                                                    currentUser.personName, Colors.white, 32.sp, FontWeight.w500),
                                              ),
                                              Click(
                                                onTap: () async {
                                                  if (await HHttp.checkInternetValid()) {
                                                    Global.showAlertDialog(
                                                      Lang.logout_account,
                                                      Lang.confirm_logout,
                                                      contentCenter: true,
                                                      okText: Lang.exit,
                                                      okCallBack: () {
                                                        User.instance.clearUserData();
                                                      },
                                                    );
                                                  } else {
                                                    Global.showAlertDialog(
                                                      Lang.logout_account,
                                                      Lang.confirm_logout_offline,
                                                      contentCenter: true,
                                                      okText: Lang.exit,
                                                      okCallBack: () {
                                                        User.instance.clearUserData();
                                                      },
                                                    );
                                                  }
                                                },
                                                child: Container(
                                                  height: 44.sp,
                                                  padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 16.sp, 8.sp),
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(32.sp),
                                                    color: color53,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Image.asset("res/icons/icon_exit.png", width: 24.sp),
                                                      SizedBox(width: 10.sp),
                                                      MyText(Lang.logout, Colors.white, 20.sp),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 12.sp),
                                        ConstrainedBox(
                                          constraints: BoxConstraints(maxWidth: 450.sp),
                                          child: SingleText(currentUser.getTenantName(), Colors.white70, 20.sp),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                Click(
                                  child: Container(
                                    width: 583.sp,
                                    height: 80.sp,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(top: 48.sp),
                                    decoration: BoxDecoration(
                                      color: Colors.white24,
                                      borderRadius: BorderRadius.circular(24.sp),
                                    ),
                                    child: MyText(Lang.my_center, Colors.white, 32.sp, FontWeight.w600),
                                  ),
                                  onTap: () {
                                    push(const MyPage());
                                  },
                                ),
                              ],
                            )
                          : Column(
                              children: [
                                Row(
                                  children: [
                                    GestureDetector(
                                      child: Image.asset("res/icons/icon_not_login.png", width: 88.sp, height: 88.sp),
                                      onTap: () {
                                        clickEnv++;
                                        if (clickEnv > 9 && clickEnv % 2 == 0) {
                                          showBottomListDialog(
                                              context, HttpEnv.values.map((e) => "${e.name}环境").toList(), (index) {
                                            HttpEnv env = HttpEnv.values[index];
                                            Global.sharedPrefs.setString("http_env", env.name);
                                            HHttp.initHttp();
                                            setState(() {});
                                          });
                                        }
                                      },
                                    ),
                                    SizedBox(width: 24.sp),
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          SingleText(Lang.not_login, Colors.white, 32.sp, FontWeight.w500),
                                          SizedBox(height: 12.sp),
                                          MyText(Lang.login_show_tenant, Colors.white, 20.sp),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                Click(
                                  onTap: showLoginDialog,
                                  child: Container(
                                    width: 583.sp,
                                    height: 80.sp,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(top: 48.sp),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(24.sp),
                                    ),
                                    child: MyText(Lang.login_now, color2B, 32.sp, FontWeight.w600),
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                  SizedBox(height: 24.sp),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.fromLTRB(48.sp, 48.sp, 0, 0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32.sp),
                        image: const DecorationImage(
                          image: AssetImage("res/imgs/home_more_bg.png"),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          if (kDebugMode || isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
                            push(const CameraListPage());
                          }
                        },
                        child: MyText(Lang.waiting_new_function, Colors.white, 40.sp, FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 24.sp),
            Flexible(
              flex: 1089,
              child: Container(
                width: double.infinity,
                height: 952.sp,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(32.sp),
                  image: const DecorationImage(
                    image: AssetImage("res/imgs/home_scan_bg.png"),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.topRight,
                      child: Image.asset(
                        "res/imgs/home_scan_img.png",
                        height: 1.sh - 96.sp,
                        fit: BoxFit.contain,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(48.sp),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          MyText(Lang.mouth_data_collect, color2B, 40.sp, FontWeight.w600),
                          Row(
                            children: [
                              Expanded(
                                child: Click(
                                  onTap: () {
                                    if (isLogin) {
                                      HHttp.request(
                                        "/v3/quickscan/campaign/getIdBySingle",
                                        "POST",
                                        (data) {
                                          ScanActivity scanActivity = ScanActivity();
                                          scanActivity.campaignId = data["campaignId"];
                                          scanActivity.campaignName = Lang.scan_in_hospital;
                                          scanActivity.singleCampaign = true;
                                          push(ScanRecordListPage(scanActivity));
                                          Global.sharedPrefs.setString("local_campaign_id", data["campaignId"]);
                                        },
                                        errCallBack: () {
                                          String? localId = Global.sharedPrefs.getString("local_campaign_id");
                                          if (isNotEmpty(localId)) {
                                            localSingleCampaignId = localId!;
                                          }
                                          ScanActivity scanActivity = ScanActivity();
                                          scanActivity.campaignId = localSingleCampaignId;
                                          scanActivity.campaignName = Lang.scan_in_hospital;
                                          scanActivity.singleCampaign = true;
                                          push(ScanRecordListPage(scanActivity));
                                        },
                                        params: {},
                                      );
                                    } else {
                                      showLoginDialog();
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 24.sp),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(24.sp),
                                      boxShadow: [
                                        BoxShadow(
                                          color: colorShader,
                                          blurRadius: 1.sp,
                                          spreadRadius: 1.sp,
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            MyText(Lang.scan_in_hospital, colorBlueDeep, 36.sp, FontWeight.w600),
                                            Image.asset("res/icons/icon_right_brand.png", width: 48.sp),
                                          ],
                                        ),
                                        SizedBox(height: 8.sp),
                                        MyText(Lang.scan_in_hospital_desc, colorBlueDeep.withOpacity(0.48), 24.sp),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 16.sp),
                              Expanded(
                                child: Click(
                                  onTap: () {
                                    if (isLogin) {
                                      push(const ScanActivityListPage());
                                    } else {
                                      showLoginDialog();
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 24.sp),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(24.sp),
                                      boxShadow: [
                                        BoxShadow(
                                          color: colorShader,
                                          blurRadius: 1.sp,
                                          spreadRadius: 1.sp,
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            MyText(Lang.scan_out_hospital, colorBlueDeep, 36.sp, FontWeight.w600),
                                            Image.asset("res/icons/icon_right_brand.png", width: 48.sp),
                                          ],
                                        ),
                                        SizedBox(height: 8.sp),
                                        MyText(Lang.scan_out_hospital_desc, colorBlueDeep.withOpacity(0.48), 24.sp),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    HHttp.httpEnv == HttpEnv.prod && isEmpty(Global.sharedPrefs.getString("http_env"))
                        ? const SizedBox()
                        : Align(
                            alignment: Alignment.topRight,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  padding: EdgeInsets.only(top: 40.sp, right: 40.sp),
                                  child: Banner(
                                    message: Global.sharedPrefs.getString("http_env") ?? HHttp.httpEnv.name,
                                    location: BannerLocation.bottomStart,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  showLoginDialog() {
    showCustomDialog(Login());
  }

  @override
  void onRouteResume(Route nextRoute) {
    if (showLogin) {
      showLogin = false;
      showLoginDialog();
    }
    disconnectUltra(disconnectWifi: true);
  }
}
