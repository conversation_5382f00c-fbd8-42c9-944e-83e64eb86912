import 'package:flutter/material.dart';
import 'package:mooeli/pages/base_page.dart';

class OfflineHomePage extends BasePage {
  const OfflineHomePage({super.key});

  @override
  State<OfflineHomePage> createState() => _OfflineHomePageState();
}

class _OfflineHomePageState extends BasePageState<OfflineHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
      ),
      body: Stack(
        children: [
          Image.asset('res/icons/icon_offline_home_background.png', fit: BoxFit.cover,)
        ],
      ),
    );
  }
}
