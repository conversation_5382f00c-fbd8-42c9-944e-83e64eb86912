import 'package:flutter/material.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:video_player/video_player.dart';

enum HVideoFitTYpe {
  fill,
  cover,
  contain,
}

class HVideoPlayer extends StatefulWidget {
  final String targetFilePathName;
  final String title;
  final bool isLoop;
  final double volume;
  final VideoPlayerController? initVideoController;

  //true:效果同保持原比例前提下进行缩放填满不拉伸
  //false:保持原比例不变的前提下填充
  final HVideoFitTYpe fitType;

  const HVideoPlayer(
      {Key? key,
      this.title = "",
      this.targetFilePathName = "",
      this.initVideoController,
      this.isLoop = false,
      this.fitType = HVideoFitTYpe.contain,
      this.volume = 1.0})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return CustomVideoPlayerState();
  }
}

class CustomVideoPlayerState extends State<HVideoPlayer> {
  VideoPlayerController? videoController;

  @override
  initState() {
    super.initState();
    _startPlay(widget.targetFilePathName);
  }

  replay() {
    if (videoController != null) {
      videoController!.seekTo(const Duration(seconds: 0));
    }
  }

  @override
  dispose() {
    logger("HVideoPlayer dispose");
    if (videoController != null && widget.initVideoController == null) {
      videoController!.dispose();
    }
    super.dispose();
  }

  pausePlay() {
    if (videoController != null) {
      videoController!.pause();
    }
  }

  resumePlay() {
    if (videoController != null) {
      videoController!.play();
    }
  }

  stopPlay() {
    if (videoController != null) {
      videoController!.dispose();
    }
  }

  Future<void> _startPlay(targetFilePathName) async {
    if (widget.initVideoController != null) {
      videoController = widget.initVideoController;
    } else {
      videoController = VideoPlayerController.asset(targetFilePathName);
      await videoController!.setLooping(widget.isLoop);
      if (widget.volume != 1.0) {
        await videoController!.setVolume(widget.volume);
      }
      await videoController!.initialize().then((_) {
        setState(() {});
        videoController!.play();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.black,
        body: Center(
          child: videoController == null
              ? Text(Lang.loading_video)
              : widget.fitType == HVideoFitTYpe.cover
                  ? SizedBox.expand(
                      child: FittedBox(
                          fit: BoxFit.cover,
                          child: SizedBox(
                              width: videoController!.value.size.width * videoController!.value.aspectRatio,
                              height: videoController!.value.size.height * videoController!.value.aspectRatio,
                              child: VideoPlayer(videoController!))),
                    )
                  : widget.fitType == HVideoFitTYpe.contain
                      ? AspectRatio(
                          aspectRatio: videoController!.value.aspectRatio,
                          child: VideoPlayer(videoController!),
                        )
                      : widget.fitType == HVideoFitTYpe.fill
                          ? VideoPlayer(videoController!)
                          : VideoPlayer(videoController!),
        ));
  }
}
