import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/video_player/HVideoPlayer.dart';

class HVideoSelectFitType extends StatefulWidget {
  final double width;

  const HVideoSelectFitType({Key? key, required this.width}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _HVideoSelectFitTypeState();
  }
}

class _HVideoSelectFitTypeState extends State<HVideoSelectFitType>
    with SingleTickerProviderStateMixin {
  //select fit type
  late Animation<double> aniFitTypeUI;
  late AnimationController aniControllFitTypeUI;
  bool isShowFitTypeUI = false;

  @override
  void initState() {
    super.initState();

    aniControllFitTypeUI = AnimationController(
        duration: const Duration(milliseconds: 200), vsync: this);
    aniFitTypeUI =
        CurvedAnimation(parent: aniControllFitTypeUI, curve: Curves.bounceIn);
    aniFitTypeUI =
        Tween<double>(begin: 0, end: 1).animate(aniControllFitTypeUI);
    toggleSelectFitTypeUI();
  }

  toggleSelectFitTypeUI() {
    if (isShowFitTypeUI) {
      aniControllFitTypeUI.reverse();
    } else {
      aniControllFitTypeUI.forward();
    }
    isShowFitTypeUI = !isShowFitTypeUI;
  }

  getSelectWidgets(bool isFullScreen) {
    List fitTypeConfig = [
      {
        "title": Lang.fill_in,
        "fitType":HVideoFitTYpe.fill,
      },
      {
        "title": Lang.default_type,
        "fitType":HVideoFitTYpe.contain,
      },
      {
        "title": Lang.stretch,
        "fitType":HVideoFitTYpe.cover,
      },
    ];
    List<Widget> listViews = [];
    for (int i = 0; i < fitTypeConfig.length; i++) {
      listViews.add(
        Text(fitTypeConfig[i]["title"],
            style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: isFullScreen ? 20.sp : 14.sp)),
      );
      return listViews;
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isFullScreen = 1.sw > 1.sh;
    return Opacity(
        opacity: aniFitTypeUI.value,
        child: Align(
          alignment: Alignment.centerRight,
          child: Container(
            width: widget.width,
            height: double.infinity,
            decoration: BoxDecoration(color: Colors.black.withOpacity(0.8)),
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  children: getSelectWidgets(isFullScreen),
                ),
              ),
            ),
          ),
        ));
  }
}
