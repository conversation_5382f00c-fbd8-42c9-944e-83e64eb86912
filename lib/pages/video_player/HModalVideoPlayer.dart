import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/video_player/HVideoPlayer.dart';
import 'package:mooeli/pages/video_player/HVideoUI.dart';
import 'package:video_player/video_player.dart';

class HModalVideoPlayer extends StatefulWidget {
  final String targetFilePathName;
  final String title;
  final dynamic closeBotton;
  final bool isLoop;
  final double volume;

  const HModalVideoPlayer(
      {Key? key,
        required this.targetFilePathName,
        required this.closeBotton,
        this.isLoop = false,
        this.volume = 0,
        this.title = ""})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _HModalVideoPlayerState();
  }
}

class _HModalVideoPlayerState extends State<HModalVideoPlayer> {
  VideoPlayerController? videoController;

  double oldVideoWidth = 328.sp;
  double oldVideoHeight = 184.sp;
  double videoWidth = 0;
  double videoHeight = 0;
  bool isFullScreen = false;

  @override
  initState() {
    super.initState();
    videoWidth = oldVideoWidth;
    videoHeight = oldVideoHeight;
    _startPlay(widget.targetFilePathName);
  }

  @override
  dispose() {
    if (videoController != null) {
      videoController!.dispose();
    }
    super.dispose();
  }

  Future<void> _startPlay(targetFilePathName) async {
    videoController = VideoPlayerController.asset(targetFilePathName);
    await videoController!.setLooping(widget.isLoop);
    if(widget.volume!=0){
      await videoController!.setVolume(widget.volume);
    }
    await videoController!.initialize().then((_) {
      setState(() {});
      videoController!.play();
    });
  }

  intoFullScreen() {
    setState(() {
      videoWidth = double.infinity;
      videoHeight = double.infinity;
      isFullScreen = true;
    });
  }

  exitFullScreen() {
    setState(() {
      videoWidth = oldVideoWidth;
      videoHeight = oldVideoHeight;
      isFullScreen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return isFullScreen
        ? Scaffold(
        backgroundColor: Colors.transparent,
        body: SizedBox(
          width: videoWidth,
          height: videoHeight,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.sp),
            child: Stack(
              children: [
                HVideoPlayer(targetFilePathName: widget.targetFilePathName,initVideoController: videoController),
                HVideoUI(
                    onIntoFullScreen: intoFullScreen,
                    onExitFullScreen: exitFullScreen,
                    videoController: videoController!,
                    title: widget.title),
              ],
            ),
          ),
        ))
        : Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          alignment: Alignment.center,
          width: double.infinity,
          height: double.infinity,
          child: Container(
            alignment: Alignment.center,
            width: double.infinity,
            height: double.infinity,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    width: videoWidth,
                    height: videoHeight,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.sp),
                      child: Stack(
                        children: [
                          HVideoPlayer(targetFilePathName: widget.targetFilePathName,initVideoController: videoController),
                          HVideoUI(
                              onIntoFullScreen: intoFullScreen,
                              onExitFullScreen: exitFullScreen,
                              videoController: videoController!,
                              title: widget.title),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 20.sp),
                  GestureDetector(
                      onTap: widget.closeBotton,
                      child: Image.asset("res/icons/modal_close.png",
                          width: 32.sp, height: 32.sp)),
                ],
              ),
            ),
          ),
        ));
  }
}
