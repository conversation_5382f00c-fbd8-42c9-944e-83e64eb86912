import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/video_player/HVideoProgress.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:orientation/orientation.dart';
import 'package:video_player/video_player.dart';

import 'HFullScreenVideoPlayer.dart';

class HVideoUI extends StatefulWidget {
  final String title;
  final VideoPlayerController videoController;
  final dynamic onIntoFullScreen;
  final dynamic onExitFullScreen;

  const HVideoUI({
    Key? key,
    required this.videoController,
    required this.title,
    this.onIntoFullScreen,
    this.onExitFullScreen,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => CustomVideoUIState();
}

class CustomVideoUIState extends State<HVideoUI> with SingleTickerProviderStateMixin {
  int curPosition = 0;
  int bufferPosition = 0;
  int totalPosition = 0;

  bool isPlaying = false;

  bool isDraging = false;
  double moveDis = 0;
  Duration dragTimeDuration = const Duration(seconds: 0);

  double xnPlaySpeed = 2.0;
  bool isInXnPlaySpeeding = false;

  bool isSettingLight = false;
  bool isSettingVolume = false;

  double showLightValue = 0;
  double showVolumeValue = 0;

  //UI
  late Animation<double> aniPlayerUI;
  late AnimationController aniControllPlayerUI;
  bool isShowVideoPlayerUI = false;
  Timer? showUITimer;

  @override
  initState() {
    super.initState();
    widget.videoController.addListener(updatePlayerInfo);
    updatePlayerInfo();
    callMcByFunName("getBrightness").then((value) {
      showLightValue = value;
    });
    callMcByFunName("getVolume").then((value) {
      showVolumeValue = value;
    });
    //UI
    aniControllPlayerUI = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);
    aniPlayerUI = CurvedAnimation(parent: aniControllPlayerUI, curve: Curves.bounceIn);
    aniPlayerUI = Tween<double>(begin: 0, end: 1).animate(aniControllPlayerUI)
      ..addListener(() {
        setState(() {});
      });
    toggleVideoPlayerUI();
  }

  toggleVideoPlayerUI() {
    if (isShowVideoPlayerUI) {
      aniControllPlayerUI.reverse();
    } else {
      aniControllPlayerUI.forward();
      //
      if (showUITimer != null) {
        showUITimer!.cancel();
        showUITimer = null;
      }
      showUITimer = Timer(const Duration(seconds: 3), () {
        if (isShowVideoPlayerUI) {
          toggleVideoPlayerUI();
        }
      });
    }
    isShowVideoPlayerUI = !isShowVideoPlayerUI;
  }

  showSelectFitType() {}

  updatePlayerInfo() {
    var cur = widget.videoController.value.position.inSeconds;
    var buffer = 0;
    for (DurationRange range in widget.videoController.value.buffered) {
      if ((range.end.inSeconds) >= buffer) {
        buffer = range.end.inSeconds;
      }
    }
    var total = widget.videoController.value.duration.inSeconds;

    setState(() {
      if (!isDraging) {
        curPosition = cur;
      }
      bufferPosition = buffer;
      totalPosition = total;
      isPlaying = widget.videoController.value.isPlaying;
    });
  }

  @override
  dispose() {
    aniControllPlayerUI.dispose();
    widget.videoController.removeListener(updatePlayerInfo);
    if (showUITimer != null) {
      showUITimer!.cancel();
      showUITimer = null;
    }
    super.dispose();
  }

  togglePlayOrPause() {
    if (widget.videoController.value.isPlaying) {
      widget.videoController.pause();
    } else {
      widget.videoController.play();
    }
  }

  String _getTimeString(Duration time) {
    final minutes = time.inMinutes.remainder(Duration.minutesPerHour).toString();
    final seconds = time.inSeconds.remainder(Duration.secondsPerMinute).toString().padLeft(2, '0');
    return time.inHours > 0 ? "${time.inHours}:${minutes.padLeft(2, "0")}:$seconds" : "$minutes:$seconds";
  }

  intoFullScreen() {
    OrientationPlugin.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    OrientationPlugin.forceOrientation(DeviceOrientation.landscapeRight);

    if (widget.onIntoFullScreen != null) {
      widget.onIntoFullScreen();
    } else {
      Global.globalNavigator.currentState!.push(MaterialPageRoute(builder: (context) {
        return HFullScreenVideoPlayer(videoController: widget.videoController);
      }));
    }
  }

  exitFullScreen() {
    // OrientationPlugin.setPreferredOrientations([DeviceOrientation.portraitUp]);
    // OrientationPlugin.forceOrientation(DeviceOrientation.portraitUp);

    if (widget.onExitFullScreen != null) {
      widget.onExitFullScreen();
    } else {
      Navigator.pop(context);
    }
  }

  getOffsetValue(DragUpdateDetails details, double curValue, double maxValue, {bool vertical = false}) {
    double tempMoveDis = moveDis + (vertical ? details.delta.dy : details.delta.dx);
    if (vertical) {
      tempMoveDis = -tempMoveDis;
    }
    double ratio = tempMoveDis / (vertical ? context.size!.height : context.size!.width);
    double offsetValue = curValue + (ratio * maxValue);

    if (offsetValue < 0 || offsetValue > maxValue) {
      if (offsetValue < 0) {
        offsetValue = 0;
      }
      if (offsetValue > maxValue) {
        offsetValue = maxValue;
      }
    } else {
      moveDis = tempMoveDis;
    }
    return offsetValue;
  }

  @override
  Widget build(BuildContext context) {
    double maskW = double.infinity;
    bool isFullScreen = 1.sw > 1.sh;
    double maskH = isFullScreen ? 150.sp : 50.sp;
    double maxBarWidth = isFullScreen ? 100.sp : 60.sp;
    double showBarValueRaido = isSettingVolume ? showVolumeValue : showLightValue;
    double showBarWidth = maxBarWidth * showBarValueRaido;
    IconData vlShowIcon = isSettingVolume
        ? (showBarValueRaido > 0.6
            ? Icons.volume_up
            : showBarValueRaido > 0.3
                ? Icons.volume_down
                : Icons.volume_mute)
        : (showBarValueRaido > 0.6
            ? Icons.brightness_high
            : showBarValueRaido > 0.3
                ? Icons.brightness_medium
                : Icons.brightness_low);
    callMcByFunName("getBrightness").then((value) {
      showLightValue = value;
    });
    callMcByFunName("getVolume").then((value) {
      showVolumeValue = value;
    });

    return Stack(children: [
      isDraging || isInXnPlaySpeeding || isSettingLight || isSettingVolume
          ? Align(
              alignment: Alignment.center,
              child: Container(
                margin: EdgeInsets.only(bottom: isFullScreen ? 100.sp : 30.sp),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: Padding(
                    padding:
                        EdgeInsets.fromLTRB(isFullScreen ? 26.sp : 16.sp, 8.sp, isFullScreen ? 26.sp : 16.sp, 8.sp),
                    child: (isDraging || isInXnPlaySpeeding)
                        ? Text(
                            isDraging
                                ? ("${_getTimeString(dragTimeDuration)} / ${_getTimeString(widget.videoController.value.duration)}")
                                : ("$xnPlaySpeed ${Lang.speed_forward}"),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                              fontSize: isFullScreen ? 20.sp : 14.sp,
                            ))
                        : (Row(mainAxisSize: MainAxisSize.min, children: [
                            Icon(vlShowIcon, size: isFullScreen ? 30.sp : 18.sp, color: Colors.white),
                            SizedBox(width: 10.sp),
                            Container(
                              width: maxBarWidth,
                              height: 3.sp,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(4.sp),
                              ),
                              child: Stack(alignment: Alignment.centerLeft, children: [
                                Container(
                                  width: showBarWidth,
                                  height: 3.sp,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4.sp),
                                  ),
                                )
                              ]),
                            )
                          ]))),
              ),
            )
          : const SizedBox(),
      Opacity(
          opacity: aniPlayerUI.value,
          child: Stack(
            children: [
              Container(
                color: Colors.black.withOpacity(0.18),
              ),
              // ShaderMask(
              //   shaderCallback: (rect) {
              //     return LinearGradient(
              //       begin: Alignment.topCenter,
              //       end: Alignment.bottomCenter,
              //       // 这里设置透明度变化，防止出现黑边问题
              //       colors: [Colors.black.withOpacity(0.6), Colors.transparent],
              //     ).createShader(Rect.fromLTRB(0, 0, rect.width, rect.height));
              //   },
              //   blendMode: BlendMode.dstIn,
              //   child: Container(
              //     width: maskW,
              //     height: maskH,
              //     decoration: BoxDecoration(
              //       //设置0 为了防止出现黑边问题
              //       color: Colors.black.withOpacity(0),
              //     ),
              //   ),
              // ),
              // Align(
              //   alignment: Alignment.bottomCenter,
              //   child: ShaderMask(
              //     shaderCallback: (rect) {
              //       return LinearGradient(
              //         begin: Alignment.bottomCenter,
              //         end: Alignment.topCenter,
              //         // 这里设置透明度变化，防止出现黑边问题
              //         colors: [Colors.black.withOpacity(0.6), Colors.transparent],
              //       ).createShader(Rect.fromLTRB(0, 0, rect.width, rect.height));
              //     },
              //     blendMode: BlendMode.dstIn,
              //     child: Container(
              //       width: maskW,
              //       height: maskH,
              //       decoration: BoxDecoration(
              //         //设置0 为了防止出现黑边问题
              //         color: Colors.black.withOpacity(0),
              //       ),
              //     ),
              //   ),
              // ),
              Padding(
                padding: EdgeInsets.fromLTRB(isFullScreen ? 50.sp : 10.sp, isFullScreen ? 30.sp : 10.sp,
                    isFullScreen ? 50.sp : 10.sp, isFullScreen ? 30.sp : 10.sp),
                child: Stack(
                  children: [
                    GestureDetector(
                        onLongPressStart: (_) {
                          setState(() {
                            isInXnPlaySpeeding = true;
                          });
                          widget.videoController.setPlaybackSpeed(xnPlaySpeed);
                        },
                        onLongPressEnd: (_) {
                          setState(() {
                            isInXnPlaySpeeding = false;
                          });
                          widget.videoController.setPlaybackSpeed(1.0);
                        },
                        onTap: () {
                          toggleVideoPlayerUI();
                        },
                        onDoubleTap: togglePlayOrPause,
                        // onVerticalDragStart: (DragStartDetails details) {
                        //   if (details.localPosition.dx <=
                        //       context.size!.width / 2) {
                        //     setState(() {
                        //       isSettingLight = true;
                        //       moveDis = 0;
                        //     });
                        //   } else {
                        //     setState(() {
                        //       isSettingVolume = true;
                        //       moveDis = 0;
                        //     });
                        //   }
                        // },
                        // onVerticalDragEnd: (_) {
                        //   setState(() {
                        //     isSettingVolume = false;
                        //     isSettingLight = false;
                        //   });
                        // },
                        // onVerticalDragUpdate: (DragUpdateDetails details) {
                        //   if (isSettingVolume) {
                        //     callMcByFunName("getVolume").then((value) {
                        //       double offsetValue = getOffsetValue(
                        //           details, value, 1.0,
                        //           vertical: true);
                        //
                        //       callMcByFunName("setVolume",
                        //           params: {"volume": offsetValue}).then((_) {
                        //         setState(() {});
                        //       });
                        //     });
                        //   } else {
                        //     callMcByFunName("getBrightness").then((value) {
                        //       double offsetValue = getOffsetValue(
                        //           details, value, 1.0,
                        //           vertical: true);
                        //
                        //       callMcByFunName("setBrightness",
                        //           params: {"brightness": offsetValue}).then((_) {
                        //         setState(() {});
                        //       });
                        //     });
                        //   }
                        // },
                        onHorizontalDragStart: (_) {
                          setState(() {
                            isDraging = true;
                            moveDis = 0;
                          });
                        },
                        onHorizontalDragEnd: (_) {
                          widget.videoController.seekTo(dragTimeDuration);
                          setState(() {
                            isDraging = false;
                            moveDis = 0;
                          });
                        },
                        onHorizontalDragUpdate: (DragUpdateDetails details) {
                          int offsetValue = getOffsetValue(
                                  details,
                                  widget.videoController.value.position.inSeconds.toDouble(),
                                  widget.videoController.value.duration.inSeconds.toDouble())
                              .toInt();
                          setState(() {
                            dragTimeDuration = Duration(seconds: offsetValue);
                            curPosition = offsetValue;
                          });
                        },
                        child: Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: const BoxDecoration(color: Colors.transparent),
                        )),
                    Align(
                        alignment: Alignment.topLeft,
                        child: Row(
                          children: [
                            !isFullScreen
                                ? const SizedBox()
                                : GestureDetector(
                                    onTap: exitFullScreen,
                                    child: Icon(Icons.arrow_back_ios, size: 26.sp, color: Colors.white),
                                  ),
                            Text(widget.title,
                                style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: isFullScreen ? 20.sp : 14.sp)),
                            // const Spacer(),
                            // GestureDetector(
                            //     onTap: showSelectFitType,
                            //     child: Icon(Icons.fit_screen,
                            //         size: isFullScreen ? 30.sp : 18.sp,
                            //         color: Colors.white)),
                          ],
                        )),
                    Align(
                        alignment: Alignment.bottomCenter,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Row(
                              children: [
                                isFullScreen
                                    ? const SizedBox()
                                    : GestureDetector(
                                        onTap: togglePlayOrPause,
                                        child: Icon(
                                            widget.videoController.value.isPlaying ? Icons.pause : Icons.play_arrow,
                                            size: 20.sp,
                                            color: Colors.white),
                                      ),
                                isFullScreen ? const SizedBox() : SizedBox(width: 10.sp),
                                Expanded(
                                    child: CustomVideoProgress(
                                  timeLabelLocation: TimeLabelLocation.sides,
                                  progress: Duration(seconds: curPosition),
                                  buffered: Duration(seconds: bufferPosition),
                                  total: Duration(seconds: totalPosition),
                                  progressBarColor: Colors.white,
                                  baseBarColor: Colors.white.withOpacity(0.24),
                                  bufferedBarColor: Colors.white.withOpacity(0.38),
                                  thumbColor: Colors.white,
                                  barHeight: isFullScreen ? 4.sp : 3.sp,
                                  thumbRadius: isFullScreen ? 8.sp : 6.sp,
                                  thumbGlowColor: Colors.white.withOpacity(0.5),
                                  thumbGlowRadius: 12.sp,
                                  timeLabelTextStyle: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                      fontSize: isFullScreen ? 18.sp : 13.sp),
                                  onDragStart: (ThumbDragDetails details) {
                                    setState(() {
                                      isDraging = true;
                                    });
                                  },
                                  onDragUpdate: (ThumbDragDetails details) {
                                    setState(() {
                                      dragTimeDuration = details.timeStamp;
                                    });
                                  },
                                  onDragEnd: () {
                                    setState(() {
                                      isDraging = false;
                                    });
                                  },
                                  onSeek: (duration) {
                                    widget.videoController.seekTo(duration);
                                  },
                                )),
                                isFullScreen ? const SizedBox() : SizedBox(width: 10.sp),
                                isFullScreen
                                    ? const SizedBox()
                                    : GestureDetector(
                                        onTap: () {
                                          if (isFullScreen) {
                                            exitFullScreen();
                                          } else {
                                            intoFullScreen();
                                          }
                                        },
                                        child: Icon(isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                                            size: 20.sp, color: Colors.white),
                                      ),
                              ],
                            ),
                            !isFullScreen ? const SizedBox() : SizedBox(height: 10.sp),
                            !isFullScreen
                                ? const SizedBox()
                                : Row(
                                    children: [
                                      GestureDetector(
                                        onTap: togglePlayOrPause,
                                        child: Icon(
                                            widget.videoController.value.isPlaying ? Icons.pause : Icons.play_arrow,
                                            size: 36.sp,
                                            color: Colors.white),
                                      ),
                                      const Spacer(),
                                      GestureDetector(
                                        onTap: () {
                                          if (isFullScreen) {
                                            exitFullScreen();
                                          } else {
                                            intoFullScreen();
                                          }
                                        },
                                        child: Icon(isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                                            size: 36.sp, color: Colors.white),
                                      ),
                                    ],
                                  )
                          ],
                        )),
                  ],
                ),
              ),
              // HVideoSelectFitType(width:100.sp),
            ],
          ))
    ]);
  }
}
