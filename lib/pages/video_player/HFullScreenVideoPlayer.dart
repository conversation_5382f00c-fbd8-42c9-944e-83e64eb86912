import 'package:flutter/material.dart';
import 'package:mooeli/pages/video_player/HVideoPlayer.dart';
import 'package:video_player/video_player.dart';

class HFullScreenVideoPlayer extends StatefulWidget {
  const HFullScreenVideoPlayer({Key? key, required this.videoController})
      : super(key: key);

  final VideoPlayerController videoController;

  @override
  State<StatefulWidget> createState() {
    return _HFullScreenVideoPlayerState();
  }
}

class _HFullScreenVideoPlayerState extends State<HFullScreenVideoPlayer> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            decoration: const BoxDecoration(
              color: Colors.black,
            ),
            alignment: Alignment.center,
            child: HVideoPlayer(initVideoController: widget.videoController)));
  }
}
