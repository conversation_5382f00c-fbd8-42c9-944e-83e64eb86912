import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail100_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail101_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail102_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail109_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail110_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail_mouth_page.dart';
import 'package:mooeli/pages/lingya/lingya_doc_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:sprintf/sprintf.dart';

class LingyaRecordPage extends BasePage {
  LingyaItem lingya;
  List<LingyaPhase> phaseList;

  List<LingyaRecord> recordList;

  LingyaRecordPage(this.lingya, this.phaseList, this.recordList, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaRecordPageState();
  }
}

class LingyaRecordPageState extends BasePageState<LingyaRecordPage> {
  LingyaRecord selectedRecord = LingyaRecord();

  bool loaded = false;

  List<LingyaFile> fileList = [];
  LingyaFile? fileCBCT;

  @override
  initState() {
    super.initState();
    if (isNotEmpty(widget.recordList)) {
      selectedRecord = widget.recordList.first;
      getFileList();
    } else {
      loaded = true;
    }
    getPhaseList();
    imageCache.clear(); // 清除缓存
    imageCache.clearLiveImages(); // 清理实时图像缓存
  }

  getPhaseList() {
    HHttp.request(
      "/v3/doctor/phase/listByCaseId",
      "POST",
      (data) {
        setState(() {
          widget.phaseList = (data as List).map((i) => LingyaPhase.fromJson(i)).toList();
          widget.phaseList.sort((a, b) => a.modifyTime - b.modifyTime);
        });
        getRecordList();
      },
      errCallBack: (err) {
        getRecordList();
      },
      params: {
        "caseId": widget.lingya.caseId,
      },
    );
  }

  getRecordList() async {
    HHttp.request(
      "/v3/doctor/record/listByCaseId",
      "POST",
      (data) {
        setState(() {
          widget.recordList = (data as List).map((i) => LingyaRecord.fromJson(i)).toList();
          widget.recordList.sort((a, b) => b.createTime - a.createTime);
        });
      },
      params: {
        "caseId": widget.lingya.caseId,
      },
    );
  }

  getFileList() {
    HHttp.request(
      "/v3/doctor/record/listFile",
      "POST",
      (data) {
        setState(() {
          loaded = true;
          fileList = (data as List).map((i) => LingyaFile.fromJson(i)).where((file) {
            if (file.category == "300") {
              setState(() {
                fileCBCT = file;
              });
            }
            return categoryMap.containsKey(file.category);
          }).toList();
        });
        if (isNotEmpty(fileList)) {
          List<String> ids = [];
          for (var file in fileList) {
            if (isNotEmpty(file.fileId)) {
              ids.add(file.fileId);
            }
          }
          downloadAnalytics(
            ids,
            (id, path) {
              for (var file in fileList) {
                if (file.fileId == id) {
                  setState(() {
                    file.imagePath = path;
                  });
                }
              }
            },
            "RECORD",
          );
        }
      },
      params: {
        "recordId": selectedRecord.recordId,
      },
    );
  }

  _checkAnalytics(BasePage page, LingyaFile file) {
    //TODO chenxb 会员
    // getAnalyticsInfo(
    //   file.getAnalyticsId(),
    //   (data) {
    push(page);
    //   },
    //   onError: (err) {
    //     if (err["code"] == "1_2_3x7_2" || err["code"] == "3_2_6x7_3") {
    //       Global.showCustomDialog(
    //         SaveQrcodeView(),
    //         isBringXBtn: false,
    //       );
    //     } else {
    //       HHttp.toastError(err);
    //     }
    //   },
    //   onlyShowStatus: true,
    // );
  }

  _onClickFile(LingyaFile file) {
    logger("click LingyaFile: ${file.toJson()}");
    switch (file.category) {
      case "100": //正面像
        _checkAnalytics(LingyaDetail100Page(file), file);
        break;
      case "101": //侧面像
        _checkAnalytics(LingyaDetail101Page(file), file);
        break;
      case "102": //正面微笑像
        _checkAnalytics(LingyaDetail102Page(file), file);
        break;
      case "103": //正面咬合像
      case "105": //左侧咬合像
      case "106": //下牙弓像
      case "107": //右侧咬合像
      case "108": //上牙弓像
        List<String> types = ["103", "105", "106", "107", "108"];
        List<LingyaFile> files = fileList.where((file) => types.contains(file.category)).toList();
        int index = files.indexOf(file);
        _checkAnalytics(LingyaDetailMouthPage(files, index), file);
        break;
      case "109": //全颌曲面断层片
        _checkAnalytics(LingyaDetail109Page(file), file);
        break;
      case "110": //头颅侧位定位片
        _checkAnalytics(LingyaDetail110Page(file), file);
        break;
      case "200": //上颌模型
      case "201": //下颌模型
        // HHttp.request(
        //   "/v2/access/build-subaccess",
        //   "POST",
        //   (data) {
        //     Global.openUrlByWebView(
        //       context,
        //       "3D分析-${selectedRecord.phaseName}-${Global.getDateByTimestamp(milliseconds: int.parse(file.createTime))}",
        //       "${HHttp.getH5Host()}dental-3dstudio/#/?Access=${data["access"]}&language=zh",
        //       horizontal: true,
        //       hideAppbar: true,
        //     );
        //   },
        //   params: {"caseId": file.caseId, "recordId": file.recordId, "data": {}},
        // );
        break;
      case "300":
        // List<String> modes = ["MPR", "CPR", "TMJ"];
        // showBottomListDialog(context, modes, (index) {
        //   Map param = {
        //     "caseId": file.caseId,
        //     "recordId": file.recordId,
        //     "phaseId": file.phaseId,
        //     "fileId": file.fileId,
        //     "analysisInfoMap": file.analysisInfoMap,
        //     "sessionId": currentUser.sessionId,
        //   };
        //
        //   String info = EncryptUtils.aesEncrypt(jsonEncode(param));
        //   Global.openUrlByWebView(
        //     context,
        //     "CBCT",
        //     "${HHttp.getH5Host()}dental-cbct/?title=CBCT分析-${widget.lingya.name}-${Global.getDateByTimestamp(milliseconds: int.parse(file.createTime))}&info=$info&mode=${modes[index]}&language=zh",
        //     horizontal: true,
        //     hideAppbar: true,
        //   );
        // });

        break;
      default:
        viewLingyaImage(file);
        break;
    }
  }

  Widget _buildFileItem(String category) {
    Iterable<LingyaFile> files = fileList.where((element) => element.category == category);
    LingyaFile? file;
    if (isNotEmpty(files)) {
      file = files.first;
    }

    return GestureDetector(
      onTap: () {
        if (file != null) {
          _onClickFile(file!);
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 8.sp),
          SizedBox(
            width: 94.66.sp,
            height: 66.sp,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  "res/imgs/photo_border.png",
                  width: 94.66.sp,
                  height: 66.sp,
                  fit: BoxFit.contain,
                ),
                Image.asset(
                  "res/imgs/default_$category.png",
                  width: 64.sp,
                  height: 64.sp,
                  fit: BoxFit.contain,
                ),
                file != null
                    ? Hero(
                        tag: file.fileId,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.sp),
                          child: getLingyaFileImage(file, 94.sp, 65.sp),
                        ),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
          SizedBox(height: 6.sp),
          MyText(categoryMap[category]!, color2B, 12.sp),
        ],
      ),
    );
  }

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        leading: Builder(
          builder: (BuildContext context) {
            return IconButton(
              icon: Platform.isAndroid ? const Icon(Icons.arrow_back) : const Icon(Icons.arrow_back_ios_new),
              onPressed: pop,
            );
          },
        ),
        title: Text(Lang.contract_info),
      ),
      body: _getBodyView(),
      drawer: _getDrawerView(),
    );
  }

  _getBodyView() {
    LingyaPhase phase = widget.phaseList.where((phase) => phase.phaseId == selectedRecord.phaseId).first;
    // if (isEmpty(fileList) && fileCBCT == null) {
    //   return Center(
    //     child: MyText(loaded ? Lang.no_record : Lang.loading, color7C, 14.sp),
    //   );
    // }
    return SingleChildScrollView(
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.all(20.sp),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  child: Image.asset(
                    "res/imgs/icon_menu.png",
                    width: 24.sp,
                  ),
                  onTap: () {
                    _scaffoldKey.currentState?.openDrawer();
                  },
                ),
                SizedBox(width: 10.sp),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 1.sw - 200.sp),
                      child: MyText(
                          "${selectedRecord.phaseName} - ${selectedRecord.name}", color2B, 16.sp, FontWeight.w500),
                    ),
                    MyText(Global.getDateByTimestamp(milliseconds: selectedRecord.createTime, type: 1), colorAB, 12.sp),
                  ],
                ),
                Flexible(child: Row()),
                GestureDetector(
                  onTap: () {
                    if (phase.hasDocument()) {
                      push(LingyaDocPage(phase, widget.phaseList));
                    } else {
                      toast(Lang.case_no_doc);
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.fromLTRB(15.sp, 10.sp, 15.sp, 10.sp),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(60.sp),
                    ),
                    alignment: Alignment.center,
                    child: Row(
                      children: [
                        Image.asset(
                          phase.hasDocument() ? "res/imgs/icon_doc.png" : "res/imgs/icon_doc_empty.png",
                          width: 20.sp,
                        ),
                        SizedBox(width: 4.sp),
                        MyText(Lang.case_doc, phase.hasDocument() ? color2B : colorB8, 14.sp, FontWeight.w500),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 10.sp),
            Container(
              width: 1.sw - 40.sp,
              padding: EdgeInsets.fromLTRB(20.sp, 15.sp, 20.sp, 20.sp),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.sp),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MyText(Lang.remark, color2B, 16.sp, FontWeight.w500),
                  SizedBox(height: 8.sp),
                  MyText(selectedRecord != null ? selectedRecord.note : "", color2B, 12.sp),
                ],
              ),
            ),
            SizedBox(height: 16.sp),
            _getPhotoContainer(Lang.face_mouth_photos, ["101", "100", "102", "108", "104", "106", "107", "103", "105"]),
            SizedBox(height: 16.sp),
            _getPhotoContainer(Lang.x_photos, ["109", "110"]),
            SizedBox(height: 16.sp),
            _getPhotoContainer(Lang.teeth_photos, ["200", "201"]),
            SizedBox(height: 16.sp),
            fileCBCT != null ? _getPhotoContainer(Lang.cbct_photos, ["300"]) : const SizedBox(),
          ],
        ),
      ),
    );
  }

  _getPhotoContainer(String title, List<String> categories) {
    return Container(
        width: 1.sw - 40.sp,
        padding: EdgeInsets.fromLTRB(20.sp, 15.sp, 20.sp, 16.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyText(title, color2B, 16.sp, FontWeight.w500),
            SizedBox(
              height: 102.sp * (categories.length / 3.0).ceil(),
              child: GridView(
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 1,
                ),
                children: categories.map((e) => _buildFileItem(e)).toList(),
              ),
            ),
          ],
        ));
  }

  _getDrawerView() {
    return Container(
      width: 313.sp,
      height: 1.sh,
      color: colorFB,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(20.sp, 68.sp, 20.sp, 4.sp),
            child: MyText(Lang.select_contract, color2B, 20.sp, FontWeight.w500),
          ),
          Padding(
            padding: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
            child: MyText(sprintf(Lang.total_contract_count, [widget.recordList.length]), colorAB, 12.sp),
          ),
          Container(width: 313.sp, height: 1.sp, color: colorFB),
          SizedBox(
            height: 1.sh - 144.sp,
            child: SingleChildScrollView(
              child: Column(
                children: widget.phaseList.map((phase) => _getPhaseMenuView(phase)).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> collapsePhaseIds = [];

  Widget _getPhaseMenuView(LingyaPhase phase) {
    List<LingyaRecord> records = widget.recordList.where((record) => record.phaseId == phase.phaseId).toList();
    return Padding(
      padding: EdgeInsets.only(left: 20.sp),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16.sp),
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (collapsePhaseIds.contains(phase.phaseId)) {
                      collapsePhaseIds.remove(phase.phaseId);
                    } else {
                      collapsePhaseIds.add(phase.phaseId);
                    }
                  });
                },
                child: Image.asset(
                  collapsePhaseIds.contains(phase.phaseId) ? "res/imgs/icon_collapse.png" : "res/imgs/icon_expand.png",
                  width: 16.sp,
                  height: 16.sp,
                ),
              ),
              ..._getTreeLine(phase, records),
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (collapsePhaseIds.contains(phase.phaseId)) {
                      collapsePhaseIds.remove(phase.phaseId);
                    } else {
                      collapsePhaseIds.add(phase.phaseId);
                    }
                  });
                },
                child: Container(
                  width: 253.sp,
                  height: 40.sp,
                  padding: EdgeInsets.fromLTRB(8.sp, 0.sp, 8.sp, 0.sp),
                  margin: EdgeInsets.fromLTRB(0.sp, 4.sp, 0.sp, 4.sp),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: colorPink1A,
                    borderRadius: BorderRadius.circular(4.sp),
                  ),
                  child: SingleText(phase.name, color2B, 14.sp, FontWeight.w500),
                ),
              ),
              collapsePhaseIds.contains(phase.phaseId) || isEmpty(records)
                  ? SizedBox(height: 16.sp)
                  : Column(
                      children: records
                          .map((record) => GestureDetector(
                                onTap: () {
                                  setState(() {
                                    selectedRecord = record;
                                    loaded = false;
                                    fileList = [];
                                    fileCBCT = null;
                                    _scaffoldKey.currentState?.closeDrawer();
                                    getFileList();
                                  });
                                },
                                child: Container(
                                    width: 253.sp,
                                    height: 40.sp,
                                    padding: EdgeInsets.fromLTRB(8.sp, 0.sp, 8.sp, 0.sp),
                                    margin: EdgeInsets.fromLTRB(0.sp, 4.sp, 0.sp, 4.sp),
                                    alignment: Alignment.centerLeft,
                                    decoration: BoxDecoration(
                                      color: selectedRecord != null && record.recordId == selectedRecord.recordId
                                          ? colorBlue1A
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(4.sp),
                                    ),
                                    child: Row(
                                      children: [
                                        Image.asset("res/imgs/icon_contract.png", width: 24.sp),
                                        SizedBox(width: 8.sp),
                                        SingleText(record.name, color2B, 14.sp, FontWeight.w500),
                                      ],
                                    )),
                              ))
                          .toList(),
                    ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _getTreeLine(LingyaPhase phase, List<LingyaRecord> records) {
    List<Widget> list = [];
    if (collapsePhaseIds.contains(phase.phaseId) || isEmpty(records)) {
      list.add(const SizedBox());
    } else {
      list.add(Image.asset("res/imgs/icon_line0.png", width: 19.sp, fit: BoxFit.fitWidth));
      for (int i = 0; i < records.length - 1; i++) {
        list.add(Image.asset("res/imgs/icon_line1.png", width: 19.sp, fit: BoxFit.fitWidth));
      }
      list.add(Image.asset("res/imgs/icon_line2.png", width: 19.sp, fit: BoxFit.fitWidth));
    }
    return list;
  }
}
