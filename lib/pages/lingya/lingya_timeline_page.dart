// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:mooeli/common/global.dart';
// import 'package:mooeli/common/http.dart';
// import 'package:mooeli/common/http_manager.dart';
// import 'package:mooeli/manager/languaga_manager.dart';
// import 'package:mooeli/model/lingya_data.dart';
// import 'package:mooeli/pages/base_page.dart';
// import 'package:mooeli/pages/mooeli/mooeli_add_monitor_page.dart';
// import 'package:mooeli/pages/mooeli/mooeli_contract_page.dart';
// import 'package:mooeli/utils/colors_utils.dart';
// import 'package:mooeli/utils/common_utils.dart';
// import 'package:mooeli/utils/event_utils.dart';
// import 'package:mooeli/widget/custom_button.dart';
// import 'package:mooeli/widget/my_widget.dart';
// import 'package:mooeli/widget/section_view.dart';
// import 'package:sprintf/sprintf.dart';
//
// @Deprecated("直接跳转到RecordPage")
// class LingyaTimelinePage extends BasePage {
//   LingyaItem lingya;
//
//   LingyaTimelinePage(this.lingya, {Key? key}) : super(key: key);
//
//   @override
//   State<StatefulWidget> createState() {
//     return LingyaTimelinePageState();
//   }
// }
//
// class LingyaTimelinePageState extends BasePageState<LingyaTimelinePage> {
//   final double headerHeight = 32.sp, itemHeight = 48.sp;
//
//   bool loaded = false;
//
//   List<LingyaPhase> phaseList = [];
//   List<LingyaRecord> recordList = [];
//
//   bool? hasMonitor;
//
//   @override
//   initState() {
//     super.initState();
//     addEventListener<EventAddMonitor>((event) {
//       if (event.caseId == widget.lingya.caseId) {
//         setState(() {
//           hasMonitor = true;
//         });
//         eventBus.fire(EventRefreshSection());
//       }
//     });
//     getPhaseList();
//     checkHasMonitor();
//   }
//
//   checkHasMonitor() {
//     HHttp.request(
//       "/v3/monitor/case/haveMonitor",
//       "POST",
//       (data) {
//         setState(() {
//           hasMonitor = data;
//         });
//         eventBus.fire(EventRefreshSection());
//       },
//       params: {
//         "id": widget.lingya.caseId,
//       },
//     );
//   }
//
//   getPhaseList() {
//     HHttp.request(
//       "/v3/doctor/phase/listByCaseId",
//       "POST",
//       (data) {
//         setState(() {
//           phaseList = (data['phases'] as List).map((i) => LingyaPhase.fromJson(i)).toList();
//           phaseList.sort((a, b) => a.modifyTime - b.modifyTime);
//         });
//         getRecordList();
//       },
//       errCallBack: (err) {
//         getRecordList();
//       },
//       params: {
//         "caseId": widget.lingya.caseId,
//       },
//     );
//   }
//
//   getRecordList() async {
//     HHttp.request(
//       "/v3/doctor/record/listByCaseId",
//       "POST",
//       (data) {
//         setState(() {
//           loaded = true;
//           recordList = (data as List).map((i) => LingyaRecord.fromJson(i)).toList();
//           recordList.sort((a, b) => b.createTime - a.createTime);
//         });
//       },
//       params: {
//         "caseId": widget.lingya.caseId,
//       },
//     );
//   }
//
//   _buildRecordItem(LingyaRecord record) {
//     return GestureDetector(
//       onTap: () {
//         // push(LingyaRecordPage(record));
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           color: Colors.grey[50],
//           border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
//         ),
//         alignment: Alignment.centerLeft,
//         padding: EdgeInsets.only(left: 12.sp),
//         height: itemHeight,
//         child: MyText(record.name, Colors.black, 14.sp),
//       ),
//     );
//   }
//
//   _buildInfo() {
//     logger("_buildInfo $hasMonitor");
//     return Container(
//       padding: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 16.sp),
//       width: double.infinity,
//       decoration: BoxDecoration(
//         color: Colors.grey[50],
//         borderRadius: BorderRadius.circular(8.sp),
//       ),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           ClipRRect(
//               borderRadius: BorderRadius.circular(30.sp),
//               child: isEmpty(widget.lingya.avatarPath)
//                   ? Image.asset(
//                       widget.lingya.gender == "female" ? "res/imgs/avatar_female.png" : "res/imgs/avatar_male.png",
//                       width: 47.sp,
//                       height: 47.sp,
//                       fit: BoxFit.cover,
//                     )
//                   : Image.file(
//                       File(widget.lingya.avatarPath),
//                       width: 47.sp,
//                       height: 47.sp,
//                       fit: BoxFit.cover,
//                     )),
//           SizedBox(width: 20.sp),
//           Expanded(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 MyText(widget.lingya.name, color2B, 16.sp, FontWeight.w500),
//                 SizedBox(height: 8.sp),
//                 MyText("${sprintf(Lang.year_old, [Global.getAge(widget.lingya.birthday)])}  ${widget.lingya.getGender()}",
//                     color7C, 12.sp),
//                 SizedBox(height: 4.sp),
//                 MyText("${Lang.create_time}：${Global.getDateByTimestamp(milliseconds: widget.lingya.createTime)}",
//                     color7C, 12.sp),
//                 SizedBox(height: 8.sp),
//                 hasMonitor != null
//                     ? hasMonitor!
//                         ? HCustomButton(
//                             key: UniqueKey(),
//                             text: Lang.view_mooeli_monitor,
//                             width: 0.4.sw,
//                             height: 24.sp,
//                             fontSize: 12.sp,
//                             onPress: () {
//                               push(MooeliContractPage(widget.lingya.toMooeliItem()));
//                             },
//                             bgColor: Colors.deepPurpleAccent,
//                           )
//                         : HCustomButton(
//                             key: UniqueKey(),
//                             width: 0.4.sw,
//                             height: 24.sp,
//                             fontSize: 12.sp,
//                             onPress: () {
//                               push(MooeliAddMonitorPage(widget.lingya.toMooeliItem()));
//                             },
//                             bgColor: Colors.deepPurpleAccent,
//                             child: Row(
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 Image.asset("res/icons/icon_add.png", width: 20.sp),
//                                 SizedBox(width: 4.sp),
//                                 MyText(Lang.add_mooeli_monitor, Colors.white, 16.sp, FontWeight.w500),
//                               ],
//                             ),
//                           )
//                     : const SizedBox(),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   _buildSections() {
//     if (isEmpty(recordList)) {
//       return Center(
//         child: MyText(loaded ? Lang.no_record : Lang.loading, color2B, 14.sp),
//       );
//     }
//     return SectionTableView(
//       numberOfSections: phaseList.length + 1,
//       viewForHeaderInSection: (context, section) {
//         if (section == 0) {
//           return const SizedBox();
//         }
//         return Container(
//           width: 1.sw,
//           height: headerHeight,
//           color: Colors.deepPurpleAccent[100],
//           alignment: Alignment.centerLeft,
//           padding: EdgeInsets.only(left: 12.sp),
//           child: Text(phaseList[section - 1].name),
//         );
//       },
//       heightForHeaderInSection: (int section) {
//         if (section == 0) {
//           return 0;
//         }
//         return headerHeight;
//       },
//       numberOfRowsInSection: (int section) {
//         if (section == 0) {
//           return 1;
//         }
//         List<LingyaRecord> records =
//             recordList.where((record) => record.phaseId == phaseList[section - 1].phaseId).toList();
//         return isEmpty(records) ? 1 : records.length;
//       },
//       cellForRowAtIndexPath: (BuildContext context, IndexPath indexPath) {
//         if (indexPath.section == 0) {
//           return _buildInfo();
//         }
//         List<LingyaRecord> records =
//             recordList.where((record) => record.phaseId == phaseList[indexPath.section - 1].phaseId).toList();
//         return isEmpty(records)
//             ? Container(
//                 height: itemHeight,
//                 color: Colors.grey[50],
//                 alignment: Alignment.centerLeft,
//                 padding: EdgeInsets.only(left: 12.sp),
//                 child: MyText(Lang.no_record, Colors.black45, 14.sp),
//               )
//             : _buildRecordItem(records[indexPath.row]);
//       },
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(widget.lingya.name),
//       ),
//       body: _buildSections(),
//     );
//   }
// }
