import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/lingya/lingya_doc_page.dart';
import 'package:mooeli/pages/lingya/lingya_record_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:sprintf/sprintf.dart';

class LingyaInfoPage extends BasePage {
  LingyaItem lingya;

  LingyaInfoPage(this.lingya, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaInfoPageState();
  }
}

class LingyaInfoPageState extends BasePageState<LingyaInfoPage> {
  List<LingyaPhase> phaseList = [];
  List<LingyaRecord> recordList = [];

  bool? hasMonitor;

  @override
  initState() {
    super.initState();
    addEventListener<EventAddMonitor>((event) {
      if (event.caseId == widget.lingya.caseId) {
        setState(() {
          hasMonitor = true;
        });
      }
    });
    getPhaseList();
    checkHasMonitor();
  }

  checkHasMonitor() {
    HHttp.request(
      "/v3/monitor/case/haveMonitor",
      "POST",
      (data) {
        setState(() {
          hasMonitor = data;
        });
        eventBus.fire(EventRefreshSection());
      },
      params: {
        "id": widget.lingya.caseId,
      },
    );
  }

  getPhaseList() {
    HHttp.request(
      "/v3/doctor/phase/listByCaseId",
      "POST",
      (data) {
        setState(() {
          phaseList = (data as List).map((i) => LingyaPhase.fromJson(i)).toList();
          phaseList.sort((a, b) => a.modifyTime - b.modifyTime);
        });
        getRecordList();
      },
      params: {
        "caseId": widget.lingya.caseId,
      },
    );
  }

  getRecordList() async {
    HHttp.request(
      "/v3/doctor/record/listByCaseId",
      "POST",
      (data) {
        setState(() {
          recordList = (data as List).map((i) => LingyaRecord.fromJson(i)).toList();
        });
      },
      params: {
        "caseId": widget.lingya.caseId,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.case_detail),
      ),
      body: _getBodyView(),
    );
  }

  bool expand = false;

  _getUserInfoContainer() {
    if (expand) {
      return Container(
        width: 335.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        padding: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 12.sp),
        child: _getUserInfoView(),
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 10.sp,
            height: 134.sp,
            decoration: BoxDecoration(
              color: widget.lingya.getTagColor(),
              borderRadius: BorderRadius.only(topLeft: Radius.circular(16.sp), bottomLeft: Radius.circular(16.sp)),
            ),
          ),
          Container(
            width: 325.sp,
            height: 134.sp,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(topRight: Radius.circular(16.sp), bottomRight: Radius.circular(16.sp)),
            ),
            padding: EdgeInsets.fromLTRB(10.sp, 12.sp, 20.sp, 0),
            child: _getUserInfoView(),
          ),
        ],
      );
    }
  }

  _getStatusView() {
    String icon;
    String text;
    Color color;
    List<String> tags = ["UNCLASSIFIED", "INITIAL", "PROCESS", "COMPLETED"];
    int index = tags.indexOf(widget.lingya.statusTag);
    switch (index) {
      case 1:
        icon = "res/imgs/icon_first_treat.png";
        text = Lang.first_treat;
        color = colorPink;
        break;
      case 2:
        icon = "res/imgs/icon_treating.png";
        text = Lang.in_treating;
        color = colorBrand;
        break;
      case 3:
        icon = "res/imgs/icon_completed.png";
        text = Lang.complete_treat;
        color = colorGreen;
        break;
      default:
        icon = "res/imgs/icon_unsorted.png";
        text = Lang.not_sorted;
        color = color7C;
        break;
    }
    return Container(
      padding: EdgeInsets.all(4.sp),
      decoration: BoxDecoration(
        color: color2B.withOpacity(0.05),
        borderRadius: BorderRadius.all(Radius.circular(4.sp)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(icon, width: 16.sp),
          SizedBox(width: 4.sp),
          MyText(text, color, 12.sp),
        ],
      ),
    );
  }

  _getShowText(String text) {
    return isEmpty(text) ? Lang.none : text;
  }

  List<Widget> _getUserDetailinfo() {
    if (expand) {
      return [
        SizedBox(height: 24.sp),
        Row(
          children: [
            Image.asset("res/imgs/icon_border_birthday.png", width: 20.sp),
            SizedBox(width: 8.sp),
            MyText(Global.getDateByTimestamp(milliseconds: widget.lingya.birthday), color7C, 14.sp),
          ],
        ),
        SizedBox(height: 8.sp),
        Row(
          children: [
            Image.asset("res/imgs/icon_border_phone.png", width: 20.sp),
            SizedBox(width: 8.sp),
            MyText(_getShowText(widget.lingya.contact), color7C, 14.sp),
          ],
        ),
        SizedBox(height: 8.sp),
        Row(
          children: [
            Image.asset("res/imgs/icon_border_address.png", width: 20.sp),
            SizedBox(width: 8.sp),
            MyText(_getShowText(widget.lingya.caseCode), color7C, 14.sp),
          ],
        ),
        SizedBox(height: 25.sp),
        Row(
          children: [
            Image.asset("res/imgs/icon_border_tag.png", width: 16.sp),
            SizedBox(width: 8.sp),
            MyText(Lang.sort_tags, color7C, 14.sp, FontWeight.w500),
          ],
        ),
        SizedBox(height: 12.sp),
        isEmpty(widget.lingya.tagList)
            ? MyText(Lang.none, color7C, 14.sp)
            : Wrap(
                children: widget.lingya.tagList
                    .map((tag) => Container(
                          margin: EdgeInsets.only(right: 8.sp, bottom: 8.sp),
                          padding: EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.sp),
                            border: Border.all(color: colorE1, width: 1.0.sp),
                          ),
                          child: MyText(tag, color2B, 12.sp),
                        ))
                    .toList(),
              ),
        SizedBox(height: 16.sp),
        Row(
          children: [
            Image.asset("res/imgs/icon_border_comment.png", width: 16.sp),
            SizedBox(width: 8.sp),
            MyText(Lang.other, color7C, 14.sp, FontWeight.w500),
          ],
        ),
        SizedBox(height: 12.sp),
        MyText("${Lang.main_require}:", color2B, 14.sp),
        SizedBox(height: 4.sp),
        HeightText(_getShowText(widget.lingya.chiefComplaint), color7C, 14.sp, 1.5),
        SizedBox(height: 12.sp),
        MyText("${Lang.history_allergy}:", color2B, 14.sp),
        SizedBox(height: 4.sp),
        HeightText(_getShowText(widget.lingya.allergyHistory), color7C, 14.sp, 1.5),
        SizedBox(height: 12.sp),
        MyText("${Lang.history_family}:", color2B, 14.sp),
        SizedBox(height: 4.sp),
        HeightText(_getShowText(widget.lingya.familyHistory), color7C, 14.sp, 1.5),
        SizedBox(height: 12.sp),
        MyText("${Lang.history_treatment}:", color2B, 14.sp),
        SizedBox(height: 4.sp),
        HeightText(_getShowText(widget.lingya.treatmentHistory), color7C, 14.sp, 1.5),
      ];
    } else {
      return [];
    }
  }

  _getUserInfoView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MyText(Lang.user_profile, color7C, 14.sp, FontWeight.w500),
        SizedBox(height: 12.sp),
        Row(
          children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(30.sp),
                child: isEmpty(widget.lingya.avatarPath)
                    ? Image.asset(
                        widget.lingya.gender == "female" ? "res/imgs/avatar_female.png" : "res/imgs/avatar_male.png",
                        width: 48.sp,
                        height: 48.sp,
                        fit: BoxFit.cover,
                      )
                    : Image.file(
                        File(widget.lingya.avatarPath),
                        width: 48.sp,
                        height: 48.sp,
                        fit: BoxFit.cover,
                      )),
            SizedBox(width: 12.sp),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 234.sp,
                  child: Row(
                    children: [
                      Container(
                        constraints: BoxConstraints(maxWidth: 100.sp),
                        child: SingleText(widget.lingya.name, color2B, 16.sp, FontWeight.w500),
                      ),
                      SizedBox(width: 8.sp),
                    ],
                  ),
                ),
                SizedBox(
                  width: 234.sp,
                  child: Row(
                    children: [
                      Container(
                        constraints: BoxConstraints(maxWidth: 100.sp),
                        child: MyText(
                            "${sprintf(Lang.year_old, [
                                  Global.getAge(widget.lingya.birthday)
                                ])}  ${widget.lingya.getGender()}",
                            color7C,
                            12.sp),
                      ),
                      SizedBox(width: 8.sp),
                      const Spacer(),
                      _getStatusView(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 8.sp),
        ..._getUserDetailinfo(),
        Align(
          alignment: Alignment.topCenter,
          child: GestureDetector(
            child: Image.asset(expand ? "res/imgs/icon_array_up.png" : "res/imgs/icon_array_down.png", width: 24.sp),
            onTap: () {
              setState(() {
                expand = !expand;
              });
            },
          ),
        ),
      ],
    );
  }

  _getCaseInfoView(int count, String unit, String title, onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 164.sp,
        height: 90.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.sp),
          image: DecorationImage(
            image: AssetImage(count > 0 ? "res/imgs/case_data_bg.png" : "res/imgs/case_empty_bg.png"),
            fit: BoxFit.cover,
          ),
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(width: 12.sp),
            MyText(count.toString(), Colors.white, 24.sp, FontWeight.w700),
            SizedBox(width: 4.sp),
            MyText(unit, Colors.white, 14.sp, FontWeight.w500),
            const Spacer(),
            MyText(title, Colors.white, 14.sp, FontWeight.w500),
            SizedBox(width: 12.sp),
          ],
        ),
      ),
    );
  }

  _getBodyView() {
    return SingleChildScrollView(
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.all(20.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _getUserInfoContainer(),
            SizedBox(height: 24.sp),
            MyText(Lang.case_info, color2B, 20.sp, FontWeight.w500),
            SizedBox(height: 12.sp),
            Row(
              children: [
                _getCaseInfoView(
                  recordList.length,
                  recordList.length > 1 ? Lang.unit_teams : Lang.unit_team,
                  Lang.contract_info,
                  () {
                    if (recordList.isNotEmpty) {
                      push(LingyaRecordPage(widget.lingya, phaseList, recordList));
                    }
                  },
                ),
                SizedBox(width: 6.sp),
                _getCaseInfoView(
                  phaseList.where((element) => element.hasDocument()).length,
                  phaseList.where((element) => element.hasDocument()).length > 1 ? Lang.unit_counts : Lang.unit_count,
                  phaseList.where((element) => element.hasDocument()).length > 1 ? Lang.case_docs : Lang.case_doc,
                  () {
                    if (isNotEmpty(phaseList)) {
                      showBottomWidgetsDialog(
                        context,
                        Lang.select_contract,
                        phaseList
                            .map(
                              (phase) => Container(
                                width: 1.sw - 40.sp,
                                margin: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 20.sp),
                                padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 12.sp),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16.sp),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    MyText(phase.name, phase.hasDocument() ? color2B : colorE1, 14.sp, FontWeight.w500),
                                    SizedBox(height: 4.sp),
                                    MyText(Global.getDateByTimestamp(milliseconds: phase.createTime, type: 1),
                                        phase.hasDocument() ? colorAB : colorE1, 12.sp),
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                        (index) {
                          if (phaseList[index].hasDocument()) {
                            push(LingyaDocPage(phaseList[index], phaseList));
                          } else {
                            toast(Lang.case_no_doc);
                          }
                        },
                      );
                    } else {
                      toast(Lang.case_no_contract);
                    }
                  },
                ),
              ],
            ),
            SizedBox(height: 24.sp),
            // hasMonitor != null
            //     ? Column(
            //         crossAxisAlignment: CrossAxisAlignment.start,
            //         children: [
            //           MyText(Lang.my_mooeli_case, color2B, 20.sp, FontWeight.w500),
            //           SizedBox(height: 8.sp),
            //           Container(
            //             width: 1.sw - 40.sp,
            //             padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 12.sp),
            //             decoration: BoxDecoration(
            //               color: colorBrand.withOpacity(0.1),
            //               borderRadius: BorderRadius.circular(16.sp),
            //             ),
            //             child: Column(
            //               mainAxisSize: MainAxisSize.min,
            //               crossAxisAlignment: CrossAxisAlignment.center,
            //               children: [
            //                 Row(
            //                   mainAxisAlignment: MainAxisAlignment.center,
            //                   children: [
            //                     Image.asset("res/imgs/mooeli_launch.png", width: 150.sp),
            //                     SizedBox(width: 20.sp),
            //                     Image.asset("res/imgs/mooeli_pic.png", width: 80.sp),
            //                   ],
            //                 ),
            //                 hasMonitor!
            //                     ? HCustomButton(
            //                         key: UniqueKey(),
            //                         text: "查看监控详情",
            //                         width: 0.65.sw,
            //                         height: 40.sp,
            //                         fontSize: 14.sp,
            //                         bgColor: colorBrand,
            //                         onPress: () {
            //                           push(MooeliRecordPage(widget.lingya.toMooeliItem()));
            //                         },
            //                       )
            //                     : HCustomButton(
            //                         key: UniqueKey(),
            //                         width: 0.65.sw,
            //                         height: 40.sp,
            //                         fontSize: 14.sp,
            //                         bgColor: colorBrand,
            //                         onPress: () {
            //                           push(MooeliAddMonitorPage(widget.lingya.toMooeliItem()));
            //                         },
            //                         child: Row(
            //                           mainAxisSize: MainAxisSize.min,
            //                           children: [
            //                             Image.asset("res/icons/icon_add.png", width: 20.sp),
            //                             SizedBox(width: 4.sp),
            //                             MyText(Lang.add_mooeli_monitor, Colors.white, 16.sp, FontWeight.w500),
            //                           ],
            //                         ),
            //                       )
            //               ],
            //             ),
            //           )
            //         ],
            //       )
            //     : const SizedBox(),
            SizedBox(height: 60.sp),
          ],
        ),
      ),
    );
  }
}
