import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/calculate_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

//气道分析
class LingyaDetail5023Page extends BasePage {
  LingyaFile file;
  String id;

  LingyaDetail5023Page(this.file, this.id, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail5023PageState();
  }
}

class LingyaDetail5023PageState extends BasePageState<LingyaDetail5023Page> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final double itemHeight = 38.sp;
  bool loaded = false, error = false;
  dynamic draftJson = {};
  double ruler = 10;

  String displayImage = "";

  Map airwayConfig = {
    "PNS-Phw": {
      "测量项目": "PNS-Phw",
      "male": [24.26, 25.44, 24.85, 0.59],
      "boy": [24.26, 25.44, 24.85, 0.59],
      "female": [24.26, 25.44, 24.85, 0.59],
      "girl": [24.26, 25.44, 24.85, 0.59],
      "describe1": "鼻咽部气道长度正常",
      "describe2": "鼻咽部气道长度正常",
      "describe3": "鼻咽部气道长度偏小",
      "airway": 1
    },
    "UAL": {
      "测量项目": "UAL",
      "male": [57.6, 69.4, 63.5, 5.9],
      "boy": [57.6, 69.4, 63.5, 5.9],
      "female": [57.6, 69.4, 63.5, 5.9],
      "girl": [57.6, 69.4, 63.5, 5.9],
      "describe1": "上气道长度正常",
      "describe2": "上气道长度正常",
      "describe3": "上气道长度偏小",
      "airway": 2
    },
    "Nph": {
      "测量项目": "Nph",
      "male": [6.98, 10.66, 8.82, 1.84],
      "boy": [6.98, 10.66, 8.82, 1.84],
      "female": [6.98, 10.66, 8.82, 1.84],
      "girl": [6.98, 10.66, 8.82, 1.84],
      "describe1": "鼻咽区域正常",
      "describe2": "鼻咽区域正常 ",
      "describe3": "鼻咽区域偏小",
      "airway": 3
    },
    "Phsp": {
      "测量项目": "Phsp",
      "male": [13.55, 20.95, 17.25, 3.7],
      "boy": [13.55, 20.95, 17.25, 3.7],
      "female": [13.55, 20.95, 17.25, 3.7],
      "girl": [13.55, 20.95, 17.25, 3.7],
      "describe1": "咽间隙正常 ",
      "describe2": "咽间隙正常",
      "describe3": "咽间隙偏小",
      "airway": 4
    },
    "Opsp": {
      "测量项目": "Opsp",
      "male": [4.2, 7.6, 5.9, 1.7],
      "boy": [4.2, 7.6, 5.9, 1.7],
      "female": [4.2, 7.6, 5.9, 1.7],
      "girl": [4.2, 7.6, 5.9, 1.7],
      "describe1": "口咽间隙正常 ",
      "describe2": "口咽间隙正常",
      "describe3": "口咽间隙偏小",
      "airway": 5
    },
    "PAS": {
      "测量项目": "PAS",
      "male": [9.69, 11.21, 10.45, 0.76],
      "boy": [9.69, 11.21, 10.45, 0.76],
      "female": [9.69, 11.21, 10.45, 0.76],
      "girl": [9.69, 11.21, 10.45, 0.76],
      "describe1": "后气道间隙正常",
      "describe2": "后气道间隙正常 ",
      "describe3": "后气道间隙偏小",
      "airway": 6
    },
    "UP-PhW": {
      "测量项目": "UP-PhW",
      "male": [6, 18, 12, 6],
      "boy": [6, 18, 12, 6],
      "female": [6, 18, 12, 6],
      "girl": [6, 18, 12, 6],
      "describe1": "咽气道正常",
      "describe2": "咽气道正常",
      "describe3": "咽气道偏窄",
      "airway": 7
    },
    "TB-PhW": {
      "测量项目": "TB-PhW",
      "male": [11, 19, 15, 4],
      "boy": [11, 19, 15, 4],
      "female": [11, 19, 15, 4],
      "girl": [11, 19, 15, 4],
      "describe1": "咽气道正常",
      "describe2": "咽气道正常",
      "describe3": "咽气道偏窄",
      "airway": 8
    }
  };

  // {
  // 1: ['PNS','Po','Or'],
  // 2: ['PNS','H1','ATA'],
  // 3: ['PNS',"ATA"],
  // 4: ['PNS','ATA','Et','UP','Po','Or','H'],
  // 5: ['PNS','ATA','Et','UP','Po','Or'],
  // 6: ['B','Go'],
  // 7: ['UP','Po','Or'],
  // 8: ['Go','Po','Or'],
  // }
  Map<int, List<String>> airwayPointName = {
    1: ['100010', '100004', '100007'],
    2: ['100010', '100104', '100100'],
    3: ['100010', "100100"],
    4: ['100010', '100100', '100101', '100105', '100004', '100007', '100103'],
    5: ['100010', '100100', '100101', '100105', '100004', '100007'],
    6: ['100018', '100017'],
    7: ['100105', '100004', '100007'],
    8: ['100017', '100004', '100007'],
  };

  List<List<String>> airwayLineArray = [
    ['100110', '100111', '100112', '100113', '100114', '100115', '100116', '100117', '100118', '100119'],
    ['100010', '100121', '100122', '100123', '100105', '100124', '100125', '100126', '100127', '100010'],
    ['100130', '100131', '100132', '100133', '100101', '100136', '100137', '100138'],
    ['100103', '100936', '100104', '100938', '100939', '100940', '100941', '100942', '100103'],
  ];

  @override
  initState() {
    super.initState();
    getAirwayConfigJson((json) {
      if (isNotEmpty(json) && isNotEmpty(json["气道分析法"])) {
        setState(() {
          airwayConfig = json["气道分析法"];
        });
      }
      _getAnalyticsInfo();
    });
  }

  _getAnalyticsInfo() {
    getAnalyticsInfo(
      widget.id,
      (data) {
        setState(() {
          if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
            getDraftContent(data["draft"], (draft) {
              logger("getDraftContent: $draft");
              draftJson = draft["draft"];
              if (isNotEmpty(draftJson["pptFileMap"]) && isNotEmpty(draftJson["pptFileMap"]["displayId"])) {
                String fileId = draftJson["pptFileMap"]["displayId"];
                downloadBase64(fileId, (image) {
                  setState(() {
                    displayImage = image.substring(1, image.length - 1).replaceAll(RegExp(r'\s'), '').split(',').last;
                  });
                });
              }
              loaded = true;
            });
          } else if (isNotEmpty(data["algorithmInfoMap"])) {
            String gender = data["caseInfoMap"]["gender"];
            int age = Global.getAge(int.parse(data["caseInfoMap"]["birthday"]));
            if (age < 12) {
              gender = sexMap[gender];
            }
            try {
              ruler = double.parse(data["shareSetting"]["ruler"]);
            } catch (e) {
              //
            }
            String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
            getAlgorithmResult(algorithmId, (data) {
              double ratio = 1.0;
              var point1 = data["kps"]['100000'];
              var point2 = data["kps"]['100001'];
              if (isNotEmpty(point1) && isNotEmpty(point2)) {
                ratio = math.sqrt(math.pow(point1[1] - point2[1], 2) + math.pow(point2[0] - point1[0], 2)) / ruler;
              }
              getImageSize(widget.file.imagePath, (w, h) {
                setState(() {
                  startAirwayMeasure(data, gender, ratio, h * 1.0);
                  loaded = true;
                });
              });
            });
          } else {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          }
        });
      },
    );
  }

  // 气道数据
  startAirwayMeasure(Map data, String gender, double ruler, double height) {
    Map pointArrayMap = {};
    for (int index in airwayPointName.keys) {
      List list = [];
      for (String id in airwayPointName[index]!) {
        list.add(data["kps"][id]);
      }
      pointArrayMap[index] = list;
    }
    // logger("startAirwayMeasure pointArrayMap: $pointArrayMap");
    List pointLineArray = [];
    for (List<String> array in airwayLineArray) {
      List list = [];
      for (String id in array) {
        list.add(data["kps"][id]);
      }
      pointLineArray.add(list);
    }
    // logger("startAirwayMeasure pointLineArray: $pointLineArray");
    for (String key in airwayConfig.keys) {
      double res = 0;
      Map target = airwayConfig[key];
      try {
        int airway = target["airway"];
        List pointArray = pointArrayMap[airway];
        pointArray.addAll(pointLineArray);
        // logger("startAirwayMeasure pointArray: $pointArray");
        var result = CalculateTool110.instance.getAirwayResult(airway, pointArray, ruler, height: height);
        res = double.parse(double.parse(result.toString()).toStringAsFixed(2));
      } catch (e) {
        //
      }
      target["section"] = target[gender];
      target["res"] = res;
      target["colorObj"] = CalculateTool110.instance.getResColor(res, target);
      int resStatus = CalculateTool110.instance.getMeasureStatus(res, target["section"]);
      target["resStatus"] = resStatus;
      logger("startAirwayMeasure target: $target");
    }
    draftJson["reportRes"] = airwayConfig;
  }

  _buildFormList() {
    List<Widget> children = [];
    if (isEmpty(draftJson) || isEmpty(draftJson["reportRes"])) {
      children.add(Container(
        padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
        child: Center(
          child: GestureDetector(
            onTap: _getAnalyticsInfo,
            child: MyText(
                loaded
                    ? error
                        ? Lang.ai_status_fail
                        : Lang.no_analytic_result
                    : Lang.in_analytic,
                color2B,
                14.sp),
          ),
        ),
      ));
    } else {
      children.add(
        Padding(
          padding: EdgeInsets.only(top: 20.sp),
          child: getOriginPhoto(displayImage, widget.file),
        ),
      );

      children.add(_buildForm(draftJson["reportRes"] as Map));
      children.add(getSloganView());
    }
    return children;
  }

  _buildRow(List<String> texts, {bool bold = false, Color? text2Color}) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
      fontWeight: bold ? FontWeight.w600 : FontWeight.w400,
    );
    return Container(
      width: 1.sw,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
      decoration: BoxDecoration(
        border: bold ? const Border() : Border(top: BorderSide(color: color2B.withOpacity(0.05))),
      ),
      constraints: BoxConstraints(minHeight: bold ? itemHeight : 48.sp),
      child: Row(
        children: [
          SizedBox(width: 0.5.sw - 48.sp, child: Text(texts[0], style: style)),
          SizedBox(width: 0.3.sw, child: Text(texts[1], style: style)),
          SizedBox(
              width: 0.2.sw - 16.sp,
              child: Text(texts[2],
                  style: text2Color != null
                      ? TextStyle(
                          color: text2Color!,
                          fontSize: 14.sp,
                        )
                      : style)),
        ],
      ),
    );
  }

  _buildForm(Map data) {
    List<Widget> children = [];
    children.add(Container(
      width: 1.sw - 40.sp,
      constraints: BoxConstraints(minHeight: itemHeight),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: colorF3,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.sp),
          topRight: Radius.circular(8.sp),
        ),
      ),
      child: _buildRow([Lang.measure_name, Lang.measure_norm, Lang.measure_value], bold: true),
    ));
    for (String key in data.keys) {
      String value = "${data[key]["res"]}";
      if (data[key]["resStatus"] == 1) {
        value = "$value ↑";
      } else if (data[key]["resStatus"] == 2) {
        value = "$value ↓";
      }
      children.add(_buildRow(
        [key, "${data[key]["section"][2]} ± ${data[key]["section"][3]}", value],
        text2Color: string2Color(data[key]["colorObj"]["color"]),
      ));
      if (data[key]["resStatus"] != 0) {
        children.add(
          Container(
            width: 1.sw - 40.sp,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 12.sp),
            child: Row(
              children: [
                SizedBox(width: 4.sp),
                ...List.filled(
                  (data[key]["colorObj"]["status"]) ~/ 1 - 1,
                  Icon(
                    IconFont.icon_star,
                    size: 10.sp,
                    color: string2Color(data[key]["colorObj"]["color"]),
                  ),
                ),
                SizedBox(width: 4.sp),
                Expanded(
                  child: MyText(data[key]["describe3${getLangStr()}"], color7C, 14.sp),
                ),
              ],
            ),
          ),
        );
      }
    }

    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(12.sp, 20.sp, 12.sp, 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: children,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: _buildFormList(),
      ),
    );
  }
}
