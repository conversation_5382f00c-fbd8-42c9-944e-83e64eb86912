import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/calculate_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/paint_manager.dart';
import 'package:mooeli/model/calculate_data.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

//侧貌分析
class LingyaDetail101Page extends BasePage {
  LingyaFile file;

  LingyaDetail101Page(this.file, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail101PageState();
  }
}

class LingyaDetail101PageState extends BasePageState<LingyaDetail101Page> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  ScrollController tabController = ScrollController();

  int imageWidth = 0, imageHeight = 0;
  double canvasWidth = 335.sp, canvasHeight = 0;

  MyPainter myPainter = MyPainter();

  int selectIndex = 0;
  String selectKey = "";

  final double itemHeight = 38.sp;
  bool loaded = false, error = false;
  Map<String, dynamic> faceMap = {};

  Map pointsMap = {};

  String gender = "";

  @override
  initState() {
    super.initState();
    getFaceProfileConfigJson((json) {
      setState(() {
        faceMap = json;
      });
      _getAnalyticsInfo();
    });
    _getImageSize();
  }

  _getImageSize() {
    getImageSize(widget.file.imagePath, (width, height) {
      setState(() {
        imageWidth = width;
        imageHeight = height;
        canvasWidth = 335.sp;
        canvasHeight = canvasWidth * imageHeight / imageWidth;
      });
      logger("getImageSize image: $imageWidth*$imageHeight, canvas: $canvasWidth*$canvasHeight");
    });
  }

  _getAnalyticsInfo() {
    if (isNotEmpty(widget.file.analysisInfoMap)) {
      getAnalyticsInfo(
        widget.file.getAnalyticsId(),
        (data) {
          setState(() {
            gender = data["caseInfoMap"]["gender"];
            int age = Global.getAge(int.parse(data["caseInfoMap"]["birthday"]));
            if (age < 12) {
              gender = sexMap[gender];
            }
          });
          if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
            getDraftContent(data["draft"], (draft) {
              logger("getDraftContent: $draft");
              setState(() {
                loaded = true;
                pointsMap = draft["draft"]["points"] as Map;
              });
            });
            onSelectTab(faceMap.keys.first, 0);
          } else if (isEmpty(data["algorithmInfoMap"])) {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          } else {
            getDraftPoints(data, (json) {
              setState(() {
                loaded = true;
                pointsMap = json;
              });
              onSelectTab(faceMap.keys.first, 0);
            });
          }
        },
      );
    }
  }

  _buildFormList() {
    List<Widget> children = [
      _buildTitleView(selectKey),
      Stack(
        children: [
          getDetailImageSw(widget.file, () => viewLingyaImage(widget.file)),
          GestureDetector(
            onTap: () => viewLingyaImage(widget.file),
            child: CustomPaint(
              size: Size(canvasWidth, canvasHeight),
              painter: myPainter,
            ),
          ),
        ],
      )
    ];
    if (isEmpty(pointsMap)) {
      children.add(Container(
        padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
        child: Center(
          child: MyText(
            loaded
                ? error
                    ? Lang.ai_status_fail
                    : Lang.no_analytic_result
                : Lang.in_analytic,
            color2B,
            14.sp,
          ),
        ),
      ));
    } else {
      children.add(_buildForm(faceMap[selectKey] as Map));
      children.add(getSloganView());
    }
    children.add(SizedBox(height: 40.sp));
    return children;
  }

  _getTabTitles() {
    List keys = faceMap.keys.toList();
    List<Widget> list = [];
    for (int i = 0; i < keys.length; i++) {
      list.add(_getTitleView(i, keys[i]));
    }
    return list;
  }

  _getTitleView(int index, String key) {
    bool isSelect = index == selectIndex;
    return GestureDetector(
      onTap: () {
        // if (index >= 2 && index > selectIndex) {
        //   tabController.animateTo(tabController.offset + 0.2.sw,
        //       duration: const Duration(milliseconds: 300), curve: Curves.linear);
        // } else if (index <= 2 && index < selectIndex) {
        //   tabController.animateTo(tabController.offset - 0.2.sw,
        //       duration: const Duration(milliseconds: 300), curve: Curves.linear);
        // }
        onSelectTab(key, index);
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
        height: 30.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.sp),
          color: isSelect ? colorBrand : Colors.white,
        ),
        child: MyText(
          faceMap[key].values.first["type${getLangStr()}"],
          isSelect ? Colors.white : color2B,
          14.sp,
          FontWeight.w500,
        ),
      ),
    );
  }

  void onSelectTab(String key, int index) {
    setState(() {
      selectIndex = index;
      selectKey = key;
    });
    List pointArray = [];
    for (String title in faceMap[key].keys) {
      for (Map point in (faceMap[key][title]["pointArray"] as List)) {
        //point: {000000: "xy"}
        logger("onSelectTab pointsMap: ${pointsMap[point.keys.first]}");
        if (isNotEmpty(pointsMap[point.keys.first])) {
          Map value = pointsMap[point.keys.first] as Map;
          pointArray.add({"x": value["x"] * canvasWidth / imageWidth, "y": value["y"] * canvasHeight / imageHeight});
        }
      }
    }
    // for (String key in pointsMap.keys) {
    //   pointArray.add(
    //       {"x": pointsMap[key]["x"] * canvasWidth / imageWidth, "y": pointsMap[key]["y"] * canvasHeight / imageHeight});
    // }
    logger("onSelectTab pointArray: $pointArray");
    myPainter.points = pointArray;
  }

  final Map _iconMap = {
    "鼻部": IconFont.icon_bibu,
    "唇部": IconFont.icon_chunbufen,
    "面型": IconFont.icon_mianxing,
  };

  _buildTitleView(String type) {
    if (isEmpty(faceMap) || isEmpty(faceMap[type])) {
      return SizedBox(height: 42.sp);
    }
    return Container(
      width: 1.sw,
      padding: EdgeInsets.fromLTRB(28.sp, 12.sp, 20.sp, 12.sp),
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Icon(
            _iconMap[type],
            color: color2B,
            size: 16.sp,
            weight: 500,
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 12.sp, right: 12.sp),
              child: MyText(faceMap[type].values.first["type${getLangStr()}"], color2B, 16.sp, FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  _buildTitleRow(List<String> texts) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
    );
    return Container(
      width: 1.sw,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
      constraints: BoxConstraints(minHeight: itemHeight),
      child: Row(
        children: [
          SizedBox(width: 0.5.sw - 64.sp, child: Text(texts[0], style: style)),
          SizedBox(width: 0.3.sw, child: Text(texts[1], style: style)),
          SizedBox(width: 0.2.sw, child: Text(texts[2], style: style)),
        ],
      ),
    );
  }

  _buildValueRow(List<String> texts, Color text2Color, String subTitle) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
    );
    return Container(
      width: 1.sw - 40.sp,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: color2B.withOpacity(0.05))),
      ),
      constraints: BoxConstraints(minHeight: itemHeight),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(width: 0.5.sw - 64.sp, child: Text(texts[0], style: style)),
              SizedBox(width: 0.3.sw, child: Text(texts[1], style: style)),
              SizedBox(width: 0.2.sw, child: MyText(texts[2], text2Color, 14.sp)),
            ],
          ),
          SizedBox(height: 4.sp),
          isEmpty(subTitle) ? const SizedBox() : MyText(subTitle, color7C, 12.sp),
        ],
      ),
    );
  }

  _buildForm(Map map) {
    List<Widget> children = [];
    children.add(
      Container(
        width: 1.sw - 40.sp,
        constraints: BoxConstraints(minHeight: itemHeight),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: colorF3,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.sp),
            topRight: Radius.circular(8.sp),
          ),
        ),
        child: _buildTitleRow([Lang.measure_name, Lang.measure_norm, Lang.measure_value]),
      ),
    );
    for (String title in map.keys) {
      try {
        Map data = map[title] as Map;

        String value = "";
        int algorithm = 0;
        int type = 0;
        if (data["algorithm"] is String) {
          String str = data["algorithm"];
          int left = str.indexOf("(");
          int right = str.indexOf(")");
          algorithm = int.parse(str.substring(0, left));
          type = int.parse(str.substring(left + 1, right));
        } else {
          algorithm = data["algorithm"] ~/ 1;
        }
        List keys = (data["pointArray"] as List).map((e) => (e as Map).keys.first).toList();
        List points = keys.map((e) => [pointsMap[e]["x"], pointsMap[e]["y"]]).toList();
        logger("algorithm: $algorithm $type $points");
        CalResult101? result = CalculateTool101.instance.getCalculationResult(algorithm, points, data[gender], type);

        if (result != null) {
          value = result.angle.toStringAsFixed(2);
          if (result.commit == 1) {
            value = "$value ↑";
          } else if (result.commit == 2) {
            value = "$value ↓";
          }
          int star = (result.angle - data[gender][2]).abs() ~/ data[gender][3];
          star = star > 3 ? 3 : star;
          children.add(_buildValueRow(
            [data[getLangKey()], "${data[gender][2]} ± ${data[gender][3]}", value],
            getValueColor(star),
            lang == "zh" ? data["en"] : "",
          ));
          if (result.commit != 0) {
            children.add(
              Container(
                width: 1.sw,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 12.sp),
                child: Row(
                  children: [
                    SizedBox(width: 4.sp),
                    ...List.filled(
                      star,
                      Icon(
                        IconFont.icon_star,
                        size: 10.sp,
                        color: getValueColor(star),
                      ),
                    ),
                    SizedBox(width: 4.sp),
                    Expanded(
                      child: MyText(data["describe${result.commit}${getLangStr()}"], color7C, 14.sp),
                    ),
                  ],
                ),
              ),
            );
          }
        }
      } catch (ex) {
        //
      }
    }

    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(0, 20.sp, 0, 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: children,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(categoryMap[widget.file.category] ?? widget.file.name),
      ),
      body: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Container(
              margin: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 8.sp),
              padding: EdgeInsets.fromLTRB(5.sp, 4.sp, 5.sp, 4.sp),
              height: 38.sp,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(18.sp),
              ),
              child: ScrollConfiguration(
                behavior: NoOverScrollBehavior(),
                child: SingleChildScrollView(
                  controller: tabController,
                  scrollDirection: Axis.horizontal,
                  child: Row(children: _getTabTitles()),
                ),
              ),
            ),
          ),
          Container(
            constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: _buildFormList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
