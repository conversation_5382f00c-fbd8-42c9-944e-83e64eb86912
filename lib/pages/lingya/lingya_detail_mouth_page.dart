import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/pager_view.dart';

//口内照
class LingyaDetailMouthPage extends BasePage {
  List<LingyaFile> files;
  int index;

  LingyaDetailMouthPage(this.files, this.index, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetailMouthPageState();
  }
}

class LingyaDetailMouthPageState extends BasePageState<LingyaDetailMouthPage> {
  final double itemHeight = 38.sp, contentHeight = 48.sp;
  bool loaded = false, error = false;

  dynamic algorithmJson = {};
  dynamic draftJson = {};
  Map<int, List> diseaseMap = {}; //有疾病的牙齿

  List allTeeth = []; //String
  List hengyaTeeth = []; //String
  List babyTeeth = []; //String
  int currentIndex = 0;

  Map analyticsMap = {};

  List<String> positions = ["front", "left", "lower", "right", "upper"];

  @override
  initState() {
    super.initState();
    currentIndex = widget.index;
    getLingyaPointsConfigJson((json) {
      logger("getLingyaPointsConfigJson ${json["xrayfront"]}");
      _getAnalyticsInfo();
    });
  }

  _getAnalyticsInfo() {
    if (isNotEmpty(widget.files.first.analysisInfoMap)) {
      getAnalyticsInfo(
        widget.files.first.getAnalyticsId(),
        (data) {
          // if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
          //   setState(() {
          //     draftJson = data["draft"];
          //     allTeeth = (draftJson["diseaseMap"] as Map).keys.toList();
          //     hengyaTeeth = allTeeth.where((element) => teethIconMap.keys.contains(element)).toList();
          //     babyTeeth = allTeeth.where((element) => babyTeethIconMap.keys.contains(element)).toList();
          //     _getDiseaseMapFromDraft();
          //   });
          //   delay(1000, () {
          //     setState(() {
          //       loaded = true;
          //     });
          //   });
          // }
          if (isNotEmpty(data["algorithmInfoMap"])) {
            String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
            getAlgorithmResult(algorithmId, (data) {
              setState(() {
                algorithmJson = data;
                if (isEmpty(data["draft"])) {
                  Set teeth = {};
                  for (String pos in positions) {
                    try {
                      teeth.addAll((algorithmJson["image"][pos]["teeth"] as Map).keys.toList());
                    } catch (ex) {
                      //
                    }
                  }
                  allTeeth.clear();
                  allTeeth.addAll(teeth);
                  hengyaTeeth = allTeeth.where((element) => teethIconMap.keys.contains(element)).toList();
                  babyTeeth = allTeeth.where((element) => babyTeethIconMap.keys.contains(element)).toList();
                  _getDiseaseMap();
                }
              });
              getFitAnalyticsResult(data);
              delay(1000, () {
                setState(() {
                  loaded = true;
                });
              });
            });
          } else {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          }
        },
      );
    }
  }

  void getFitAnalyticsResult(json) {
    try {
      if (json["image"]["left"]["info"]["molar"]["type"] != null) {
        analyticsMap["LeftMolar"] = getMolarDesc(json["image"]["left"]["info"]["molar"]["type"]);
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["right"]["info"]["molar"]["type"] != null) {
        analyticsMap["RightMolar"] = getMolarDesc(json["image"]["right"]["info"]["molar"]["type"]);
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["front"]["info"]["middle_line"]["type"] > -5) {
        analyticsMap["Mid"] = getMidDesc(json["image"]["front"]["info"]["middle_line"]["type"]);
        analyticsMap["MidValue"] = "${json["image"]["front"]["info"]["middle_line"]["ref"].toStringAsFixed(2)}mm";
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["front"]["info"]["bite"]["type"] > -5) {
        analyticsMap["Bite"] = getBiteDesc(json["image"]["front"]["info"]["bite"]["type"]);
        analyticsMap["BiteValue"] = "${json["image"]["front"]["info"]["bite"]["ref"].toStringAsFixed(2)}mm";
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["left"]["info"]["overjet"]["type"] > -3) {
        analyticsMap["Overjet"] = getOverjetDesc(json["image"]["left"]["info"]["overjet"]["type"]);
        analyticsMap["OverjetValue"] = "${json["image"]["left"]["info"]["overjet"]["ref"].toStringAsFixed(2)}mm";
      }
    } catch (ex) {
      //
    }
    setState(() {});
  }

  _getDiseaseMapFromDraft() {
    for (String number in (draftJson["diseaseMap"] as Map).keys) {
      dynamic disease = (draftJson["diseaseMap"][number]["disease"] as Map);
      for (String diseaseNo in disease.keys) {
        if (disease[diseaseNo]["show"] ?? false) {
          if (diseaseMap[int.parse(diseaseNo)] == null) {
            diseaseMap[int.parse(diseaseNo)] = [];
          }
          if (!diseaseMap[int.parse(diseaseNo)]!.contains(int.parse(number))) {
            diseaseMap[int.parse(diseaseNo)]!.add(int.parse(number));
          }
        }
      }
    }
  }

  _getDiseaseMap() {
    if (isNotEmpty(algorithmJson)) {
      for (String pos in positions) {
        try {
          if (isNotEmpty(algorithmJson["image"][pos]["disease"])) {
            for (dynamic disease in (algorithmJson["image"][pos]["disease"] as Map).values) {
              int label = disease["label"];
              if (diseaseMap[label] == null) {
                diseaseMap[label] = [];
              }
              diseaseMap[label]!.addAll(disease["map"] as List);
            }
          }
        } catch (ex) {
          //
        }
      }
    }
  }

  _buildImageViews() {
    List<Widget> list = [];
    if (isNotEmpty(widget.files)) {
      for (int i = 0; i < widget.files.length; i++) {
        LingyaFile file = widget.files[i];
        list.add(
          Container(
            margin: EdgeInsets.only(left: 4.sp, right: 4.sp),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.sp),
              child: getDetailImageByHeight(file, 0.5.sw),
            ),
          ),
        );
      }
    }
    return list;
  }

  _buildList() {
    List<Widget> children = [
      Container(
        width: 1.sw,
        height: 36.sp,
        alignment: Alignment.center,
        child: MyText("${currentIndex + 1} / ${widget.files.length}", color2B, 14.sp),
      ),
      Container(
        constraints: BoxConstraints(maxHeight: 0.5.sw),
        child: MyPagerView(
          children: _buildImageViews(),
          hideIndicator: true,
          viewportFraction: 0.8,
          initIndex: currentIndex,
          circular: false,
          onPageChanged: (index) {
            setState(() {
              currentIndex = index;
            });
          },
          clickBanner: (index) {
            if (widget.files.length > 1) {
              viewLingyaImages(fileList: widget.files, index: index);
            } else {
              viewLingyaImage(widget.files[index]);
            }
          },
        ),
      ),
      Container(
        width: 1.sw,
        height: 36.sp,
        alignment: Alignment.topCenter,
        padding: EdgeInsets.only(top: 12.sp),
        child: MyText(categoryMap[widget.files[currentIndex].category]!, color2B, 14.sp),
      ),
    ];
    if (isEmpty(algorithmJson) && isEmpty(draftJson)) {
      children.add(Container(
        padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
        child: Center(
          child: MyText(
              loaded
                  ? error
                      ? Lang.ai_status_fail
                      : Lang.no_analytic_result
                  : Lang.in_analytic,
              color2B,
              14.sp),
        ),
      ));
    } else {
      logger("$diseaseMap", key: "diseaseMap || ");
      children.addAll([
        Padding(
          padding: EdgeInsets.only(top: 24.sp),
          child: MyText(Lang.analytic_result, color2B, 20.sp, FontWeight.w600),
        ),
        _getTeethView(),
        _getTeethTagView(),
        _getTeethLostTagView(),
        _getBabyTeethContainer(),
        _buildForm(),
        getSloganView(),
      ]);
    }
    return children;
  }

  _getTeethTagView() {
    List<Widget> children = [];
    Map<int, String> tagMap = {};
    for (String number in hengyaTeeth) {
      String disease = "";
      for (int key in diseaseMap.keys) {
        if (diseaseMap[key]!.contains(int.parse(number)) && diseaseNameMap.containsKey(key.toString())) {
          if (isEmpty(disease)) {
            disease = diseaseNameMap[key.toString()]!;
          } else {
            disease += ", ${diseaseNameMap[key.toString()]!}";
          }
        }
      }
      if (disease.isNotEmpty) {
        disease += "  ";
        tagMap[int.parse(number)] = disease;
      }
    }
    List<int> numbers = tagMap.keys.toList();
    numbers.sort();
    for (int n in numbers) {
      children.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                margin: EdgeInsets.only(top: 4.sp, right: 6.sp, bottom: 4.sp),
                padding: EdgeInsets.fromLTRB(8.sp, 2.sp, 8.sp, 2.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.sp),
                  color: colorToothDisease.withOpacity(0.2),
                ),
                child: MyText("#$n", colorToothDisease, 12.sp)),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 1.sw - 110.sp),
              child: MyText(tagMap[n]!, color2B, 14.sp),
            ),
          ],
        ),
      );
    }
    return Padding(
      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
      child: Wrap(children: children),
    );
  }

  _getTeethLostTagView() {
    List lostTeeth =
        teethIconMap.keys.where((element) => !hengyaTeeth.contains(element) && int.parse(element) % 10 < 8).toList();
    if (isEmpty(lostTeeth)) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Container(
              margin: EdgeInsets.only(top: 4.sp, right: 10.sp, bottom: 4.sp),
              padding: EdgeInsets.fromLTRB(8.sp, 2.sp, 8.sp, 2.sp),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.sp),
                color: colorRed_1.withOpacity(0.2),
              ),
              child: MyText("#${lostTeeth.join("、")}", colorRed_1, 12.sp)),
          MyText(Lang.teeth_lost, color2B, 14.sp),
        ],
      ),
    );
  }

  _getBabyTeethTagView() {
    List<Widget> children = [];
    Map<int, String> tagMap = {};
    for (String number in babyTeeth) {
      String disease = "";
      for (int key in diseaseMap.keys) {
        if (diseaseMap[key]!.contains(int.parse(number)) && diseaseNameMap.containsKey(number)) {
          if (isEmpty(disease)) {
            disease = diseaseNameMap[key.toString()]!;
          } else {
            disease += ", ${diseaseNameMap[key.toString()]!}";
          }
        }
      }
      if (disease.isNotEmpty) {
        disease += "  ";
        tagMap[int.parse(number)] = disease;
      }
    }
    List<int> numbers = tagMap.keys.toList();
    numbers.sort();
    for (int n in numbers) {
      children.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                margin: EdgeInsets.only(top: 4.sp, right: 6.sp, bottom: 4.sp),
                padding: EdgeInsets.fromLTRB(8.sp, 2.sp, 8.sp, 2.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.sp),
                  color: colorToothDisease.withOpacity(0.2),
                ),
                child: MyText("#$n", colorToothDisease, 12.sp)),
            MyText(tagMap[n]!, color2B, 14.sp),
          ],
        ),
      );
    }
    return Wrap(children: children);
  }

  _getBabyTeethContainer() {
    if (isEmpty(babyTeeth)) {
      return const SizedBox();
    }
    return Container(
      margin: EdgeInsets.only(top: 20.sp),
      padding: EdgeInsets.fromLTRB(12.sp, 20.sp, 12.sp, 20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyText(Lang.baby_tooth, color2B, 14.sp, FontWeight.w500),
          _getBabyTeethView(),
          _getBabyTeethTagView(),
        ],
      ),
    );
  }

  _buildForm() {
    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(0.sp, 40.sp, 0.sp, 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildTitle(Lang.molar_relation, top: true),
          _buildContent(Lang.left_side, analyticsMap["LeftMolar"]),
          _buildContent(Lang.right_side, analyticsMap["RightMolar"]),
          _buildTitle(Lang.tooth_overbite),
          _buildContent(analyticsMap["Bite"], analyticsMap["BiteValue"]),
          _buildTitle(Lang.tooth_overjet),
          _buildContent(analyticsMap["Overjet"], analyticsMap["OverjetValue"]),
          _buildTitle(Lang.midline_deviation),
          _buildContent(analyticsMap["Mid"], analyticsMap["MidValue"]),
        ],
      ),
    );
  }

  _buildTitle(String text, {bool top = false}) {
    return Container(
      width: 1.sw - 40.sp,
      constraints: BoxConstraints(minHeight: itemHeight),
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
      decoration: BoxDecoration(
        color: colorF3,
        borderRadius: top
            ? BorderRadius.only(
                topLeft: Radius.circular(8.sp),
                topRight: Radius.circular(8.sp),
              )
            : BorderRadius.zero,
      ),
      child: MyText(text, color2B, 14.sp, FontWeight.w600),
    );
  }

  _buildContent(String? text, String? value) {
    return Container(
        width: 1.sw - 40.sp,
        constraints: BoxConstraints(minHeight: contentHeight),
        decoration: BoxDecoration(
          border: Border(top: BorderSide(color: color2B.withOpacity(0.05))),
        ),
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
        child: Row(
          children: [
            Expanded(
              child: MyText(text ?? "-", color2B, 14.sp),
            ),
            Expanded(
              child: MyText(value ?? "-", color2B, 14.sp),
            ),
          ],
        ));
  }

  _getTeethView() {
    List<Widget> upList = [];
    for (int i = 18; i >= 11; i--) {
      upList.add(_getSingleTooth(i, true));
    }
    for (int i = 21; i <= 28; i++) {
      upList.add(_getSingleTooth(i, true));
    }
    List<Widget> downList = [];
    for (int i = 48; i >= 41; i--) {
      downList.add(_getSingleTooth(i, false));
    }
    for (int i = 31; i <= 38; i++) {
      downList.add(_getSingleTooth(i, false));
    }
    return Container(
      margin: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
      child: Column(
        children: [
          Row(children: upList),
          Row(children: downList),
        ],
      ),
    );
  }

  _getBabyTeethView() {
    List<Widget> upList = [];
    for (int i = 55; i >= 51; i--) {
      upList.add(_getSingleTooth(i, true, true));
    }
    for (int i = 61; i <= 65; i++) {
      upList.add(_getSingleTooth(i, true, true));
    }
    List<Widget> downList = [];
    for (int i = 75; i >= 71; i--) {
      downList.add(_getSingleTooth(i, false, true));
    }
    for (int i = 81; i <= 85; i++) {
      downList.add(_getSingleTooth(i, false, true));
    }
    return Container(
      margin: EdgeInsets.fromLTRB(0, 8.sp, 0, 8.sp),
      child: Column(
        children: [
          Row(children: upList),
          Row(children: downList),
        ],
      ),
    );
  }

  _getToothIcon(int index) {
    String number = index.toString();
    bool isMissed = !hengyaTeeth.contains(number);
    bool isZhichi = index % 10 == 8;
    bool isDisease = diseaseMap.values.where((list) => list.contains(index)).isNotEmpty;

    getColor() {
      if (isDisease) {
        return colorToothDisease;
      } else if (isMissed) {
        return isZhichi ? colorE1 : colorRed_1;
      } else {
        return colorToothNormal;
      }
    }

    return Icon(
      teethIconMap[number],
      size: 24.sp,
      color: getColor(),
    );
  }

  _getBabyToothIcon(int index) {
    String number = index.toString();
    bool isMissed = !babyTeeth.contains(number);
    bool isDisease = diseaseMap.values.where((list) => list.contains(index)).isNotEmpty;

    getColor() {
      if (isDisease) {
        return colorToothDisease;
      } else if (isMissed) {
        return colorE1;
      } else {
        return colorToothNormal;
      }
    }

    return Icon(
      babyTeethIconMap[number],
      size: 24.sp,
      color: getColor(),
    );
  }

  _getSingleTooth(int index, bool up, [bool isBaby = false]) {
    String number = index.toString();
    List<Widget> children = [
      isBaby ? _getBabyToothIcon(index) : _getToothIcon(index),
      SizedBox(height: 6.sp),
      MyText(number, color2B, 12.sp),
    ];

    return Container(
      width: (1.sw - 64.sp) / (isBaby ? 10 : 16),
      height: 64.sp,
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: up ? children : children.reversed.toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.intraoral_photo),
      ),
      body: Padding(
        padding: EdgeInsets.all(20.sp),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _buildList(),
          ),
        ),
      ),
    );
  }

  @override
  void onRouteResume(Route nextRoute) {
    //这里是防止图片浏览魔法效果返回后白屏的现象
    setState(() {});
    delay(500, () {
      setState(() {});
    });
  }
}
