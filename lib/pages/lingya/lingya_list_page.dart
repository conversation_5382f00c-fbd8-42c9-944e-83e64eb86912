import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/lingya/lingya_info_page.dart';
import 'package:mooeli/pages/lingya/lingya_search_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class LingyaListPage extends BasePage {
  const LingyaListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaListPageState();
  }
}

class LingyaListPageState extends BasePageState<LingyaListPage> {
  List<LingyaItem> lingyaList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;
  Map<int, int> sourceCountMap = {};

  SearchCondition savedCondition = SearchCondition();
  SearchCondition tempCondition = SearchCondition();

  ScrollController sourceController = ScrollController();

  List<LingyaTag> tagList = [];
  List<String> colorList = [
    '#EF8666', // 橙色
    '#F6CD56', // 黄色
    '#6FC9C3', // 绿色
    '#9DC7FF', // 蓝色
    '#C6B5F2', // 紫色
    '#B8B8B8', // 灰色
    '#EA605B', // 红色
  ];

  @override
  initState() {
    super.initState();
    addEventListener<EventSwitchTenant>((event) {
      initAsyncState();
    });
    initAsyncState();
  }

  @override
  onLanguageChanged() {
    initAsyncState();
  }

  getTagList() {
    HHttp.request("/v3/doctor/case/listAllCustomTag", "POST", (data) {
      tagList = (data as List).map((name) => LingyaTag("", name)).toList();
    }, params: {});
  }

  initAsyncState() async {
    await refreshLingyaList();
    getTagList();
  }

  refreshLingyaList() async {
    return await getLingyaList(true);
  }

  onLoadMore() {
    getLingyaList(false);
  }

  getLingyaList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    List filters = [];

    addFilterParam(name, operator, value) {
      filters.add({
        "columnName": name,
        "filterEnum": operator,
        "columnValue": value,
      });
    }

    if (savedCondition.statusTag > 0) {
      List<String> tags = ["UNCLASSIFIED", "INITIAL", "PROCESS", "COMPLETED"];
      addFilterParam("statusTag", "eq", tags[savedCondition.statusTag - 1]);
    }
    if (isNotEmpty(savedCondition.gender)) {
      addFilterParam("gender", "eq", savedCondition.gender);
    }
    if (savedCondition.startTime > 0) {
      addFilterParam("createTime", "geq", savedCondition.startTime);
    }
    if (savedCondition.endTime > 0) {
      addFilterParam("createTime", "leq", savedCondition.endTime);
    }
    Map<String, dynamic> params = {};
    params = {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "sortParamList": [
        {
          "columnName": savedCondition.sortBy,
          "sortEnum": savedCondition.sortOrder,
        }
      ],
      "colorTagList": savedCondition.colorSelectList,
      "subscribe": savedCondition.statusTag == -1,
      "filterParamList": filters,
      "customTagList": savedCondition.tagSelectList.map((e) => e.tagName).toList(),
    };
    HHttp.request("/v3/doctor/case/page", "POST", (data) {
      List<LingyaItem> list = (data["caseList"] as List).map((i) => LingyaItem.fromJson(i)).toList();
      if (isNotEmpty(list)) {
        List<String> ids = [];
        for (var item in list) {
          if (isNotEmpty(item.avatarFileId)) {
            ids.add(item.avatarFileId);
          }
        }
        downloadAvatar(
          ids,
          (id, path) {
            for (var item in list) {
              if (item.avatarFileId == id) {
                setState(() {
                  item.avatarPath = path;
                });
              }
            }
          },
          "RECORD",
          documentDir: true,
        );
      }

      setState(() {
        allLoad = true;
        if (refresh) {
          lingyaList = list;
          if (lingyaList.length >= pageSize) {
            allLoad = false;
          }
        } else {
          if (isNotEmpty(list)) {
            if (list.length >= pageSize) {
              allLoad = false;
            }
            lingyaList.addAll(list);
          }
        }
        isLoading = false;
      });

      if (savedCondition.statusTag == 0 && !savedCondition.hasCondition()) {
        int totalCount = data["totalCount"];
        int newCount = getJsonInt(data, "weekAdd");
        eventBus.fire(EventPatientCount(totalCount, newCount));

        setState(() {
          sourceCountMap[1] = getJsonInt(data, "unclassified");
          sourceCountMap[2] = getJsonInt(data, "initial");
          sourceCountMap[3] = getJsonInt(data, "process");
          sourceCountMap[4] = getJsonInt(data, "complete");
        });
      }
      logger("LingyaItem.fromJson 111");
    }, errCallBack: (_) {
      setState(() {
        isLoading = false;
      });
    }, params: params);
  }

  changeFavor(LingyaItem item) {
    HHttp.request("/v3/doctor/case/update", "POST", (data) {
      setState(() {
        item.subscribe = !item.subscribe;
      });
    }, params: {
      "birthday": item.birthday.toString(),
      "caseId": item.caseId,
      "subscribe": !item.subscribe,
    });
  }

  getItemView(LingyaItem item) {
    return GestureDetector(
      onTap: () {
        push(LingyaInfoPage(item));
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
        width: double.infinity,
        height: 90.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 1.sp,
              spreadRadius: 1.sp,
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 10.sp,
              height: 90.sp,
              decoration: BoxDecoration(
                color: item.getTagColor(),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(16.sp), bottomLeft: Radius.circular(16.sp)),
              ),
            ),
            SizedBox(width: 12.sp),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.sp),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(30.sp),
                        child: isEmpty(item.avatarPath)
                            ? Image.asset(
                                item.gender == "female" ? "res/imgs/avatar_female.png" : "res/imgs/avatar_male.png",
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )
                            : Image.file(
                                File(item.avatarPath),
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )),
                    SizedBox(width: 12.sp),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 250.sp,
                          height: 24.sp,
                          child: Row(
                            children: [
                              Container(
                                constraints: BoxConstraints(maxWidth: 140.sp),
                                child: SingleText(item.name, color2B, 16.sp, FontWeight.w500),
                              ),
                              SizedBox(width: 8.sp),
                              Click(
                                  child: Image.asset(
                                    item.subscribe ? "res/imgs/icon_subscribe.png" : "res/imgs/icon_unsubscribe.png",
                                    width: 16.sp,
                                  ),
                                  onTap: () {
                                    changeFavor(item);
                                  }),
                              const Spacer(),
                              MyText(Global.getDateByTimestamp(milliseconds: item.createTime), colorBB, 12.sp),
                            ],
                          ),
                        ),
                        SizedBox(height: 2.sp),
                        MyText("${item.getGender()}  ${sprintf(Lang.year_old, [Global.getAge(item.birthday)])}",
                            color2B, 12.sp),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 12.sp),
                Row(
                  children: [
                    Image.asset("res/imgs/icon_tag.png", width: 16.sp),
                    getTagView(item),
                  ],
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  getTagView(LingyaItem item) {
    if (isEmpty(item.tagList)) {
      return Padding(
        padding: EdgeInsets.only(left: 8.sp, right: 8.sp),
        child: MyText(Lang.none, colorBB, 12.sp),
      );
    } else {
      List<Widget> tagViews = [
        Padding(
          padding: EdgeInsets.only(left: 8.sp, right: 8.sp),
          child: MyText(item.tagList.first, colorBB, 12.sp),
        )
      ];
      for (int i = 1; i < item.tagList.length; i++) {
        tagViews.add(Container(width: 1.sp, height: 16.sp, color: const Color(0xFFA69ADD)));
        tagViews.add(Padding(
          padding: EdgeInsets.only(left: 8.sp, right: 8.sp),
          child: MyText(item.tagList[i], colorBB, 12.sp),
        ));
      }

      return SizedBox(
        width: 280.sp,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(children: tagViews),
        ),
      );
    }
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < lingyaList.length; i++) {
      allWidget.add(getItemView(lingyaList[i]));
    }
    return allWidget;
  }

// 下拉刷新
  Future onRefresh() async {
    await refreshLingyaList();
  }

  showFilterDialog() {
    Global.showBottomModal(
        maxHeight: 0.8.sh,
        context,
        Padding(
          padding: EdgeInsets.zero,
          child: Container(
            decoration: BoxDecoration(
              color: colorBg,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 0),
                  decoration: BoxDecoration(
                    color: colorBg,
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    Stack(
                      children: [
                        Align(
                          alignment: Alignment.center,
                          child: MyText(Lang.filter_title, color2B, 16.sp, FontWeight.w500),
                        ),
                        Align(
                          alignment: Alignment.topRight,
                          child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Image.asset("res/imgs/modal_close.png", width: 20.sp, height: 20.sp),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12.sp),
                    FilterDialogView(tagList, colorList, tempCondition),
                  ]),
                ),
                Container(width: 1.sw, height: 1.sp, color: const Color(0x66E0E4EE)),
                SizedBox(height: 16.sp),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    HCustomButton(
                      fontSize: 16.sp,
                      onPress: () {
                        hideKeyboard();
                        bool needSearch = savedCondition.hasCondition();
                        logger("needSearch  $needSearch");
                        setState(() {
                          savedCondition = SearchCondition(statusTag: savedCondition.statusTag);
                          tempCondition = SearchCondition();
                        });
                        if (needSearch) {
                          refreshLingyaList();
                        }
                        Navigator.pop(context);
                      },
                      borderColor: Colors.white24,
                      borderWidth: 1.sp,
                      textColor: color2B,
                      text: Lang.reset,
                      width: 160.sp,
                      height: 42.sp,
                      ms: 1000,
                      bgColor: Colors.white,
                    ),
                    SizedBox(width: 15.sp),
                    HCustomButton(
                        fontSize: 16.sp,
                        onPress: () {
                          hideKeyboard();
                          bool needSearch = !savedCondition.isEqual(tempCondition);
                          logger("needSearch  $needSearch");
                          setState(() {
                            savedCondition = SearchCondition.copy(tempCondition);
                          });
                          if (needSearch) {
                            refreshLingyaList();
                          }
                          Navigator.pop(context);
                        },
                        textColor: Colors.white,
                        text: Lang.confirm,
                        width: 160.sp,
                        height: 42.sp,
                        ms: 1000,
                        bgColor: colorBrand),
                  ],
                ),
                SizedBox(height: 30.sp),
              ],
            ),
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: LingyaAppBar(
            contentHeight: 148.sp,
            child: SizedBox(
              height: 148.sp,
              child: Column(
                children: [
                  Container(
                    width: 1.sw,
                    padding: EdgeInsets.fromLTRB(20.sp, 52.sp, 20.sp, 0),
                    child: Row(
                      children: [
                        Expanded(child: MyText(Lang.case_list, color2B, 20.sp, FontWeight.w500)),
                        GestureDetector(
                          child: Image.asset(
                            savedCondition.hasCondition() ? "res/imgs/icon_filtered.png" : "res/imgs/icon_filter.png",
                            width: 38.sp,
                          ),
                          onTap: () {
                            setState(() {
                              tempCondition = SearchCondition.copy(savedCondition);
                            });
                            showFilterDialog();
                          },
                        ),
                        SizedBox(width: 12.sp),
                        GestureDetector(
                          child: Image.asset(
                            "res/imgs/icon_search.png",
                            width: 38.sp,
                          ),
                          onTap: () {
                            push(const LingyaSearchPage());
                          },
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 8.sp),
                    padding: EdgeInsets.fromLTRB(5.sp, 4.sp, 5.sp, 4.sp),
                    width: double.infinity,
                    height: 38.sp,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(18.sp),
                      boxShadow: [
                        BoxShadow(
                          color: colorShader,
                          blurRadius: 1.sp,
                          spreadRadius: 1.sp,
                        ),
                      ],
                    ),
                    child: ScrollConfiguration(
                      behavior: NoOverScrollBehavior(),
                      child: SingleChildScrollView(
                        controller: sourceController,
                        scrollDirection: Axis.horizontal,
                        child: Row(children: [
                          _getSourceView(0, Lang.all),
                          _getSourceView(-1, Lang.favor),
                          _getSourceView(1, Lang.not_sorted),
                          _getSourceView(2, Lang.first_treat),
                          _getSourceView(3, Lang.in_treating),
                          _getSourceView(4, Lang.complete_treat),
                        ]),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          body: RefreshList(
            childList: getListView(),
            isLoading: isLoading,
            onRefresh: onRefresh,
            onLoadMore: onLoadMore,
            nullImgName: "res/imgs/empty_pic.png",
            nullText: Lang.no_filter_case,
            nullWidget: savedCondition.hasCondition() || savedCondition.statusTag != 0
                ? null
                : isLoading
                    ? MyText(Lang.loading, color7C, 14.sp)
                    : RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: Lang.no_case_login,
                          style: TextStyle(
                            fontSize: 14.sp,
                            height: 1.5,
                            color: colorAB,
                          ),
                          children: [
                            TextSpan(
                              text: Lang.lyoral_web,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: colorAB,
                              ),
                            ),
                            TextSpan(
                              text: Lang.create_case,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: colorAB,
                              ),
                            ),
                          ],
                        ),
                      ),
          ),
        ),
        // _getSearchView(),
      ],
    );
  }

  _getSourceView(int source, String text) {
    bool isSelect = source == savedCondition.statusTag;
    // if (sourceCountMap.containsKey(source)) {
    //   text = "$text·${sourceCountMap[source]}";
    // }
    return GestureDetector(
      onTap: () {
        if (!isSelect) {
          setState(() {
            savedCondition.statusTag = source;
          });
          refreshLingyaList();
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
        height: 30.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.sp),
          color: isSelect ? colorBrand : Colors.white,
        ),
        child: MyText(text, isSelect ? Colors.white : color2B, 14.sp, isSelect ? FontWeight.w500 : FontWeight.w400),
      ),
    );
  }
}

class FilterDialogView extends BasePage {
  List<LingyaTag> tagList;
  List<String> colorList;
  SearchCondition tempCondition;

  FilterDialogView(this.tagList, this.colorList, this.tempCondition);

  @override
  FilterDialogViewState createState() {
    return FilterDialogViewState();
  }
}

class FilterDialogViewState extends BasePageState<FilterDialogView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: 0.8.sh - 160.sp),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8.sp),
            MyText(Lang.filter, color2B, 16.sp, FontWeight.w500),
            SizedBox(height: 16.sp),
            // MyText(Lang.color, color2B, 14.sp),
            // SizedBox(height: 8.sp),
            // Wrap(
            //   children: widget.colorList.map((color) => _getColorView(color)).toList(),
            // ),
            // SizedBox(height: 12.sp),
            MyText(Lang.time, color2B, 14.sp),
            Row(
              children: [
                Click(
                  child: Container(
                    width: 104.sp,
                    height: 30.sp,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: widget.tempCondition.startTime > 0 ? colorBrand : Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: widget.tempCondition.startTime > 0 ? colorBrand : colorE1),
                    ),
                    child: widget.tempCondition.startTime > 0
                        ? MyText(Global.getDateByTimestamp(milliseconds: widget.tempCondition.startTime), Colors.white,
                            14.sp, FontWeight.w500)
                        : MyText(Lang.start_time, color2B, 14.sp),
                  ),
                  onTap: () async {
                    final DateTime? date = await showDatePicker(
                      context: context,
                      initialDate: widget.tempCondition.startTime > 0
                          ? DateTime.fromMillisecondsSinceEpoch(widget.tempCondition.startTime)
                          : widget.tempCondition.endTime > 0
                              ? DateTime.fromMillisecondsSinceEpoch(widget.tempCondition.endTime)
                              : DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: widget.tempCondition.endTime > 0
                          ? DateTime.fromMillisecondsSinceEpoch(widget.tempCondition.endTime)
                          : DateTime.now(),
                      locale: currentLocale,
                    );
                    if (date != null) {
                      setState(() {
                        widget.tempCondition.startTime = date!.millisecondsSinceEpoch;
                      });
                    }
                  },
                ),
                Container(
                  width: 8.sp,
                  height: 3.sp,
                  margin: EdgeInsets.all(4.sp),
                  decoration: BoxDecoration(
                    color: color2B,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Click(
                  child: Container(
                    width: 104.sp,
                    height: 30.sp,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: widget.tempCondition.endTime > 0 ? colorBrand : Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: widget.tempCondition.endTime > 0 ? colorBrand : colorE1),
                    ),
                    child: widget.tempCondition.endTime > 0
                        ? MyText(Global.getDateByTimestamp(milliseconds: widget.tempCondition.endTime), Colors.white,
                            14.sp, FontWeight.w500)
                        : MyText(Lang.end_time, color2B, 14.sp),
                  ),
                  onTap: () async {
                    final DateTime? date = await showDatePicker(
                      context: context,
                      initialDate: widget.tempCondition.endTime > 0
                          ? DateTime.fromMillisecondsSinceEpoch(widget.tempCondition.endTime)
                          : DateTime.now(),
                      firstDate: widget.tempCondition.startTime > 0
                          ? DateTime.fromMillisecondsSinceEpoch(widget.tempCondition.startTime)
                          : DateTime(2000),
                      lastDate: DateTime.now(),
                      locale: currentLocale,
                    );
                    if (date != null) {
                      setState(() {
                        widget.tempCondition.endTime = date!.millisecondsSinceEpoch + 86399999;
                      });
                    }
                  },
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      widget.tempCondition.startTime = 0;
                      widget.tempCondition.endTime = 0;
                    });
                  },
                  icon: const Icon(Icons.cancel),
                  iconSize: 20.sp,
                  color: colorE1,
                ),
              ],
            ),
            isNotEmpty(widget.tagList)
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 16.sp),
                      MyText(Lang.tag, color2B, 14.sp),
                      SizedBox(height: 8.sp),
                      Wrap(
                        children: widget.tagList.map((tag) => _getTagView(tag)).toList(),
                      ),
                    ],
                  )
                : const SizedBox(),
            SizedBox(height: 16.sp),
            MyText(Lang.gender, color2B, 14.sp),
            SizedBox(height: 8.sp),
            Row(
              children: [
                _getSelectView(Lang.all, isEmpty(widget.tempCondition.gender), () {
                  setState(() {
                    widget.tempCondition.gender = "";
                  });
                }),
                SizedBox(width: 11.5.sp),
                _getSelectView(Lang.gender_man, widget.tempCondition.gender == "male", () {
                  setState(() {
                    widget.tempCondition.gender = "male";
                  });
                }),
                SizedBox(width: 11.5.sp),
                _getSelectView(Lang.gender_woman, widget.tempCondition.gender == "female", () {
                  setState(() {
                    widget.tempCondition.gender = "female";
                  });
                }),
              ],
            ),
            SizedBox(height: 20.sp),
            MyText(Lang.order, color2B, 16.sp, FontWeight.w500),
            SizedBox(height: 8.sp),
            Row(
              children: [
                _getSelectView(Lang.update_time, widget.tempCondition.sortBy == "modifyTime", () {
                  setState(() {
                    widget.tempCondition.sortBy = "modifyTime";
                  });
                }),
                SizedBox(width: 11.5.sp),
                _getSelectView(Lang.create_time, widget.tempCondition.sortBy == "createTime", () {
                  setState(() {
                    widget.tempCondition.sortBy = "createTime";
                  });
                }),
                SizedBox(width: 11.5.sp),
                _getSelectView(Lang.name, widget.tempCondition.sortBy == "name", () {
                  setState(() {
                    widget.tempCondition.sortBy = "name";
                  });
                }),
              ],
            ),
            Container(
              width: 1.sw - 40.sp,
              height: 1.sp,
              color: colorE1,
              margin: EdgeInsets.only(top: 12.sp, bottom: 12.sp),
            ),
            Row(
              children: [
                _getSelectView(Lang.order_inc, widget.tempCondition.sortOrder == "ascending", () {
                  setState(() {
                    widget.tempCondition.sortOrder = "ascending";
                  });
                }),
                SizedBox(width: 11.5.sp),
                _getSelectView(Lang.order_desc, widget.tempCondition.sortOrder == "descending", () {
                  setState(() {
                    widget.tempCondition.sortOrder = "descending";
                  });
                }),
              ],
            ),
            SizedBox(height: 20.sp),
          ],
        ),
      ),
    );
  }

  Widget _getSelectView(String text, bool isSelected, dynamic onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        width: 104.sp,
        height: 30.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.sp),
          color: isSelected ? colorBrand : Colors.white,
          border: Border.all(color: isSelected ? colorBrand : colorE1, width: 1.0.sp),
        ),
        child: MyText(text, isSelected ? Colors.white : color2B, 12.sp),
      ),
    );
  }

  Widget _getTagView(LingyaTag tag) {
    return GestureDetector(
      onTap: () {
        setState(() {
          if (!widget.tempCondition.tagSelectList.contains(tag)) {
            widget.tempCondition.tagSelectList.add(tag);
          } else {
            widget.tempCondition.tagSelectList.remove(tag);
          }
        });
      },
      child: Container(
        margin: EdgeInsets.only(right: 6.sp, bottom: 6.sp),
        padding: EdgeInsets.fromLTRB(12.sp, 6.sp, 12.sp, 6.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.sp),
          color: widget.tempCondition.tagSelectList.contains(tag) ? colorBrand : Colors.white,
          border:
              Border.all(color: widget.tempCondition.tagSelectList.contains(tag) ? colorBrand : colorE1, width: 1.0.sp),
        ),
        child: MyText(tag.tagName, widget.tempCondition.tagSelectList.contains(tag) ? Colors.white : color2B, 12.sp),
      ),
    );
  }

  Widget _getColorView(String color) {
    return GestureDetector(
      onTap: () {
        setState(() {
          if (!widget.tempCondition.colorSelectList.contains(color)) {
            widget.tempCondition.colorSelectList.add(color);
          } else {
            widget.tempCondition.colorSelectList.remove(color);
          }
        });
      },
      child: Container(
        width: 24.sp,
        height: 24.sp,
        margin: EdgeInsets.only(right: 20.sp, bottom: 10.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.sp),
          border: Border.all(color: string2Color(color), width: 2.0.sp),
        ),
        alignment: Alignment.center,
        child: widget.tempCondition.colorSelectList.contains(color)
            ? Container(
                width: 12.sp,
                height: 12.sp,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.sp),
                  color: string2Color(color),
                ),
              )
            : const SizedBox(),
      ),
    );
  }
}

class SearchCondition {
  int startTime = 0, endTime = 0;
  List<LingyaTag> tagSelectList = [];
  List<String> colorSelectList = [];
  String gender = ""; // male / female
  String sortBy = "createTime"; // name / createTime / modifyTime
  String sortOrder = "descending"; // ascending / descending
  int statusTag = 0;

  SearchCondition({this.statusTag = 0});

  SearchCondition.copy(SearchCondition condition) {
    startTime = condition.startTime;
    endTime = condition.endTime;
    tagSelectList.clear();
    tagSelectList.addAll(condition.tagSelectList);
    colorSelectList.clear();
    colorSelectList.addAll(condition.colorSelectList);
    gender = condition.gender;
    sortBy = condition.sortBy;
    sortOrder = condition.sortOrder;
    statusTag = condition.statusTag;
  }

  bool hasCondition() {
    return startTime > 0 ||
        endTime > 0 ||
        isNotEmpty(tagSelectList) ||
        isNotEmpty(colorSelectList) ||
        isNotEmpty(gender) ||
        sortBy != "createTime" ||
        sortOrder != "descending";
  }

  bool isEqual(SearchCondition condition) {
    return toString() == condition.toString();
  }

  @override
  String toString() {
    return "SearchCondition $colorSelectList-$startTime-$endTime-${tagSelectList.map((e) => e.tagName).toList()}-$gender-$sortBy-$sortOrder";
  }
}
