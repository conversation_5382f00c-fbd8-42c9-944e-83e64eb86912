import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/lingya/lingya_info_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class LingyaSearchPage extends BasePage {
  const LingyaSearchPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaSearchPageState();
  }
}

class LingyaSearchPageState extends BasePageState<LingyaSearchPage> {
  List<LingyaItem> lingyaList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;

  Timer? timer;
  TextEditingController keywordController = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    delay(200, () {
      showKeyboard(focusNode);
    });
  }

  refreshLingyaList() async {
    return await getLingyaList(true);
  }

  onLoadMore() {
    getLingyaList(false);
  }

  getLingyaList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    List filters = [];

    addFilterParam(name, operator, value) {
      filters.add({
        "columnName": name,
        "filterEnum": operator,
        "columnValue": value,
      });
    }

    if (isNotEmpty(keywordController.text)) {
      addFilterParam("index", "like", keywordController.text);
    } else {
      setState(() {
        pageIndex = 0;
        isLoading = false;
        lingyaList.clear();
      });
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    Map<String, dynamic> params = {};
    params = {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "filterParamList": filters,
    };
    HHttp.request(
      "/v3/doctor/case/page",
      "POST",
      (data) {
        List<LingyaItem> list = (data["caseList"] as List).map((i) => LingyaItem.fromJson(i)).toList();
        if (isNotEmpty(list)) {
          List<String> ids = [];
          for (var item in list) {
            if (isNotEmpty(item.avatarFileId)) {
              ids.add(item.avatarFileId);
            }
          }
          downloadThumbnail(
            ids,
            (id, path) {
              for (var item in list) {
                if (item.avatarFileId == id) {
                  setState(() {
                    item.avatarPath = path;
                  });
                }
              }
            },
            "RECORD",
          );
        }

        setState(() {
          allLoad = true;
          if (refresh) {
            lingyaList = list;
            if (lingyaList.length >= pageSize) {
              allLoad = false;
            }
          } else {
            if (isNotEmpty(list)) {
              if (list.length >= pageSize) {
                allLoad = false;
              }
              lingyaList.addAll(list);
            }
          }
          isLoading = false;
        });
      },
      errCallBack: (_) {
        setState(() {
          isLoading = false;
        });
      },
      params: params,
    );
  }

  changeFavor(LingyaItem item) {
    HHttp.request("/v3/doctor/case/update", "POST", (data) {
      setState(() {
        item.subscribe = !item.subscribe;
      });
    }, params: {
      "birthday": item.birthday.toString(),
      "caseId": item.caseId,
      "subscribe": !item.subscribe,
    });
  }

  getItemView(LingyaItem item) {
    return GestureDetector(
      onTap: () {
        push(LingyaInfoPage(item));
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
        width: double.infinity,
        height: 90.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 1.sp,
              spreadRadius: 1.sp,
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 10.sp,
              height: 90.sp,
              decoration: BoxDecoration(
                color: item.getTagColor(),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(16.sp), bottomLeft: Radius.circular(16.sp)),
              ),
            ),
            SizedBox(width: 12.sp),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.sp),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(30.sp),
                        child: isEmpty(item.avatarPath)
                            ? Image.asset(
                                item.gender == "female" ? "res/imgs/avatar_female.png" : "res/imgs/avatar_male.png",
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )
                            : Image.file(
                                File(item.avatarPath),
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )),
                    SizedBox(width: 12.sp),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 250.sp,
                          height: 24.sp,
                          child: Row(
                            children: [
                              Container(
                                constraints: BoxConstraints(maxWidth: 140.sp),
                                child: SingleText(item.name, color2B, 16.sp, FontWeight.w500),
                              ),
                              SizedBox(width: 8.sp),
                              Click(
                                  child: Image.asset(
                                    item.subscribe ? "res/imgs/icon_subscribe.png" : "res/imgs/icon_unsubscribe.png",
                                    width: 16.sp,
                                  ),
                                  onTap: () {
                                    changeFavor(item);
                                  }),
                              const Spacer(),
                              MyText(Global.getDateByTimestamp(milliseconds: item.createTime), colorBB, 12.sp),
                            ],
                          ),
                        ),
                        SizedBox(height: 2.sp),
                        MyText("${item.getGender()}  ${sprintf(Lang.year_old, [Global.getAge(item.birthday)])}",
                            color2B, 12.sp),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 12.sp),
                Row(
                  children: [
                    Image.asset("res/imgs/icon_tag.png", width: 16.sp),
                    getTagView(item),
                  ],
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  getTagView(LingyaItem item) {
    if (isEmpty(item.tagList)) {
      return Padding(
        padding: EdgeInsets.only(left: 8.sp, right: 8.sp),
        child: MyText(Lang.none, colorBB, 12.sp),
      );
    } else {
      List<Widget> tagViews = [
        Padding(
          padding: EdgeInsets.only(left: 8.sp, right: 8.sp),
          child: MyText(item.tagList.first, colorBB, 12.sp),
        )
      ];
      for (int i = 1; i < item.tagList.length; i++) {
        tagViews.add(Container(width: 1.sp, height: 16.sp, color: const Color(0xFFA69ADD)));
        tagViews.add(Padding(
          padding: EdgeInsets.only(left: 8.sp, right: 8.sp),
          child: MyText(item.tagList[i], colorBB, 12.sp),
        ));
      }

      return SizedBox(
        width: 280.sp,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(children: tagViews),
        ),
      );
    }
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < lingyaList.length; i++) {
      allWidget.add(getItemView(lingyaList[i]));
    }
    return allWidget;
  }

// 下拉刷新
  Future onRefresh() async {
    await refreshLingyaList();
  }

  delaySearch() {
    if (timer != null) {
      timer!.cancel();
    }
    timer = Timer(const Duration(milliseconds: 800), () {
      refreshLingyaList();
    });
  }

  @override
  void dispose() {
    if (timer != null) {
      timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
        contentHeight: 90.sp,
        child: Container(
          margin: EdgeInsets.fromLTRB(20.sp, 44.sp, 20.sp, 0),
          child: Row(
            children: [
              Expanded(
                child: Container(
                    height: 38.sp,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.fromLTRB(10.sp, 0, 10.sp, 0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: colorBg),
                      borderRadius: BorderRadius.circular(20.sp),
                    ),
                    child: Stack(
                      children: [
                        TextField(
                          controller: keywordController,
                          focusNode: focusNode,
                          textAlign: TextAlign.start,
                          textAlignVertical: TextAlignVertical.center,
                          maxLength: 20,
                          style: TextStyle(fontSize: 14.sp, color: color2B),
                          onChanged: (value) {
                            delaySearch();
                          },
                          decoration: InputDecoration(
                            prefix: SizedBox(
                              width: 30.sp,
                            ),
                            hintText: Lang.input_name_or_id,
                            hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                            counterText: '',
                            border: InputBorder.none,
                            isDense: true,
                          ),
                        ),
                        Positioned(
                          top: 6.sp,
                          child: Container(
                            padding: EdgeInsets.only(right: 12.sp),
                            child: Image.asset("res/imgs/icon_search2.png", height: 20.sp),
                          ),
                        ),
                      ],
                    )),
              ),
              SizedBox(width: 12.sp),
              Click(
                onTap: () {
                  hideKeyboard();
                  pop();
                },
                child: MyText(Lang.cancel, colorBrand, 14.sp),
              ),
            ],
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: Container(
          width: 1.sw,
          height: 1.sh,
          color: Colors.transparent,
          child: Stack(
            children: [
              Click(
                  onTap: hideKeyboard,
                  child: SizedBox(
                    width: 1.sw,
                    height: 1.sh - 100.sp,
                  )),
              isEmpty(keywordController.text)
                  ? const SizedBox()
                  : RefreshList(
                      childList: getListView(),
                      isLoading: isLoading,
                      onRefresh: onRefresh,
                      onLoadMore: onLoadMore,
                      nullImgName: "res/imgs/empty_pic.png",
                      nullWidget: getEmptyText(),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getEmptyText() {
    int index = Lang.cannot_find_case.indexOf("__");
    String str1 = Lang.cannot_find_case.substring(0, index);
    String str2 = Lang.cannot_find_case.substring(index + 2);
    return RichText(
      text: TextSpan(
        text: str1,
        style: TextStyle(
          fontSize: 24.sp,
          color: colorAB,
        ),
        children: [
          TextSpan(
            text: keywordController.text,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorBrand,
            ),
          ),
          TextSpan(
            text: str2,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorAB,
            ),
          ),
        ],
      ),
    );
  }
}
