import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:sprintf/sprintf.dart';

//全景片分析
class LingyaDetail109Page extends BasePage {
  LingyaFile file;

  LingyaDetail109Page(this.file, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail109PageState();
  }
}

class LingyaDetail109PageState extends BasePageState<LingyaDetail109Page> {
  bool loaded = false, error = false;

  dynamic analyticsJson = {};
  dynamic algorithmJson = {};
  dynamic draftJson = {};
  Map<int, List> diseaseMap = {}; //有疾病的牙齿

  Map badTeethMap = {}; //疾病对照表
  List allTeeth = []; //String
  List hengyaTeeth = []; //String
  List babyTeeth = []; //String
  Set badSet = {};

  String displayImage = "";

  @override
  initState() {
    super.initState();
    getLingyaPointsConfigJson((json) {
      badTeethMap = (json as Map)["xrayfront"] as Map;
      _getAnalyticsInfo();
    });
  }

  _getAnalyticsInfo() {
    if (isNotEmpty(widget.file.analysisInfoMap)) {
      getAnalyticsInfo(
        widget.file.getAnalyticsId(),
        (data) {
          analyticsJson = data;
          if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
            getDraftContent(data["draft"], (draft) {
              logger("getDraftContent: $draft");
              setState(() {
                loaded = true;
                draftJson = draft["draft"];
                allTeeth = (draftJson["diseaseMap"] as Map).keys.toList();
                hengyaTeeth = allTeeth.where((element) => teethIconMap.keys.contains(element)).toList();
                babyTeeth = allTeeth.where((element) => babyTeethIconMap.keys.contains(element)).toList();
                _getDiseaseMapFromDraft();
                if (isNotEmpty(draftJson["pptFileMap"]) && isNotEmpty(draftJson["pptFileMap"]["displayId"])) {
                  String fileId = draftJson["pptFileMap"]["displayId"];
                  downloadBase64(fileId, (image) {
                    setState(() {
                      displayImage = image.substring(1, image.length - 1).replaceAll(RegExp(r'\s'), '').split(',').last;
                    });
                  });
                }
              });
            });
          } else if (isNotEmpty(data["algorithmInfoMap"])) {
            String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
            getAlgorithmResult(algorithmId, (data) {
              setState(() {
                loaded = true;
                algorithmJson = data;
                allTeeth = (algorithmJson["teeth"] as Map).keys.toList();
                hengyaTeeth = allTeeth.where((element) => teethIconMap.keys.contains(element)).toList();
                babyTeeth = allTeeth.where((element) => babyTeethIconMap.keys.contains(element)).toList();
                _getDiseaseMap();
              });
            });
          } else {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          }
        },
      );
    }
  }

  _getDiseaseMapFromDraft() {
    for (String number in (draftJson["diseaseMap"] as Map).keys) {
      dynamic disease = (draftJson["diseaseMap"][number]["disease"] as Map);
      for (String diseaseNo in disease.keys) {
        if (disease[diseaseNo]["show"] ?? false) {
          if (diseaseMap[int.parse(diseaseNo)] == null) {
            diseaseMap[int.parse(diseaseNo)] = [];
          }
          if (!diseaseMap[int.parse(diseaseNo)]!.contains(int.parse(number))) {
            diseaseMap[int.parse(diseaseNo)]!.add(int.parse(number));
          }
        }
      }
    }
    for (int key in diseaseMap.keys) {
      badSet.addAll(diseaseMap[key]!.toList());
    }
  }

  _getDiseaseMap() {
    if (isNotEmpty(algorithmJson) && isNotEmpty(algorithmJson["disease"])) {
      for (dynamic disease in (algorithmJson["disease"] as Map).values) {
        int label = disease["label"];
        if (diseaseMap[label] == null) {
          diseaseMap[label] = [];
        }
        diseaseMap[disease["label"]]!.addAll(disease["map"] as List);
      }
    }
    for (int key in diseaseMap.keys) {
      badSet.addAll(diseaseMap[key]!.toList());
    }
  }

  _buildList() {
    List<Widget> children = [
      Align(
        alignment: Alignment.topCenter,
        child: getOriginPhoto(displayImage, widget.file),
      )
    ];
    if (isEmpty(algorithmJson) && isEmpty(draftJson)) {
      children.add(Container(
        padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
        child: Center(
          child: MyText(
              loaded
                  ? error
                      ? Lang.ai_status_fail
                      : Lang.no_analytic_result
                  : Lang.in_analytic,
              color2B,
              14.sp),
        ),
      ));
    } else {
      children.addAll([
        Padding(
          padding: EdgeInsets.only(top: 24.sp),
          child: MyText(Lang.analytic_result, color2B, 20.sp, FontWeight.w600),
        ),
        _getCountText(),
        Padding(
          padding: EdgeInsets.only(top: 24.sp, left: 12.sp),
          child: MyText(Lang.permanent_tooth, color2B, 14.sp, FontWeight.w500),
        ),
        _getTeethView(),
        _getTeethTagView(),
        _getTeethLostTagView(),
        _getBabyTeethContainer(),
        getSloganView(),
      ]);
    }
    return children;
  }

  _getCountText() {
    // int hengyaCount = hengyaTeeth.length;
    int missingCount = teethIconMap.keys.where((element) => int.parse(element) % 10 < 8).length -
        hengyaTeeth.where((element) => int.parse(element) % 10 < 8).length;
    // int rouyaCount = allTeeth.where((element) => int.parse(element) >= 50 && int.parse(element) < 90).length;
    // int duoshengCount = allTeeth.where((element) => int.parse(element) >= 90).length;

    return Container(
      margin: EdgeInsets.only(top: 12.sp),
      padding: EdgeInsets.all(12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyText(sprintf(Lang.all_teeth_count, [allTeeth.length]), color2B, 14.sp, FontWeight.w500),
          SizedBox(height: 12.sp),
          SizedBox(
            width: 1.sw - 64.sp,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.all(4.sp),
                  child: Icon(IconFont.icon_fangan, size: 14.sp, color: colorToothNormal),
                ),
                HeightText(sprintf(Lang.normal_teeth_count, [allTeeth.length - badSet.length]), color2B, 14.sp, 1.5),
                Expanded(
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 6.sp),
                    child: Container(
                      width: 1.sp,
                      height: 34.sp,
                      color: colorE8,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(4.sp),
                  child: Icon(IconFont.icon_fangan, size: 14.sp, color: colorRed_1),
                ),
                HeightText(sprintf(Lang.loss_teeth_count, [missingCount]), color2B, 14.sp, 1.5),
                Expanded(
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(top: 6.sp),
                    child: Container(
                      width: 1.sp,
                      height: 34.sp,
                      color: colorE8,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(4.sp),
                  child: Icon(IconFont.icon_fangan, size: 14.sp, color: colorToothDisease),
                ),
                HeightText(sprintf(Lang.abnormal_teeth_count, [badSet.length]), color2B, 14.sp, 1.5),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _getTeethView() {
    List<Widget> upList = [];
    for (int i = 18; i >= 11; i--) {
      upList.add(_getSingleTooth(i, true));
    }
    for (int i = 21; i <= 28; i++) {
      upList.add(_getSingleTooth(i, true));
    }
    List<Widget> downList = [];
    for (int i = 48; i >= 41; i--) {
      downList.add(_getSingleTooth(i, false));
    }
    for (int i = 31; i <= 38; i++) {
      downList.add(_getSingleTooth(i, false));
    }
    return Container(
      margin: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
      child: Column(
        children: [
          Row(children: upList),
          Row(children: downList),
        ],
      ),
    );
  }

  _getBabyTeethView() {
    List<Widget> upList = [];
    for (int i = 55; i >= 51; i--) {
      upList.add(_getSingleTooth(i, true, true));
    }
    for (int i = 61; i <= 65; i++) {
      upList.add(_getSingleTooth(i, true, true));
    }
    List<Widget> downList = [];
    for (int i = 75; i >= 71; i--) {
      downList.add(_getSingleTooth(i, false, true));
    }
    for (int i = 81; i <= 85; i++) {
      downList.add(_getSingleTooth(i, false, true));
    }
    return Container(
      margin: EdgeInsets.fromLTRB(0, 8.sp, 0, 8.sp),
      child: Column(
        children: [
          Row(children: upList),
          Row(children: downList),
        ],
      ),
    );
  }

  _getToothIcon(int index) {
    String number = index.toString();
    bool isMissed = !hengyaTeeth.contains(number);
    bool isZhichi = index % 10 == 8;
    bool isDisease = diseaseMap.values.where((list) => list.contains(index)).isNotEmpty;

    getColor() {
      if (isDisease) {
        return colorToothDisease;
      } else if (isMissed) {
        return isZhichi ? colorE1 : colorRed_1;
      } else {
        return colorToothNormal;
      }
    }

    return Icon(
      teethIconMap[number],
      size: 24.sp,
      color: getColor(),
    );
  }

  _getBabyToothIcon(int index) {
    String number = index.toString();
    bool isMissed = !babyTeeth.contains(number);
    bool isDisease = diseaseMap.values.where((list) => list.contains(index)).isNotEmpty;

    getColor() {
      if (isDisease) {
        return colorToothDisease;
      } else if (isMissed) {
        return colorE1;
      } else {
        return colorToothNormal;
      }
    }

    return Icon(
      babyTeethIconMap[number],
      size: 24.sp,
      color: getColor(),
    );
  }

  _getSingleTooth(int index, bool up, [bool isBaby = false]) {
    String number = index.toString();
    List<Widget> children = [
      isBaby ? _getBabyToothIcon(index) : _getToothIcon(index),
      SizedBox(height: 6.sp),
      MyText(number, color2B, 12.sp),
    ];

    return Container(
      width: (1.sw - 64.sp) / (isBaby ? 10 : 16),
      height: 64.sp,
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: up ? children : children.reversed.toList(),
      ),
    );
  }

  _getTeethTagView() {
    List<Widget> children = [];
    for (String number in hengyaTeeth) {
      String disease = "";
      for (int key in diseaseMap.keys) {
        if (diseaseMap[key]!.contains(int.parse(number))) {
          if (isEmpty(disease)) {
            disease = badTeethMap[key.toString()]["des${getLangStr()}"];
          } else {
            disease += ", ${badTeethMap[key.toString()]["des${getLangStr()}"]}";
          }
        }
      }
      if (disease.isNotEmpty) {
        disease += "  ";
        children.add(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                  margin: EdgeInsets.only(top: 4.sp, right: 6.sp, bottom: 4.sp),
                  padding: EdgeInsets.fromLTRB(8.sp, 2.sp, 8.sp, 2.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.sp),
                    color: colorToothDisease.withOpacity(0.2),
                  ),
                  child: MyText("#$number", colorToothDisease, 12.sp)),
              MyText(disease, color2B, 14.sp),
            ],
          ),
        );
      }
    }
    return Padding(
      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
      child: Wrap(children: children),
    );
  }

  _getTeethLostTagView() {
    List lostTeeth =
        teethIconMap.keys.where((element) => !hengyaTeeth.contains(element) && int.parse(element) % 10 < 8).toList();
    if (isEmpty(lostTeeth)) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Container(
              margin: EdgeInsets.only(top: 4.sp, right: 10.sp, bottom: 4.sp),
              padding: EdgeInsets.fromLTRB(8.sp, 2.sp, 8.sp, 2.sp),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.sp),
                color: colorRed_1.withOpacity(0.2),
              ),
              child: MyText("#${lostTeeth.join("、")}", colorRed_1, 12.sp)),
          MyText(Lang.teeth_lost, color2B, 14.sp),
        ],
      ),
    );
  }

  _getBabyTeethTagView() {
    List<Widget> children = [];
    for (String number in babyTeeth) {
      String disease = "";
      for (int key in diseaseMap.keys) {
        if (diseaseMap[key]!.contains(int.parse(number))) {
          disease += "${badTeethMap[key.toString()]["des"]}  ";
        }
      }
      if (disease.isNotEmpty) {
        children.add(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                  margin: EdgeInsets.only(top: 4.sp, right: 6.sp, bottom: 4.sp),
                  padding: EdgeInsets.fromLTRB(8.sp, 2.sp, 8.sp, 2.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.sp),
                    color: colorToothDisease.withOpacity(0.2),
                  ),
                  child: MyText("#$number", colorToothDisease, 12.sp)),
              MyText(disease, color2B, 14.sp),
            ],
          ),
        );
      }
    }
    return Wrap(children: children);
  }

  _getBabyTeethContainer() {
    if (isEmpty(babyTeeth)) {
      return const SizedBox();
    }
    return Container(
      margin: EdgeInsets.only(top: 20.sp),
      padding: EdgeInsets.fromLTRB(12.sp, 20.sp, 12.sp, 20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyText(Lang.baby_tooth, color2B, 14.sp, FontWeight.w500),
          _getBabyTeethView(),
          _getBabyTeethTagView(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(categoryMap[widget.file.category] ?? widget.file.name),
      ),
      body: Padding(
        padding: EdgeInsets.all(20.sp),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _buildList(),
          ),
        ),
      ),
    );
  }
}
