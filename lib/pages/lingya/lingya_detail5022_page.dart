import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/lingya/lingya_text_convert.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

import '../../manager/calculate_manager.dart';

//骨龄分析
class LingyaDetail5022Page extends BasePage {
  LingyaFile file;
  String id;

  LingyaDetail5022Page(this.file, this.id, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail5022PageState();
  }
}

class LingyaDetail5022PageState extends BasePageState<LingyaDetail5022Page> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final double itemHeight = 38.sp;
  bool loaded = false, error = false;
  dynamic draftJson = {};
  dynamic boneSettingJson = {};
  double ruler = 10;

  int selectIndex = 0;
  String selectKey = "";

  List<String> titleList = ["骨龄定性分析测量", "骨龄定量分析法", "深度学习分析法"];
  int cvs = 0;
  Map learningMap = {};

  String displayImage = "";

  Map boneConfig = {
    "骨龄定量分析法": {
      "@2": {
        "测量项目": "@2",
        "describe": "C2椎体凹陷角度 ",
        "pointArray": [100202, 100200, 100204],
        "algorithm": 2
      },
      "AH3": {
        "测量项目": "AH3",
        "describe": "C3椎体前部高度 ",
        "pointArray": [100232, 100224, 100228],
        "algorithm": 4
      },
      "PH3": {
        "测量项目": "PH3",
        "describe": "C3椎体后部高度 ",
        "pointArray": [100220, 100224, 100228],
        "algorithm": 4
      },
      "AH3/PH3": {
        "测量项目": "AH3/PH3",
        "describe": "C3椎体前部高度与后部高度的比值 ",
        "pointArray": [100232, 100224, 100228, 100220, 100224, 100228],
        "algorithm": 16
      },
      "H4": {
        "测量项目": "H4",
        "describe": "C4椎体高度 ",
        "pointArray": [100254, 100244, 100248],
        "algorithm": 4
      },
      "W4": {
        "测量项目": "W4",
        "describe": "C4椎体宽度 ",
        "pointArray": [100250, 100240, 100244],
        "algorithm": 4
      },
      "H4/W4": {
        "测量项目": "H4/W4",
        "describe": "C4椎体高度与宽度的比值 ",
        "pointArray": [100254, 100244, 100248, 100250, 100240, 100244],
        "algorithm": 16
      },
      "CVMS": {"测量项目": "CVMS", "describe": "–4.13 ＋3.57× H4/W4 ＋4.07 × AH3/PH3＋ 0.03 × @2"}
    },
  };
  Map boneSetting = {
    //骨龄定性分析测量
    // "骨龄定性分析测量": {
    "C2-1": {"椎骨": "C2-1", "period": "CS1起始期", "describe": "下缘平坦"},
    "C2-2": {"椎骨": "C2-2", "period": "CS2快速期", "describe": "下缘有凹陷"},
    "C2-3": {"椎骨": "C2-3", "period": "CS3过渡期", "describe": "下缘有凹陷"},
    "C2-4": {"椎骨": "C2-4", "period": "CS4减速期", "describe": "下缘有凹陷"},
    "C2-5": {"椎骨": "C2-5", "period": "CS5成熟期", "describe": "下缘有凹陷"},
    "C2-6": {"椎骨": "C2-6", "period": "CS6完成期", "describe": "下缘有明显凹陷"},
    "C3-1": {"椎骨": "C3-1", "period": "CS1起始期", "describe": "下缘平坦，呈梯形"},
    "C3-2": {"椎骨": "C3-2", "period": "CS2快速期", "describe": "下缘有凹陷，呈梯形"},
    "C3-3": {"椎骨": "C3-3", "period": "CS3过渡期", "describe": "下缘有凹陷，呈梯形或水平矩形"},
    "C3-4": {"椎骨": "C3-4", "period": "CS4减速期", "describe": "下缘有凹陷，呈水平梯形"},
    "C3-5": {"椎骨": "C3-5", "period": "CS5成熟期", "describe": "下缘有凹陷，呈方形或水平矩形"},
    "C3-6": {"椎骨": "C3-6", "period": "CS6完成期", "describe": "下缘有明显凹陷，呈方形或垂直矩形"},
    "C4-1": {"椎骨": "C4-1", "period": "CS1起始期", "describe": "下缘平坦，呈梯形"},
    "C4-2": {"椎骨": "C4-2", "period": "CS2快速期", "describe": "下缘有凹陷，呈梯形"},
    "C4-3": {"椎骨": "C4-3", "period": "CS3过渡期", "describe": "下缘有凹陷，呈梯形或水平矩形"},
    "C4-4": {"椎骨": "C4-4", "period": "CS4减速期", "describe": "下缘有凹陷，呈水平矩形"},
    "C4-5": {"椎骨": "C4-5", "period": "CS5成熟期", "describe": "下缘有凹陷，呈方形或水平矩形"},
    "C4-6": {"椎骨": "C4-6", "period": "CS6完成期", "describe": "下缘凹陷明显，呈方形或垂直矩形"}
    // }
  };

  @override
  initState() {
    super.initState();
    getBoneConfigJson((json) {
      if (isNotEmpty(json) && isNotEmpty(json["骨龄定量分析法"])) {
        boneConfig = {"骨龄定量分析法": json["骨龄定量分析法"]};
        logger("_buildRationList $boneConfig");
      }
      if (isNotEmpty(json) && isNotEmpty(json["骨龄定性分析测量"])) {
        boneSetting = json["骨龄定性分析测量"];
      }
      _getAnalyticsInfo();
    });
    getBoneRuleConfigJson((json) {
      setState(() {
        boneSettingJson = json;
      });
    });
    // getBoneDlConfigJson((json) {
    //   setState(() {
    //     learningMap = json;
    //   });
    // });
  }

  _getAnalyticsInfo() {
    getAnalyticsInfo(
      widget.id,
      (data) {
        setState(() {
          if (isNotEmpty(data["algorithmInfoMap"])) {
            try {
              ruler = double.parse(data["shareSetting"]["ruler"]);
            } catch (e) {
              //
            }
            String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
            getAlgorithmResult(algorithmId, (data) {
              setState(() {
                cvs = (data["cvs"] as double).ceil();
                if (isEmpty(data["draft"])) {
                  startBoneMeasure(data);
                }
                onSelectTitle(titleList.first, 0);
                loaded = true;
              });
            });
          } else {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          }

          if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
            getDraftContent(data["draft"], (draft) {
              logger("getDraftContent: $draft");
              Map draftJson = draft["draft"];
              if (isNotEmpty(draftJson["pptFileMap"]) && isNotEmpty(draftJson["pptFileMap"]["displayId"])) {
                String fileId = draftJson["pptFileMap"]["displayId"];
                downloadBase64(fileId, (image) {
                  setState(() {
                    displayImage = image.substring(1, image.length - 1).replaceAll(RegExp(r'\s'), '').split(',').last;
                  });
                });
              }
            });
            onSelectTitle(titleList.first, 0);
          }
        });
      },
    );
  }

  // 骨龄数据
  startBoneMeasure(data) {
    Map info = {};
    info["rationRes"] = getBoneRationRes(info, data);
    // info["deepRes"] = learningMap[timeDeep[cvs]] ?? "...";
    // info["deepTime"] = timeDeep[cvs];
    Map boneRepairInfo = {
      'C2': {'period': 'CS1起始期', 'describe': '下缘平坦', 'boneIcon': 1, 'boneIconFont': 32},
      'C3': {'period': 'CS1起始期', 'describe': '下缘平坦', 'boneIcon': 1, 'boneIconFont': 24},
      'C4': {'period': 'CS1起始期', 'describe': '下缘平坦', 'boneIcon': 1, 'boneIconFont': 22}
    };
    setState(() {
      draftJson["reportRes"] = info["rationRes"];
      if (isNotEmpty(data["vertebrae_labels"])) {
        List labels = data["vertebrae_labels"] as List;
        for (int i = 0; i < boneRepairInfo.keys.length && i < labels.length; i++) {
          int label = labels[i];
          String key = boneRepairInfo.keys.toList()[i];
          boneRepairInfo[key]['describe'] = boneSetting["$key-$label"]['describe${getLangStr()}'];
          boneRepairInfo[key]['boneIcon'] = label;
        }
        draftJson["boneSetting"] = boneRepairInfo;
      }
    });
    return info;
  }

  // 骨龄定量分析
  getBoneRationRes(Map obj, Map data) {
    double ratio = 1.0;
    var point1 = data["kps"]['100000'];
    var point2 = data["kps"]['100001'];
    if (isNotEmpty(point1) && isNotEmpty(point2)) {
      ratio = math.sqrt(math.pow(point1[1] - point2[1], 2) + math.pow(point2[0] - point1[0], 2)) / ruler;
    }
    var algorithmRatio = [3, 4, 5, 6, 7, 9, 13, 15];

    for (String fn in boneConfig.keys) {
      Map info = boneConfig[fn];
      for (String key in info.keys) {
        Map target = info[key];
        int? algorithm = target["algorithm"];
        if (algorithm != null) {
          algorithm = algorithm ~/ 1;
          try {
            List<List<double>> pointArray = [];
            for (int item in target["pointArray"]) {
              List point = data["kps"][item.toString()];
              pointArray.add([point[0], point[1]]);
            }
            double? res = CalculateTool110.instance.getAlgorithmResult(algorithm, pointArray);
            if (res == null) {
              break;
            }
            res = res * 1.0;

            if (algorithmRatio.contains(algorithm)) {
              res = res / ratio;
            }
            target["res"] = res.toStringAsFixed(2);
          } catch (e) {
            //
          }
        }
      }

      Map endRes = info['CVMS'];
      if (isNotEmpty(endRes)) {
        Map endMap = {
          'H4/W4': 0,
          'AH3/PH3': 0,
          '@2': 0,
        };
        for (String key in endMap.keys) {
          if (isNotEmpty(info[key]) && isNotEmpty(info[key]["res"])) {
            endMap[key] = double.parse(info[key]["res"]);
          }
        }
        double cvms = -4.13 + 3.57 * endMap['H4/W4']! + 4.07 * endMap['AH3/PH3']! + 0.03 * endMap['@2']!;
        endRes["res"] = cvms.toStringAsFixed(4);
        endRes["descript"] = _getTime(cvms);
        obj["rationFun"] = _getTime(cvms);
      }
    }
    return boneConfig;
  }

  String _getTime(double cvms) {
    int cvmsIndex = 0;
    for (int i = 0; i < rationSection.length; i++) {
      if (cvms > rationSection[i]) {
        cvmsIndex = i + 1;
      }
    }
    String cvmsStr = "";
    if (cvmsIndex == 0) {
      cvmsStr = "CVMS < ${rationSection[0]}";
    } else if (cvmsIndex >= rationSection.length) {
      cvmsStr = "CVMS > ${rationSection.last}";
    } else {
      cvmsStr = "${rationSection[cvmsIndex - 1]} < CVMS < ${rationSection[cvmsIndex]}";
    }
    return cvmsStr;
  }

  _getTitleDropView() {
    return Container(
        height: 38.sp,
        margin: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 0),
        padding: EdgeInsets.fromLTRB(12.sp, 0, 6.sp, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: colorBrand.withOpacity(0.8),
          ),
          borderRadius: BorderRadius.circular(8.sp),
        ),
        child: DropdownButton<String>(
          value: selectKey,
          iconEnabledColor: colorBrand,
          underline: const SizedBox(height: 0),
          items: titleList
              .map((key) => DropdownMenuItem<String>(
                    value: key,
                    child: MyText(
                      getBoneAnalyticsStr(key),
                      selectKey == key ? colorBrand : color2B,
                      14.sp,
                      selectKey == key ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              onSelectTitle(value!, titleList.indexOf(value));
            });
          },
        ));
  }

  void onSelectTitle(String key, int index) {
    setState(() {
      selectIndex = index;
      selectKey = key;
    });
  }

  List<Widget> _buildFormList() {
    if (isEmpty(draftJson) || isEmpty(draftJson["reportRes"])) {
      return [
        Container(
          padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
          child: Center(
            child: MyText(
                loaded
                    ? error
                        ? Lang.ai_status_fail
                        : Lang.no_analytic_result
                    : Lang.in_analytic,
                color2B,
                14.sp),
          ),
        )
      ];
    } else {
      List<Widget> children = [
        Padding(
          padding: EdgeInsets.only(top: 20.sp),
          child: getOriginPhoto(displayImage, widget.file),
        ),
        Align(
          alignment: Alignment.topLeft,
          child: _getTitleDropView(),
        ),
        // Align(
        //   alignment: Alignment.topLeft,
        //   child: Padding(
        //     padding: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 12.sp),
        //     child: MyText(selectKey, color2B, 16.sp, FontWeight.w500),
        //   ),
        // ),
      ];

      Map report = draftJson["reportRes"] as Map;

      switch (selectIndex) {
        case 0:
          if (isNotEmpty(draftJson["boneSetting"])) {
            children.add(_buildQualityForm(draftJson["boneSetting"] as Map));
          }
          break;
        case 1:
          String type2 = titleList[1];
          if (isNotEmpty(report[type2])) {
            children.add(_buildRationForm(report[type2] as Map));
            children.add(_buildRationList(report[type2] as Map));
          }
          break;
        case 2:
          if (cvs > 0) {
            children.add(_buildLearningForm(report[titleList[1]] as Map));
          }
          break;
      }
      children.add(getSloganView());
      return children;
    }
  }

  _buildIconRow(String text1, String text2, {IconData? icon, bool bold = false}) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
      fontWeight: bold ? FontWeight.w600 : FontWeight.w400,
    );
    return Container(
      width: 1.sw,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
      decoration: BoxDecoration(
        border: bold ? const Border() : Border(top: BorderSide(color: color2B.withOpacity(0.05))),
      ),
      constraints: BoxConstraints(minHeight: itemHeight),
      child: Row(
        children: [
          SizedBox(width: 0.4.sw - 64.sp, child: Text(text1, style: style)),
          SizedBox(
              width: 0.25.sw,
              child: icon != null
                  ? Icon(
                      icon,
                      color: color2B,
                      size: 30.sp,
                    )
                  : const SizedBox()),
          SizedBox(width: 0.35.sw, child: Text(text2, style: style)),
        ],
      ),
    );
  }

  _buildTextRow(List<String> texts, {bool bold = false}) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
      fontWeight: bold ? FontWeight.w600 : FontWeight.w400,
    );
    return Container(
      width: 1.sw,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
      decoration: BoxDecoration(
        border: bold ? const Border() : Border(top: BorderSide(color: color2B.withOpacity(0.05))),
      ),
      constraints: BoxConstraints(minHeight: bold ? itemHeight : 48.sp),
      child: Row(
        children: [
          SizedBox(width: 0.3.sw, child: Text(texts[0], style: style)),
          SizedBox(width: 0.3.sw - 36.sp, child: Text(texts[1], style: style)),
          SizedBox(width: 0.4.sw - 30.sp, child: Text(texts[2], style: style)),
        ],
      ),
    );
  }

  _buildQualityForm(Map map) {
    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
              width: 1.sw - 40.sp,
              constraints: BoxConstraints(minHeight: itemHeight),
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(left: 12.sp),
              decoration: BoxDecoration(
                color: colorF3,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.sp),
                  topRight: Radius.circular(8.sp),
                ),
              ),
              child: MyText(Lang.stage, color2B, 14.sp, FontWeight.w600)),
          Container(
              width: 1.sw - 40.sp,
              constraints: BoxConstraints(minHeight: 48.sp),
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(left: 12.sp),
              child: MyText(
                  isEmpty(boneSettingJson)
                      ? "-"
                      : getBonePeriodStr(boneSettingJson[(map["C2"]["boneIcon"] ~/ 1).toString()]
                          [(map["C3"]["boneIcon"] ~/ 1).toString()][(map["C4"]["boneIcon"] ~/ 1).toString()]),
                  color2B,
                  14.sp)),
          Container(
              width: 1.sw - 40.sp,
              constraints: BoxConstraints(minHeight: itemHeight),
              color: colorF3,
              child: _buildIconRow(Lang.stage_profile, Lang.stage_phase, bold: true)),
          ...map.keys
              .map((key) => Container(
                  constraints: BoxConstraints(minHeight: 48.sp),
                  child: _buildIconRow(key, map[key]["describe"],
                      icon: iconMap["icon_CS${map[key]["boneIcon"] ~/ 1}_$key"])))
              .toList(),
        ],
      ),
    );
  }

  // Map timeDeep = {
  //   1: 'CS1起始期',
  //   2: 'CS2快速期',
  //   3: 'CS3过渡期',
  //   4: 'CS4减速期',
  //   5: 'CS5成熟期',
  //   6: 'CS6完成期',
  // };
  // Map rationTimeEnum = {
  //   1: '第Ⅰ期加速期',
  //   2: '第Ⅱ期高速期',
  //   3: '第Ⅲ期减速期',
  //   4: '第Ⅳ期完成期',
  // };
  List<double> rationSection = [1.7404, 2.623, 3.5199];

  Map iconMap = {
    "icon_CS6_C4": IconFont.icon_CS6_C4,
    "icon_CS6_C3": IconFont.icon_CS6_C3,
    "icon_CS5_C4": IconFont.icon_CS5_C4,
    "icon_CS5_C3": IconFont.icon_CS5_C3,
    "icon_CS4_C4": IconFont.icon_CS4_C4,
    "icon_CS6_C2": IconFont.icon_CS6_C2,
    "icon_CS5_C2": IconFont.icon_CS5_C2,
    "icon_CS4_C3": IconFont.icon_CS4_C3,
    "icon_CS4_C2": IconFont.icon_CS4_C2,
    "icon_CS3_C4": IconFont.icon_CS3_C4,
    "icon_CS3_C3": IconFont.icon_CS3_C3,
    "icon_CS3_C2": IconFont.icon_CS3_C2,
    "icon_CS2_C4": IconFont.icon_CS2_C4,
    "icon_CS2_C3": IconFont.icon_CS2_C3,
    "icon_CS2_C2": IconFont.icon_CS2_C2,
    "icon_CS1_C4": IconFont.icon_CS1_C4,
    "icon_CS1_C3": IconFont.icon_CS1_C3,
    "icon_CS1_C2": IconFont.icon_CS1_C2,
  };

  _buildRationForm(Map map) {
    double cvms = double.parse(map["CVMS"]["res"].toString());
    int cvmsIndex = 0;
    for (int i = 0; i < rationSection.length; i++) {
      if (cvms > rationSection[i]) {
        cvmsIndex = i + 1;
      }
    }
    String cvmsStr = _getTime(cvms);
    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
        Container(
            width: 1.sw - 40.sp,
            constraints: BoxConstraints(minHeight: itemHeight),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 12.sp),
            decoration: BoxDecoration(
              color: colorF3,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.sp),
                topRight: Radius.circular(8.sp),
              ),
            ),
            child: MyText(Lang.stage_phase, color2B, 14.sp, FontWeight.w600)),
        Container(
          width: 1.sw - 40.sp,
          constraints: BoxConstraints(minHeight: 48.sp),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 12.sp),
          child: MyText(getBoneRation(cvmsIndex + 1), color2B, 14.sp),
        ),
        Container(
            width: 1.sw - 40.sp,
            constraints: BoxConstraints(minHeight: itemHeight),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 12.sp),
            color: colorF3,
            child: MyText(Lang.qcvm_judge, color2B, 14.sp, FontWeight.w600)),
        Container(
          width: 1.sw - 40.sp,
          constraints: BoxConstraints(minHeight: 48.sp),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 12.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8.sp),
              bottomRight: Radius.circular(8.sp),
            ),
          ),
          child: MyText(
            cvmsStr,
            color2B,
            14.sp,
          ),
        ),
      ]),
    );
  }

  _buildRationList(Map map) {
    List<Widget> children = [
      Container(
        width: 1.sw - 40.sp,
        constraints: BoxConstraints(minHeight: itemHeight),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: colorF3,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.sp),
            topRight: Radius.circular(8.sp),
          ),
        ),
        child: _buildTextRow([Lang.measure_name, Lang.measure_norm, Lang.measure_desc], bold: true),
      )
    ];
    for (String title in map.keys) {
      children.add(_buildTextRow([title, map[title]["res"].toString(), map[title]["describe${getLangStr()}"]]));
    }

    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: children,
      ),
    );
  }

  _buildLearningForm(Map map) {
    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
        Container(
            width: 1.sw - 40.sp,
            constraints: BoxConstraints(minHeight: itemHeight),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 12.sp),
            decoration: BoxDecoration(
              color: colorF3,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.sp),
                topRight: Radius.circular(8.sp),
              ),
            ),
            child: MyText(Lang.stage, color2B, 14.sp, FontWeight.w600)),
        Container(
          width: 1.sw - 40.sp,
          constraints: BoxConstraints(minHeight: 48.sp),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 12.sp),
          child: MyText(getBonePeriod(cvs), color2B, 14.sp),
        ),
        Container(
            width: 1.sw - 40.sp,
            constraints: BoxConstraints(minHeight: itemHeight),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 12.sp),
            color: colorF3,
            child: MyText(Lang.conclusion, color2B, 14.sp, FontWeight.w600)),
        Container(
          width: 1.sw - 40.sp,
          constraints: BoxConstraints(minHeight: 48.sp),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8.sp),
              bottomRight: Radius.circular(8.sp),
            ),
          ),
          child: HeightText(getBoneDl(cvs), color2B, 14.sp, 1.5),
        ),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: _buildFormList(),
      ),
    );
  }
}
