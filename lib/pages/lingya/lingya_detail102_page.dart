import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/calculate_manager.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/paint_manager.dart';
import 'package:mooeli/model/calculate_data.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

//微笑照分析
class LingyaDetail102Page extends BasePage {
  LingyaFile file;

  LingyaDetail102Page(this.file, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail102PageState();
  }
}

class LingyaDetail102PageState extends BasePageState<LingyaDetail102Page> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  int imageWidth = 0, imageHeight = 0;
  double canvasWidth = 335.sp, canvasHeight = 0;

  MyPainter myPainter = MyPainter();

  final double itemHeight = 38.sp;
  bool loaded = false, error = false;
  CalResult102? calResult;
  String midResult = "";

  Map pointsMap = {};

  @override
  initState() {
    super.initState();
    _getAnalyticsInfo();
    _getImageSize();
  }

  _getImageSize() {
    getImageSize(widget.file.imagePath, (width, height) {
      setState(() {
        imageWidth = width;
        imageHeight = height;
        canvasWidth = 335.sp;
        canvasHeight = canvasWidth * imageHeight / imageWidth;
      });
      logger("getDraftContent getImageSize image: $imageWidth*$imageHeight, canvas: $canvasWidth*$canvasHeight");
    });
  }

  _getAnalyticsInfo() {
    if (isNotEmpty(widget.file.analysisInfoMap)) {
      getAnalyticsInfo(
        widget.file.getAnalyticsId(),
        (data) {
          if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
            getDraftContent(data["draft"], (draft) {
              logger("getDraftContent: ${draft.keys}");
              setState(() {
                loaded = true;
                pointsMap = draft["draft"]["points"] as Map;
                logger("getDraftContent pointsMap: ${pointsMap.values.map((e) => [e["x"], e["y"]]).toList()}");
                _drawPoints();
                _startCalculateResult(draft["draft"]["rotate"]);
              });
            });
          } else if (isEmpty(data["algorithmInfoMap"])) {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          } else {
            getDraftPoints(data, (json) {
              setState(() {
                loaded = true;
                if (isNotEmpty(json)) {
                  pointsMap = json;
                  _drawPoints();
                  _startCalculateResult(0);
                }
              });
            });
          }
        },
      );
    }
  }

  _drawPoints() {
    List pointArray = [];
    for (String key in pointsMap.keys) {
      pointArray.add(
          {"x": pointsMap[key]["x"] * canvasWidth / imageWidth, "y": pointsMap[key]["y"] * canvasHeight / imageHeight});
    }
    logger("onSelectTab pointArray: $pointArray");
    myPainter.points = pointArray;

    try {
      myPainter.centerLine = Line(
        pointsMap["020100"]["x"] * canvasWidth / imageWidth,
        pointsMap["020100"]["y"] * canvasHeight / imageHeight,
        pointsMap["020101"]["x"] * canvasWidth / imageWidth,
        pointsMap["020101"]["y"] * canvasHeight / imageHeight,
      );
    } catch (ex) {
      myPainter.centerLine = Line(0, 0, 0, 0);
    }
  }

  _startCalculateResult(double rotate) {
    setState(() {
      calResult = CalculateTool102.instance.getGapSpaceRes(rotate, pointsMap);
      int midPos = CalculateTool102.instance.calcFaceTeethMidlinePos(pointsMap);
      midResult = midPos == 0
          ? Lang.midline_normal
          : midPos == 1
              ? Lang.midline_left
              : Lang.midline_right;
    });
  }

  _buildFormList() {
    List<Widget> children = [
      SizedBox(height: 20.sp),
      Stack(
        children: [
          getDetailImageSw(widget.file, () => viewLingyaImage(widget.file)),
          GestureDetector(
            onTap: () => viewLingyaImage(widget.file),
            child: CustomPaint(
              size: Size(canvasWidth, canvasHeight),
              painter: myPainter,
            ),
          ),
        ],
      )
    ];
    if (isEmpty(pointsMap)) {
      children.add(Container(
        padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
        child: Center(
          child: MyText(
              loaded
                  ? error
                      ? Lang.ai_status_fail
                      : Lang.no_analytic_result
                  : Lang.in_analytic,
              color2B,
              14.sp),
        ),
      ));
    } else {
      children.add(_buildForm());
    }
    children.add(getSloganView());
    return children;
  }

  _buildTitleRow(String title1, String title2) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
    );
    return Container(
      width: 1.sw,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
      decoration: BoxDecoration(
        color: colorF3,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.sp),
          topRight: Radius.circular(8.sp),
        ),
      ),
      constraints: BoxConstraints(minHeight: itemHeight),
      child: Row(
        children: [
          SizedBox(width: 0.7.sw - 64.sp, child: Text(title1, style: style)),
          SizedBox(width: 0.3.sw, child: Text(title2, style: style)),
        ],
      ),
    );
  }

  _buildValueRow(String title, String value) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
    );
    return Container(
      width: 1.sw,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: color2B.withOpacity(0.05))),
      ),
      constraints: BoxConstraints(minHeight: itemHeight),
      child: Row(
        children: [
          SizedBox(width: 0.7.sw - 64.sp, child: Text(title, style: style)),
          SizedBox(width: 0.3.sw, child: Text(value, style: style)),
        ],
      ),
    );
  }

  _buildForm() {
    if (calResult == null) {
      return const SizedBox();
    }

    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(0.sp, 20.sp, 0.sp, 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildTitleRow(Lang.measure_name, Lang.measure_value),
          Container(
            width: 1.sw - 40.sp,
            constraints: BoxConstraints(minHeight: itemHeight),
            padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
            alignment: Alignment.centerLeft,
            color: colorE8,
            child: MyText(Lang.buccal_corridors, color2B, 14.sp, FontWeight.w500),
          ),
          Container(
            width: 1.sw - 40.sp,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.fromLTRB(12.sp, 12.sp, 12.sp, 12.sp),
            child: Row(
              children: [
                SizedBox(width: 4.sp),
                ...List.filled(
                  2,
                  Icon(
                    IconFont.icon_star,
                    size: 10.sp,
                    color: colorBlue,
                  ),
                ),
                SizedBox(width: 4.sp),
                Expanded(
                  child: MyText(calResult!.summary, color7C, 14.sp),
                ),
              ],
            ),
          ),
          _buildValueRow(Lang.buccal_corridors, "${calResult!.target[0]}%"),
          _buildValueRow(Lang.smile_width, "${calResult!.target[1]}%"),
          _buildValueRow(Lang.smile_fullness, "${calResult!.target[2]}%"),
          Container(
            width: 1.sw - 40.sp,
            constraints: BoxConstraints(minHeight: itemHeight),
            padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
            alignment: Alignment.centerLeft,
            color: colorE8,
            child: MyText(Lang.midline_analysis, color2B, 14.sp, FontWeight.w500),
          ),
          Container(
            width: 1.sw - 40.sp,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.fromLTRB(12.sp, 12.sp, 12.sp, 12.sp),
            child: Row(
              children: [
                SizedBox(width: 4.sp),
                ...List.filled(
                  2,
                  Icon(
                    IconFont.icon_star,
                    size: 10.sp,
                    color: colorBlue,
                  ),
                ),
                SizedBox(width: 4.sp),
                MyText(midResult, color7C, 14.sp),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(categoryMap[widget.file.category] ?? widget.file.name),
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: _buildFormList(),
        ),
      ),
    );
  }
}
