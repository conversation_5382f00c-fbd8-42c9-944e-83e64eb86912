import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail5021_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail5022_page.dart';
import 'package:mooeli/pages/lingya/lingya_detail5023_page.dart';
import 'package:mooeli/pages/lingya/lingya_text_convert.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

//侧位片分析
class LingyaDetail110Page extends BasePage {
  LingyaFile file;

  LingyaDetail110Page(this.file, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail110PageState();
  }
}

class LingyaDetail110PageState extends BasePageState<LingyaDetail110Page> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int currentIndex = 0;
  List titles = [];
  List<int> typeList = [5021, 5022, 5023];

  @override
  void initState() {
    super.initState();
    logger("getTabBars ${(widget.file.analysisInfoMap as Map).keys}");
    titles = (widget.file.analysisInfoMap as Map)
        .keys
        .where((element) => typeList.contains(int.parse(element.toString())))
        .toList();
    logger("getTabBars $titles");
    _tabController = TabController(vsync: this, length: titles.length);
    _tabController.animation!.addListener(() {
      setState(() {
        currentIndex = _tabController.index;
      });
    });
  }

  List<Widget> getTabBars() {
    return titles
        .map((title) => Tab(
              child: MyText(
                getXRayTitle(analyticsMap[title]!),
                currentIndex == titles.indexOf(title) ? colorBrand : color2B,
                16.sp,
                currentIndex == titles.indexOf(title) ? FontWeight.w500 : FontWeight.w400,
              ),
            ))
        .toList();
  }

  List<Widget> getTabViews() {
    return (widget.file.analysisInfoMap as Map)
        .values
        .where((element) => typeList.contains(element["type"]))
        .map((element) {
      switch (element["type"]) {
        case 5021:
          return LingyaDetail5021Page(widget.file, element["id"]);
        case 5022:
          return LingyaDetail5022Page(widget.file, element["id"]);
        case 5023:
          return LingyaDetail5023Page(widget.file, element["id"]);
        default:
          return Container(
            alignment: Alignment.center,
            child: Text(Lang.no_record),
          );
      }
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: Colors.blue,
        title: Text(categoryMap[widget.file.category] ?? widget.file.name),
        bottom: TabBar(
          controller: _tabController,
          tabs: getTabBars(),
          padding: EdgeInsets.symmetric(horizontal: 20.sp),
          isScrollable: true,
          indicatorColor: colorBrand,
          labelColor: Colors.white,
        ),
      ),
      body: TabBarView(controller: _tabController, children: getTabViews()),
    );
  }
}
