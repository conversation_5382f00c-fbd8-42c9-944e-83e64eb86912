import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/iconfont.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/calculate_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/paint_manager.dart';
import 'package:mooeli/model/calculate_data.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

//正面像分析
class LingyaDetail100Page extends BasePage {
  LingyaFile file;

  LingyaDetail100Page(this.file, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDetail100PageState();
  }
}

class LingyaDetail100PageState extends BasePageState<LingyaDetail100Page> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  ScrollController tabController = ScrollController();

  int imageWidth = 0, imageHeight = 0;
  double canvasWidth = 335.sp, canvasHeight = 0;

  MyPainter myPainter = MyPainter();

  final double itemHeight = 38.sp;
  bool loaded = false, error = false;
  Map<String, dynamic> faceMap = {
    '三庭五眼': {
      '大三庭': {},
      '小三庭': {},
      '五眼': {},
    },
    '黄金比例': {
      '发缘-鼻尖-颏下': {},
      '发缘-鼻根-口裂': {},
      '发缘-鼻根-颏下': {},
      '鼻尖-口裂-颏下': {},
      '鼻根-口裂-颏下': {},
      '鼻根-鼻尖-口裂': {},
      '长宽比': {},
      '眼角距离：口角距离': {},
    },
    '唇形': {
      '上唇：下唇': {},
    },
    '对称性': {
      '眼倾斜': {},
      '耳基倾斜': {},
      '唇倾斜': {},
      '下颌角倾斜': {},
      '颏部偏斜': {},
      '鼻部偏斜': {},
      '下颌支倾斜偏差': {},
      '下颌体倾斜偏差': {},
      '左右下颌角偏差': {},
    }
  };

  final Map _iconMap = {
    "三庭五眼": IconFont.icon_santingwuyan,
    "黄金比例": IconFont.icon_huangjinfenge,
    "唇形": IconFont.icon_chunhoudu,
    "对称性": IconFont.icon_a_duicheng1,
  };

  final Map _fontSizeMap = {
    "三庭五眼": 20.sp,
    "黄金比例": 20.sp,
    "唇形": 12.sp,
    "对称性": 28.sp,
  };

  int selectIndex = 0;
  String selectKey = "";

  Map pointsMap = {};

  @override
  initState() {
    super.initState();
    getFrontFaceConfigJson((json) {
      setState(() {
        for (String key in (json as Map).keys) {
          faceMap[json[key]["type"]][key] = json[key];
        }
      });
      logger("faceMap : $faceMap");
      _getAnalyticsInfo();
    });
    _getImageSize();
  }

  _getImageSize() {
    getImageSize(widget.file.imagePath, (width, height) {
      setState(() {
        imageWidth = width;
        imageHeight = height;
        canvasWidth = 335.sp;
        canvasHeight = canvasWidth * imageHeight / imageWidth;
      });
      logger("getImageSize image: $imageWidth*$imageHeight, canvas: $canvasWidth*$canvasHeight");
      onSelectTab(faceMap.keys.first, 0);
    });
  }

  _getAnalyticsInfo() {
    if (isNotEmpty(widget.file.analysisInfoMap)) {
      getAnalyticsInfo(
        widget.file.getAnalyticsId(),
        (data) {
          if (isNotEmpty(data["draft"]) && data["draft"] != "{}") {
            logger("getAlgorithmResult drafe ${data["draft"].length}");
            getDraftContent(data["draft"], (draft) {
              logger("getDraftContent: $draft");
              setState(() {
                loaded = true;
                pointsMap = draft["draft"]["points"] as Map;
              });
            });
            onSelectTab(faceMap.keys.first, 0);
          } else if (isEmpty(data["algorithmInfoMap"])) {
            if (data["status"] == 7) {
              setState(() {
                error = true;
                loaded = true;
              });
            } else {
              delay(1000, _getAnalyticsInfo);
            }
          } else {
            getDraftPoints(data, (json) {
              setState(() {
                loaded = true;
                pointsMap = json;
              });
              onSelectTab(faceMap.keys.first, 0);
            });
          }
        },
      );
    }
  }

  _buildFormList() {
    List<Widget> children = [
      _buildTitleView(selectKey),
      Stack(
        children: [
          getDetailImageSw(widget.file, () => viewLingyaImage(widget.file)),
          GestureDetector(
            onTap: () => viewLingyaImage(widget.file),
            child: CustomPaint(
              size: Size(canvasWidth, canvasHeight),
              painter: myPainter,
            ),
          ),
        ],
      )
    ];
    if (isEmpty(pointsMap)) {
      children.add(Container(
        padding: EdgeInsets.fromLTRB(24.sp, 60.sp, 24.sp, 0),
        child: Center(
          child: MyText(
            loaded
                ? error
                    ? Lang.ai_status_fail
                    : Lang.no_analytic_result
                : Lang.in_analytic,
            color2B,
            14.sp,
          ),
        ),
      ));
    } else {
      children.add(_buildForm(faceMap[selectKey] as Map));
      children.add(getSloganView());
    }
    return children;
  }

  _getTabTitles() {
    List keys = faceMap.keys.toList();
    List<Widget> list = [];
    for (int i = 0; i < keys.length; i++) {
      list.add(_getTitleView(i, keys[i]));
    }
    return list;
  }

  _getTitleView(int index, String key) {
    bool isSelect = key == selectKey;
    return GestureDetector(
      onTap: () {
        // if (index >= 2 && index > selectIndex) {
        //   tabController.animateTo(tabController.offset + 0.2.sw,
        //       duration: const Duration(milliseconds: 300), curve: Curves.linear);
        // } else if (index <= 2 && index < selectIndex) {
        //   tabController.animateTo(tabController.offset - 0.2.sw,
        //       duration: const Duration(milliseconds: 300), curve: Curves.linear);
        // }
        onSelectTab(key, index);
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
        height: 30.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.sp),
          color: isSelect ? colorBrand : Colors.white,
        ),
        child: MyText(
          faceMap[key].values.first["type${getLangStr()}"],
          isSelect ? Colors.white : color2B,
          14.sp,
          FontWeight.w500,
        ),
      ),
    );
  }

  _buildTitleView(String type) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.fromLTRB(28.sp, 0.sp, 20.sp, 12.sp),
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          SizedBox(
            height: 32.sp,
            child: Icon(
              _iconMap[type],
              color: color2B,
              size: _fontSizeMap[type],
              weight: 500,
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 12.sp, right: 8.sp),
              child: MyText(faceMap[type].values.first["type${getLangStr()}"], color2B, 16.sp, FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  _buildTitleRow(List<String> texts) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
    );
    return Row(
      children: [
        SizedBox(width: 0.55.sw - 64.sp, child: Text(texts[0], style: style)),
        SizedBox(width: 0.25.sw, child: Text(texts[1], style: style)),
        SizedBox(width: 0.2.sw, child: Text(texts[2], style: style)),
      ],
    );
  }

  _buildValueRow(List<String> texts, Color text2Color, String subTitle) {
    TextStyle style = TextStyle(
      color: color2B,
      fontSize: 14.sp,
    );
    return Container(
      width: 1.sw - 40.sp,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: color2B.withOpacity(0.05))),
      ),
      constraints: BoxConstraints(minHeight: itemHeight),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(width: 0.55.sw - 64.sp, child: Text(texts[0], style: style)),
              SizedBox(width: 0.25.sw, child: Text(texts[1], style: style)),
              SizedBox(width: 0.2.sw, child: MyText(texts[2], text2Color, 14.sp)),
            ],
          ),
          SizedBox(height: 4.sp),
          isEmpty(subTitle) ? const SizedBox() : MyText(subTitle, color7C, 12.sp),
        ],
      ),
    );
  }

  _buildForm(Map map) {
    List<Widget> children = [];
    children.add(
      Container(
        width: 1.sw - 40.sp,
        constraints: BoxConstraints(minHeight: itemHeight),
        padding: EdgeInsets.fromLTRB(12.sp, 0.sp, 12.sp, 0.sp),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: colorF3,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.sp),
            topRight: Radius.circular(8.sp),
          ),
        ),
        child: _buildTitleRow([Lang.measure_name, Lang.measure_norm, Lang.measure_value]),
      ),
    );

    Map p1 = pointsMap['000100'];
    Map p2 = pointsMap['000101'];
    double midLine = (p1["y"] - p2["y"]) / (p1["x"] - p2["x"]);

    for (String title in map.keys) {
      try {
        Map data = map[title] as Map;

        int algorithm = 0;
        int type = 0;
        if (data["algorithm"] is String) {
          String str = data["algorithm"];
          int left = str.indexOf("(");
          int right = str.indexOf(")");
          algorithm = int.parse(str.substring(0, left));
          type = int.parse(str.substring(left + 1, right));
        } else {
          algorithm = data["algorithm"] ~/ 1;
        }

        List keys = (data["pointArray"] as List).map((e) => (e as Map).keys.first).toList();
        List points = keys.map((e) => [pointsMap[e]["x"], pointsMap[e]["y"]]).toList();

        Target100 target = Target100.fromJson(data);

        CalResult100? result = CalculateTool100.instance.getCalculationResult(algorithm, points, target, type, midLine);

        if (result != null) {
          int star = isNotEmpty(result.commit) && result.commit != "0" ? 2 : 0;
          children.add(_buildValueRow(
            [data[getLangKey()], data["standard"], result.res],
            getValueColor(star),
            lang == "zh" ? data["en"] : "",
          ));
          if (star > 0) {
            children.add(
              Container(
                width: 1.sw - 40.sp,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 12.sp),
                child: Row(
                  children: [
                    SizedBox(width: 4.sp),
                    ...List.filled(
                      2,
                      Icon(
                        IconFont.icon_star,
                        size: 10.sp,
                        color: getValueColor(star),
                      ),
                    ),
                    SizedBox(width: 4.sp),
                    Expanded(
                      child: MyText(result.commit, color2B, 14.sp),
                    ),
                  ],
                ),
              ),
            );
          }
        }
      } catch (ex) {
        //
      }
    }

    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(0, 20.sp, 0, 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: children,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.type_100),
      ),
      body: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Container(
              margin: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 8.sp),
              padding: EdgeInsets.fromLTRB(5.sp, 4.sp, 5.sp, 4.sp),
              height: 38.sp,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(18.sp),
              ),
              child: ScrollConfiguration(
                behavior: NoOverScrollBehavior(),
                child: SingleChildScrollView(
                  controller: tabController,
                  scrollDirection: Axis.horizontal,
                  child: Row(children: _getTabTitles()),
                ),
              ),
            ),
          ),
          Container(
            constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: _buildFormList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void onSelectTab(String key, int index) {
    setState(() {
      selectIndex = index;
      selectKey = key;
    });

    List<Line> lines = [];
    List pointArray = [];
    for (String title in faceMap[key].keys) {
      List<Map> points = [];
      Set<String> typeSet = {};
      for (Map point in (faceMap[key][title]["pointArray"] as List)) {
        //point: {000000: "y"}
        if (isNotEmpty(pointsMap[point.keys.first])) {
          Map value = pointsMap[point.keys.first] as Map;
          points.add(value);
          pointArray.add({"x": value["x"] * canvasWidth / imageWidth, "y": value["y"] * canvasHeight / imageHeight});
          typeSet.add(point.values.first);
        }
      }

      LineType type;
      if (typeSet.length == 1) {
        switch (typeSet.first) {
          case "x":
            type = LineType.onlyX;
            break;
          case "y":
            type = LineType.onlyY;
            break;
          default:
            type = LineType.xAndY;
            break;
        }
      } else {
        type = LineType.xOrY;
      }

      logger("onSelectTab $title: $points");
      switch (type) {
        case LineType.onlyX:
          for (Map value in points) {
            lines.add(Line(
              value["x"] * canvasWidth / imageWidth,
              0,
              value["x"] * canvasWidth / imageWidth,
              canvasHeight,
            ));
          }
          break;
        case LineType.onlyY:
          for (Map value in points) {
            lines.add(Line(
              0,
              value["y"] * canvasHeight / imageHeight,
              canvasWidth,
              value["y"] * canvasHeight / imageHeight,
            ));
          }
          break;
        case LineType.xAndY:
          logger("onSelectTab xy $title points: $points");
          if (title != "左右下颌角偏差") {
            for (int i = 1; i < points.length; i = i + 2) {
              lines.add(Line(
                points[i - 1]["x"] * canvasWidth / imageWidth,
                points[i - 1]["y"] * canvasHeight / imageHeight,
                points[i]["x"] * canvasWidth / imageWidth,
                points[i]["y"] * canvasHeight / imageHeight,
              ));
            }
          }
          break;
        case LineType.xOrY:
          logger("onSelectTab xOrY points: $points");
          if (points.length == 4) {
            double x0 = points[0]["x"] * canvasWidth / imageWidth;
            double x1 = points[1]["x"] * canvasWidth / imageWidth;
            double y0 = points[2]["y"] * canvasHeight / imageHeight;
            double y1 = points[3]["y"] * canvasHeight / imageHeight;
            // lines.add(Line(x0, (y0 + y1) / 2, x1, (y0 + y1) / 2));
            // lines.add(Line((x0 + x1) / 2, y0, (x0 + x1) / 2, y1));
          }
          break;
      }
    }
    logger("onSelectTab lines: $lines");
    logger("onSelectTab pointArray: $pointArray");
    myPainter.lines = lines;
    myPainter.points = pointArray;

    try {
      myPainter.centerLine = Line(
        pointsMap["000100"]["x"] * canvasWidth / imageWidth,
        pointsMap["000100"]["y"] * canvasHeight / imageHeight,
        pointsMap["000101"]["x"] * canvasWidth / imageWidth,
        pointsMap["000101"]["y"] * canvasHeight / imageHeight,
      );
    } catch (ex) {
      myPainter.centerLine = Line(0, 0, 0, 0);
    }
  }
}

enum LineType {
  onlyX,
  onlyY,
  xOrY,
  xAndY,
}
