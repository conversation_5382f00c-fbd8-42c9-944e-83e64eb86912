import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class LingyaDocPage extends BasePage {
  LingyaPhase phase;
  List<LingyaPhase> phaseList = [];

  LingyaDocPage(this.phase, this.phaseList, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LingyaDocPageState();
  }
}

class LingyaDocPageState extends BasePageState<LingyaDocPage> {
  int selectIndex = 0;
  String selectKey = "";
  ScrollController tabController = ScrollController();

  Map<String, String> keyTitleMap = {
    "baseInfo": Lang.doc_base_info,
    "clinicalInfo": Lang.doc_clinical_info,
    "diagnosticInfo": Lang.doc_diagnose_info,
    "inspectionInfo": Lang.doc_inspect_info,
    "treatmentAndOtherInfo": Lang.doc_treatment_info,
    "generalBaseInfo": Lang.doc_base_info,
    "generalClinicalInfo": Lang.doc_clinical_info,
  };

  @override
  void initState() {
    super.initState();
    selectIndex = 0;
    selectKey = (widget.phase.document["newData"] as Map).keys.first;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.case_doc),
        actions: [
          GestureDetector(
            child: Padding(
              padding: EdgeInsets.only(right: 20.sp),
              child: Image.asset(
                "res/imgs/icon_menu.png",
                width: 24.sp,
              ),
            ),
            onTap: () {
              showBottomWidgetsDialog(
                context,
                Lang.select_contract,
                widget.phaseList
                    .map(
                      (phase) => Container(
                        width: 1.sw - 40.sp,
                        margin: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 20.sp),
                        padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 12.sp),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.sp),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MyText(phase.name, phase.hasDocument() ? color2B : colorE1, 14.sp, FontWeight.w500),
                            SizedBox(height: 4.sp),
                            MyText(Global.getDateByTimestamp(milliseconds: phase.createTime, type: 1),
                                phase.hasDocument() ? colorAB : colorE1, 12.sp),
                          ],
                        ),
                      ),
                    )
                    .toList(),
                (index) {
                  if (widget.phaseList[index].hasDocument()) {
                    setState(() {
                      widget.phase = widget.phaseList[index];
                      selectIndex = 0;
                      tabController.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.linear);
                      selectKey = (widget.phase.document["newData"] as Map).keys.first;
                    });
                  } else {
                    toast(Lang.case_no_doc);
                  }
                },
              );
            },
          )
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 8.sp),
            padding: EdgeInsets.fromLTRB(5.sp, 4.sp, 5.sp, 4.sp),
            height: 38.sp,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(18.sp),
            ),
            child: ScrollConfiguration(
              behavior: NoOverScrollBehavior(),
              child: SingleChildScrollView(
                controller: tabController,
                scrollDirection: Axis.horizontal,
                child: Row(children: _getTabTitles()),
              ),
            ),
          ),
          _getContentView(),
        ],
      ),
    );
  }

  _getContentView() {
    switch (selectKey) {
      case "baseInfo":
      case "generalBaseInfo":
        return _getBaseInfoView();
      case "clinicalInfo":
      case "generalClinicalInfo":
        return _getClinicalInfoView();
      case "diagnosticInfo":
        return _getDiagnoseInfoView();
      case "inspectionInfo":
        return _getInspectionInfoView();
      case "treatmentAndOtherInfo":
        return _getTreatmentInfoView();
    }
    return const SizedBox();
  }

  _getTabTitles() {
    Map map = (widget.phase.document["newData"] as Map);
    List<Widget> list = [];
    List keys = map.keys.toList();
    for (int i = 0; i < keys.length; i++) {
      if (isNotEmpty(map[keys[i]])) {
        list.add(_getTitleView(i, keys[i]));
      }
    }
    return list;
  }

  _getTitleView(int index, String key) {
    bool isSelect = index == selectIndex;
    return GestureDetector(
      onTap: () {
        // if (index >= 2 && index > selectIndex) {
        //   tabController.animateTo(tabController.offset + 0.2.sw,
        //       duration: const Duration(milliseconds: 300), curve: Curves.linear);
        // } else if (index <= 2 && index < selectIndex) {
        //   tabController.animateTo(tabController.offset - 0.2.sw,
        //       duration: const Duration(milliseconds: 300), curve: Curves.linear);
        // }
        setState(() {
          selectIndex = index;
          selectKey = key;
        });
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
        height: 30.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.sp),
          color: isSelect ? colorBrand : Colors.white,
        ),
        child: MyText(
          keyTitleMap[key] ?? Lang.other,
          isSelect ? Colors.white : color2B,
          14.sp,
          FontWeight.w500,
        ),
      ),
    );
  }

  _getShowText(text, [String unit = ""]) {
    return isEmpty("$text") ? Lang.none : "$text$unit";
  }

  _getFormTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.sp),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset("res/imgs/icon_note.png", width: 24.sp),
          SizedBox(width: 8.sp),
          MyText(title, color2B, 16.sp, FontWeight.w500),
        ],
      ),
    );
  }

  _getFormView(String title, content, [bool vertical = false, String unit = ""]) {
    if (content == null) {
      return const SizedBox();
    }
    try {
      if (vertical) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyText(title, color2B, 14.sp, FontWeight.w500),
            SizedBox(height: 4.sp),
            HeightText(_getShowText(content), color2B, 14.sp, 1.5),
            SizedBox(height: 16.sp),
          ],
        );
      } else {
        return Container(
          constraints: BoxConstraints(maxWidth: 1.sw - 80.sp),
          padding: EdgeInsets.only(bottom: 12.sp),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(title, color2B, 14.sp, FontWeight.w500),
              SizedBox(width: 16.sp),
              Flexible(
                child: MyText(_getShowText(content.toString(), unit), color2B, 14.sp),
              ),
            ],
          ),
        );
      }
    } catch (ex) {
      return const SizedBox();
    }
  }

  _container(List<Widget> children) {
    return Container(
      width: 1.sw - 40.sp,
      margin: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 20.sp),
      padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  _getTime(String time) {
    if (time.contains("T")) {
      return time.substring(0, time.indexOf("T"));
    } else {
      return time;
    }
  }

  _getBaseInfoView() {
    bool isOrtho = widget.phase.document["type"] == "orthodontics";
    String baseKey = isOrtho ? "baseInfo" : "generalBaseInfo";
    Map json = widget.phase.document["newData"][baseKey];
    return Container(
      constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
      child: SingleChildScrollView(
        child: Column(
          children: [
            _container(
              [
                _getFormTitle(Lang.doc_base_info),
                _getFormView(Lang.name, json["base"]["name"]),
                _getFormView(Lang.age, json["base"]["age"] ~/ 1),
                _getFormView(Lang.gender, json["base"]["gender"] == "female" ? Lang.gender_woman : Lang.gender_man),
                _getFormView(Lang.birthday, Global.getDateByTimestamp(milliseconds: json["base"]["birthday"] ~/ 1)),
                _getFormView(Lang.phone, json["base"]["contact"]),
                _getFormView(Lang.case_code, json["base"]["caseCode"]),
              ],
            ),
            isOrtho
                ? _container(
                    [
                      _getFormTitle(Lang.other_info),
                      _getFormView(Lang.doctor, json["more"]["doctorName"]),
                      _getFormView(Lang.office, json["more"]["department"]),
                      _getFormView(Lang.organization, json["more"]["medicalInstitutions"]),
                      _getFormView(Lang.height, json["more"]["height"], false, "cm"),
                      _getFormView(Lang.weight, json["more"]["weight"], false, "kg"),
                      _getFormView(Lang.medical_start_time, _getTime(json["more"]["startTime"] ?? "")),
                    ],
                  )
                : _container(
                    [
                      _getFormTitle(Lang.other_info),
                      _getFormView(Lang.doctor, json["more"]["doctorName"]),
                      _getFormView(Lang.office, json["more"]["department"]),
                      _getFormView(Lang.organization, json["more"]["medicalInstitutions"]),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  _getDiseaseClassify(json) {
    String result = "";
    try {
      for (String key in json.keys) {
        if (json[key]) {
          result = "$result$key ";
        }
      }
    } catch (e) {
      //
    }
    return result;
  }

  _getClinicalInfoView() {
    bool isOrtho = widget.phase.document["type"] == "orthodontics";
    Map json = widget.phase.document["newData"][isOrtho ? "clinicalInfo" : "generalClinicalInfo"];
    return Container(
      constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
      child: SingleChildScrollView(
        child: isOrtho
            ? _container(
                [
                  SizedBox(height: 12.sp),
                  _getFormView(Lang.chief_complaint, json["chiefComplaint"], true),
                  _getFormView(Lang.disease_classification, _getDiseaseClassify(json["diseaseClassification"]), true),
                  _getFormView(Lang.disease_nature_time, json["diseaseNatureAndTime"], true),
                  _getFormView(Lang.oralTrauma_history, json["oralTraumaHistory"], true),
                  _getFormView(Lang.medication_history, json["medicationHistory"], true),
                  _getFormView(Lang.oral_tooth_extraction_history, json["oralToothExtractionHistory"], true),
                  _getFormView(Lang.premature_loss_baby_teeth, json["prematureLossOfBabyTeeth"], true),
                  _getFormView(Lang.retention_baby_teeth, json["retentionOfBabyTeeth"], true),
                  _getFormView(Lang.dental_caries, json["dentalCaries"], true),
                  _getFormView(Lang.family_history, json["familyHistory"], true),
                  _getFormView(Lang.bad_habit, json["badHabit"], true),
                ],
              )
            : _container([
                SizedBox(height: 12.sp),
                _getFormView(Lang.chief_complaint, json["chiefComplaint"], true),
                _getFormView(Lang.present_illness, json["presentIllness"], true),
                _getFormView(Lang.past_illness, json["pastIllness"], true),
                _getFormView(Lang.physical_examination, json["physicalExamination"], true),
                _getFormView(Lang.diagnosis, json["diagnosis"], true),
                _getFormView(Lang.other, json["other"], true),
              ]),
      ),
    );
  }

  _getDiagnoseInfoView() {
    Map json = widget.phase.document["newData"]["diagnosticInfo"];
    return Container(
      constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
      child: SingleChildScrollView(
        child: _container([
          SizedBox(height: 12.sp),
          _getFormView(Lang.bony, json["bony"]),
          _getFormView(
            Lang.teeth,
            json["teeth"],
          ),
          _getFormView(Lang.face_type, json["faceType"]),
          _getFormView(Lang.growth_type, json["growthType"]),
          _getFormView(
            Lang.crowding,
            (json["crowding"] as List).join("  "),
          ),
          _getFormView(
            Lang.molar_relation,
            (json["molarRelationship"] as List).join("  "),
          ),
        ]),
      ),
    );
  }

  _getInspectionInfoView() {
    Map json = widget.phase.document["newData"]["inspectionInfo"];
    return Container(
      constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
      child: SingleChildScrollView(
        child: Column(
          children: [
            _container(
              [
                _getFormTitle(Lang.base_clinical_check),
                _getFormView(Lang.development_status, json["baseClinicalCheck"]["developmentStatus"]),
                _getFormView(Lang.nutritional_status, json["baseClinicalCheck"]["nutritionalStatus"]),
                _getFormView(Lang.oral_hygiene, json["baseClinicalCheck"]["oralHygiene"]),
                _getFormView(Lang.periodontium, json["baseClinicalCheck"]["periodontium"]),
              ],
            ),
            _container(
              [
                _getFormTitle(Lang.joint_check),
                _getFormView(Lang.mouth_opening, json["jointCheck"]["mouthOpening"]),
                _getFormView(Lang.mouth_type, json["jointCheck"]["mouthType"]),
                _getFormView(Lang.sound, json["jointCheck"]["sound"]),
                _getFormView(Lang.pain, json["jointCheck"]["pain"]),
                _getFormView(Lang.other_exception, json["jointCheck"]["otherExceptions"]),
              ],
            ),
            _container(
              [
                _getFormTitle(Lang.face_check),
                _getFormView(Lang.front, (json["faceCheck"]["front"] as List).join("  "), true),
                _getFormView(Lang.side, (json["faceCheck"]["side"] as List).join("  "), true),
              ],
            ),
            _container(
              [
                _getFormTitle(Lang.xray_analysis),
                _getFormView(Lang.surface_slices, json["XrayAnalysis"]["surfaceSlices"]),
                _getFormView(Lang.other, json["XrayAnalysis"]["other"]),
                _getFormView(Lang.cranial, json["XrayAnalysis"]["cranial"]),
              ],
            ),
          ],
        ),
      ),
    );
  }

  _getTreatmentInfoView() {
    Map json = widget.phase.document["newData"]["treatmentAndOtherInfo"];
    return Container(
      constraints: BoxConstraints(maxHeight: 1.sh - 150.sp),
      child: SingleChildScrollView(
        child: _container([
          SizedBox(height: 12.sp),
          _getFormView(Lang.treatment, json["treatment"], true),
          _getFormView(Lang.other, json["other"], true),
        ]),
      ),
    );
  }
}
