//中文转多语言

import 'package:mooeli/manager/languaga_manager.dart';

String getBonePeriodStr(String period) {
  Map map = {
    'CS1起始期': Lang.bone_period_cs1,
    'CS2快速期': Lang.bone_period_cs2,
    'CS3过渡期': Lang.bone_period_cs3,
    'CS4减速期': Lang.bone_period_cs4,
    'CS5成熟期': Lang.bone_period_cs5,
    'CS6完成期': Lang.bone_period_cs6,
    'CS2快速期/CS3过渡期': Lang.bone_period_cs23,
    'CS3过渡期/CS4减速期': Lang.bone_period_cs34,
  };
  return map[period] ?? period;
}

String getBoneAnalyticsStr(String way) {
  Map map = {
    '骨龄定性分析测量': Lang.bone_qualitative,
    '骨龄定性分析法': Lang.bone_qualitative,
    '骨龄定量分析法': Lang.bone_quantitative,
    '深度学习分析法': Lang.bone_learning,
  };
  return map[way] ?? way;
}

String getLateralAnalyticsStr(String way) {
  Map map = {
    "华西综合分析法": <PERSON>.lateral_huaxi,
    "北京大学分析法": <PERSON>.lateral_beijing,
    "九院分析法": Lang.lateral_jiuyuan,
    "Downs分析法": Lang.lateral_downs,
    "Jarabak分析法": Lang.lateral_jarabak,
    "Wylie分析法": Lang.lateral_wylie,
    "Steiner分析法": Lang.lateral_steiner,
    "Ricketts分析法": Lang.lateral_ricketts,
    "McNamara分析法": Lang.lateral_mcnamara,
    "ABO分析法": Lang.lateral_abo,
    "Tweed分析法": Lang.lateral_tweed,
    "Holdaway分析法": Lang.lateral_holdway,
    "Burstone分析法": Lang.lateral_burstone,
    "Riedel分析法": Lang.lateral_riedel,
  };
  return map[way] ?? way;
}

String getXRayTitle(String title) {
  Map map = {
    "头影分析": Lang.xray_lateral,
    "骨龄分析": Lang.xray_bone,
    "气道分析": Lang.xray_airway,
  };
  return map[title] ?? title;
}

String getBonePeriod(int period) {
  switch (period) {
    case 1:
      return Lang.bone_period_cs1;
    case 2:
      return Lang.bone_period_cs2;
    case 3:
      return Lang.bone_period_cs3;
    case 4:
      return Lang.bone_period_cs4;
    case 5:
      return Lang.bone_period_cs5;
    case 6:
      return Lang.bone_period_cs6;
    default:
      return "-";
  }
}

String getBoneDl(int period) {
  switch (period) {
    case 1:
      return Lang.bone_dl_cs1;
    case 2:
      return Lang.bone_dl_cs2;
    case 3:
      return Lang.bone_dl_cs3;
    case 4:
      return Lang.bone_dl_cs4;
    case 5:
      return Lang.bone_dl_cs5;
    case 6:
      return Lang.bone_dl_cs6;
    default:
      return "-";
  }
}

String getBoneRation(int period) {
  switch (period) {
    case 1:
      return Lang.bone_ration_1;
    case 2:
      return Lang.bone_ration_2;
    case 3:
      return Lang.bone_ration_3;
    case 4:
      return Lang.bone_ration_4;
    default:
      return "-";
  }
}
