import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class SelectAreaPage extends BasePage {
  const SelectAreaPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SelectAreaPageState();
  }
}

class _SelectAreaPageState extends BasePageState<SelectAreaPage> {
  int area = 0;

  @override
  void initState() {
    super.initState();
    area = getLoginArea() ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
        contentHeight: 48.sp,
        child: Column(
          children: [
            SizedBox(height: MediaQuery.of(context).padding.top + 4.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Click(
                  onTap: pop,
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(20.sp, 4.sp, 20.sp, 4.sp),
                    child: MyText(Lang.cancel, color2B, 16.sp),
                  ),
                ),
                MyText(Lang.switch_area, color2B, 18.sp, FontWeight.w500),
                Click(
                  onTap: () {
                    setLoginArea(area);
                    eventBus.fire(EventSelectArea());
                    pop();
                  },
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(20.sp, 4.sp, 20.sp, 4.sp),
                    child: MyText(Lang.confirm, colorBrand, 16.sp),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: loginAreasConfig.keys
              .map((key) {
                return Click(
                  onTap: () {
                    setState(() {
                      area = int.parse(key);
                    });
                  },
                  child: Container(
                    width: 335.sp,
                    height: 60.sp,
                    margin: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
                    padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.sp),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MyText(loginAreasConfig[key]["name"], color2B, 16.sp),
                        SizedBox(
                          width: 24.sp,
                          height: 24.sp,
                          child: Material(
                            color: Colors.transparent,
                            child: Radio<int>(
                              activeColor: Colors.black,
                              value: int.parse(key),
                              onChanged: (value) {
                                setState(() {
                                  area = int.parse(key);
                                });
                              },
                              groupValue: area,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              })
              .toList()
              .reversed
              .toList(),
        ),
      ),
    );
  }
}
