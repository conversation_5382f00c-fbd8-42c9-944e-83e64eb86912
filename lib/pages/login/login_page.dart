import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/model/user_info.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

class Login extends BasePage {
  bool showAlert = false;

  Login({Key? key, this.showAlert = false}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _LoginState();
  }
}

class _LoginState extends BasePageState<Login> {
  bool isLoging = false;

  String account = "";
  String verifyCode = "";
  String password = "";

  bool canGetCode = false;
  bool isCounting = false;
  int counter = 60;
  Timer? counterTimer;

  final GlobalKey _loginButtonKey = GlobalKey<HCustomButtonState>();
  FocusNode inputVerTextFieldFocus = FocusNode();

  bool isSelectAgreement = false;

  bool isShowError = false;
  String msgError = Lang.incorrect_password;

  //处理输入两遍验证码的问题
  TextEditingController controllerAccount = TextEditingController();
  TextEditingController controllerVerifyCode = TextEditingController();
  TextEditingController controllerPassword = TextEditingController();

  bool showPassword = false;
  bool passwordLengthOk = false, passwordContentOk = false;

  bool passwordError = false;

  bool loginMode = true;
  int loginType = 1; //1-密码登录，2-验证码登录
  bool usePhone = true;

  int area = 0;

  bool hasErrorJson = false;

  @override
  onLanguageChanged() {
    initAreaConfig();
    (_loginButtonKey.currentState as HCustomButtonState).setText(loginMode ? Lang.login : Lang.register_and_login);
  }

  @override
  initState() {
    super.initState();
    initAreaConfig();
    area = getLoginArea() ?? 0;
    usePhone = area == 0;
    setScreenVertiacl();
    addEventListener<EventSelectArea>((event) {
      setState(() {
        int newArea = getLoginArea() ?? 0;
        if (area != newArea) {
          controllerAccount.clear();
          account = "";
          area = newArea;
          loginType = 1;
          usePhone = area == 0;
          isShowError = false;
          passwordError = false;
          checkLoginBtnVisible();
        }
      });
    });
    // callMcByFunName("getAppVersion").then((verStr) {
    //   Global.appVersion = verStr ?? "1.0.0";
    //   Global.checkNewestVersion(retry: 3);
    // });
    delay(500, () {
      HHttp.initHttp();
      getErrorConfigJson();
      checkLoginBtnVisible();
      // if (widget.showAlert) {
      //   Global.showCustomDialog(
      //       Center(
      //         child: Container(
      //           width: 276.sp,
      //           height: 163.sp,
      //           decoration: BoxDecoration(
      //             color: Colors.white,
      //             borderRadius: BorderRadius.circular(16.sp),
      //           ),
      //           child: Column(
      //             children: [
      //               SizedBox(height: 20.sp),
      //               Row(
      //                 mainAxisSize: MainAxisSize.min,
      //                 children: [
      //                   Image.asset("res/icons/icon_alert2.png", width: 18.sp),
      //                   SizedBox(width: 2.sp),
      //                   MyText(Lang.attention, color2B, 14.sp, FontWeight.w500),
      //                 ],
      //               ),
      //               Expanded(
      //                 child: Padding(
      //                     padding: EdgeInsets.fromLTRB(26.sp, 6.sp, 26.sp, 0),
      //                     child: HeightText(Lang.login_exit_desc, color2B, 12.sp, 1.5)),
      //               ),
      //               Container(width: 276.sp, height: 1.sp, color: colorE1),
      //               Click(
      //                 child: Container(
      //                   width: 276.sp,
      //                   height: 40.sp,
      //                   color: Colors.transparent,
      //                   alignment: Alignment.center,
      //                   child: MyText(Lang.ok, color2B, 14.sp),
      //                 ),
      //                 onTap: () {
      //                   BotToast.cleanAll();
      //                 },
      //               ),
      //             ],
      //           ),
      //         ),
      //       ),
      //       1.sw,
      //       1.sh);
      // }
    });
  }

  getErrorConfigJson() {
    requestErrorCodeConfig(callback: (json) {
      HHttp.errorCodeMap = json;
      hasErrorJson = true;
    });
    delay(1000, () {
      if (!hasErrorJson) {
        getErrorConfigJson();
      }
    });
  }

  switchMode() {
    setState(() {
      loginMode = !loginMode;
      msgError = "";
    });

    (_loginButtonKey.currentState as HCustomButtonState).setText(loginMode ? Lang.login : Lang.register_and_login);
    checkLoginBtnVisible();
  }

  checkLoginBtnVisible() {
    // logger("checkLoginBtnVisible $isLoging $account $password");
    if (!isLoging) {
      if (loginMode) {
        if (loginType == 1) {
          if (usePhone) {
            (_loginButtonKey.currentState as HCustomButtonState)
                .setVisible(isNotEmpty(account) && isNotEmpty(password));
          } else {
            (_loginButtonKey.currentState as HCustomButtonState).setVisible(checkMail(account) && isNotEmpty(password));
          }
        } else if (loginType == 2) {
          (_loginButtonKey.currentState as HCustomButtonState).setVisible(isNotEmpty(account) && checkCode(verifyCode));
        }
      } else {
        (_loginButtonKey.currentState as HCustomButtonState)
            .setVisible(isNotEmpty(account) && checkCode(verifyCode) && isNotEmpty(password) && !passwordError);
      }
    }
    setState(() {
      canGetCode = (usePhone && checkPhone(account) || !usePhone && checkMail(account)) && !isCounting;
    });
  }

  checkLogin() async {
    setState(() {
      isLoging = true;
      isShowError = false;
    });
    (_loginButtonKey.currentState as HCustomButtonState).setVisible(false);

    // if (HHttp.httpEnv == HttpEnv.prod && isNotEmpty(Global.sharedPrefs.getString("http_env")) || !loginMode) {
    reqLogin();
    // } else {
    //   HHttp.request(
    //     "/v3/auth/checkLogin",
    //     "POST",
    //     (data) {
    //       if (data) {
    //         Global.showAlertDialog(
    //           Lang.account_login_other_device,
    //           Lang.login_push_other_device,
    //           okText: Lang.continue_login,
    //           okRed: true,
    //           okCallBack: () {
    //             reqLogin();
    //           },
    //           cancelText: Lang.cancel_login,
    //         );
    //         setState(() {
    //           isLoging = false;
    //         });
    //         checkLoginBtnVisible();
    //       } else {
    //         reqLogin();
    //       }
    //     },
    //     errCallBack: (resp) {
    //       (_loginButtonKey.currentState as HCustomButtonState).setVisible(true);
    //       setState(() {
    //         isLoging = false;
    //         msgError = HHttp.getErrorMessage(resp);
    //         //isShowError = true;
    //         toast(msgError);
    //       });
    //       checkLoginBtnVisible();
    //     },
    //     params: {
    //       "userNameOrPhoneOrMailbox": account,
    //     },
    //   );
    // }
  }

  reqLogin() async {
    loginCallback(data) {
      HHttp.resetCallFrontSaveList();
      currentUser = UserInfo.fromJson(data);
      HHttp.setToken(currentUser.accessToken);
      if (isNotEmpty(currentUser.avatarId)) {
        downloadAvatar(
          [currentUser.avatarId],
          (id, path) {
            if (id == currentUser.avatarId) {
              currentUser.avatarPath = path;
              eventBus.fire(EventRefreshUserInfo());
            }
          },
          "PROVIDER",
          documentDir: true,
        );
      }

      Global.sharedPrefs.setString("login_account", account);
      if (!loginMode || loginType == 1) {
        Global.sharedPrefs.setString("login_password", password);
        Global.sharedPrefs.setInt("login_time", DateTime.now().millisecondsSinceEpoch);
      }
      Global.sharedPrefs.setInt("login_mode", loginMode ? loginType : 0);

      HHttp.request("/v3/tenant/list", "POST", (data) {
        try {
          List<LingyaTenant> tenantList =
              (data["tenantList"] as List).map((e) => LingyaTenant.fromJson(e, true)).toList();
          tenantList = tenantList.where((t) => t.isNormal()).toList();
          if (tenantList.isNotEmpty) {
            if (currentUser.personId == currentUser.providerId) {
              HHttp.request(
                "/v3/tenant/switch",
                "POST",
                (data) {
                  saveCurrentToken(data);
                  SmartDialog.dismiss();
                  eventBus.fire(EventLogin());
                },
                params: {
                  "id": tenantList.first.id,
                },
              );
            } else {
              SmartDialog.dismiss();
              eventBus.fire(EventLogin());
            }
          } else {
            SmartDialog.dismiss();
            HHttp.setToken("");
            currentUser = UserInfo();
            logger("UserInfo clear");
            Global.sharedPrefs.remove("login_user");
            setState(() {
              isLoging = false;
            });
            checkLoginBtnVisible();
            showCustomDialog(
              Container(
                width: 520.sp,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.sp),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 16.sp),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          MyText(Lang.no_permission, color2B, 24.sp, FontWeight.w600),
                          GestureDetector(
                            onTap: () {
                              SmartDialog.dismiss();
                            },
                            child: Image.asset("res/imgs/icon_close.png", width: 32.sp),
                          ),
                        ],
                      ),
                    ),
                    Divider(color: colorE1),
                    Padding(
                      padding: EdgeInsets.all(24.sp),
                      child: MyText(Lang.login_only_for_tenant, color2B, 24.sp),
                    ),
                    Image.asset("res/imgs/official_qrcode.png", width: 160.sp),
                    Align(
                      alignment: Alignment.topRight,
                      child: GestureDetector(
                        onTap: () {
                          SmartDialog.dismiss();
                        },
                        child: Container(
                          padding: EdgeInsets.fromLTRB(24.sp, 10.sp, 24.sp, 10.sp),
                          margin: EdgeInsets.fromLTRB(0, 40.sp, 24.sp, 24.sp),
                          decoration: BoxDecoration(
                            color: colorBlueDeep,
                            borderRadius: BorderRadius.circular(8.sp),
                          ),
                          child: MyText(Lang.i_knew, Colors.white, 24.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        } catch (ex) {
          //
        }
      }, params: {});
    }

    errorCallback(resp) {
      (_loginButtonKey.currentState as HCustomButtonState).setVisible(true);
      setState(() {
        isLoging = false;
      });
      setState(() {
        msgError = HHttp.getErrorMessage(resp);
        //isShowError = true;
        toast(msgError);
      });
    }

    setState(() {
      isLoging = true;
      isShowError = false;
    });
    (_loginButtonKey.currentState as HCustomButtonState).setVisible(false);

    if (loginMode) {
      if (loginType == 1) {
        HHttp.request(
          usePhone ? "/v3/auth/usernameLogin" : "/v3/auth/mailboxLogin",
          "POST",
          loginCallback,
          errCallBack: errorCallback,
          params: {
            (usePhone ? "username" : "mailbox"): account,
            "password": password,
          },
        );
      } else {
        HHttp.request(
          "/v3/auth/phoneLogin",
          "POST",
          (data) {
            if (data["userInfo"] != null) {
              loginCallback(data["userInfo"]);
            } else {
              errorCallback({"code": "3_2_1x5_1"});
            }
          },
          errCallBack: errorCallback,
          params: {
            "phoneNumber": account,
            "username": account,
            "password": "",
            "verificationCode": verifyCode,
          },
        );
      }
    } else {
      HHttp.request(
        usePhone ? "/v3/auth/phoneRegister" : "/v3/auth/mailboxRegister",
        "POST",
        loginCallback,
        errCallBack: errorCallback,
        params: {
          usePhone ? "phoneNumber" : "mailbox": account,
          "password": password,
          "verificationCode": verifyCode,
        },
      );
    }
  }

  openUserAgreement(bool isInModal) {
    // if (isInModal) {
    //   Navigator.pop(context);
    // }
    Global.openUserAgreement(context);
  }

  openPrivacy(bool isInModal) {
    // if (isInModal) {
    //   Navigator.pop(context);
    // }
    // Global.openPrivacy(context);
    SmartDialog.dismiss();
    eventBus.fire(EventLoging());
    Global.openUrlByWebView(
      context,
      Lang.privacy,
      "${HHttp.getOfficialHost()}lechipai_scan/privacy_agreement.html",
    );
  }

  void showBottomModal() {
    Global.showBottomModal(
        maxHeight: 335.sp,
        context,
        Padding(
            padding: EdgeInsets.all(40.sp),
            child: Column(
              children: [
                MyText(Lang.please_read_and_agree, color2B, 24.sp, FontWeight.w500),
                SizedBox(height: 28.sp),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // TextButton(
                    //     style: ButtonStyle(
                    //       padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
                    //     ),
                    //     onPressed: () {
                    //       setState(() {
                    //         openUserAgreement(true);
                    //       });
                    //     },
                    //     child: Text("《${Lang.user_agreement}》", style: TextStyle(fontSize: 14.sp, color: colorBrand))),
                    // SizedBox(width: 24.sp),
                    TextButton(
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
                        ),
                        onPressed: () {
                          setState(() {
                            openPrivacy(true);
                          });
                        },
                        child: Text("《${Lang.privacy}》",
                            style: TextStyle(
                              fontSize: 24.sp,
                              color: colorBrand,
                            ))),
                  ],
                ),
                SizedBox(height: 8.sp),
                HCustomButton(
                    height: 54.sp,
                    bgColor: colorBrand,
                    onPress: () {
                      setState(() {
                        isSelectAgreement = true;
                      });
                      Navigator.pop(context);
                      checkLogin();
                    },
                    text: Lang.agree_and_continue),
              ],
            )));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.black12,
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          // 触摸收起键盘
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          width: 1.sw,
          height: 1.sh,
          color: Colors.black12,
          child: Center(
            child: Stack(
              children: [
                SingleChildScrollView(child: getInputView()),
                Positioned(
                  top: 32.sp,
                  right: 32.sp,
                  child: GestureDetector(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Image.asset(
                      "res/imgs/icon_close.png",
                      width: 32.sp,
                      // color: colorA4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  checkRegisterPassword() {
    passwordLengthOk = checkPasswordLength(password);
    passwordContentOk = checkPasswordContent(password);
    // isShowError = password.isNotEmpty;
    setState(() {
      passwordError = !checkPassword(password);
    });
  }

  String getLanguage() {
    Locale? locale = getCurrentLocale();
    if (locale == null) {
      return Lang.language_auto;
    } else {
      Map languageMap = getLanguageMap();
      List list = languageMap.values.toList();
      for (int i = 1; i < languageMap.length; i++) {
        if (list[i] != null && list[i] is Locale && locale.languageCode == (list[i] as Locale).languageCode) {
          return languageMap.keys.toList()[i];
        }
      }
    }
    return Lang.language_auto;
  }

  Widget getInputView() {
    return Container(
      width: 656.sp,
      height: 748.sp,
      // margin: EdgeInsets.only(top: 22.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 48.sp),
            child: GestureDetector(
              child: Image.asset("res/imgs/login_logo.png", height: 48.sp),
              onLongPress: () {
                if (isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
                  setState(() {
                    account = "wyq104";
                    password = "********.";
                    controllerAccount.text = account;
                    controllerPassword.text = password;
                    checkLoginBtnVisible();
                  });
                }
                // if (HHttp.httpEnv != HttpEnv.prod) {
                //   showBottomListDialog(context, HttpEnv.values.map((e) => "${e.name}环境").toList(), (index) {
                //     HttpEnv env = HttpEnv.values[index];
                //     Global.sharedPrefs.setString("http_env", env.name);
                //     HHttp.initHttp();
                //     setState(() {});
                //   });
                // }
              },
            ),
          ),
          SizedBox(height: 66.sp),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Click(
                onTap: () {
                  setState(() {
                    loginType = 1;
                    checkLoginBtnVisible();
                  });
                },
                child: Column(
                  children: [
                    MyText(Lang.login_by_password, loginType == 1 ? colorBrand : color2B, 28.sp, FontWeight.w600),
                    Container(
                      width: 24.sp,
                      height: 4.sp,
                      margin: EdgeInsets.only(top: 12.sp),
                      color: loginType == 1 ? colorBrand : Colors.transparent,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 64.sp),
              Click(
                onTap: () {
                  setState(() {
                    loginType = 2;
                    checkLoginBtnVisible();
                  });
                },
                child: Column(
                  children: [
                    MyText(Lang.login_by_code, loginType == 2 ? colorBrand : color2B, 28.sp, FontWeight.w600),
                    Container(
                      width: 24.sp,
                      height: 4.sp,
                      margin: EdgeInsets.only(top: 12.sp),
                      color: loginType == 2 ? colorBrand : Colors.transparent,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 32.sp),
          usePhone ? getInputAccountView() : getInputMailView(),
          SizedBox(height: 32.sp),
          loginType == 1 ? getInputPasswordView() : getInputCodeView(),
          SizedBox(height: 32.sp),
          HCustomButton(
            key: _loginButtonKey,
            width: 478.sp,
            height: 76.sp,
            text: Lang.login,
            bgColor: colorBrand,
            radius: 16.sp,
            initVisible: false,
            onPress: () {
              // 触摸收起键盘
              FocusScope.of(context).requestFocus(FocusNode());

              setState(() {
                if (isSelectAgreement) {
                  checkLogin();
                } else {
                  toast(Lang.please_read_and_agree);
                  // showBottomModal();
                }
              });
            },
          ),
          Container(
            margin: EdgeInsets.all(10.sp),
            alignment: Alignment.topCenter,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Transform.scale(
                  scale: 0.75,
                  child: Checkbox(
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      fillColor: MaterialStateProperty.resolveWith((Set<MaterialState> states) {
                        return colorBrand;
                      }),
                      side: BorderSide(color: color2B),
                      checkColor: Colors.white,
                      value: isSelectAgreement,
                      onChanged: (value) {
                        setState(() {
                          isSelectAgreement = value!;
                        });
                      }),
                ),
                Container(
                  constraints: BoxConstraints(maxWidth: 488.sp),
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: Lang.read_and_agree,
                          style: TextStyle(color: color2B, fontSize: 20.sp),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              setState(() {
                                isSelectAgreement = !isSelectAgreement;
                              });
                            },
                        ),
                        TextSpan(
                            text: "《${Lang.privacy}》",
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                setState(() {
                                  openPrivacy(false);
                                });
                              },
                            style: TextStyle(color: colorBrand, fontSize: 20.sp)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          MyText(Lang.icp_code_only_cn, colorB8, 18.sp),
          SizedBox(height: 24.sp),
        ],
      ),
    );
  }

  Widget getInputAccountView() {
    return Container(
      height: 76.sp,
      margin: EdgeInsets.only(left: 89.sp, right: 89.sp),
      padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 4.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: colorE1),
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: TextField(
        controller: controllerAccount,
        onChanged: (value) {
          setState(() {
            account = value;
            checkLoginBtnVisible();
            isShowError = false;
          });
        },
        autofocus: false,
        style: TextStyle(fontSize: 24.sp, color: color2B),
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: Lang.input_account,
          hintStyle: TextStyle(fontSize: 24.sp, color: const Color(0xFFBBBBBB)),
        ),
      ),
    );
  }

  Widget getInputMailView() {
    return Container(
      height: 76.sp,
      margin: EdgeInsets.only(left: 89.sp, right: 89.sp),
      padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 4.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: colorE1),
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: TextField(
        controller: controllerAccount,
        keyboardType: TextInputType.emailAddress,
        onChanged: (value) {
          setState(() {
            account = value;
            checkLoginBtnVisible();
            isShowError = false;
          });
        },
        autofocus: false,
        style: TextStyle(fontSize: 24.sp, color: color2B),
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: Lang.mail,
          hintStyle: TextStyle(fontSize: 24.sp, color: const Color(0xFFBBBBBB)),
        ),
      ),
    );
  }

  Widget getInputPasswordView() {
    return Container(
      height: 76.sp,
      margin: EdgeInsets.only(left: 89.sp, right: 89.sp),
      padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 4.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: colorE1),
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              controller: controllerPassword,
              obscureText: !showPassword,
              keyboardType: TextInputType.visiblePassword,
              onChanged: (value) {
                setState(() {
                  password = value;
                  isShowError = false;
                  checkRegisterPassword();
                  checkLoginBtnVisible();
                });
              },
              autofocus: false,
              style: TextStyle(fontSize: 24.sp, color: color2B),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: Lang.input_password,
                hintStyle: TextStyle(fontSize: 24.sp, color: colorB8),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                showPassword = !showPassword;
              });
            },
            child: Image.asset(showPassword ? "res/icons/password_visible.png" : "res/icons/password_invisible.png",
                width: 32.sp, height: 32.sp),
          ),
        ],
      ),
    );
  }

  getInputCodeView() {
    return Container(
      height: 76.sp,
      margin: EdgeInsets.only(left: 89.sp, right: 89.sp),
      padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 4.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: colorE1),
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              controller: controllerVerifyCode,
              focusNode: inputVerTextFieldFocus,
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if ('$verifyCode$verifyCode' == value && verifyCode.length >= 6) {
                  //键入重复的情况
                  setState(() {
                    controllerVerifyCode.value = TextEditingValue(
                      text: verifyCode, //不赋值新的 用旧的;
                      selection: TextSelection.fromPosition(
                        TextPosition(affinity: TextAffinity.downstream, offset: verifyCode.length),
                      ), //  此处是将光标移动到最后,
                    );
                  });
                } else {
                  setState(() {
                    verifyCode = value;
                    checkLoginBtnVisible();
                  });
                }
                isShowError = false;
              },
              maxLength: 6,
              autofocus: false,
              style: TextStyle(fontSize: 24.sp, color: color2B),
              decoration: InputDecoration(
                counterText: '',
                isDense: true,
                border: InputBorder.none,
                hintText: Lang.input_verify_code,
                hintStyle: TextStyle(fontSize: 24.sp, color: colorB8),
              ),
            ),
          ),
          Click(
            onTap: () {
              if (canGetCode) {
                startCounter();
              }
            },
            ms: 5000,
            child: MyText(
              isCounting ? "${Lang.refetch_verify_code} (${counter}s)" : Lang.get_verify_code,
              !isCounting && canGetCode ? colorBrand : colorB8,
              24.sp,
            ),
          ),
        ],
      ),
    );
  }

  onGetVerifyCode(data) {
    //焦点自动落在输入验证码一栏
    FocusScope.of(context).requestFocus(inputVerTextFieldFocus);

    setState(() {
      isCounting = true;
    });
    toast(Lang.verify_code_sent);
    if (counterTimer != null) {
      counterTimer!.cancel();
      counterTimer = null;
    }
    counterTimer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      setState(() {
        counter -= 1;
        if (counter <= 0) {
          if (counterTimer != null) {
            counterTimer!.cancel();
            counterTimer = null;
          }
          counter = 60;
          isCounting = false;
          canGetCode = true;
        }
      });
    });
  }

  startCounter() {
    if (isCounting) {
      return;
    }

    HHttp.request(
      usePhone ? "/v3/auth/sendVerificationCode" : "/v3/auth/sendEmailCode",
      "POST",
      onGetVerifyCode,
      errCallBack: (resp) {
        setState(() {
          counter = 60;
          isCounting = false;
        });

        if (counterTimer != null) {
          counterTimer!.cancel();
          counterTimer = null;
        }
        setState(() {
          msgError = HHttp.getErrorMessage(resp);
          //isShowError = true;
          toast(msgError);
        });
      },
      params: usePhone
          ? {"phoneNumber": account}
          : {
              "mailbox": account,
              "usedBy": "signup",
              "template": "iscanbot-sign-up-$lang",
            },
    );
  }
}
