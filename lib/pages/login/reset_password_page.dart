import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

class ResetPasswordPage extends BasePage {
  bool usePhone;

  ResetPasswordPage(this.usePhone, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ResetPasswordPageState();
  }
}

class _ResetPasswordPageState extends BasePageState<ResetPasswordPage> {
  final GlobalKey _loginButtonKey = GlobalKey<HCustomButtonState>();

  TextEditingController controllerAccount = TextEditingController();
  TextEditingController controllerVerifyCode = TextEditingController();
  TextEditingController controllerPassword = TextEditingController();

  String account = "";
  String verifyCode = "";
  String password = "";

  bool canGetCode = false;
  bool isCounting = false;
  int counter = 60;
  Timer? counterTimer;

  FocusNode inputVerTextFieldFocus = FocusNode();

  bool isShowError = false;
  String msgError = Lang.incorrect_password;
  bool showAccountError = false;

  bool showPassword = false;
  bool passwordLengthOk = false, passwordContentOk = false;

  bool passwordError = false;

  @override
  void initState() {
    super.initState();
  }

  checkLoginBtnVisible() {
    setState(() {
      if (isEmpty(account)) {
        showAccountError = false;
      } else if (widget.usePhone) {
        showAccountError = !checkPhone(account);
      } else {
        showAccountError = !checkMail(account);
      }
      canGetCode = (widget.usePhone && checkPhone(account) || !widget.usePhone && checkMail(account)) && !isCounting;
    });
    (_loginButtonKey.currentState as HCustomButtonState)
        .setVisible(!showAccountError && checkCode(verifyCode) && isNotEmpty(password) && !passwordError);
  }

  checkRegisterPassword() {
    passwordLengthOk = checkPasswordLength(password);
    passwordContentOk = checkPasswordContent(password);
    // isShowError = password.isNotEmpty;
    setState(() {
      passwordError = !checkPassword(password);
    });
  }

  getInputAccountView() {
    return Container(
      height: 48.sp,
      margin: EdgeInsets.only(top: 12.sp),
      padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(24.sp)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              controller: controllerAccount,
              keyboardType: widget.usePhone ? TextInputType.phone : TextInputType.emailAddress,
              onChanged: (value) {
                setState(() {
                  account = value;
                  isShowError = false;
                  checkLoginBtnVisible();
                });
              },
              autofocus: false,
              style: TextStyle(fontSize: 16.sp, color: color2B),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: widget.usePhone ? Lang.input_phone : Lang.input_mail,
                hintStyle: TextStyle(fontSize: 16.sp, color: const Color(0xFFBBBBBB)),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              controllerAccount.clear();
              setState(() {
                showAccountError = false;
              });
            },
            child: Image.asset("res/icons/icon_clear_input.png", width: 20.sp, height: 20.sp),
          ),
        ],
      ),
    );
  }

  getInputPasswordView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 48.sp,
          padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(24.sp)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: TextField(
                  controller: controllerPassword,
                  obscureText: !showPassword,
                  keyboardType: TextInputType.visiblePassword,
                  onChanged: (value) {
                    setState(() {
                      password = value;
                      isShowError = false;
                      checkRegisterPassword();
                      checkLoginBtnVisible();
                    });
                  },
                  autofocus: false,
                  style: TextStyle(fontSize: 16.sp, color: color2B),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: Lang.new_password,
                    hintStyle: TextStyle(fontSize: 16.sp, color: colorB8),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    showPassword = !showPassword;
                  });
                },
                child: Image.asset(showPassword ? "res/icons/password_visible.png" : "res/icons/password_invisible.png",
                    width: 16.sp, height: 16.sp),
              ),
            ],
          ),
        ),
        SizedBox(height: 8.sp),
        isEmpty(password)
            ? const SizedBox()
            : Wrap(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(width: 16.sp),
                      Icon(passwordLengthOk ? Icons.check_circle : Icons.circle_outlined,
                          size: 12.sp, color: passwordLengthOk ? colorGreen : colorRed),
                      SizedBox(width: 4.sp),
                      Container(
                        constraints: BoxConstraints(maxWidth: 1.sw - 88.sp),
                        child: MyText(Lang.password_length_rule, passwordLengthOk ? colorGreen : colorRed, 12.sp),
                      ),
                    ],
                  ),
                  SizedBox(width: 8.sp),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(width: 16.sp),
                      Icon(passwordContentOk ? Icons.check_circle : Icons.circle_outlined,
                          size: 12.sp, color: passwordContentOk ? colorGreen : colorRed),
                      SizedBox(width: 4.sp),
                      Container(
                        constraints: BoxConstraints(maxWidth: 1.sw - 88.sp),
                        child: MyText(Lang.password_content_rule, passwordContentOk ? colorGreen : colorRed, 12.sp),
                      ),
                    ],
                  ),
                ],
              ),
      ],
    );
  }

  getInputCodeView() {
    return Container(
      height: 48.sp,
      padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(24.sp)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: TextField(
              controller: controllerVerifyCode,
              focusNode: inputVerTextFieldFocus,
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if ('$verifyCode$verifyCode' == value && verifyCode.length >= 6) {
                  //键入重复的情况
                  setState(() {
                    controllerVerifyCode.value = TextEditingValue(
                      text: verifyCode, //不赋值新的 用旧的;
                      selection: TextSelection.fromPosition(
                        TextPosition(affinity: TextAffinity.downstream, offset: verifyCode.length),
                      ), //  此处是将光标移动到最后,
                    );
                  });
                } else {
                  setState(() {
                    verifyCode = value;
                    checkLoginBtnVisible();
                  });
                }
                isShowError = false;
              },
              maxLength: 6,
              autofocus: false,
              style: TextStyle(fontSize: 16.sp, color: color2B),
              decoration: InputDecoration(
                counterText: '',
                isDense: true,
                border: InputBorder.none,
                hintText: Lang.input_verify_code,
                hintStyle: TextStyle(fontSize: 16.sp, color: colorB8),
              ),
            ),
          ),
          Click(
            onTap: () {
              if (canGetCode) {
                startCounter();
              }
            },
            ms: 5000,
            child: MyText(
              isCounting ? "${Lang.refetch_verify_code} (${counter}s)" : Lang.get_verify_code,
              canGetCode ? color2B : colorB8,
              16.sp,
            ),
          ),
        ],
      ),
    );
  }

  onGetVerifyCode(data) {
    //焦点自动落在输入验证码一栏
    FocusScope.of(context).requestFocus(inputVerTextFieldFocus);

    setState(() {
      isCounting = true;
    });
    Global.toast(Lang.verify_code_sent);
    if (counterTimer != null) {
      counterTimer!.cancel();
      counterTimer = null;
    }
    counterTimer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      setState(() {
        counter -= 1;
        if (counter <= 0) {
          if (counterTimer != null) {
            counterTimer!.cancel();
            counterTimer = null;
          }
          counter = 60;
          isCounting = false;
        }
      });
    });
  }

  Widget getBodyView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 16.sp),
          child: MyText(Lang.reset_password, color2B, 24.sp, FontWeight.w500),
        ),
        getInputAccountView(),
        showAccountError
            ? Padding(
                padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 10.sp, 10.sp),
                child: MyText(widget.usePhone ? Lang.incorrect_phone : Lang.incorrect_mail, colorRed, 14.sp),
              )
            : SizedBox(height: 16.sp),
        getInputCodeView(),
        SizedBox(height: 16.sp),
        getInputPasswordView(),
        isShowError
            ? Padding(
                padding: EdgeInsets.only(top: 12.sp, left: 16.sp, right: 16.sp),
                child: MyText(msgError, colorRed, 14.sp),
              )
            : const SizedBox(),
        SizedBox(height: 16.sp),
        HCustomButton(
          key: _loginButtonKey,
          text: Lang.confirm_modify_code,
          bgColor: colorBrand,
          ms: 5000,
          radius: 40.sp,
          initVisible: false,
          onPress: () {
            hideKeyboard();
            HHttp.request(
              "/v3/auth/forgetPassword",
              "POST",
              (data) {
                Global.toast(Lang.reset_password_successful);
                pop();
              },
              errCallBack: (resp) {
                setState(() {
                  msgError = HHttp.getErrorMessage(resp);
                  isShowError = true;
                });
              },
              params: {
                widget.usePhone ? "phoneNumber" : "mailbox": account,
                "newPassword": password,
                "verificationCode": verifyCode,
              },
            );
          },
        ),
      ],
    );
  }

  startCounter() {
    if (isCounting) {
      return;
    }

    HHttp.request(
      widget.usePhone ? "/v3/auth/sendVerificationCode" : "/v3/auth/sendEmailCode",
      "POST",
      onGetVerifyCode,
      errCallBack: (resp) {
        setState(() {
          counter = 60;
          isCounting = false;
        });

        if (counterTimer != null) {
          counterTimer!.cancel();
          counterTimer = null;
        }
        setState(() {
          msgError = HHttp.getErrorMessage(resp);
          isShowError = true;
        });
      },
      params: widget.usePhone
          ? {"phoneNumber": account}
          : {
              "mailbox": account,
              "usedBy": "verify",
              "template": "iscanbot-reset-password-$lang",
            },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(),
        body: Stack(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                // 触摸收起键盘
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: const SizedBox(
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(20.sp, 48.sp, 20.sp, 20.sp),
              child: getBodyView(),
            ),
          ],
        ));
  }
}
