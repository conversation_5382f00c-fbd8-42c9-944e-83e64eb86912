import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ToothCameraTouchFoucs extends StatefulWidget {
  const ToothCameraTouchFoucs({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => ToothCameraTouchFoucsState();
}

class ToothCameraTouchFoucsState extends State<ToothCameraTouchFoucs>
    with SingleTickerProviderStateMixin {
  double dx = -500;
  double dy = -500;

  late Animation<double> aniFingerUI;
  late AnimationController aniControllFingerUI;

  @override
  initState() {
    super.initState();
    aniControllFingerUI = AnimationController(
        duration: const Duration(milliseconds: 500), vsync: this);
    aniFingerUI =
        CurvedAnimation(parent: aniControllFingerUI, curve: Curves.bounceInOut);
    aniFingerUI = TweenSequence([
      //这是一个动画序列，weight表示权重
      TweenSequenceItem(
          tween: Tween(begin: 1.0, end: 0.0)
              .chain(CurveTween(curve: Curves.easeIn)),
          weight: 1),
      TweenSequenceItem(
          tween: Tween(begin: 0.0, end: 1.0)
              .chain(CurveTween(curve: Curves.easeIn)),
          weight: 1),
      TweenSequenceItem(
          tween: Tween(begin: 1.0, end: 0.0)
              .chain(CurveTween(curve: Curves.easeIn)),
          weight: 1),
    ]).animate(aniControllFingerUI);
    aniFingerUI.addListener(() {
      setState(() {});
    });
  }

  setFingerFocus(dx, dy) {
    aniControllFingerUI.reset();
    aniControllFingerUI.forward();
    setState(() {
      this.dx = dx;
      this.dy = dy;
    });
  }

  @override
  dispose() {
    aniControllFingerUI.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
        left: aniFingerUI.value <= 0 ? 0 : (dx + 20.sp),
        top: aniFingerUI.value <= 0 ? 0 : (dy - 40.sp),
        child: Opacity(
            opacity: aniFingerUI.value,
            child: Image.asset(
              "res/scan/finger_focus.png",
              width: 80.sp,
              height: 80.sp,
            )));
  }
}
