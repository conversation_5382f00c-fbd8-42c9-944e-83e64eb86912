import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_uvc_camera/flutter_uvc_camera.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:path_provider/path_provider.dart';

class AiFaceCameraView extends BasePage {
  const AiFaceCameraView({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AiFaceCameraViewState();
}

class _AiFaceCameraViewState extends BasePageState<AiFaceCameraView> with WidgetsBindingObserver {
  bool isDispose = false;
  UVCCameraController? cameraController;

  // late AiFaceCameraIsolate isolate;

  late String tCacheImgPath;

  //
  dynamic routerParams;
  bool showPermissionText = false;

  bool isFrontCamera = true;

  @override
  void initState() {
    super.initState();
    initStateAsync();
  }

  sendSmileEvent(String operation, [String? content]) {
    Map param = {
      "value1": getCurrentContactId(),
      "value2": getCurrentTimelineId(),
    };
    if (isNotEmpty(content)) {
      param["content"] = content;
    }
    sendEventPoint("smile", operation, param);
  }

  void initStateAsync() async {
    initializeCamera();
    await Global.initGlobalAsync();
    // isolate = AiFaceCameraIsolate();
    // await isolate.start();
  }

  void initializeCamera() async {
    cameraController = UVCCameraController();
    cameraController?.msgCallback = (state) {};
  }

  //拍照
  Future<void> takePicture() async {
    try {
      String deviceStr = Global.deviceInfo["name"] ?? "phone";
      deviceStr = deviceStr.replaceAll(' ', '_');
      deviceStr = deviceStr.replaceAll('-', '_');
      String picName = "1-face$type-1-0-$deviceStr-${Global.appVersion}-${DateTime.now().millisecondsSinceEpoch}";
      Directory parentDir = await getTemporaryDirectory();
      String savePath = "${parentDir.path}/$picName.jpg";
      String? path = await cameraController!.takePicture();
      if (isEmpty(path)) {
        toast(Lang.loading_image_error);
        return;
      } else {
        File file = File(path!);
        if (file.existsSync()) {
          ffiFlipImage(path, savePath);
          // await file.copy(savePath);
          logger("saveUvcImage copy to $savePath");
          await file.delete();
          logger("saveUvcImage delete $path");
        }
      }

      tCacheImgPath = savePath!;
      logger("takePicture $savePath");

      showModalBottomSheet(
          isScrollControlled: true,
          context: context,
          builder: (BuildContext context) {
            return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: const BoxDecoration(
                  color: Colors.black,
                ),
                child: Stack(
                  children: [
                    Align(alignment: Alignment.center, child: Image.file(File(tCacheImgPath))),
                    Padding(
                        padding: EdgeInsets.symmetric(vertical: 64.sp),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: IconButton(
                              onPressed: () {
                                pop();
                              },
                              icon: Image.asset("res/icons/tp_close.png", width: 36.sp, height: 36.sp)),
                        )),
                    SafeArea(
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Padding(
                            padding: EdgeInsets.all(64.sp),
                            child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                      onTap: () async {
                                        sendSmileEvent("retake", "after_take");
                                        delay(100, () async {
                                          Navigator.pop(context);
                                        });
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        width: 180.sp,
                                        height: 76.sp,
                                        decoration: BoxDecoration(
                                          color: const Color(0xBB333333),
                                          borderRadius: BorderRadius.circular(16.sp),
                                          border: Border.all(color: Colors.white, width: 1.sp),
                                        ),
                                        child: Text(Lang.retake_photo,
                                            style: TextStyle(
                                                fontSize: 24.sp, color: Colors.white, fontWeight: FontWeight.w500)),
                                      )),
                                  SizedBox(width: 128.sp),
                                  GestureDetector(
                                      onTap: () async {
                                        routerParams["updateStateByType"](
                                          "newSmilePhoto",
                                          {
                                            "type": type,
                                            "path": tCacheImgPath,
                                          },
                                        );
                                        int index = types.indexOf(type);
                                        logger("saveUvcImage $index ${types.length}");
                                        if (index < 0 || index >= types.length - 1) {
                                          sendSmileEvent("finish");
                                          Navigator.pop(context);
                                          delay(100, () async {
                                            pop();
                                          });
                                        } else {
                                          setState(() {
                                            type = types[index + 1];
                                          });
                                          Navigator.pop(context);
                                        }
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        width: 180.sp,
                                        height: 76.sp,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(16.sp),
                                          border: Border.all(color: Colors.white, width: 1.sp),
                                        ),
                                        child: Text(Lang.confirm,
                                            style: TextStyle(
                                                fontSize: 24.sp, color: color2B, fontWeight: FontWeight.w500)),
                                      )),
                                ])),
                      ),
                    ),
                  ],
                ));
          });
    } on Exception catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  String type = "";
  List<String> types = [];

  @override
  Widget build(BuildContext context) {
    if (isEmpty(type)) {
      routerParams ??= Global.getRouterParams(context);
      type = routerParams["type"];
      types = routerParams["types"] ?? [];
      logger("saveUvcImage $type $types");
    }

    return Stack(
      children: <Widget>[
        Container(
          width: 1.sw,
          height: 1.sh,
          decoration: const BoxDecoration(
            color: Color(0x3333333e),
          ),
        ),
        cameraController != null
            ? Center(
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationY(pi), // 水平镜像翻转
                  child: UVCCameraView(
                    cameraController: cameraController!,
                    cameraId: 61832,
                    width: 1.sw,
                    height: 1.sh,
                  ),
                ),
              )
            : const SizedBox(),
        Transform(
          alignment: Alignment.center,
          transform: isFrontCamera ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0) : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
          child: Image.asset(
            "res/scan/face_mask_$type.png",
            width: 1.sw,
            height: 1.sh,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          left: 32.sp,
          top: 56.sp,
          child: Click(
            onTap: () {
              Navigator.pop(context);
            },
            child: Image.asset("res/icons/tp_close.png", width: 36.sp, height: 36.sp),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: Padding(
            padding: EdgeInsets.only(top: 56.sp),
            child: MyText(faceMap[type] ?? "", Colors.white, 36.sp, FontWeight.w600),
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: EdgeInsets.only(right: 240.sp),
            child: IconButton(
                onPressed: () {
                  takePicture();
                  sendSmileEvent("take_photo");
                },
                icon: Image.asset("res/icons/tp_take_photo.png"),
                iconSize: 120.sp),
          ),
        ),
        // SafeArea(
        //   child: Align(
        //     alignment: Alignment.bottomCenter,
        //     child: Padding(
        //         padding: EdgeInsets.all(20.sp),
        //         child: Row(
        //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //             crossAxisAlignment: CrossAxisAlignment.center,
        //             children: [
        //               IconButton(
        //                   onPressed: null,
        //                   icon: Image.asset(
        //                     "res/icons/tp_switch_camera.png",
        //                     color: Colors.transparent,
        //                   ),
        //                   iconSize: 26.sp),
        //               IconButton(
        //                   onPressed: () {
        //                     takePicture();
        //                     sendSmileEvent("take_photo");
        //                   },
        //                   icon: Image.asset("res/icons/tp_take_photo.png"),
        //                   iconSize: 80.sp),
        //               SizedBox(width: 28.sp)
        //             ])),
        //   ),
        // ),
      ],
    );
  }

  @override
  void dispose() {
    isDispose = true;
    // isolate.dispose();

    // SystemChrome.setPreferredOrientations([
    //   DeviceOrientation.portraitUp,
    //   DeviceOrientation.portraitDown,
    // ]);

    logger("AiFaceCameraView dispose");
    super.dispose();
  }
}
