import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraTaskWidget.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/utils/TFLiteUtils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:path_provider/path_provider.dart';

class ToothCameraNewModalTask {
  ToothCameraStepEnum toothCameraStep;
  ToothCameraTypeEnum toothCameraType;
  ScanTaskDirEnum taskDirection;
  Timer? taskTimer;
  dynamic funMap;

  //set fps
  int streamTypeFps = 1;
  int totalSecond = 0;

  //set cut frames
  dynamic keyFrameSeconds;
  dynamic keyFrameProcessCounts;
  int curKeySecondIndex = 0;

  int scanTool = 1;

  int jlWaitAddCount = 0;
  List waitProcessCameraImgs = [];
  List waitProcessCameraImgsTimestamp = [];
  bool isProcessing = false;
  List resultObjs = [];

  //规则分数数组
  List<double> targetScoreList = [];

  //是否启用保护机制（赋值位置有详细说明）
  bool isEnableProtect = true;
  List protectProStartEndFramesIndex = [];

  dynamic resultCallback;
  final DeviceOrientation currentOrientation;
  bool paused = false;

  ToothCameraNewModalTask(
      {required this.funMap,
      required this.totalSecond,
      required this.currentOrientation,
      required this.toothCameraStep,
      required this.toothCameraType,
      required this.taskDirection}) {
    if (taskDirection == ScanTaskDirEnum.lr) {
      targetScoreList = [0, 0.5, 1];
    } else {
      targetScoreList = [1];
    }
    //是否启用保护机制
    //* 为了防止模型针对某些牙齿口型，一直识别错误的情况（比如方向反了，一直给错误方向,导致无法正常全局取出正常图）
    //* 根据设定的帧范围内来取出最适合的
    if (taskDirection == ScanTaskDirEnum.lr) {
      //13秒前提下(每一组都是左开右闭)
      protectProStartEndFramesIndex = [
        [8, 11],
        [14, 20],
        [23, 26],
      ];
    } else {
      //5秒前提下(每一组都是左开右闭)
      protectProStartEndFramesIndex = [
        [4, 10],
      ];
    }
  }

  //寻找最接近目标的分数和下标
  dynamic findObjByNearTargetScore(double targetScore, int arrayIndex) {
    logger(("findObjByNearTargetScore 寻找最近的目标口扫图,目标分数 -> $targetScore"));
    //记录最近一次的分差
    double lastScoreX = -1;
    //找到的最接近对应分数的obj下标
    dynamic nearObj;

    int forStart = 0;
    int forEnd = resultObjs.length;
    if (isEnableProtect) {
      forStart = protectProStartEndFramesIndex[arrayIndex][0] * streamTypeFps ~/ 2;
      forEnd = protectProStartEndFramesIndex[arrayIndex][1] * streamTypeFps ~/ 2;
      if (forEnd <= forStart) {
        if (forEnd < resultObjs.length) {
          forEnd = forStart + 1;
        } else if (forStart > 0) {
          forStart = forStart - 1;
        }
      }
    }

    logger(("findObjByNearTargetScore forStart: $forStart, forEnd: $forEnd"));

    for (int i = forStart; i < forEnd; i++) {
      Map zeroOneTwoRectInfos = Global.getNewModalRectInfo(resultObjs[i]["lastObjects"][0]);
      double curScore = -1;
      if (taskDirection == ScanTaskDirEnum.lr) {
        curScore = ((zeroOneTwoRectInfos["upper"].isNotEmpty ? zeroOneTwoRectInfos["upper"][1] : -1) +
                (zeroOneTwoRectInfos["lower"].isNotEmpty ? zeroOneTwoRectInfos["lower"][1] : -1)) /
            2;
      } else {
        curScore = zeroOneTwoRectInfos[taskDirection == ScanTaskDirEnum.toUp ? "upper" : "lower"].isNotEmpty
            ? zeroOneTwoRectInfos[taskDirection == ScanTaskDirEnum.toUp ? "upper" : "lower"][0]
            : -1;
      }
      //主要为了避免，牙弓形更好的口扫图，不一定露出了 门牙 的情况：
      //上下选图时，要尽可能先保证 上牙的11，12，21，22 或下牙的 31，32，41，42 ，四颗存在三颗才符合
      if (taskDirection != ScanTaskDirEnum.lr) {
        Map zeroOneTwoRectInfos = Global.getNewModalRectInfo(resultObjs[i]["lastObjects"][0]);
        if (taskDirection == ScanTaskDirEnum.toUp) {
          if (!zeroOneTwoRectInfos["isHave11122122"]) {
            logger(("findObjByNearTargetScore 此口扫图不符合上颌[11,12,21,22]中含有三颗存在的问题，因此pass～"));
            continue;
          }
        } else {
          if (!zeroOneTwoRectInfos["isHave31324142"]) {
            logger(("findObjByNearTargetScore 此口扫图不符合上颌[31,32,41,22]中含有三颗存在的问4题，因此pass～"));
            continue;
          }
        }
      }
      if (lastScoreX == -1) {
        lastScoreX = (targetScore - curScore).abs();
        nearObj = resultObjs[i];
        nearObj["frameIndex"] = i;
        logger(
            ("findObjByNearTargetScore 目标分数:$targetScore，已找到的分差:$lastScoreX ->-(初始))->-> 找到更小的分差:${(targetScore - curScore).abs()}($curScore)  "));
      } else {
        if ((targetScore - curScore).abs() < lastScoreX) {
          lastScoreX = (targetScore - curScore).abs();
          nearObj = resultObjs[i];
          nearObj["frameIndex"] = i;
          logger(
              ("findObjByNearTargetScore 目标分数:$targetScore，已找到的分差:$lastScoreX ->->->-> 找到更小的分差:${(targetScore - curScore).abs()}($curScore)  "));
        } else {
          logger(
              ("findObjByNearTargetScore $targetScore，已找到的分差:$lastScoreX -> 不合适分差:${(targetScore - curScore).abs()}($curScore)  "));
        }
      }
    }
    if (lastScoreX == -1) {
      nearObj = resultObjs[forStart];
      nearObj["frameIndex"] = forStart;
    }
    logger(("findObjByNearTargetScore 结果 $nearObj"));
    return nearObj;
  }

  Future<String> saveImageGetPath(dynamic obj, List imgPaths) async {
    if (selectScanByUltra(obj["_scanType"])) {
      String path = obj["_imagePath"];
      imgPaths.add(path);
      return path;
    } else {
      final startMs = DateTime.now().millisecondsSinceEpoch;
      String path = await funMap["isolateCamera2Img"](obj["_cameraImage"], saveImgName: obj["_timestampStr"]);
      logger("[Camera2Img]路径：path:$path  ms:${DateTime.now().millisecondsSinceEpoch - startMs}");
      imgPaths.add(path);
      return path;
    }
  }

  showResult() async {
    logger(("[CameraImg]显示本轮扫描结果:(共处理 ${resultObjs.length} 个图片)"));
    List imgPaths = [];
    List useIndexArray = [];
    List noUseIndexArray = [];
    List useErrorInfos = [];
    List useFrameIndexArray = [];
    List noUseFrameIndexArray = [];
    Offset centerPoint = funMap["getCenterPoint"]();
    for (int i = 0; i < targetScoreList.length; i++) {
      dynamic nearObj = findObjByNearTargetScore(targetScoreList[i], i);
      String path = await saveImageGetPath(nearObj, imgPaths);
      if (taskDirection == ScanTaskDirEnum.lr) {
        useIndexArray.add(i);
        useFrameIndexArray.add(nearObj["frameIndex"]);
        useErrorInfos.add(Global.getResultObjErrorInfo(
            nearObj, 0, i + 1, currentOrientation, toothCameraStep, toothCameraType,
            checkImgPath: path, centerPoint: centerPoint));
      } else {
        useIndexArray.add(i);
        useFrameIndexArray.add(nearObj["frameIndex"]);
        useErrorInfos.add(Global.getResultObjErrorInfo(nearObj, 0, taskDirection == ScanTaskDirEnum.toUp ? 4 : 5,
            currentOrientation, toothCameraStep, toothCameraType,
            checkImgPath: path, centerPoint: centerPoint));
      }
    }
    //左45*/右45*
    if (taskDirection == ScanTaskDirEnum.lr) {
      int leftMidFrameIndex = (useFrameIndexArray[0] + useFrameIndexArray[1]) ~/ 2 + 1;
      int rightMidFrameIndex = (useFrameIndexArray[1] + useFrameIndexArray[2]) ~/ 2 + 1;
      //左45*
      dynamic nearObjLeft = resultObjs[leftMidFrameIndex];
      await saveImageGetPath(nearObjLeft, imgPaths);
      noUseIndexArray.add(3);
      noUseFrameIndexArray.add(leftMidFrameIndex);
      // 右45*
      dynamic nearObjRight = resultObjs[rightMidFrameIndex];
      await saveImageGetPath(nearObjRight, imgPaths);
      noUseIndexArray.add(4);
      noUseFrameIndexArray.add(rightMidFrameIndex);
    } else {
      // 上下中间帧
      int upDownMidFrameIndex = useFrameIndexArray[0] ~/ 2 + 1;
      dynamic nearObjRight = resultObjs[upDownMidFrameIndex];
      await saveImageGetPath(nearObjRight, imgPaths);
      noUseIndexArray.add(1);
      noUseFrameIndexArray.add(upDownMidFrameIndex);
    }

    logger(("[CameraImg] 此阶段结束了! \n设定阶段总时间(sec):$totalSecond s\n"));

    if (resultCallback != null) {
      resultCallback({
        "imgPaths": imgPaths,
        "useIndexArray": useIndexArray,
        "useErrorInfos": useErrorInfos,
        "noUseIndexArray": noUseIndexArray,
        "useFrameIndexArray": useFrameIndexArray,
        "noUseFrameIndexArray": noUseFrameIndexArray,
      });
    }
  }

  isolateCamera2Img() async {
    logger("[CameraImg] isolateCamera2Img ${resultObjs.length}");
    isProcessing = true;
    final startMs = DateTime.now().millisecondsSinceEpoch;
    dynamic resultObj;
    String timestampStr = waitProcessCameraImgsTimestamp[0];
    // if (selectScanByUltra(scanTool)) {
    String path = waitProcessCameraImgs[0];
    resultObj = await TFLiteUtils.callTfliteLogic([path], 2);
    resultObj["_imagePath"] = path;
    // } else {
    //   CameraImage cImage = waitProcessCameraImgs[0];
    //   resultObj = await TFLiteUtils.callTfliteLogic(cImage, 3);
    //   resultObj["_cameraImage"] = cImage;
    // }
    resultObj["_scanType"] = scanTool;
    resultObj["_timestampStr"] = timestampStr;
    resultObjs.add(resultObj);

    logger("[CameraImg]处理完成：ms:${DateTime.now().millisecondsSinceEpoch - startMs}");
    if (waitProcessCameraImgs.isNotEmpty) {
      waitProcessCameraImgs.removeAt(0);
      waitProcessCameraImgsTimestamp.removeAt(0);
    }

    if (waitProcessCameraImgs.isNotEmpty) {
      logger("[CameraImg]等待处理个数:${waitProcessCameraImgs.length}");
      isolateCamera2Img();
    } else {
      logger("[CameraImg]没有等待处理的图片: ${resultObjs.length} $streamTypeFps * $totalSecond}");
      isProcessing = false;
      if (resultObjs.length >= streamTypeFps * totalSecond) {
        showResult();
      }
    }
  }

  resetAndClearCache() {
    if (taskTimer != null) {
      taskTimer!.cancel();
      taskTimer = null;
    }
    isProcessing = false;
    resultCallback = null;
    waitProcessCameraImgs = [];
    waitProcessCameraImgsTimestamp = [];
    resultObjs = [];
    curKeySecondIndex = 0;
    jlWaitAddCount = 0;
  }

  setPaused(bool p) {
    paused = p;
  }

  start(callback) async {
    logger(
        "CameraImg 开始${taskDirection == ScanTaskDirEnum.lr ? "左->右或右->左" : taskDirection == ScanTaskDirEnum.toUp ? "向上" : "向下"} 阶段开始了");

    resetAndClearCache();
    resultCallback = callback;
    taskTimer = Timer.periodic(Duration(milliseconds: 1000 ~/ streamTypeFps), (Timer t) async {
      if (paused) {
        return;
      }
      if (jlWaitAddCount < streamTypeFps * totalSecond) {
        if (selectScanByUltra(scanTool)) {
          Directory parentDir = await getTemporaryDirectory();
          String fullPath = "${parentDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg";
          funMap["saveScanImage"](fullPath, () {
            waitProcessCameraImgs.add(fullPath);
            jlWaitAddCount++;
            logger("CameraImg waitProcessCameraImgs add ${waitProcessCameraImgs.length} $jlWaitAddCount");
            String timestampStr = DateTime.now().millisecondsSinceEpoch.toString();
            waitProcessCameraImgsTimestamp.add(timestampStr);
            if (!isProcessing) {
              isolateCamera2Img();
            }
          });
        } else {
          waitProcessCameraImgs.add(funMap["getStreamImage"]());
          jlWaitAddCount++;
          logger("CameraImg waitProcessCameraImgs add ${waitProcessCameraImgs.length} $jlWaitAddCount");
          String timestampStr = DateTime.now().millisecondsSinceEpoch.toString();
          waitProcessCameraImgsTimestamp.add(timestampStr);
          if (!isProcessing) {
            isolateCamera2Img();
          }
        }
      } else {
        stop();
      }
    });
  }

  void stop() {
    if (taskTimer != null) {
      taskTimer!.cancel();
      taskTimer = null;
    }
  }
}
