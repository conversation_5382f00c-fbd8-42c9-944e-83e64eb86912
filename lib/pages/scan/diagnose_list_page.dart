import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/diagnose_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class DiagnoseListPage extends BasePage {
  const DiagnoseListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return DiagnoseListPageState();
  }
}

class DiagnoseListPageState extends BasePageState<DiagnoseListPage> {
  List<DiagnoseInfo> dataList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;

  List intentTagList = [];

  @override
  initState() {
    super.initState();
    addEventListener<EventUpdateDiagnose>((event) {
      if (isNotEmpty(dataList)) {
        setState(() {
          for (int i = 0; i < dataList.length; i++) {
            if (event.record.recordId == dataList[i].recordId) {
              event.record.createTime = dataList[i].createTime;
              event.record.modifyTime = DateTime.now().millisecondsSinceEpoch.toString();
              dataList[i] = event.record;
            }
          }
        });
      }
      if (pageIndex == 0) {
        delay(100, refreshDataList);
      }
    });
    initAsyncState();
  }

  initAsyncState() async {
    await refreshDataList();
    getDiagnoseConfigJson((json) {
      if (isNotEmpty(json) && isNotEmpty(json["IntentProject"])) {
        Map map = json["IntentProject"] as Map;
        setState(() {
          List list = [];
          for (String key in map.keys) {
            dynamic content = map[key]["projectNames"];
            if (isNotEmpty(content)) {
              if (content is List) {
                list.addAll(content);
              } else if (content is Map) {
                list.add(content);
              }
            }
          }
          intentTagList = list;
        });
      }
    });
    disconnectUltra(disconnectWifi: true);
  }

  refreshDataList() async {
    return await getDataList(true);
  }

  onLoadMore() {
    getDataList(false);
  }

  getDataList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    HHttp.request("/v3/doctor/initial/page", "POST", (data) {
      List<DiagnoseInfo> list = (data["initialVoList"] as List).map((j) => DiagnoseInfo.fromJson(j)).toList();

      if (isNotEmpty(list)) {
        List<String> ids = [];
        for (var item in list) {
          if (isNotEmpty(item.avatarFileId)) {
            ids.add(item.avatarFileId);
          }
        }
        downloadThumbnail(
          ids,
          (id, path) {
            for (var item in list) {
              if (item.avatarFileId == id) {
                setState(() {
                  item.smilePhoto = path;
                });
              }
            }
          },
          "RECORD",
        );
      }
      setState(() {
        allLoad = true;
        if (refresh) {
          dataList = list;
          if (dataList.length >= pageSize) {
            allLoad = false;
          }
        } else {
          if (isNotEmpty(list)) {
            if (list.length >= pageSize) {
              allLoad = false;
            }
            dataList.addAll(list);
          }
        }
        isLoading = false;
      });
    }, errCallBack: (resp) {
      setState(() {
        isLoading = false;
      });
    }, params: {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      // "filterParamList": [
      //   {
      //     "columnName": "caseId",
      //     "columnValue": "abf6331fd65d402a81043eec373567ec",
      //     "operator": "eq",
      //   }
      // ],
    });
  }

  String getTagText(String tag) {
    if (isNotEmpty(intentTagList)) {
      for (dynamic map in intentTagList) {
        if (map.values.contains(tag) && isNotEmpty(map[getLangKey()])) {
          // logger("getTagsText $tag -> ${map[getLangKey()]}");
          return map[getLangKey()];
        }
      }
    }
    return tag;
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < dataList.length; i++) {
      DiagnoseInfo record = dataList[i];
      allWidget.add(
        GestureDetector(
          onTap: () {
            pushNamed("DiagnoseInfoRecord", {"infoJson": record.toJson()});
          },
          child: Container(
            margin: EdgeInsets.fromLTRB(0, 6.sp, 0, 6.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.sp),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      isNotEmpty(record.smilePhoto)
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(40.sp),
                              child: Image.file(
                                File(getLocalPath(record.smilePhoto)),
                                width: 48.sp,
                                height: 48.sp,
                                fit: BoxFit.cover,
                              ),
                            )
                          : Image.asset(
                              "res/imgs/record_avatar.png",
                              width: 48.sp,
                              height: 48.sp,
                              fit: BoxFit.cover,
                            ),
                      SizedBox(width: 12.sp),
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MyText(record.name, color2B, 16.sp, FontWeight.w500),
                            SizedBox(height: 8.sp),
                            MyText(
                                "${record.getGender()}  ${sprintf(Lang.year_old, [record.getAge()])}  ${record.phone}",
                                color2B,
                                14.sp),
                            isNotEmpty(record.intentTagList)
                                ? Wrap(
                                    children: [
                                      Container(
                                        height: 22.sp,
                                        margin: EdgeInsets.only(top: 4.sp),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            MyText("${Lang.deal_project}:  ", color2B, 14.sp),
                                          ],
                                        ),
                                      ),
                                      ...record.intentTagList
                                          .sublist(0, math.min(5, record.intentTagList.length))
                                          .map(
                                            (tag) => Container(
                                              constraints: BoxConstraints(minHeight: 22.sp),
                                              margin: EdgeInsets.only(top: 4.sp, right: 8.sp),
                                              padding: EdgeInsets.fromLTRB(4.sp, 1.sp, 4.sp, 2.sp),
                                              decoration: BoxDecoration(
                                                color: colorE1,
                                                borderRadius: BorderRadius.circular(4.sp),
                                              ),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  MyText(getTagText(tag), color2B, 12.sp),
                                                ],
                                              ),
                                            ),
                                          )
                                          .toList(),
                                    ],
                                  )
                                : const SizedBox(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 12.sp),
                Container(height: 1.sp, color: colorF3),
                SizedBox(height: 6.sp),
                Wrap(
                  children: [
                    Padding(
                      padding: EdgeInsets.fromLTRB(16.sp, 6.sp, 0, 6.sp),
                      child: MyText("${Lang.update_time}:  ${record.getDate(record.modifyTime)}", color7C, 12.sp),
                    ),
                    Padding(
                      padding: EdgeInsets.fromLTRB(16.sp, 6.sp, 0, 6.sp),
                      child: MyText("${Lang.create_time}:  ${record.getDate(record.createTime)}", color7C, 12.sp),
                    ),
                    // GestureDetector(
                    //   onTap: () {
                    //     Global.showAlertDialog(
                    //       Lang.delete_diagnose_title,
                    //       Lang.delete_diagnose_content,
                    //       okText: Lang.delete,
                    //       okColor: colorRed,
                    //       okCallBack: () {},
                    //     );
                    //   },
                    //   child: Padding(
                    //     padding: EdgeInsets.all(8.sp),
                    //     child: Image.asset(
                    //       "res/icons/icon_delete.png",
                    //       width: 20.sp,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
                SizedBox(height: 6.sp),
              ],
            ),
          ),
        ),
      );
    }
    return allWidget;
  }

  // 下拉刷新
  Future onRefresh() async {
    await refreshDataList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(Lang.first_diagnose)),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 0),
        child: Column(
          children: [
            HCustomButton(
              key: UniqueKey(),
              width: 335.sp,
              height: 42.sp,
              fontSize: 14.sp,
              onPress: () {
                pushNamed("DiagnoseInfoRecord", {});
              },
              bgColor: colorBrand,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset("res/icons/icon_add.png", width: 20.sp),
                  SizedBox(width: 4.sp),
                  MyText(Lang.add_diagnose, Colors.white, 16.sp, FontWeight.w500),
                ],
              ),
            ),
            SizedBox(height: 8.sp),
            Expanded(
              child: RefreshList(
                childList: getListView(),
                isLoading: isLoading,
                onRefresh: onRefresh,
                onLoadMore: onLoadMore,
                nullImgName: "res/icons/empty.png",
                nullText: Lang.no_record,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void onRouteResume(Route nextRoute) {
    super.onRouteResume(nextRoute);
    disconnectUltra(disconnectWifi: true);
  }
}
