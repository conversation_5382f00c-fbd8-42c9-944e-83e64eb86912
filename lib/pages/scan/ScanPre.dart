import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

import '../../Utils/AudioPlayerUtil.dart';

class ScanPre extends BasePage {
  const ScanPre({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ScanPreState();
}

class _ScanPreState extends BasePageState<ScanPre> with SingleTickerProviderStateMixin {
  AnimationController? _animationController;
  double _animMarginTop = 0;

  GlobalKey globalButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 500))
      ..repeat(reverse: true);
    _animationController!.addListener(() {
      setState(() {
        _animMarginTop = _animationController!.value * 15;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    if (_animationController != null) {
      _animationController!.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.scan_intraoral_photo),
      ),
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            height: double.infinity,
            margin: EdgeInsets.fromLTRB(64.sp, 80.sp, 64.sp, 144.sp),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.sp),
              color: const Color(0xffeeeeee),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MyText(Lang.prepare_step, color2B, 36.sp, FontWeight.w600),
                    SizedBox(height: 22.sp),
                    Text(Lang.setup_scanner_on_ultra,
                        style: TextStyle(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w500,
                          color: color2B,
                        )),
                    SizedBox(height: 24.sp),
                    Text(Lang.advice_bigger_iscan,
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xff999999),
                        )),
                    SizedBox(height: 12.sp),
                    Text(Lang.setup_ultra_tip1,
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xff999999),
                        )),
                    SizedBox(height: 12.sp),
                    Text(
                      Lang.setup_ultra_tip2,
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xff999999),
                      ),
                    ),
                    SizedBox(height: 34.sp),
                  ],
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 20.sp),
                      child: Stack(
                        children: [
                          Image.asset(
                            "res/scan/ultra_setup_demo.png",
                            width: 526.sp,
                            fit: BoxFit.contain,
                          ),
                          Positioned(
                            right: 132.sp,
                            top: 168.sp,
                            child: Container(
                              margin: EdgeInsets.only(top: _animMarginTop),
                              child: Image.asset(
                                "res/scan/ultra_setup_anim.png",
                                width: 36.sp,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Click(
              onTap: () {
                AudioPlayerUtil.stopSound();
                dynamic argument = Global.getRouterParams(context);
                Navigator.pushReplacementNamed(context, "ToothCameraView", arguments: argument);
              },
              child: Container(
                margin: EdgeInsets.only(bottom: 40.sp),
                padding: EdgeInsets.fromLTRB(120.sp, 20.sp, 120.sp, 20.sp),
                decoration: BoxDecoration(
                  color: colorBrand,
                  borderRadius: BorderRadius.circular(40.sp),
                  boxShadow: [
                    BoxShadow(
                      color: colorBrand,
                      blurRadius: 1.sp,
                      spreadRadius: 1.sp,
                    ),
                  ],
                ),
                child: MyText(Lang.next_step, Colors.white, 24.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
