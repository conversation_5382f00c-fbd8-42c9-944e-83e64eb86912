import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';

ui.Image? image;

class UltraHView extends StatefulWidget {
  double width;
  double height;

  UltraHView(this.width, this.height);

  @override
  State<StatefulWidget> createState() {
    return UltraHViewState();
  }
}

class UltraHViewState extends State<UltraHView> {
  Uint8List? data;

  Timer? dataTimer;
  ChangeNotifier notifier = ChangeNotifier();

  void decodeImageFromList(Uint8List list, ImageDecoderCallback callback) {
    _decodeImageFromListAsync(list, callback);
  }

  Future<void> _decodeImageFromListAsync(Uint8List list, ImageDecoderCallback callback) async {
    final Codec codec =
        await instantiateImageCodec(list, targetWidth: widget.width.toInt(), targetHeight: widget.height.toInt());
    final FrameInfo frameInfo = await codec.getNextFrame();
    callback(frameInfo.image);
  }

  startDataTimer() async {
    if (dataTimer != null) {
      dataTimer!.cancel();
      dataTimer = null;
    }

    dataTimer = Timer.periodic(const Duration(milliseconds: 33), (_) async {
      dynamic imageInfo = ffiRetrieveImage();
      if (imageInfo == null) return;
      Uint8List imageData = imageInfo["imgDatas"];
      dynamic pointer = imageInfo["pointer"];
      decodeImageFromList(imageData, (result) {
        image?.dispose();
        image = result;
        notifier.notifyListeners();
        ffiFree(pointer);
      });
    });
  }

  @override
  void initState() {
    super.initState();
    startDataTimer();
  }

  @override
  void dispose() {
    if (dataTimer != null) {
      dataTimer!.cancel();
      dataTimer = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      width: widget.width,
      height: widget.height,
      child: CustomPaint(
        foregroundPainter: MyPainter(notifier),
        child: SizedBox(
          width: widget.width,
          height: widget.height,
        ),
      ),
      //: const SizedBox(),
    );
  }
}

class MyPainter extends CustomPainter {
  ChangeNotifier repaint_;

  MyPainter(this.repaint_) : super(repaint: repaint_);

  @override
  void paint(Canvas canvas, Size size) {
    // 如果图像还没有被加载，则加载它
    // final Completer completer = Completer<ImageInfo>();
    // final Uint8List bytes = Uint8List.fromList(imageData);
    // final ImageProvider imageProvider = MemoryImage(bytes);
    // imageStream = imageProvider.resolve(ImageConfiguration.empty);
    // imageStream!.addListener(ImageStreamListener((ImageInfo info, bool _) {
    //   // 当图像数据加载完成后，保存Image对象并通知绘制器进行重绘
    //   image = info.image;
    //   return completer.complete();
    // }));

    // 如果图像已经加载完成，则绘制它
    if (image != null) {
      canvas.drawImage(image!, Offset.zero, Paint());
    }
  }

  @override
  bool shouldRepaint(MyPainter oldDelegate) {
    return true; //data != oldDelegate.data;
  }
}
