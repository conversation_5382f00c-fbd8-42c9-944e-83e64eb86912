import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/scan_activity_item_view.dart';
import 'package:mooeli/pages/scan/scan_record_item_view.dart';
import 'package:mooeli/pages/scan/scan_record_list_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/highlight_text.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_grid.dart';
import 'package:sprintf/sprintf.dart';

class ActivitySearchPage extends BasePage {
  const ActivitySearchPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ActivitySearchPageState();
  }
}

class ActivitySearchPageState extends BasePageState<ActivitySearchPage> with SingleTickerProviderStateMixin {
  List<ScanActivity> activityList = [];
  List<ScanActivity> localActivityList = [];
  List<ScanInfo> uploadList = [];
  List<ScanInfo> recordList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;

  Timer? timer;
  TextEditingController keywordController = TextEditingController();
  FocusNode focusNode = FocusNode();

  TabController? _tabController;
  int tabIndex = 0;
  List<String> tabTitles = [Lang.search_by_activity_name, Lang.search_by_person_name, Lang.search_by_record_name];
  List<String> hintTexts = [Lang.input_activity_name, Lang.input_person_name, Lang.input_record_name];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    addEventListener<EventDeleteActivity>((event) {
      Global.showAlertDialog(
        Lang.confirm_delete_activity_name,
        Lang.delete_activity_not_recoverd,
        okColor: colorRed,
        okText: Lang.delete,
        okCallBack: () {
          HHttp.request(
            "/v3/quickscan/campaign/delete",
            "POST",
            (data) {
              toast(Lang.delete_success);
              refreshLingyaList();
            },
            params: {
              "campaignId": event.activity.campaignId,
            },
          );
        },
      );
    });
    addEventListener<EventDeleteRecord>((event) {
      Global.showAlertDialog(
        Lang.delete_record,
        Lang.delete_not_recoverd,
        okColor: colorRed,
        okText: Lang.delete,
        okCallBack: () {
          if (event.record.status == RecordStatus.completeUpload) {
            HHttp.request(
              "/v3/quickscan/scanRecord/delete",
              "POST",
              (data) {
                recordList.remove(event.record);
                toast(Lang.delete_success);
                setState(() {});
              },
              params: {
                "scanRecordId": event.record.recordId,
              },
            );
          } else {
            recordList.remove(event.record);
            deleteRecords([event.record.recordId]);
            setState(() {});
          }
        },
      );
    });
    delay(200, () {
      showKeyboard(focusNode);
    });
    getAllLocalActivity();
  }

  void getAllLocalActivity() async {
    localActivityList = await getLocalActivityList();
  }

  refreshLingyaList() async {
    return await getSearchList(true);
  }

  onLoadMore() {
    getSearchList(false);
  }

  getSearchList(bool refresh) async {
    focusNode.unfocus();
    if (isLoading || !refresh && allLoad) {
      return;
    }
    List filters = [];

    addFilterParam(name, operator, value) {
      filters.add({
        "columnName": name,
        "filterEnum": operator,
        "columnValue": value,
      });
    }
    // 按活动名称：/v3/quickscan/campaign/page
    //{
    //     "columnName": "campaignName",
    //     "columnValue": "1",
    //     "filterEnum": "like"
    // }
    // 按归属人：/v3/quickscan/campaign/page
    //{
    //   "columnName": "personName",
    //   "columnValue": "1",
    //   "filterEnum": "like"
    // }
    // 按患者：/v3/quickscan/scanRecord/page
    //{
    //   "columnName": "index",
    //   "columnValue": "1",
    //   "filterEnum": "like"
    // }

    if (isNotEmpty(keywordController.text)) {
      switch (tabIndex) {
        case 0:
          addFilterParam("campaignName", "like", keywordController.text);
          break;
        case 1:
          addFilterParam("personName", "like", keywordController.text);
          break;
        case 2:
          addFilterParam("index", "like", keywordController.text);
          break;
      }
    } else {
      setState(() {
        pageIndex = 0;
        isLoading = false;
        activityList.clear();
        recordList.clear();
      });
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    Map<String, dynamic> params = {};
    params = {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "filterParamList": filters,
    };
    if (tabIndex != 2) {
      HHttp.request(
        "/v3/quickscan/campaign/page",
        "POST",
        (data) {
          List<ScanActivity> list = (data["data"] as List).map((j) => ScanActivity.fromJson(j)).toList();

          setState(() {
            allLoad = true;
            if (refresh) {
              activityList = list;
              if (activityList.length >= pageSize) {
                allLoad = false;
              }
            } else {
              if (isNotEmpty(list)) {
                if (list.length >= pageSize) {
                  allLoad = false;
                }
                activityList.addAll(list);
              }
            }
            isLoading = false;
          });
        },
        errCallBack: (_) {
          setState(() {
            isLoading = false;
          });
        },
        params: params,
      );
    } else {
      HHttp.request(
        "/v3/quickscan/scanRecord/page",
        "POST",
        (data) async {
          List<ScanInfo> list = (data["data"] as List).map((i) => ScanInfo.fromJson(i, true)).toList();
          if (isNotEmpty(list)) {
            List<String> ids = [];
            for (var item in list) {
              if (isNotEmpty(item.smileFileId)) {
                ids.add(item.smileFileId);
              }
            }
            downloadThumbnail(
              ids,
              (id, path) {
                for (var item in list) {
                  if (item.smileFileId == id) {
                    setState(() {
                      item.smilePhoto = path;
                    });
                  }
                }
              },
              "RECORD_QUICKSCAN",
            );
          }

          uploadList = await getSearchRecordList("", keywordController.text, false);
          setState(() {
            allLoad = true;
            if (refresh) {
              recordList = list;
              if (recordList.length >= pageSize) {
                allLoad = false;
              }
            } else {
              if (isNotEmpty(list)) {
                if (list.length >= pageSize) {
                  allLoad = false;
                }
                recordList.addAll(list);
              }
            }
            isLoading = false;
          });
        },
        errCallBack: (_) async{
          uploadList = await getSearchRecordList("", keywordController.text, false);
          setState(() {
            isLoading = false;
          });
        },
        params: params,
      );
    }
  }

  getItemView(ScanActivity item) {
    return GestureDetector(
      onTap: () {
        push(ScanRecordListPage(item));
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
        padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 8.sp, 16.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              constraints: BoxConstraints(maxWidth: 235.sp),
              padding: EdgeInsets.only(top: 8.sp),
              child: LightText(
                text: item.campaignName,
                lightText: keywordController.text,
                textStyle: TextStyle(color: color2B, fontSize: 16.sp, fontWeight: FontWeight.w500),
                lightStyle: TextStyle(color: colorPink, fontSize: 16.sp, fontWeight: FontWeight.w500),
              ),
            ),
            SizedBox(height: 8.sp),
            MyText("${Lang.create_time}: ${Global.getDateByTimestamp(milliseconds: item.createTime)}", color7C, 14.sp),
            SizedBox(height: 4.sp),
            Row(
              children: [
                MyText("${Lang.tenant_worker}: ", color7C, 14.sp),
                LightText(
                  text: item.creatorName,
                  lightText: keywordController.text,
                  textStyle: TextStyle(color: color2B, fontSize: 14.sp),
                  lightStyle: TextStyle(color: colorPink, fontSize: 14.sp),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  getListView() {
    if (tabIndex == 2) {
      List<ScanInfo> list = [];
      if (isNotEmpty(uploadList)) {
        list.addAll(uploadList);
      }
      if (isNotEmpty(recordList)) {
        list.addAll(recordList);
      }
      return list
          .map((item) => ScanRecordItem(item.getScanActivity(), item, showActivityName: true, key: UniqueKey()))
          .toList();
    } else {
      List<Widget> list = [];
      if (tabIndex == 0) {
        final localList = localActivityList;
        if (isNotEmpty(localList)) {
          for (int i = 0; i < localList.length; i++) {
            if (activityList.where((element) => element.campaignId == localList[i].campaignId).isEmpty &&
                localList[i].campaignName.contains(keywordController.text)) {
              list.add(ScanActivityItem(localList[i], key: UniqueKey()));
            }
          }
        }
      }
      list.addAll(activityList.map((item) => ScanActivityItem(item, key: UniqueKey())).toList());
      return list;
    }
  }

// 下拉刷新
  Future onRefresh() async {
    await refreshLingyaList();
  }

  delaySearch() {
    if (timer != null) {
      timer!.cancel();
    }
    timer = Timer(const Duration(milliseconds: 800), () {
      refreshLingyaList();
    });
  }

  @override
  void dispose() {
    if (timer != null) {
      timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
        contentHeight: 168.sp,
        child: SafeArea(
          child: Container(
            margin: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Click(
                        child: Container(
                          width: 164.sp,
                          height: 56.sp,
                          margin: EdgeInsets.only(bottom: 16.sp),
                          padding: EdgeInsets.all(8.sp),
                          decoration: BoxDecoration(color: colorBlueDeep, borderRadius: BorderRadius.circular(8.sp)),
                          child: Row(
                            children: [
                              Image.asset(
                                "res/icons/icon_back_white.png",
                                height: 32.sp,
                              ),
                              SizedBox(width: 4.sp),
                              MyText(Lang.back, Colors.white, 24.sp)
                            ],
                          ),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                        }),
                    TabBar(
                      controller: _tabController,
                      isScrollable: true,
                      indicatorColor: colorBlueDeep,
                      labelColor: colorBlueDeep,
                      unselectedLabelColor: color7C,
                      labelStyle: TextStyle(color: colorBlueDeep, fontSize: 24.sp, fontWeight: FontWeight.w600),
                      unselectedLabelStyle: TextStyle(color: color7C, fontSize: 24.sp),
                      indicatorWeight: 3.0,
                      indicatorPadding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
                      tabs: tabTitles.map((e) => Tab(text: e)).toList(),
                      onTap: (index) {
                        if (tabIndex != index) {
                          tabIndex = index;
                          hideKeyboard();
                          pageIndex = 0;
                          isLoading = false;
                          activityList.clear();
                          recordList.clear();
                          setState(() {});
                          getSearchList(true);
                        }
                      },
                    ),
                    SizedBox(width: 164.sp, height: 56.sp)
                  ],
                ),
                SizedBox(height: 24.sp),
                Container(
                  width: 920.sp,
                  height: 56.sp,
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: focusNode.hasFocus || isNotEmpty(keywordController.text) ? colorSearchBorder : colorBg,
                    ),
                    borderRadius: BorderRadius.circular(30.sp),
                  ),
                  child: Row(
                    children: [
                      Image.asset("res/icons/icon_activity_search.png", width: 30.sp),
                      SizedBox(width: 12.sp),
                      Expanded(
                        child: TextField(
                          controller: keywordController,
                          focusNode: focusNode,
                          textAlign: TextAlign.start,
                          textAlignVertical: TextAlignVertical.center,
                          maxLength: 20,
                          style: TextStyle(fontSize: 24.sp, color: color2B),
                          onChanged: (value) {
                            delaySearch();
                            setState(() {});
                          },
                          decoration: InputDecoration(
                            prefix: SizedBox(
                              width: 10.sp,
                            ),
                            hintText: hintTexts[_tabController?.index ?? 0],
                            hintStyle: TextStyle(fontSize: 24.sp, color: colorB8),
                            counterText: '',
                            border: InputBorder.none,
                            isDense: true,
                          ),
                        ),
                      ),
                      Click(
                        onTap: () {
                          setState(() {
                            keywordController.clear();
                          });
                        },
                        child: isNotEmpty(keywordController.text)
                            ? Image.asset("res/icons/icon_close_one.png", width: 30.sp)
                            : SizedBox(),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: Container(
          width: 1.sw,
          height: 1.sh,
          color: Colors.transparent,
          child: Stack(
            children: [
              Click(
                onTap: hideKeyboard,
                child: SizedBox(width: 1.sw, height: 1.sh - 100.sp),
              ),
              isEmpty(keywordController.text)
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.all(24.sp),
                      child: RefreshGrid(
                        childList: getListView(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: tabIndex == 2 ? 5 : 4,
                          childAspectRatio: tabIndex == 2 ? 344.0 / 422 : 427.0 / 268,
                          crossAxisSpacing: tabIndex == 2 ? 16.sp : 24.sp,
                          mainAxisSpacing: tabIndex == 2 ? 16.sp : 24.sp,
                        ),
                        isLoading: isLoading,
                        onRefresh: onRefresh,
                        onLoadMore: onLoadMore,
                        nullImgName: "res/imgs/empty_pic.png",
                        nullWidget: getEmptyText(tabIndex == 2),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getEmptyText(bool isRecord) {
    String hintText = isRecord ? Lang.cannot_find_record : Lang.cannot_find_activity;
    int index = hintText.indexOf("__");
    String str1 = hintText.substring(0, index);
    String str2 = hintText.substring(index + 2);
    return RichText(
      text: TextSpan(
        text: str1,
        style: TextStyle(
          fontSize: 24.sp,
          color: colorAB,
        ),
        children: [
          TextSpan(
            text: keywordController.text,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorRed,
            ),
          ),
          TextSpan(
            text: str2,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorAB,
            ),
          ),
        ],
      ),
    );
  }
}
