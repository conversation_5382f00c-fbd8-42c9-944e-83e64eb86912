import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/paint_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/export/usb_export_tool.dart';
import 'package:mooeli/pages/export/web_export_tool.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/pages/scan/activity_search_page.dart';
import 'package:mooeli/pages/scan/scan_activity_item_view.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_grid.dart';
import 'package:sprintf/sprintf.dart';

class ScanActivityListPage extends BasePage {
  const ScanActivityListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanActivityListPageState();
  }
}

class ScanActivityListPageState extends BasePageState<ScanActivityListPage> {
  List<ScanActivity> dataList = [];
  List<ScanActivity> localList = [];

  int get allDataCount => localList.length + dataList.length;
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;
  int type = 0;
  bool sortDesc = false;
  Timer? timer;
  TextEditingController keywordController = TextEditingController();
  FocusNode focusNode = FocusNode();

  //批量选择
  bool selectMode = false;
  List<ScanActivity> selectList = [];

  @override
  initState() {
    super.initState();
    addEventListener<EventEditActivity>((event) {
      showCustomDialog(EditActivityDialog(
        onClose: () {
          getDataList(true);
          refreshLocalActivity();
        },
        activity: event.activity,
      ));
    });
    addEventListener<EventDeleteActivity>((event) {
      deleteSingleActivity(event.activity);
    });
    initAsyncState();
  }

  initAsyncState() async {
    // User.instance.refreshToken(okCallback: () async {
    await refreshDataList();
    // });
    disconnectUltra(disconnectWifi: true);

    if (!Global.offlineMode) {
      final tempList = await getLocalActivityList();
      if (isNotEmpty(tempList)) {
        uploadLocalActivityList(tempList);
      }
      setState(() {
        localList = tempList;
      });
    }
  }

  uploadLocalActivityList(List<ScanActivity> list) async {
    if (isNotEmpty(list) && await HHttp.checkInternetValid()) {
      ScanActivity activity = list.last;
      HHttp.request(
        "/v3/quickscan/campaign/createById",
        "POST",
        (data) async {
          list.removeLast();
          deleteLocalActivity(activity);
          await refreshDataList();
          uploadLocalActivityList(list);
        },
        isShowErrorToast: false,
        params: {
          "id": activity.campaignId,
          "campaignName": activity.campaignName,
        },
      );
    }
  }

  refreshDataList() async {
    return await getDataList(true);
  }

  onLoadMore() {
    getDataList(false);
  }

  void refreshLocalActivity() async {
    final tempData = await getLocalActivityList();
    setState(() {
      localList = tempData;
    });
  }

  getDataList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    HHttp.request("/v3/quickscan/campaign/page", "POST", (data) {
      List<ScanActivity> list = (data["data"] as List).map((j) => ScanActivity.fromJson(j)).toList();

      setState(() {
        allLoad = true;
        if (refresh) {
          dataList = list;
          if (dataList.length >= pageSize) {
            allLoad = false;
          }
        } else {
          if (isNotEmpty(list)) {
            if (list.length >= pageSize) {
              allLoad = false;
            }
            dataList.addAll(list);
          }
        }
        isLoading = false;
      });
    }, errCallBack: (resp) {
      setState(() {
        isLoading = false;
      });
    }, params: {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
    });
  }

  getListView() {
    List<Widget> allWidget = [];
    Widget buildItem(ScanActivity activity) {
      final item = ScanActivityItem(activity, key: UniqueKey(), selectMode: selectMode,
        inSelect: selectList.contains(activity),);
      if (selectMode) {
        return GestureDetector(
          onTap: () {
            setState(() {
              if (selectList.contains(activity)) {
                selectList.remove(activity);
              } else {
                selectList.add(activity);
              }
            });
          },
          child: item,
        );
      } else {
        return item;
      }
    }

    if (isNotEmpty(localList)) {
      for (int i = 0; i < localList.length; i++) {
        if (dataList.where((element) => element.campaignId == localList[i].campaignId).isEmpty) {
          allWidget.add(buildItem(localList[i]));
        }
      }
    }
    for (int i = 0; i < dataList.length; i++) {
      allWidget.add(buildItem(dataList[i]));
    }
    logger("widgetCount:${allWidget.length}");
    return allWidget;
  }

  // 下拉刷新
  Future onRefresh() async {
    await refreshDataList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorBg,
      body: SafeArea(
        child: Row(children: [
          Container(
            width: 248.sp,
            height: 1.sh - 84.sp,
            decoration: BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
            margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
            padding: EdgeInsets.all(16.sp),
            child: Column(
              children: [
                Click(
                    child: Container(
                      height: 56.sp,
                      margin: EdgeInsets.only(bottom: 16.sp),
                      padding: EdgeInsets.all(8.sp),
                      decoration: BoxDecoration(color: colorBlueDeep, borderRadius: BorderRadius.circular(8.sp)),
                      child: Row(
                        children: [
                          Image.asset(
                            "res/icons/icon_back_white.png",
                            height: 32.sp,
                          ),
                          SizedBox(width: 4.sp),
                          MyText(Lang.back, Colors.white, 24.sp)
                        ],
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                    }),
                Click(
                    child: Container(
                      height: 56.sp,
                      margin: EdgeInsets.only(bottom: 16.sp),
                      padding: EdgeInsets.all(8.sp),
                      decoration: BoxDecoration(
                          color: type == 0 ? colorPurpleLavender : Colors.transparent,
                          borderRadius: BorderRadius.circular(8.sp)),
                      child: Row(
                        children: [
                          Image.asset(
                            "res/icons/icon_activity_all${type == 0 ? "_select" : ""}.png",
                            height: 32.sp,
                          ),
                          SizedBox(width: 4.sp),
                          MyText(Lang.all, type == 0 ? colorBlueDeep : color2B, 24.sp,
                              type == 0 ? FontWeight.w500 : FontWeight.w400),
                          const Spacer(),
                          MyText(allDataCount.toString(), type == 0 ? colorBlueDeep : color2B, 24.sp,
                              type == 0 ? FontWeight.w500 : FontWeight.w400),
                        ],
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        type = 0;
                      });
                    }),
                // Click(
                //     child: Container(
                //       height: 56.sp,
                //       margin: EdgeInsets.only(bottom: 16.sp),
                //       padding: EdgeInsets.all(8.sp),
                //       decoration: BoxDecoration(
                //           color: type == 1 ? colorPurpleLavender : Colors.transparent,
                //           borderRadius: BorderRadius.circular(8.sp)),
                //       child: Row(
                //         children: [
                //           Image.asset(
                //             "res/icons/icon_activity_collect${type == 1 ? "_select" : ""}.png",
                //             height: 32.sp,
                //           ),
                //           SizedBox(width: 4.sp),
                //           MyText(Lang.follow, type == 1 ? colorBlueDeep : color2B, 24.sp,
                //               type == 1 ? FontWeight.w500 : FontWeight.w400),
                //           const Spacer(),
                //           MyText("0", type == 1 ? colorBlueDeep : color2B, 24.sp,
                //               type == 1 ? FontWeight.w500 : FontWeight.w400),
                //         ],
                //       ),
                //     ),
                //     onTap: () {
                //       setState(() {
                //         type = 1;
                //       });
                //     }),
                // Image.asset(
                //   "res/icons/icon_activity_line.png",
                //   height: 1.sp,
                // ),
                // Click(
                //     child: Container(
                //       height: 56.sp,
                //       margin: EdgeInsets.fromLTRB(0.sp, 16.sp, 0.sp, 16.sp),
                //       padding: EdgeInsets.all(8.sp),
                //       decoration: const BoxDecoration(
                //         color: Colors.transparent,
                //       ),
                //       child: Row(
                //         children: [
                //           Image.asset(
                //             "res/icons/icon_sort${sortDesc ? "_desc" : ""}.png",
                //             height: 32.sp,
                //           ),
                //           SizedBox(width: 4.sp),
                //           MyText(sortDesc ? Lang.time_sort : Lang.time_desc, color2B, 24.sp),
                //         ],
                //       ),
                //     ),
                //     onTap: () {
                //       setState(() {
                //         sortDesc = !sortDesc;
                //       });
                //     }),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              height: 1.sh - 84.sp,
              margin: EdgeInsets.fromLTRB(8.sp, 16.sp, 8.sp, 24.sp),
              child: Column(children: [
                Click(
                  onTap: () {
                    push(const ActivitySearchPage());
                  },
                  child: Container(
                    height: 56.sp,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(32.sp),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 8.sp, horizontal: 12.sp),
                    child: Row(children: [
                      Image.asset("res/icons/icon_activity_search.png", width: 30.sp),
                      SizedBox(width: 12.sp),
                      MyText(Global.offlineMode ? Lang.offline_search_activity_hint : Lang.search_activity_hint, colorA4, 24.sp),
                      // TextField(
                      //   controller: keywordController,
                      //   focusNode: focusNode,
                      //   textAlign: TextAlign.start,
                      //   textAlignVertical: TextAlignVertical.center,
                      //   maxLength: 20,
                      //   style: TextStyle(fontSize: 14.sp, color: color2B),
                      //   onChanged: (value) {
                      //     // delaySearch();
                      //   },
                      //   decoration: InputDecoration(
                      //     prefix: SizedBox(
                      //       width: 10.sp,
                      //     ),
                      //     hintText: Lang.input_activity_search,
                      //     hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                      //     counterText: '',
                      //     border: InputBorder.none,
                      //     isDense: true,
                      //   ),
                      // ),
                    ]),
                  ),
                ),
                SizedBox(height: 24.sp),
                Expanded(
                  child: RefreshGrid(
                    childList: getListView(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      childAspectRatio: 426.66 / 284,
                      crossAxisSpacing: 24.sp,
                      mainAxisSpacing: 24.sp,
                    ),
                    isLoading: isLoading,
                    onRefresh: onRefresh,
                    onLoadMore: onLoadMore,
                    nullImgName: "res/icons/empty.png",
                    nullText: Lang.no_record,
                  ),
                ),
              ]),
            ),
          ),
          Container(
              width: 248.sp,
              height: 1.sh - 84.sp,
              decoration:
                  BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
              margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
              child: Column(
                children: selectMode ? _buildSelectModeViews() : _buildCommonModeViews(),
              ))
        ]),
      ),
    );
  }

  void deleteSingleActivity(ScanActivity activity) async {
    Global.showAlertDialog(
      Lang.confirm_delete_activity_name,
      Lang.delete_activity_not_recoverd,
      okColor: colorRed,
      okText: Lang.delete,
      okCallBack: () {
        HHttp.request(
          "/v3/quickscan/campaign/delete",
          "POST",
              (data) {
            toast(Lang.delete_success);
            refreshDataList();
          },
          errCallBack: (err) {
            deleteLocalActivity(activity);
            refreshLocalActivity();
            refreshDataList();
          },
          params: {
            "campaignId": activity.campaignId,
          },
        );
      },
    );
  }

  void deleteSelectedActivities(List<ScanActivity> activities) async {
    if (Global.offlineMode) {
      deleteLocalActivities(activities);
      selectList.removeWhere((e) => activities.contains(e));
      setState(() { });
      return;
    }
    if (activities.isEmpty) {
      return;
     }
    HHttp.request(
      "/v3/quickscan/campaign/batchDelete",
      "POST",
          (data) {
        toast(Lang.delete_success);
        selectList.removeWhere((e) => activities.contains(e));
        refreshDataList();
      },
      errCallBack: (err) {
        deleteLocalActivities(activities);
        refreshLocalActivity();
        selectList.removeWhere((e) => activities.contains(e));
        refreshDataList();
      },
      params: {
        "campaignIdList": activities.map((e) => e.campaignId).toList(),
      },
    );

  }

  List<Widget> _buildCommonModeViews() {
    return [
      HCustomButton(
        key: UniqueKey(),
        height: 56.sp,
        fontSize: 24.sp,
        radius: 8.sp,
        onPress: () {
          setState(() {
            selectList.clear();
            selectMode = true;
          });
        },
        bgColor: colorPurpleLavender,
        child: MyText(Lang.batch_select_record, colorBlueDeep, 24.sp),
      ),
      const Spacer(),
      HCustomButton(
        key: UniqueKey(),
        height: 56.sp,
        fontSize: 24.sp,
        fixedBottom: false,
        radius: 8.sp,
        onPress: () {
          showCustomDialog(
            EditActivityDialog(
              onClose: () {
                getDataList(true);
                refreshLocalActivity();
              },
            ),
          );
        },
        bgColor: colorBlueDeep,
        child: MyText(Lang.create_activity, Colors.white, 24.sp),
      ),
    ];
  }

  List<Widget> _buildSelectModeViews() {
    final isSelectAll = selectList.length == allDataCount && selectList.isNotEmpty;
    return [
      HCustomButton(
        key: UniqueKey(),
        height: 56.sp,
        fontSize: 24.sp,
        radius: 8.sp,
        onPress: () {
          setState(() {
            selectList.clear();
            selectMode = false;
          });
        },
        bgColor: colorPurpleLavender,
        child: MyText(Lang.exit_batch_select, colorBlueDeep, 24.sp),
      ),
      HCustomButton(
        key: UniqueKey(),
        height: 56.sp,
        fontSize: 24.sp,
        radius: 8.sp,
        onPress: () {
          setState(() {
            if (isSelectAll) {
              selectList.clear();
            }else {
              selectList = [...dataList, ...localList];
            }
          });
        },
        bgColor: colorPurpleLavender,
        child: MyText(isSelectAll ? Lang.cancel_select_all : Lang.batch_select_all_record, colorBlueDeep, 24.sp),
      ),
      const Spacer(),
      if (isNotEmpty(selectList)) ...[
        MyText(sprintf(Lang.select_record_count, [selectList.length]), color2B, 20.sp),
        SizedBox(height: 16.sp),
        Image.asset("res/imgs/dotted_divider.png", width: 180.sp),
        SizedBox(height: 16.sp),
      ],
      if (Global.offlineMode)
        ...[
          HCustomButton(
            key: UniqueKey(),
            height: 56.sp,
            fontSize: 24.sp,
            radius: 8.sp,
            onPress: () {
              showUsbExportSelectView((type, path) {

              });
            },
            bgColor: colorBlueDeep,
            child: MyText(Lang.export_to_usb, Colors.white, 24.sp),
          ),
          HCustomButton(
            key: UniqueKey(),
            height: 56.sp,
            fontSize: 24.sp,
            radius: 8.sp,
            onPress: () {
              selectWebExportDialog();
            },
            bgColor: colorBlueDeep,
            child: MyText(Lang.lan_sharing, Colors.white, 24.sp),
          ),
        ],
      HCustomButton(
        key: UniqueKey(),
        height: 56.sp,
        fontSize: 24.sp,
        fixedBottom: false,
        radius: 8.sp,
        onPress: () {
          hideKeyboard();
          Global.showAlertDialog(
            Lang.confirm_delete_activity_name,
            Lang.delete_activity_not_recoverd,
            okColor: colorRed,
            okText: Lang.delete,
            okCallBack: (){
              final selectedList = selectList;
              deleteSelectedActivities(selectedList);
            },
          );
        },
        bgColor: colorRed.withOpacity(isEmpty(selectList) ? 0.6 : 1),
        child: MyText(Lang.delete, Colors.white, 24.sp),
      ),
    ];
  }

  @override
  void onRouteResume(Route nextRoute) {
    super.onRouteResume(nextRoute);
    refreshDataList();
    disconnectUltra(disconnectWifi: true);
  }
}

class EditActivityDialog extends BasePage {
  dynamic onClose;
  ScanActivity? activity;

  EditActivityDialog({this.onClose, this.activity});

  @override
  EditActivityDialogState createState() => EditActivityDialogState();
}

class EditActivityDialogState extends BasePageState<EditActivityDialog> {
  bool isEdit = false;
  late ScanActivity scanActivity;

  TextEditingController nameController = TextEditingController();
  TextEditingController workerController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  bool showNameEmptyError = false;

  @override
  void initState() {
    super.initState();
    if (widget.activity != null) {
      isEdit = true;
      scanActivity = widget.activity!.copy();
      setActivityText();
      _getActivityInfo();
    } else {
      isEdit = false;
      scanActivity = ScanActivity();
    }
  }

  setActivityText() {
    nameController.text = scanActivity.campaignName;
    workerController.text = scanActivity.contactName;
    phoneController.text = scanActivity.contactPhone;
    addressController.text = scanActivity.contactAddress;
    codeController.text = scanActivity.qrCodeDescription;
  }

  _getActivityInfo() {
    HHttp.request(
      "/v3/quickscan/campaign/get",
      "POST",
      (data) {
        setState(() {
          scanActivity = ScanActivity.fromJson(data);
        });
        setActivityText();
      },
      params: {"campaignId": scanActivity.campaignId},
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: hideKeyboard,
      child: Container(
        width: 800.sp,
        margin: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 96.sp,
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(left: 32.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MyText(isEdit ? Lang.edit_activity : Lang.add_collect, color2B, 28.sp, FontWeight.w500),
                  Click(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Padding(
                      padding: EdgeInsets.all(32.sp),
                      child: Image.asset(
                        "res/imgs/icon_close.png",
                        width: 32.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Divider(height: 1.sp, color: colorE1),
            SizedBox(height: 32.sp),
            Container(
              padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
              height: 653.sp,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        MyText(Lang.collect_data_name, color2B, 24.sp, FontWeight.w500),
                        MyText("* ", Colors.red, 24.sp, FontWeight.w500),
                      ],
                    ),
                    editField(
                      Lang.input_collect_name,
                      nameController,
                      (text) {
                        setState(() {
                          scanActivity.campaignName = text;
                        });
                      },
                      maxLength: 50,
                      showError: showNameEmptyError,
                    ),
                    if (showNameEmptyError)
                      Padding(
                        padding: EdgeInsets.only(top: 8.sp, bottom: 24.sp, left: 16.sp),
                        child: Text(Lang.input_activity_name_error, style: TextStyle(color: Colors.red, fontSize: 20.sp)),
                      ),
                    Container(
                      width: 752.sp,
                      alignment: Alignment.topCenter,
                      padding: EdgeInsets.only(bottom: 24.sp),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(width: 65.sp, height: 1.sp, color: colorB8),
                          SizedBox(width: 16.sp),
                          MyText(Lang.data_in_cover, color7C, 20.sp),
                          SizedBox(width: 16.sp),
                          Container(width: 65.sp, height: 1.sp, color: colorB8),
                        ],
                      ),
                    ),
                    MyText(Lang.tenant_worker_name, color2B, 24.sp, FontWeight.w500),
                    editField(
                      Lang.input_tenant_worker_name,
                      workerController,
                      (text) {
                        setState(() {
                          scanActivity.contactName = text;
                        });
                      },
                      maxLength: 50,
                    ),
                    MyText(Lang.tenant_worker_phone, color2B, 24.sp, FontWeight.w500),
                    editField(
                      Lang.input_tenant_worker_phone,
                      phoneController,
                      (text) {
                        setState(() {
                          scanActivity.contactPhone = text;
                        });
                      },
                      inputType: TextInputType.phone,
                      maxLength: 50,
                    ),
                    MyText(Lang.tenant_worker_address, color2B, 24.sp, FontWeight.w500),
                    editField(Lang.input_tenant_worker_address, addressController, (text) {
                      setState(() {
                        scanActivity.contactAddress = text;
                      });
                    }, maxLength: 50),
                    MyText(Lang.logo_setting, color2B, 24.sp, FontWeight.w500),
                    SizedBox(height: 12.sp),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        hideKeyboard();
                        setState(() {
                          scanActivity.showPdfLogo = !scanActivity.showPdfLogo;
                        });
                      },
                      child: Row(
                        children: [
                          SizedBox(width: 2.sp),
                          Transform.scale(
                            scale: 0.75,
                            child: SizedBox(
                              width: 20.sp,
                              height: 20.sp,
                              child: Checkbox(
                                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                  fillColor: MaterialStateProperty.resolveWith((Set<MaterialState> states) {
                                    return colorBrand;
                                  }),
                                  side: BorderSide(color: color2B),
                                  checkColor: Colors.white,
                                  value: scanActivity.showPdfLogo,
                                  onChanged: (value) {
                                    setState(() {
                                      scanActivity.showPdfLogo = value!;
                                    });
                                  }),
                            ),
                          ),
                          SizedBox(width: 8.sp),
                          MyText(Lang.report_show_ts_logo, color2B, 20.sp),
                        ],
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        hideKeyboard();
                        setState(() {
                          scanActivity.showPdfLogo = !scanActivity.showPdfLogo;
                        });
                      },
                      child: Container(
                        width: 752.sp,
                        height: 56.sp,
                        margin: EdgeInsets.only(top: 8.sp),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F7FC),
                          borderRadius: BorderRadius.circular(6.sp),
                        ),
                        child: Image.asset("res/imgs/pdf_logo.png", height: 24.sp),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24.sp),
            Divider(height: 1.sp, color: colorE1),
            SizedBox(height: 24.sp),
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {
                    SmartDialog.dismiss();
                  },
                  child: Container(
                    width: 96.sp,
                    height: 56.sp,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: colorPurpleLavender,
                      borderRadius: BorderRadius.circular(8.sp),
                    ),
                    child: Text(Lang.cancel,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w500,
                          color: colorBlueDeep,
                        )),
                  ),
                ),
                SizedBox(width: 24.sp),
                GestureDetector(
                  onTap: () {
                    String nickname = nameController.text;
                    logger("HTTP-LOG nickname: $nickname");
                    if (isEmpty(nickname)) {
                      toast(Lang.input_collect_name);
                      setState(() {
                        showNameEmptyError = true;
                      });
                      return;
                    }
                    SmartDialog.dismiss();
                    toast(isEdit ? Lang.save_success : Lang.create_success);
                    if (isEdit) {
                      HHttp.request(
                        "/v3/quickscan/campaign/update",
                        "POST",
                        (data) {
                          if (widget.onClose != null) {
                            widget.onClose();
                          }
                        },
                        errCallBack: (err) {
                          updateLocalActivity(scanActivity);
                          if (widget.onClose != null) {
                            widget.onClose();
                          }
                        },
                        params: {
                          "campaignId": scanActivity.campaignId,
                          "campaignName": scanActivity.campaignName,
                          "contactName": scanActivity.contactName,
                          "contactPhone": scanActivity.contactPhone,
                          "contactAddress": scanActivity.contactAddress,
                          "qrCodeDescription": scanActivity.qrCodeDescription,
                          "showPdfLogo": scanActivity.showPdfLogo,
                        },
                      );
                    } else {
                      HHttp.request(
                        "/v3/quickscan/campaign/create",
                        "POST",
                        (data) {
                          if (widget.onClose != null) {
                            widget.onClose();
                          }
                        },
                        errCallBack: (err) {
                          addLocalActivity(scanActivity);
                          if (widget.onClose != null) {
                            widget.onClose();
                          }
                        },
                        params: {
                          "campaignName": scanActivity.campaignName,
                          "contactName": scanActivity.contactName,
                          "contactPhone": scanActivity.contactPhone,
                          "contactAddress": scanActivity.contactAddress,
                          "qrCodeDescription": scanActivity.qrCodeDescription,
                          "showPdfLogo": scanActivity.showPdfLogo,
                        },
                      );
                    }
                  },
                  child: Container(
                    width: 96.sp,
                    height: 56.sp,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: colorBlueDeep,
                      borderRadius: BorderRadius.circular(8.sp),
                    ),
                    child: Text(isEdit ? Lang.save : Lang.create,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        )),
                  ),
                ),
                SizedBox(width: 24.sp),
              ],
            ),
            SizedBox(height: 24.sp),
          ],
        ),
      ),
    );
  }

  editField(
    String hintText,
    TextEditingController controller,
    dynamic onText, {
    TextInputType inputType = TextInputType.text,
    maxLength = 10000,
    bool showError = false,
  }) {
    return Container(
      constraints: BoxConstraints(minHeight: 58.sp),
      padding: EdgeInsets.fromLTRB(16.sp, 7.sp, 16.sp, 7.sp),
      margin: EdgeInsets.fromLTRB(0.sp, 12.sp, 0.sp, showError ? 0 : 24.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        border: Border.all(color: colorE1),
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: TextFormField(
        maxLines: null,
        controller: controller,
        textAlign: TextAlign.start,
        maxLength: maxLength,
        keyboardType: inputType,
        onChanged: onText,
        style: TextStyle(fontSize: 24.sp, color: color2B, height: 1.5),
        decoration: InputDecoration(
          counterText: '',
          isDense: true,
          contentPadding: EdgeInsets.zero,
          border: InputBorder.none,
          hintText: hintText,
          hintStyle: TextStyle(fontSize: 24.sp, color: colorB8),
        ),
      ),
    );
  }
}

class DeleteActivityDialog extends BasePage {
  ScanActivity scanActivity;
  dynamic onDelete;

  DeleteActivityDialog(this.scanActivity, this.onDelete);

  @override
  DeleteActivityDialogState createState() => DeleteActivityDialogState();
}

class DeleteActivityDialogState extends BasePageState<DeleteActivityDialog> {
  String inputName = "";

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // 触摸收起键盘
        hideKeyboard();
      },
      child: Container(
        margin: EdgeInsets.all(32.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(width: 20.sp),
                        MyText(Lang.delete_activity, color2B, 16.sp, FontWeight.w500),
                        GestureDetector(
                          onTap: () {
                            SmartDialog.dismiss();
                          },
                          child: Padding(
                            padding: EdgeInsets.all(2.sp),
                            child: Image.asset(
                              "res/imgs/icon_close.png",
                              width: 16.sp,
                              color: colorA4,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20.sp),
                    Container(
                      constraints: BoxConstraints(maxWidth: 250.sp),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          HeightText(Lang.confirm_delete_activity, color2B, 14.sp, 1.5),
                          HeightText(Lang.delete_activity_not_recoverd, colorRed, 14.sp, 1.5),
                          SizedBox(height: 12.sp),
                          HeightText(Lang.confirm_delete_activity_name, color2B, 14.sp, 1.5),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.sp),
                    Container(
                      constraints: BoxConstraints(minHeight: 36.sp),
                      padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: const Color(0xffe4e4e4),
                        ),
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      child: TextFormField(
                        maxLines: null,
                        maxLength: 50,
                        textAlign: TextAlign.start,
                        onChanged: (text) {
                          setState(() {
                            inputName = text;
                          });
                        },
                        style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333), height: 1.5),
                        decoration: InputDecoration(
                          counterText: '',
                          isDense: true,
                          contentPadding: EdgeInsets.zero,
                          border: InputBorder.none,
                          hintText: Lang.input_collect_name,
                          hintStyle: TextStyle(fontSize: 14.sp, color: const Color(0xFFCCCCCC)),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.sp),
                  ],
                )),
            Divider(height: 1.sp, color: colorE1),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Container(
                      width: 0.36.sw,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      padding: EdgeInsets.fromLTRB(16.sp, 18.sp, 16.sp, 18.sp),
                      child: Text(Lang.cancel,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: color2B,
                          )),
                    )),
                Container(
                  width: 0.5.sp,
                  height: 54.sp,
                  color: colorE1,
                ),
                GestureDetector(
                    onTap: () {
                      hideKeyboard();
                      if (inputName == widget.scanActivity.campaignName) {
                        widget.onDelete();
                      }
                    },
                    child: Container(
                      width: 0.4.sw,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      padding: EdgeInsets.fromLTRB(16.sp, 18.sp, 16.sp, 18.sp),
                      child: Text(Lang.delete,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: inputName == widget.scanActivity.campaignName ? colorRed : colorB8,
                          )),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
