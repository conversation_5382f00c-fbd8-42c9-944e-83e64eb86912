import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/pages/scan/UltraHView.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
// import 'package:mooeli_ultra/UltraCameraView.dart';
// import 'package:mooeli_ultra/mooeli_ultra.dart';
import 'package:wifi_iot/wifi_iot.dart';

int ultraType = 0; //1-UltraT, 2-UltraH
int lastType = 0;

String ultraWifiPrefix = "MOO";
String monitorName = "MOOELI";

void setMonitorName() {
  int area = getLoginArea() ?? 0;
  monitorName = area == 0 ? "MOOELI" : "IOOELI";
}

void setUltraWifi(bool isWhitelist) {
  int area = getLoginArea() ?? 0;
  if (area == 0) {
    ultraWifiPrefix = isWhitelist ? "MOO" : "MOO-LeChiPai";
  } else {
    ultraWifiPrefix = "IOOELI";
  }
}

connectUltra(String ssid) async {
  // bool checkIp = await isUltraIp();
  // bool connect = checkIp;
  // if (!checkIp) {
  //   connect = await WiFiForIoTPlugin.connect(ssid,
  //       password: "Ultra001", security: NetworkSecurity.WPA, withInternet: false, joinOnce: true);
  //   logger("connectUltra wifi: $connect");
  //   eventBus.fire(EventConnectUltra(connect));
  // }
  // await WiFiForIoTPlugin.forceWifiUsage(true);
  // checkIp = await isUltraIp();
  // if (connect || checkIp) {
  //   await connectUltraDevice();
  // }
}

disconnectUltra({bool disconnectWifi = false, bool atOnce = false}) async {
  // if (atOnce) {
  //   await disconnectUltraDevice();
  //   if (disconnectWifi) {
  //     await WiFiForIoTPlugin.forceWifiUsage(false);
  //     bool disconnect = await WiFiForIoTPlugin.disconnect();
  //     logger("disconnectUltra : $disconnect");
  //   }
  //   return;
  // }
  // Future.delayed(const Duration(milliseconds: 300), () async {
  //   await disconnectUltraDevice();
  //   if (disconnectWifi) {
  //     await WiFiForIoTPlugin.forceWifiUsage(false);
  //     bool disconnect = await WiFiForIoTPlugin.disconnect();
  //     logger("disconnectUltra : $disconnect");
  //   }
  // });
}

int connectCounter = 0; //从连接上wifi开始计时

setUltraType(int type) {
  // if (ultraType != type && ultraType > 0 && lastType > 0) {
  //   logger("UltraManager disconnectUltraDevice on type changed: $ultraType -> $type");
  //   // disconnectUltra(disconnectWifi: true);
  // }
  // if (lastType != type && type > 0) {
  //   connectCounter = 0;
  // }
  // lastType = type;
  // if (ultraType != type && type != 0) {
  //   ultraType = type;
  //   eventBus.fire(EventUltraType(type));
  // }
}

Future<bool> isUltraIp() async {
  // int type = ffiGetDeviceType();
  // setUltraType(type);
  // // logger("ultra type: $type");
  // if (type > 0) {
  //   return true;
  // } else {
    return false;
  // }
  //
  // String? ssid = await WiFiForIoTPlugin.getSSID();
  // if (ssid != null && ssid.startsWith(ultraWifiPrefix)) {
  //   logger("CheckUltraConnectTask ssid: $ssid");
  //   setUltraType(ssid.startsWith("${ultraWifiPrefix}_H") ? 2 : 1);
  //   return true;
  // } else {
  //   String? ip = await WiFiForIoTPlugin.getIP();
  //   logger("CheckUltraConnectTask ssid: $ssid, ip: $ip");
  //   if (ip != null) {
  //     if (ip.contains("192.168.99")) {
  //       setUltraType(1);
  //       return true;
  //     } else if (ip.contains("192.168.97")) {
  //       setUltraType(2);
  //       return true;
  //     }
  //   }
  //   return false;
  // }
}

Widget getUltraView() {
  // if (ultraType == 1) {
  //   return Container(
  //     color: Colors.white,
  //     width: 1.sw,
  //     height: 1.sh,
  //     child: const UltraCameraView(),
  //   );
  // } else if (ultraType == 2) {
  //   return UltraHView(1.sw, 1.sh);
  // } else {
    return const SizedBox();
  // }
}

Future<bool> checkUltraConnected() async {
  bool result = false;
  // switch (ultraType) {
  //   case 1:
  //     result = await callMcByFunNameByUltra("isConnectedCamera");
  //     break;
  //   case 2:
  //     if (ffiIsDeviceConnect() && !hasImage && enableConnect) {
  //       ffiEnableImageTransit();
  //     }
  //     result = hasImage;
  //     break;
  // }
  // // if (!result && ultraType == 2) {
  // //   connectCounter++;
  // //   if (connectCounter >= (Platform.isAndroid ? 10 : 30)) {
  // //     disconnectUltra(disconnectWifi: true);
  // //     connectCounter = 0;
  // //   }
  // // }
  // logger(
  //     "UltraManager checkUltraConnected: ${ffiIsDeviceConnect()}, type: $ultraType, result: $result, enableConnect: $enableConnect");
  return result;
}

Future<int> getUltraBattery() async {
  int result = 0;
  // switch (ultraType) {
  //   case 1:
  //     result = await callMcByFunNameByUltra("getBatteryPercentValue");
  //     break;
  //   case 2:
  //     try {
  //       result = ffiGetBatteryPercent().toInt();
  //     } catch (ex) {
  //       //
  //     }
  //     break;
  // }
  return result;
}

Future<void> saveUltraImage(String path) async {
  // switch (ultraType) {
  //   case 1:
  //     await callMcByFunNameByUltra("saveNewestCameraImg", params: {"path": path});
  //     break;
  //   case 2:
  //     ffiSaveImage(path);
  //     // Uint8List? uint8list = getImageData();
  //     // if (uint8list != null) {
  //     //   final image = img.decodeImage(uint8list);
  //     //   img.flipHorizontal(image!);
  //     //   final jpg = img.encodeJpg(image);
  //     //   File file = File(path);
  //     //   await file.writeAsBytes(jpg);
  //     // }
  //     break;
  // }
}

bool enableConnect = false;

Future<void> connectUltraDevice() async {
  logger("UltraManager connectUltraDevice, type: $ultraType");
  // enableConnect = true;
  // switch (ultraType) {
  //   case 1:
  //     await callMcByFunNameByUltra("connectCamera");
  //     break;
  //   case 2:
  //     if (!ffiIsDeviceConnect()) {
  //       ffiConnectDevice();
  //     }
  //     if (ffiIsDeviceConnect()) {
  //       ffiEnableImageTransit();
  //     }
  //
  //     ////尝试绑定ip
  //     // String? ip = await WiFiForIoTPlugin.getIP();
  //     // if (isNotEmpty(ip)) {
  //     //   final serverAddress = (await InternetAddress.lookup(ip!)).first;
  //     //   logger("UltraManager connectUltraDevice serverAddress: $serverAddress");
  //     //   final clientSocket = await RawDatagramSocket.bind(
  //     //       serverAddress.type == InternetAddressType.IPv6 ? InternetAddress.anyIPv6 : InternetAddress.anyIPv4, 0);
  //     //   logger("UltraManager connectUltraDevice bindIp: ${clientSocket.toString()}");
  //     // }
  //
  //     break;
  // }
}

Future<void> disconnectUltraDevice() async {
  // hasImage = false;
  // enableConnect = false;
  // logger("UltraManager disconnectUltraDevice, type: $ultraType");
  // switch (ultraType) {
  //   case 1:
  //     await callMcByFunNameByUltra("disconnectCamera");
  //     break;
  //   case 2:
  //     ffiDisconnectDevice();
  //     break;
  // }
}
