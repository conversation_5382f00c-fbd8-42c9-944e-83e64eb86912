import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/diagnose_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/highlight_text.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class DiagnoseSearchPage extends BasePage {
  String? keyword;

  DiagnoseSearchPage({this.keyword, Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return DiagnoseSearchPageState();
  }
}

class DiagnoseSearchPageState extends BasePageState<DiagnoseSearchPage> {
  List<DiagnoseSearchInfo> recordList = [];
  int pageIndex = 0;
  int pageSize = 20;
  bool isLoading = false;
  bool allLoad = false;

  Timer? timer;
  TextEditingController keywordController = TextEditingController();
  FocusNode focusNode = FocusNode();

  List convertList = [];

  @override
  void initState() {
    super.initState();
    getScanConfigJson((json) {
      if (isNotEmpty(json) && isNotEmpty(json["V3ConversionProject"])) {
        Map map = json["V3ConversionProject"] as Map;
        List list = [];
        for (String key in map.keys) {
          if (isNotEmpty(map[key]["projectNames"])) {
            list.addAll(map[key]["projectNames"]);
          }
        }
        setState(() {
          convertList = list;
        });
      }
    });
    if (isEmpty(widget.keyword)) {
      delay(200, () {
        showKeyboard(focusNode);
      });
    } else {
      keywordController.text = widget.keyword!;
      refreshLingyaList();
    }
  }

  refreshLingyaList() async {
    return await getSearchResult(true);
  }

  onLoadMore() {
    getSearchResult(false);
  }

  getSearchResult(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    List filters = [];

    if (isNotEmpty(keywordController.text)) {
      filters.add({
        "columnName": "index",
        "filterEnum": "like",
        "columnValue": keywordController.text,
      });
    } else {
      setState(() {
        pageIndex = 0;
        isLoading = false;
        recordList.clear();
      });
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    HHttp.request("/v3/quickscan/scanRecord/pageByInitial", "POST", (data) {
      List<DiagnoseSearchInfo> list =
          (data['data'] as List).map((i) => DiagnoseSearchInfo.fromJson(i)).toList();
      setState(() {
        allLoad = true;
        if (refresh) {
          recordList = list;
          if (recordList.length >= pageSize) {
            allLoad = false;
          }
        } else {
          if (isNotEmpty(list)) {
            if (list.length >= pageSize) {
              allLoad = false;
            }
            recordList.addAll(list);
          }
        }
        isLoading = false;
      });
    }, errCallBack: (err) {
      HHttp.toastError(err);
      setState(() {
        isLoading = false;
      });
    }, params: {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "filterParamList": filters,
    });
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < recordList.length; i++) {
      DiagnoseSearchInfo record = recordList[i];
      allWidget.add(
        GestureDetector(
          onTap: () {
            eventBus.fire(EventSearchScan(keywordController.text, record));
            hideKeyboard();
            pop();
          },
          child: Container(
            margin: EdgeInsets.fromLTRB(20.sp, 6.sp, 20.sp, 6.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.sp),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      isNotEmpty(record.avatarFilePath)
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(40.sp),
                              child: Image.network(
                                record.avatarFilePath,
                                width: 48.sp,
                                height: 48.sp,
                                fit: BoxFit.cover,
                              ),
                            )
                          : Image.asset(
                              "res/imgs/record_avatar.png",
                              width: 48.sp,
                              height: 48.sp,
                              fit: BoxFit.cover,
                            ),
                      SizedBox(width: 12.sp),
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            LightText(
                              text: record.name,
                              lightText: keywordController.text,
                              textStyle: TextStyle(color: color2B, fontSize: 16.sp, fontWeight: FontWeight.w500),
                              lightStyle: TextStyle(color: colorPink, fontSize: 16.sp, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(height: 8.sp),
                            Row(
                              children: [
                                MyText(
                                    "${record.getGender()}  ${sprintf(Lang.year_old, [record.age])}  ", color2B, 14.sp),
                                LightText(
                                  text: record.phone,
                                  lightText: keywordController.text,
                                  textStyle: TextStyle(color: color2B, fontSize: 14.sp),
                                  lightStyle: TextStyle(color: colorPink, fontSize: 14.sp),
                                ),
                              ],
                            ),
                            SizedBox(height: 8.sp),
                            MyText("${Lang.scan_time}:  ${record.getDate(record.scanCreateTime)}", color2B, 14.sp),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                isNotEmpty(record.tagList)
                    ? Container(
                        margin: EdgeInsets.only(top: 8.sp),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(height: 1.sp, color: colorF3),
                            Padding(
                              padding: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 16.sp),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  MyText(Lang.scan_convert_project, color2B, 14.sp),
                                  Wrap(
                                    children: record.tagList
                                        .map(
                                          (tag) => Container(
                                            margin: EdgeInsets.only(top: 8.sp, right: 8.sp),
                                            padding: EdgeInsets.all(4.sp),
                                            decoration: BoxDecoration(
                                              color: colorBrand,
                                              borderRadius: BorderRadius.circular(4.sp),
                                            ),
                                            child: MyText(getTagText(tag), colorBrand, 12.sp),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox(height: 16.sp),
              ],
            ),
          ),
        ),
      );
    }
    return allWidget;
  }

  String getTagText(String tag) {
    if (isNotEmpty(convertList)) {
      for (dynamic map in convertList) {
        if (map.values.contains(tag) && isNotEmpty(map[lang])) {
          logger("getTagsText $tag -> ${map[lang]}");
          return map[lang];
        }
      }
    }
    return tag;
  }

// 下拉刷新
  Future onRefresh() async {
    await refreshLingyaList();
  }

  delaySearch() {
    if (timer != null) {
      timer!.cancel();
    }
    timer = Timer(const Duration(milliseconds: 800), () {
      refreshLingyaList();
    });
  }

  @override
  void dispose() {
    if (timer != null) {
      timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
        contentHeight: 90.sp,
        child: Container(
          margin: EdgeInsets.fromLTRB(20.sp, 44.sp, 20.sp, 6.sp),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 38.sp,
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.fromLTRB(10.sp, 0, 10.sp, 0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: colorBg),
                    borderRadius: BorderRadius.circular(20.sp),
                  ),
                  child: TextField(
                    controller: keywordController,
                    focusNode: focusNode,
                    textAlign: TextAlign.start,
                    textAlignVertical: TextAlignVertical.center,
                    maxLength: 20,
                    style: TextStyle(fontSize: 14.sp, color: color2B),
                    onChanged: (value) {
                      delaySearch();
                    },
                    decoration: InputDecoration(
                      counterText: '',
                      border: InputBorder.none,
                      hintText: Lang.search_scan_hint,
                      hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                      isDense: true,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.sp),
              Click(
                onTap: () {
                  hideKeyboard();
                  pop();
                },
                child: MyText(Lang.cancel, colorBrand, 14.sp),
              ),
            ],
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: Container(
          width: 1.sw,
          height: 1.sh,
          color: Colors.transparent,
          child: Stack(
            children: [
              Click(
                  onTap: hideKeyboard,
                  child: SizedBox(
                    width: 1.sw,
                    height: 1.sh - 100.sp,
                  )),
              isEmpty(keywordController.text)
                  ? const SizedBox()
                  : RefreshList(
                      childList: getListView(),
                      isLoading: isLoading,
                      onRefresh: onRefresh,
                      onLoadMore: onLoadMore,
                      nullImgName: "res/imgs/empty_pic.png",
                      nullWidget: getEmptyText(),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getEmptyText() {
    int index = Lang.cannot_find_case.indexOf("__");
    String str1 = Lang.cannot_find_case.substring(0, index);
    String str2 = Lang.cannot_find_case.substring(index + 2);
    return RichText(
      text: TextSpan(
        text: str1,
        style: TextStyle(
          fontSize: 24.sp,
          color: colorAB,
        ),
        children: [
          TextSpan(
            text: keywordController.text,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorBrand,
            ),
          ),
          TextSpan(
            text: str2,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorAB,
            ),
          ),
        ],
      ),
    );
  }
}
