import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';

class AiFaceCameraIsolate {
  late Isolate _isolate;
  late ReceivePort _receivePort;
  late SendPort _sendPort;

  SendPort get sendPort => _sendPort;

  Future<void> start() async {
    _receivePort = ReceivePort();
    _isolate = await Isolate.spawn(entryPoint, _receivePort.sendPort);
    _sendPort = await _receivePort.first;
  }

  static void entryPoint(SendPort sendPort) async {
    final port = ReceivePort();
    sendPort.send(port.sendPort);
    await for (final dynamic msgObj in port) {
      if (msgObj != null) {
        logicProcess(msgObj);
      } else {
        if (kDebugMode) {
          print("Isolate process error: recvice msg obj is null!");
        }
      }
    }
  }

  static void logicProcess(dynamic msgObj) {
    dynamic obj = ffiGetAiFaceImgByDatas(msgObj["imgDatas"],msgObj["imgWidth"],msgObj["imgHeight"]);
    msgObj["responseSendPort"].send(obj["imgDatas"]);
    ffiFree(obj["pointer"]);
  }

  dispose(){
    _receivePort.close();
    _isolate.kill();
  }
}
