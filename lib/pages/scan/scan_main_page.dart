import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/scan_activity_list_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class ScanMainPage extends BasePage {
  const ScanMainPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanMainPageState();
  }
}

class ScanView {
  String tagName;
  String pageName;
  BasePage view;

  ScanView(this.tagName, this.pageName, this.view);
}

class ScanMainPageState extends BasePageState<ScanMainPage> {
  List<ScanView> scanViewList = [];

  bool isKeyboardShow = false;

  @override
  initState() {
    super.initState();
    addEventListener<EventScanView>((event) {
      scanViewList.add(event.view);
      setState(() {});
    });
    addEventListener<EventScanType>((event) {
      scanViewList.removeLast();
      setState(() {});
    });
    scanViewList.add(ScanView(Lang.activity_list, Lang.scan_activity_list, const ScanActivityListPage()));
  }

  getNavigatorView() {
    List<Widget> views = [
      Click(onTap: pop, child: MyText(Lang.home_page, color2B, 24.sp, FontWeight.w600)),
    ];
    for (int i = 0; i < scanViewList.length; i++) {
      views.add(
        Padding(
          padding: EdgeInsets.fromLTRB(8.sp, 0, 8.sp, 0),
          child: Image.asset("res/icons/icon_right_black.png", width: 32.sp),
        ),
      );
      views.add(
        Click(
          onTap: () {
            scanViewList.removeRange(i + 1, scanViewList.length);
            eventBus.fire(EventScanViewRefresh(scanViewList.last.view.toString()));
            setState(() {});
          },
          child: MyText(
            scanViewList[i].tagName,
            i == scanViewList.length - 1 ? colorBrand : color2B,
            24.sp,
            FontWeight.w600,
          ),
        ),
      );
    }
    return Row(children: views);
  }

  @override
  Widget build(BuildContext context) {
    var keyboardShow = MediaQuery.of(context).viewInsets.bottom > 5.sp;
    logger("isKeyboardShow: $isKeyboardShow -> $keyboardShow");
    if (isKeyboardShow != keyboardShow) {
      isKeyboardShow = keyboardShow;
      eventBus.fire(EventKeyboard(isKeyboardShow));
    }
    return WillPopScope(
      onWillPop: () async {
        if (scanViewList.length > 1) {
          scanViewList.removeLast();
          setState(() {});
          return false;
        } else {
          return true;
        }
      },
      child:  Container(
        decoration: BoxDecoration(
          color: colorPurpleLilac,
          image: const DecorationImage(
              image: AssetImage("res/imgs/my_page_content_bg.png"),
              fit: BoxFit.cover),
        ),
        child: Stack(
          children: scanViewList
              .map((e) => e.view)
              .toList(),
        ),
      ),
    );
  }
}
