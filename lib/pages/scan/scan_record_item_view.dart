import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/mooeli/mooeli_record_page.dart';
import 'package:mooeli/pages/scan/ScanInfoRecord.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/web/web_report_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class ScanRecordItem extends BasePage {
  ScanActivity activity;
  ScanInfo record;
  bool showActivityName;
  bool selectMode = false;
  bool inSelect = false;

  ScanRecordItem(
    this.activity,
    this.record, {
    this.showActivityName = false,
    this.selectMode = false,
    this.inSelect = false,
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanRecordItemState();
  }
}

class ScanRecordItemState extends BasePageState<ScanRecordItem> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isFront = true;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _animation = Tween(begin: 0.0, end: 1.0).animate(_controller)
      ..addListener(() {
        setState(() {});
      });
  }

  void _flip() {
    hideKeyboard();
    if (_isFront) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
    _isFront = !_isFront;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.selectMode) {
      _isFront = true;
    }
    return Transform(
      alignment: FractionalOffset.center,
      transform: Matrix4.identity()
        ..setEntry(3, 2, 0.001) // Perspective
        ..rotateY(3.14 * _animation.value),
      child: _animation.value <= 0.5
          ? _getScanInfoItem(widget.record)
          : Transform(
              alignment: Alignment.center,
              transform: Matrix4.diagonal3Values(-1.0, 1.0, 1.0),
              child: _getBackItem(widget.record),
            ),
    );
  }

  _getScanInfoItem(ScanInfo record) {
    return Container(
      width: 314.sp,
      height: 414.sp,
      margin: EdgeInsets.all(4.sp),
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
        // border: widget.inSelect ? Border.all(color: colorBrandBorder, width: 4.sp) : null,
        boxShadow: [
          BoxShadow(
            color: widget.inSelect
                ? colorBlueDeep.withOpacity(0.8)
                : widget.selectMode
                    ? const Color(0xFFD8D3F0)
                    : colorShader,
            blurRadius: 2.sp,
            spreadRadius: widget.selectMode ? 4.sp : 2.sp,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              record.getStatusView(),
              const Spacer(),
              widget.selectMode ? _selectedCircle() : GestureDetector(
                onTap: widget.selectMode ? null : _flip,
                child: Builder(
                  builder: (context) {
                    return Image.asset("res/icons/icon_activity_more.png", width: 32.sp);
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 46.sp),
          isNotEmpty(record.smilePhoto)
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(44.sp),
                  child: Image.file(
                    File(getLocalPath(record.smilePhoto)),
                    width: 88.sp,
                    height: 88.sp,
                    fit: BoxFit.cover,
                  ),
                )
              : Image.asset("res/imgs/record_avatar.png", width: 88.sp, height: 88.sp, fit: BoxFit.cover),
          SizedBox(height: 12.sp),
          Container(
            width: 254.sp,
            height: 40.sp,
            alignment: Alignment.center,
            child: SingleText(isNotEmpty(record.recordName) ? record.recordName : "-", color2B, 28.sp, FontWeight.w600),
          ),
          if (widget.showActivityName) ...[
            Container(
              width: 254.sp,
              height: 32.sp,
              alignment: Alignment.center,
              child: SingleText(record.campaignName, colorA4, 20.sp),
            ),
          ],
          Container(
            width: 254.sp,
            height: 32.sp,
            alignment: Alignment.center,
            child:
                SingleText(Global.getShowTime(milliseconds: record.customTime, ignoreSameDay: false), colorA4, 20.sp),
          ),
          const Spacer(),
          if (!widget.selectMode)
            _getActionView(record)
        ],
      ),
    );
  }

  _getBackItem(ScanInfo record) {
    return Container(
      width: 314.sp,
      height: 414.sp,
      margin: EdgeInsets.all(4.sp),
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
        boxShadow: [
          BoxShadow(
            color: colorShader,
            blurRadius: 1.sp,
            spreadRadius: 1.sp,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              record.getStatusView(),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  hideKeyboard();
                  eventBus.fire(EventDeleteRecord(record));
                },
                child: Builder(
                  builder: (context) {
                    return Image.asset("res/icons/icon_delete.png", width: 32.sp);
                  },
                ),
              ),
              SizedBox(width: 20.sp),
              GestureDetector(
                onTap: _flip,
                child: Builder(
                  builder: (context) {
                    return Image.asset("res/icons/icon_reset.png", width: 32.sp);
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 16.sp),
          MyText(isNotEmpty(record.recordName) ? record.recordName : "-", color2B, 28.sp, FontWeight.w600),
          SizedBox(height: 8.sp),
          SingleText(Global.getShowTime(milliseconds: record.customTime, ignoreSameDay: false), colorA4, 20.sp),
          const Spacer(),
          //移除这个功能
          // Click(
          //   onTap: widget.selectMode
          //       ? null
          //       : () {
          //           hideKeyboard();
          //           push(
          //             ScanInfoRecord(
          //               activity: widget.activity,
          //               recordData: record.toJson(),
          //             ),
          //           );
          //         },
          //   child: Container(
          //     width: 266.sp,
          //     height: 56.sp,
          //     decoration: BoxDecoration(
          //       color: colorPurpleLavender,
          //       borderRadius: BorderRadius.circular(8.sp),
          //     ),
          //     alignment: Alignment.center,
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       crossAxisAlignment: CrossAxisAlignment.center,
          //       children: [
          //         Image.asset(
          //           "res/icons/icon_export_brand.png",
          //           height: 28.sp,
          //         ),
          //         SizedBox(width: 8.sp),
          //         MyText(Lang.export_u_card, colorBlueDeep, 24.sp)
          //       ],
          //     ),
          //   ),
          // )
        ],
      ),
    );
  }

  _getActionView(ScanInfo record) {
    Widget actionRow = const SizedBox();
    switch (record.status) {
      case RecordStatus.recording:
        actionRow = Click(
          onTap: widget.selectMode
              ? null
              : () {
                  hideKeyboard();
                  push(
                    ScanInfoRecord(
                      activity: widget.activity,
                      recordData: record.toJson(),
                    ),
                  );
                },
          child: Container(
            width: 266.sp,
            height: 56.sp,
            decoration: BoxDecoration(
              color: colorPurpleLavender,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Image.asset(
                //   "res/icons/icon_edit_brand.png",
                //   height: 28.sp,
                // ),
                // SizedBox(width: 8.sp),
                MyText(Lang.complete_record, colorBlueDeep, 24.sp)
              ],
            ),
          ),
        );
        break;
      case RecordStatus.waitingUpload:
        actionRow = Click(
          child: Container(
            width: 266.sp,
            height: 56.sp,
            decoration: BoxDecoration(
              color: colorPurpleLavender,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Image.asset(
                //   "res/icons/icon_upload_brand.png",
                //   height: 28.sp,
                // ),
                // SizedBox(width: 8.sp),
                MyText(Lang.upload_record, colorBlueDeep, 24.sp)
              ],
            ),
          ),
          onTap: () {
            hideKeyboard();
            eventBus.fire(EventUploadSingleRecord(record));
          },
        );
        break;
      case RecordStatus.uploading:
        actionRow = Click(
          onTap: widget.selectMode
              ? null
              : () {
                  hideKeyboard();
                  MooeliItem item = MooeliItem();
                  item.caseId = record.campaignId;
                  item.caseName = record.recordName;
                  push(MooeliRecordPage(item, scanInfo: record, singleRecord: true));
                },
          child: Container(
            width: 266.sp,
            height: 56.sp,
            decoration: BoxDecoration(
              color: colorPurpleLavender,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Image.asset(
                //   "res/icons/icon_detail_brand.png",
                //   height: 28.sp,
                // ),
                // SizedBox(width: 8.sp),
                MyText(Lang.view_detail, colorBlueDeep, 24.sp)
              ],
            ),
          ),
        );
        break;
      case RecordStatus.failUpload:
        actionRow = Click(
          child: Container(
            width: 266.sp,
            height: 56.sp,
            decoration: BoxDecoration(
              color: colorPurpleLavender,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Image.asset(
                //   "res/icons/icon_retry_brand.png",
                //   height: 28.sp,
                // ),
                // SizedBox(width: 8.sp),
                MyText(Lang.retry_upload, colorBlueDeep, 24.sp)
              ],
            ),
          ),
          onTap: () {
            hideKeyboard();
            eventBus.fire(EventUploadSingleRecord(record));
          },
        );
        break;
      case RecordStatus.completeUpload:
        actionRow = Row(
          children: [
            Flexible(
              child: Click(
                onTap: widget.selectMode
                    ? null
                    : () {
                        hideKeyboard();
                        MooeliItem item = MooeliItem();
                        item.caseId = record.campaignId;
                        item.caseName = record.recordName;
                        push(MooeliRecordPage(item, scanInfo: record, recordId: record.recordId, singleRecord: true));
                      },
                child: Container(
                  height: 56.sp,
                  decoration: BoxDecoration(
                    color: colorPurpleLavender,
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Image.asset(
                      //   "res/icons/icon_detail_brand.png",
                      //   height: 28.sp,
                      // ),
                      // SizedBox(width: 8.sp),
                      MyText(Lang.view_detail, colorBlueDeep, 24.sp)
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.sp),
            Flexible(
              child: Click(
                onTap: widget.selectMode
                    ? null
                    : () {
                        User.instance.refreshToken(okCallback: () {
                          push(WebReportPage(record));
                        });
                      },
                child: Container(
                  height: 56.sp,
                  decoration: BoxDecoration(
                    color: colorPurpleLavender,
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Image.asset(
                      //   "res/icons/icon_detail_brand.png",
                      //   height: 28.sp,
                      // ),
                      // SizedBox(width: 8.sp),
                      MyText(Lang.make_report, colorBlueDeep, 24.sp)
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
        break;
      default:
        actionRow = const SizedBox();
        break;
    }
    return actionRow;
  }

  Widget _selectedCircle() {
    return SizedBox(
      width: 32.sp,
      height: 32.sp,
      child: ClipOval(
        child: Image.asset(widget.inSelect ? 'res/icons/icon_selected.png' : 'res/icons/icon_unselected.png'),
      ),

    );
  }
}
