import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/default_style.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/common/local_account.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/CheckScanImgSwiper.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

enum ScanStep {
  SmilePhoto, //微笑照
  IntraoralPhoto, //口内照
  UserComment, //备注
  Tags, //标签
  DentalPlaquePhoto, //牙菌斑检测
}

class ScanInfoRecord extends BasePage {
  ScanActivity activity;
  Map recordData;

  ScanInfoRecord({Key? key, required this.activity, this.recordData = const {}}) : super(key: key);

  @override
  State<StatefulWidget> createState() => ScanInfoRecordState();
}

class ScanInfoRecordState extends BasePageState<ScanInfoRecord>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  String caseId = ""; //活动ID

  int scanTool = SCAN_TOOL_ULTRA;

  bool hideTag = false;
  Map<String, List<String>> tagMap = {};

  Map stageMap = {};

  ScanInfo currentRecord = ScanInfo();

  List swiperScanList = [];

  bool showCloseUltraTip = false;

  List<int> widgetIndexs = [0, 1, 2, 3];

  final GlobalKey buttonKey = GlobalKey<HCustomButtonState>();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController ageController = TextEditingController(text: "0");
  TextEditingController remarkController = TextEditingController();

  final ScrollController _scrollController = ScrollController();

  Map faceTypes = {};

  bool editMode = false;

  int stepIndex = 0;
  final List<GlobalKey> _keys = List.generate(4, (index) => GlobalKey());
  List<double> childrenHeight = List.generate(4, (index) => 0.0);
  List<double> childrenPos = List.generate(5, (index) => 0.0);
  bool noScrollPos = false;
  int currentPos = 0;

  @override
  initState() {
    super.initState();
    initScanMode(1);
    WidgetsBinding.instance.addObserver(this);
    setScreenVertiacl();
    addEventListener<EventSmilePhoto>((event) {
      setState(() {
        currentRecord.setPhoto(event.type, event.path);
      });
      checkIsCanPostAndReBuildList();
    });
    addEventListener<EventUserComment>((event) {
      setState(() {
        currentRecord.note = event.comment;
      });
    });
    addEventListener<EventPopScan>((event) {
      if (!event.fromRecord && selectScanByUltra(scanTool)) {
        setState(() {
          // showCloseUltraTip = true;
        });
      }
    });
    addEventListener<EventUploadRecord>((event) {
      pop();
    });
    addEventListener<EventQrCode>((event) {
      setState(() {
        currentRecord.recordSn = event.code;
        currentRecord.repeatSn = false;
      });
    });
    initAsync();
    for (String key in ["102", "100", "101"]) {
      faceTypes[key] = faceMap[key];
    }
  }

  List convertList = [];

  initAsync() async {
    await Global.initGlobalAsync();

    delay(100, () async {
      getScanConfigJson((json) {
        if (isNotEmpty(json) && isNotEmpty(json["quickScanV3_conversionProject"])) {
          Map map = json["quickScanV3_conversionProject"] as Map;
          List list = map.values.toList();
          setState(() {
            convertList = list;
            for (String key in map.keys) {
              if (isNotEmpty(map[key]["title"][lang]) && isNotEmpty(map[key]["projectNames"])) {
                try {
                  List<String> value = [];
                  for (Map name in map[key]["projectNames"]) {
                    value.add(name[lang]);
                  }
                  tagMap[map[key]["title"][lang]] = value;
                  logger("getScanTagsConfigJson ${tagMap[map[key]["title"][lang]]}");
                } catch (ex) {
                  logger("getScanTagsConfigJson error: $ex");
                }
              }
            }
          });
        }
        if (isNotEmpty(json) && isNotEmpty(json["quickScanV3_progressStage"])) {
          stageMap = json["quickScanV3_progressStage"] ?? {};
          if (currentRecord.progressStage == "NONE" && isNotEmpty(stageMap)) {
            currentRecord.progressStage = stageMap.values.first["code"];
          }
        }
      });

      if (isNotEmpty(Global.getRouterParams(context)) && isNotEmpty(Global.getRouterParams(context)["recordData"])) {
        showCurrentRecord(ScanInfo.fromJson(Global.getRouterParams(context)["recordData"]));
        editMode = true;
      } else if (isNotEmpty(widget.recordData)) {
        showCurrentRecord(ScanInfo.fromJson(widget.recordData));
        editMode = true;
      }
      // bool currentInList = false;
      // recordList = await loadCacheRecordList(caseId);
      // if (isNotEmpty(recordList)) {
      //   if (!editMode && recordList.last.status == RecordStatus.recording) {
      //     showCurrentRecord(recordList.last);
      //     currentInList = true;
      //   }
      // }
      // if (!editMode && !currentInList) {
      //   String cache = Global.sharedPrefs.getString("current_record_$caseId") ?? "";
      //   if (isNotEmpty(cache)) {
      //     dynamic json = jsonDecode(cache);
      //     ScanInfo record = ScanInfo.fromJson(json);
      //     if (recordList.map((e) => e.recordId).contains(record.recordId)) {
      //       showCurrentRecord(ScanInfo());
      //     } else {
      //       showCurrentRecord(record);
      //     }
      //   }
      // }
      logger(currentRecord.toJson().toString(), key: "currentRecord init");

      checkIsCanPostAndReBuildList();
    });

    delay(1000, getChildrenHeights);
  }

  saveRecordCache() async {
    String jsonStr = jsonEncode(currentRecord.toJson());
    logger(jsonStr, key: "saveRecordCache");
    Global.sharedPrefs.setString("current_record_$caseId", jsonStr);
    currentRecord.campaignId = caseId;
    currentRecord.campaignName = widget.activity.campaignName;
    if (currentRecord.hasAllRequest() && currentRecord.status == RecordStatus.recording) {
      currentRecord.status = RecordStatus.waitingUpload;
    }
    await saveRecordToDb(caseId, currentRecord);
  }

  //监听后台返回事件
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    // logger("ScanConnectUltra didChangeAppLifecycleState: $state");
    switch (state) {
      case AppLifecycleState.paused:
        if (currentRecord.hasData()) {
          saveCurrentRecordToList();
        }
        break;
      default:
    }
  }

  @override
  void dispose() {
    if (currentRecord.hasData()) {
      saveCurrentRecordToList();
      if (currentRecord.hasAllRequest()) {
        currentRecord = ScanInfo();
        saveRecordCache();
      }
      Global.toast(Lang.auto_saved);
    } else {
      saveRecordCache();
    }
    SmartDialog.dismiss();
    disconnectUltra();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  //别的页面数据回来或更新数据均走这里
  updateStateByType(String typeStr, dynamic params, {dynamic callback}) async {
    switch (typeStr) {
      case "newSmilePhoto": //微笑照返回
        setState(() {
          currentRecord.setPhoto(params["type"], params["path"]);
        });
        break;
      case "deleteSmilePhoto": //删除了微笑照
        setState(() {
          currentRecord.setPhoto(params["type"], "");
        });
        break;
      case "newUserComment": //修改了文字备注
        setState(() {
          currentRecord.note = params;
        });
        break;
      case "deleteUserComment": //删除了文字备注
        setState(() {
          currentRecord.note = "";
        });
        break;
      case "selectNewTag": //选中新标签
      case "deleteNewTag": //删除选中标签
        setState(() {
          if (typeStr == "selectNewTag") {
            currentRecord.tagList.add(params);
          } else {
            for (int i = 0; i < currentRecord.tagList.length; i++) {
              if (params.toString() == currentRecord.tagList[i].toString()) {
                currentRecord.tagList.removeAt(i);
                break;
              }
            }
          }
        });
        break;
      case "updateScanImgs": //更新口扫照片
        ToothCameraTypeEnum toothCameraType = params["toothCameraType"];
        List tempShowUseImgs = params["tempShowUseImgs"] ?? [];
        List tempShowUnuseImgs = params["tempShowUnuseImgs"] ?? [];
        dynamic uploadFinishCb = params["uploadFinishCb"];

        for (dynamic path in tempShowUseImgs) {
          await Global.copyImageDocument(path, "scan_${currentRecord.recordId}", save: true);
        }
        // for (dynamic path in tempShowUnuseImgs) {
        //   await Global.copyImageDocument(path, save: true);
        // }

        switch (toothCameraType) {
          case ToothCameraTypeEnum.occlusion:
            setState(() {
              currentRecord.closePhotos = tempShowUseImgs;
              currentRecord.closeXPhotos = tempShowUnuseImgs;
            });
            break;
          case ToothCameraTypeEnum.openMaskOff:
            setState(() {
              currentRecord.openPhotos = tempShowUseImgs;
              currentRecord.openXPhotos = tempShowUnuseImgs;
            });
            break;
          case ToothCameraTypeEnum.maxillary:
            setState(() {
              currentRecord.upPhotos = tempShowUseImgs;
              currentRecord.upXPhotos = tempShowUnuseImgs;
            });
            break;
          case ToothCameraTypeEnum.mandibular:
            setState(() {
              currentRecord.downPhotos = tempShowUseImgs;
              currentRecord.downXPhotos = tempShowUnuseImgs;
            });
            break;
          case ToothCameraTypeEnum.openMaskOn:
            setState(() {
              currentRecord.maskPhotos = tempShowUseImgs;
              currentRecord.maskXPhotos = tempShowUnuseImgs;
            });
            break;
        }
        uploadFinishCb();
        break;
    }

    saveCurrentRecordToList();
    checkIsCanPostAndReBuildList();
  }

  addChildScanImgByRowIndex(parentList, targetList, ToothCameraTypeEnum type, [bool isLight = false]) {
    for (int i = 0; i < targetList.length; i++) {
      if (File(getLocalPath(targetList[i])).existsSync()) {
        parentList.add({
          "img": targetList[i], //每组口扫图片
          "type": type, //口扫步骤 ToothCameraTypeEnum
          "index": i, //每组图片序号，0-左，1-中，2-右
          "light": isLight,
        });
      }
    }
  }

  checkIsCanPostAndReBuildList() {
    swiperScanList = [];

    if (widgetIndexs.contains(ScanStep.IntraoralPhoto.index)) {
      addChildScanImgByRowIndex(swiperScanList, currentRecord.upPhotos, ToothCameraTypeEnum.maxillary);
      addChildScanImgByRowIndex(swiperScanList, currentRecord.closePhotos, ToothCameraTypeEnum.occlusion);
      addChildScanImgByRowIndex(swiperScanList, currentRecord.openPhotos, ToothCameraTypeEnum.openMaskOff);
      if (!hideMask) {
        addChildScanImgByRowIndex(swiperScanList, currentRecord.maskPhotos, ToothCameraTypeEnum.openMaskOn);
      }
      addChildScanImgByRowIndex(swiperScanList, currentRecord.downPhotos, ToothCameraTypeEnum.mandibular);
    }

    // if (checkRecordSizeMax() && buttonKey.currentState != null) {
    //   (buttonKey.currentState as HCustomButtonState).setText(Lang.save);
    // } else {
    //   (buttonKey.currentState as HCustomButtonState).setText(Lang.save_and_add_next);
    // }
    delay(1000, getChildrenHeights);
  }

  //获取每个步骤完成后的页面
  getScanStepView() {
    double width = 192.sp, height = 120.sp;
    List<Widget> listWidgets = [];
    List defaultScanImgs = [
      ["default_up.png"],
      ["default_close1.png", "default_close2.png", "default_close3.png"],
      ["default_open1.png", "default_open2.png", "default_open3.png"],
      ["default_mask1.png", "default_mask2.png", "default_mask3.png"],
      ["default_down.png"],
    ];
    List<ToothCameraTypeEnum> scanSteps = [
      ToothCameraTypeEnum.maxillary, //上颌
      ToothCameraTypeEnum.occlusion, //咬合
      ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
      ToothCameraTypeEnum.openMaskOn, //微张(带上牙套)
      ToothCameraTypeEnum.mandibular, //下颌
    ];
    for (int i = 0; i < 5; i++) {
      if (hideMask && i == 3) {
        continue;
      }
      List targetList = [];
      if (i == 0) {
        targetList = currentRecord.upPhotos;
      } else if (i == 1) {
        targetList = currentRecord.closePhotos;
      } else if (i == 2) {
        targetList = currentRecord.openPhotos;
      } else if (i == 3) {
        targetList = currentRecord.maskPhotos;
      } else if (i == 4) {
        targetList = currentRecord.downPhotos;
      }
      String title = getTitleByImgRow(scanSteps[i]);
      List<Widget> rowChilds = [];
      List<String> errorIndexReason = [];
      for (int j = 0; j < (i == 0 || i == 4 ? 1 : 3); j++) {
        bool isHaveErrorInfo = Global.isHaveErrorInfoByScanImgPath(j < targetList.length ? targetList[j] : "");
        rowChilds.add(Container(
          margin: EdgeInsets.fromLTRB(12.sp, 16.sp, 12.sp, 16.sp),
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.sp),
            color: const Color(0xffe6e6e6),
          ),
          child: GestureDetector(
            onTap: () {
              if (j < targetList.length) {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return CheckScanImgSwiper(
                      isCanReScan: true,
                      updateStateByType: updateStateByType,
                      initChooseImgPath: targetList[j],
                      scanImgInfoList: swiperScanList);
                }));
              }
            },
            child: Stack(alignment: Alignment.center, children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12.sp),
                child: j < targetList.length && File(getLocalPath(targetList[j])).existsSync()
                    ? Image.file(
                        File(getLocalPath(targetList[j])),
                        width: width,
                        height: height,
                        fit: BoxFit.cover,
                      )
                    : Image.asset("res/scan/${defaultScanImgs[i][j]}", width: width, height: height, fit: BoxFit.cover),
              ),
              !isHaveErrorInfo
                  ? const SizedBox()
                  : Positioned(
                      top: 8.sp,
                      right: 8.sp,
                      child: Image.asset("res/scan/scan_img_error.png", width: 32.sp),
                    ),
            ]),
          ),
        ));
        if (isHaveErrorInfo) {
          errorIndexReason.add(
              "${j + 1} - ${Global.getAlertTextByScanErrorCode(getErrorCode(targetList[j]), targetList[j])["content"]}");
        }
      }
      listWidgets.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 987.sp,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 60.sp,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.sp),
                      color: const Color(0xffececec),
                    ),
                    child: MyText(title, color7C, 24.sp, FontWeight.w500),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: rowChilds,
                  ),
                ],
              ),
            ),
            Image.asset(
              "res/imgs/scan_step_quote.png",
              width: 40.sp,
              height: 212.sp,
            ),
            SizedBox(width: 40.sp),
            GestureDetector(
              onTap: () async {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return ToothCameraView(initDirectionType: scanSteps[i], initRouterParamas: {
                    "updateStateByType": updateStateByType,
                  });
                }));
              },
              child: Container(
                width: 196.sp,
                height: 56.sp,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border: Border.all(color: colorBlueDeep),
                  color: colorPurpleLavender,
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: MyText(Lang.retake_current_record, colorBlueDeep, 24.sp),
              ),
            )
          ],
        ),
      );
    }
    listWidgets.add(
      Padding(
        padding: EdgeInsets.only(top: 28.sp, right: 278.sp),
        child: HCustomButton(
          onPress: () async {
            Navigator.pushNamed(context, "ScanPre", arguments: {
              "updateStateByType": updateStateByType,
              "scanTool": scanTool,
            });
          },
          bgColor: colorBlueDeep,
          radius: 8.sp,
          text: Lang.retake_all_photos,
          width: 320.sp,
          height: 56.sp,
          fontSize: 24.sp,
        ),
      ),
    );
    return Padding(
        padding: EdgeInsets.fromLTRB(8.sp, 32.sp, 0, 32.sp),
        child: Column(
          children: listWidgets,
        ));
  }

  getUserInputView() {
    return Container(
      key: _keys[0],
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 0, 16.sp),
            child: MyText(Lang.base_info, Colors.black, 32.sp, FontWeight.w500),
          ),
          Divider(height: 1.sp, color: colorE1),
          SizedBox(height: 24.sp),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 24.sp),
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MyText(Lang.name, Colors.black, 24.sp),
                              MyText("*", colorRedMuted, 24.sp),
                            ],
                          ),
                        ),
                        SizedBox(width: 24.sp),
                        Expanded(
                          flex: 1,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MyText(Lang.gender, Colors.black, 24.sp),
                              MyText("*", colorRedMuted, 24.sp),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Container(
                            height: 56.sp,
                            margin: EdgeInsets.only(top: 12.sp),
                            padding: EdgeInsets.fromLTRB(16.sp, 7.sp, 16.sp, 7.sp),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: colorE1,
                              ),
                              borderRadius: BorderRadius.circular(8.sp),
                            ),
                            child: TextFormField(
                              controller: nameController,
                              maxLines: 1,
                              maxLength: 30,
                              textAlign: TextAlign.start,
                              onChanged: (value) {
                                currentRecord.recordName = value;
                              },
                              style: TextStyle(fontSize: 28.sp, color: color2B),
                              decoration: InputDecoration(
                                counterText: '',
                                isDense: true,
                                contentPadding: EdgeInsets.zero,
                                border: InputBorder.none,
                                hintText: Lang.input_name,
                                hintStyle: TextStyle(fontSize: 24.sp, color: colorA4),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 24.sp),
                        Expanded(
                          flex: 1,
                          child: Container(
                            height: 56.sp,
                            margin: EdgeInsets.only(top: 12.sp),
                            padding: EdgeInsets.fromLTRB(8.sp, 7.sp, 8.sp, 7.sp),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: colorE1,
                              ),
                              borderRadius: BorderRadius.circular(8.sp),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                    flex: 1,
                                    child: _getSelectView(Lang.gender_man, currentRecord.gender == "male", () {
                                      setState(() {
                                        currentRecord.gender = "male";
                                      });
                                    })),
                                SizedBox(width: 12.sp),
                                Expanded(
                                    flex: 1,
                                    child: _getSelectView(Lang.gender_woman, currentRecord.gender == "female", () {
                                      setState(() {
                                        currentRecord.gender = "female";
                                      });
                                    })),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 56.sp),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MyText(Lang.age, Colors.black, 24.sp),
                              MyText("* ", colorRedMuted, 24.sp),
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 24.sp,
                        ),
                        Expanded(
                            flex: 1,
                            child: Row(
                              children: [
                                MyText(Lang.collect_phone, Colors.black, 24.sp),
                                SizedBox(width: 4.sp),
                                MyText(Lang.optional, colorA4, 24.sp),
                              ],
                            )),
                      ],
                    ),
                    SizedBox(height: 12.sp),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Click(
                            onTap: () {
                              PickerStyle pickerStyle = DefaultPickerStyle();
                              pickerStyle.commitButton = Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.only(left: 32.sp, right: 32.sp),
                                child: Text(Lang.confirm, style: TextStyle(color: colorBlueDeep, fontSize: 32.sp)),
                              );
                              pickerStyle.cancelButton = Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.only(left: 32.sp, right: 32.sp),
                                child: Text(Lang.cancel,
                                    style: TextStyle(color: Theme.of(context!).unselectedWidgetColor, fontSize: 32.sp)),
                              );
                              pickerStyle.textSize = 28.sp;

                              List<int> singleData = List.generate(101, (index) => index);
                              Pickers.showSinglePicker(
                                context,
                                data: singleData,
                                selectData: int.parse(currentRecord.getAge()),
                                pickerStyle: pickerStyle,
                                onConfirm: (var data, int position) {
                                  setState(() {
                                    currentRecord.age = position.toString();
                                  });
                                },
                              );
                            },
                            child: Container(
                              height: 56.sp,
                              margin: EdgeInsets.only(top: 12.sp),
                              padding: EdgeInsets.fromLTRB(16.sp, 7.sp, 16.sp, 7.sp),
                              alignment: Alignment.centerLeft,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: colorE1,
                                ),
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(8.sp),
                              ),
                              child: MyText(
                                isNotEmpty(currentRecord.age) ? currentRecord.age : Lang.click_select_age,
                                isNotEmpty(currentRecord.age) ? color2B : colorA4,
                                24.sp,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 24.sp),
                        Expanded(
                          flex: 1,
                          child: Container(
                            height: 56.sp,
                            margin: EdgeInsets.only(top: 12.sp),
                            padding: EdgeInsets.fromLTRB(16.sp, 7.sp, 16.sp, 7.sp),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: colorE1,
                              ),
                              borderRadius: BorderRadius.circular(8.sp),
                            ),
                            child: TextFormField(
                              controller: phoneController,
                              maxLines: 1,
                              maxLength: 20,
                              keyboardType: TextInputType.phone,
                              textAlign: TextAlign.start,
                              onChanged: (value) {
                                currentRecord.patientPhone = value;
                              },
                              style: TextStyle(fontSize: 24.sp, color: color2B),
                              decoration: InputDecoration(
                                counterText: '',
                                isDense: true,
                                contentPadding: EdgeInsets.zero,
                                border: InputBorder.none,
                                hintText: Lang.input_phone,
                                hintStyle: TextStyle(fontSize: 24.sp, color: colorA4),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(width: 12.sp),
              Expanded(flex: 1, child: getFaceView()),
            ],
          ),
          SizedBox(height: 32.sp),
        ],
      ),
    );
  }

  _onChangeAge(int offset) {
    try {
      if (isEmpty(currentRecord.age)) {
        currentRecord.age = "0";
      }
      if (currentRecord.age.contains(".")) {
        if (currentRecord.age.indexOf(".") == currentRecord.age.length - 1) {
          int age = int.parse(currentRecord.age.substring(0, currentRecord.age.length - 1)) + offset;
          if (age >= 0 && age <= 150) {
            currentRecord.age = age.toString();
          }
        } else if (currentRecord.age.indexOf(".") == currentRecord.age.length - 2) {
          double age = double.parse(currentRecord.age) + offset;
          if (age >= 0 && age <= 150) {
            currentRecord.age = age.toString();
          }
        }
      } else {
        int age = int.parse(currentRecord.age) + offset;
        if (age >= 0 && age <= 150) {
          currentRecord.age = age.toString();
        }
      }
    } catch (e) {
      //
    }
    ageController.text = currentRecord.age;
    ageController.selection = TextSelection.fromPosition(TextPosition(offset: currentRecord.age.length));
  }

  _onInputAge(value) {
    try {
      if (isEmpty(value)) {
        currentRecord.age = "0";
      }
      if (value.contains(".")) {
        if (value.indexOf(".") == value.length - 1) {
          currentRecord.age = value;
        } else if (value.indexOf(".") == value.length - 2) {
          if (value.endsWith("5")) {
            currentRecord.age = value;
          }
        }
      } else {
        int age = int.parse(value);
        if (age >= 0 && age <= 150) {
          currentRecord.age = age.toString();
        }
      }
    } catch (e) {
      //
    }
    ageController.text = currentRecord.age;
    ageController.selection = TextSelection.fromPosition(TextPosition(offset: currentRecord.age.length));
  }

  Widget _getSelectView(String text, bool isSelected, dynamic onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        height: 40.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          color: isSelected ? colorPurpleLavender : Colors.transparent,
        ),
        child: MyText(text, isSelected ? colorBlueDeep : colorA4, 24.sp),
      ),
    );
  }

  getFaceView() {
    return SizedBox(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        // Row(
        //   children: [
        //     MyText(Lang.face_photo, color2B, 28.sp, FontWeight.w500),
        //     MyText(Lang.optional, color7C, 24.sp),
        //     SizedBox(height: 60.sp),
        //   ],
        // ),
        // SizedBox(height: 26.sp),
        SizedBox(
          height: 270.sp,
          child: GridView(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 197 / 270,
            ),
            children: faceTypes.keys.map((key) => getFaceItemView(key, faceTypes[key])).toList(),
          ),
        ),
      ],
    ));
  }

  Widget getFaceItemView(String type, String title) {
    logger("setPhoto getFaceItemView $type ${currentRecord.getPhoto(type)}");
    return SizedBox(
      height: 270.sp,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(width: 12.sp),
              MyText(title, Colors.black, 24.sp),
              SizedBox(width: 4.sp),
              MyText(Lang.optional_scan, colorA4, 20.sp),
            ],
          ),
          SizedBox(height: 10.sp),
          ClipRRect(
            borderRadius: BorderRadius.circular(12.sp),
            child: isNotEmpty(currentRecord.getPhoto(type))
                ? GestureDetector(
                    onTap: () {
                      int index = 0;
                      List<String> imgList = [];
                      List<String> typeList = [];
                      List<String> titleList = [];
                      for (String key in faceTypes.keys) {
                        if (isNotEmpty(currentRecord.getPhoto(key))) {
                          if (key == type) {
                            index = typeList.length;
                          }
                          typeList.add(key);
                          titleList.add(faceTypes[key]);
                          imgList.add(getLocalPath(currentRecord.getPhoto(key)));
                        }
                      }
                      Global.showPhotoBrowerModal(
                        context,
                        imgPathList: imgList,
                        onPageChanged: (i) {
                          index = i;
                        },
                        deleteCalllBack: () {
                          Global.showAlertDialog(Lang.warn, Lang.confirm_delete_smile_photo, okCallBack: () {
                            updateStateByType("deleteSmilePhoto", {"type": typeList[index]});
                            Navigator.pop(context);
                          });
                        },
                        cbWidget: GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            openFaceCameraView(typeList[index], titleList[index]);
                          },
                          child: Container(
                            padding: EdgeInsets.fromLTRB(80.sp, 20.sp, 80.sp, 20.sp),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white),
                              borderRadius: BorderRadius.circular(24.sp),
                            ),
                            child: MyText(Lang.retake_photo, Colors.white, 28.sp, FontWeight.w500),
                          ),
                        ),
                        titles: titleList,
                        defaultShowIndex: index,
                        initScale: 0.6,
                      );
                    },
                    child: Container(
                      width: 198.sp,
                      height: 220.sp,
                      color: Colors.transparent,
                      child: getImgByUrlOrPath(currentRecord.getPhoto(type), 160.sp),
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      openFaceCameraView(type, title);
                    },
                    child: Image.asset(
                      "res/scan/default_face_${type}_new.png",
                      width: 198.sp,
                      height: 220.sp,
                      fit: BoxFit.cover,
                    ),
                  ),
          ),
          // SizedBox(height: 32.sp),
        ],
      ),
    );
  }

  openFaceCameraView(String type, String title) async {
    List<String> types = [];
    List allTypes = faceTypes.keys.toList();
    for (int i = allTypes.indexOf(type); i < allTypes.length; i++) {
      if (isEmpty(currentRecord.getPhoto(allTypes[i]))) {
        types.add(allTypes[i]);
      }
    }
    // if (Platform.isAndroid) {
    //   PermissionStatus storageStatus = await Permission.camera.status;
    //   if (storageStatus != PermissionStatus.granted) {
    //     Global.showAlertDialog(
    //       Lang.warn,
    //       Lang.request_camera_smile,
    //       okCallBack: () {
    //         Navigator.pushNamed(context, "AiFaceCameraView", arguments: {
    //           "updateStateByType": updateStateByType,
    //           "type": type,
    //           "types": types,
    //         });
    //       },
    //     );
    //     return;
    //   }
    // }

    Navigator.pushNamed(context, "AiFaceCameraView", arguments: {
      "updateStateByType": updateStateByType,
      "type": type,
      "types": types,
    });
  }

  getScanView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onLongPress: () {
            if (HHttp.httpEnv != HttpEnv.prod || isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
              saveCurrentRecordToList();
              setState(() {
                if (isNotEmpty(localPhotos["la_up_use.zip"])) {
                  currentRecord.upPhotos = localPhotos["la_up_use.zip"]!.sublist(0, 1);
                  for (dynamic path in currentRecord.upPhotos) {
                    Global.copyImageDocument(path, "scan_${currentRecord.recordId}", save: true);
                  }
                }
                if (isNotEmpty(localPhotos["la_lr_close_use.zip"])) {
                  currentRecord.closePhotos = localPhotos["la_lr_close_use.zip"]!;
                  for (dynamic path in currentRecord.closePhotos) {
                    Global.copyImageDocument(path, "scan_${currentRecord.recordId}", save: true);
                  }
                }
                if (isNotEmpty(localPhotos["la_lr_open_use.zip"])) {
                  currentRecord.openPhotos = localPhotos["la_lr_open_use.zip"]!;
                  for (dynamic path in currentRecord.openPhotos) {
                    Global.copyImageDocument(path, "scan_${currentRecord.recordId}", save: true);
                  }
                }
                if (isNotEmpty(localPhotos["la_down_use.zip"])) {
                  currentRecord.downPhotos = localPhotos["la_down_use.zip"]!.sublist(0, 1);
                  for (dynamic path in currentRecord.downPhotos) {
                    Global.copyImageDocument(path, "scan_${currentRecord.recordId}", save: true);
                  }
                }
              });
              saveCurrentRecordToList();
              checkIsCanPostAndReBuildList();
              hideKeyboard();
              toast("口扫图片已填充！");
            }
          },
          child: Padding(
            padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 0, 16.sp),
            child: MyText(Lang.scan_intraoral_photo, Colors.black, 32.sp, FontWeight.w500),
          ),
        ),
        Container(width: 1328.sp, height: 1.sp, color: colorE1),
        currentRecord.hasScanPhoto()
            ? Container(
                padding: EdgeInsets.fromLTRB(24.sp, 0.sp, 0, 0),
                child: getScanStepView(),
              )
            : SizedBox(
                width: 1328.sp,
                child: Click(
                  onTap: () {
                    Navigator.pushNamed(context, "ScanPre", arguments: {
                      "updateStateByType": updateStateByType,
                      "scanTool": scanTool,
                    });
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 32.sp),
                      Image.asset("res/icons/icon_start_scan.png", width: 60.sp),
                      SizedBox(height: 8.sp),
                      MyText(Lang.double_click_scan, colorA4, 24.sp),
                      SizedBox(height: 32.sp),
                    ],
                  ),
                ),
              ),
      ],
    );
  }

  getSnView() {
    late Widget view;
    if (isEmpty(currentRecord.recordSn)) {
      view = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 28.sp),
            child: MyText(Lang.scan_bind_code, color2B, 28.sp, FontWeight.w500),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: HCustomButton(
              width: 440.sp,
              height: 68.sp,
              text: Lang.scan_code,
              initVisible: true,
              radius: 16.sp,
              bgColor: color2B,
              onPress: () {
                pushNamed("ScanCode", {"caseId": caseId});
              },
            ),
          ),
          SizedBox(height: 20.sp),
        ],
      );
    } else {
      view = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 0),
            child: Stack(
              children: [
                MyText(Lang.scan_bind_code, color2B, 28.sp, FontWeight.w500),
                Align(
                  alignment: Alignment.center,
                  child: Container(
                    margin: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 40.sp),
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        MyText(currentRecord.recordSn, color7C, 32.sp),
                        currentRecord.repeatSn ? MyText(Lang.code_has_been_used, colorRed, 24.sp) : const SizedBox()
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(width: 1.sw - 40.sp, height: 1.sp, color: colorE1),
          Row(
            children: [
              Click(
                onTap: () {
                  Global.showAlertDialog(
                    Lang.cancel_binding,
                    Lang.cancel_binding_confirm,
                    okText: Lang.cancel_binding,
                    cancelText: Lang.not_cancel_binding,
                    isBringXBtn: true,
                    contentCenter: true,
                    okColor: colorRed,
                    okCallBack: () {
                      setState(() {
                        currentRecord.recordSn = "";
                        currentRecord.repeatSn = false;
                      });
                    },
                  );
                },
                child: Container(
                  width: 0.5.sw - 48.sp,
                  height: 84.sp,
                  alignment: Alignment.center,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset("res/icons/icon_cancel.png", width: 28.sp),
                      SizedBox(width: 4.sp),
                      MyText(Lang.cancel_binding, colorBrand, 24.sp),
                    ],
                  ),
                ),
              ),
              Container(width: 1.sp, height: 84.sp, color: colorE1),
              Click(
                onTap: () {
                  pushNamed("ScanCode", {"caseId": caseId});
                },
                child: Container(
                  width: 0.5.sw - 49.sp,
                  height: 84.sp,
                  alignment: Alignment.center,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset("res/icons/icon_scan.png", width: 28.sp),
                      SizedBox(width: 4.sp),
                      MyText(Lang.rescan_binding, colorBrand, 24.sp),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
    return Stack(
      children: [
        Container(
          width: 1.sw - 80.sp,
          margin: EdgeInsets.only(top: 32.sp),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: view,
        ),
        Positioned(
          right: 0,
          top: 16.sp,
          child: Image.asset("res/icons/icon_beta.png", height: 48.sp),
        ),
      ],
    );
  }

  getTagView() {
    if (isEmpty(tagMap)) {
      return const SizedBox();
    }
    List<Widget> children = [];
    if (!hideTag) {
      currentRecord.tagList = currentRecord.tagList.map((e) => getTagText(e)).toList();
      for (String key in tagMap.keys) {
        List<String> tags = tagMap[key]!;
        children.add(Column(
          children: [
            Container(
              width: 864.sp,
              height: 52.sp,
              margin: EdgeInsets.only(top: 24.sp),
              padding: EdgeInsets.only(left: 16.sp),
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.sp),
                  topRight: Radius.circular(8.sp),
                ),
                color: colorF3,
              ),
              child: MyText(key, color2B, 24.sp),
            ),
            Container(
              width: 864.sp,
              padding: EdgeInsets.fromLTRB(16.sp, 24.sp, 0, 4.sp),
              decoration: BoxDecoration(
                border: Border.all(color: colorF3),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8.sp),
                  bottomRight: Radius.circular(8.sp),
                ),
              ),
              child: Wrap(
                children: tags.map((tag) => _getTagItem(tag)).toList(),
              ),
            ),
          ],
        ));
      }
    }
    return Container(
      key: _keys[2],
      margin: EdgeInsets.only(top: 32.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 0, 16.sp),
            child: Row(
              children: [
                MyText(Lang.convert_project, Colors.black, 32.sp, FontWeight.w500),
                SizedBox(width: 8.sp),
                Click(
                  onTap: () {
                    Global.showCustomDialog(
                      Center(
                        child: Container(
                          width: 616.sp,
                          padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 16.sp),
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
                          ),
                          child: HeightText(
                              Lang.convert_project_tip, Colors.white, 24.sp, 1.6, FontWeight.w400, TextAlign.center),
                        ),
                      ),
                    );
                  },
                  child: Image.asset("res/icons/brand_question.png", width: 32.sp),
                ),
              ],
            ),
          ),
          Divider(height: 1.sp, color: colorE1),
          Padding(
            padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 24.sp),
            child: StaggeredGrid.count(
              crossAxisCount: 2,
              mainAxisSpacing: 0,
              crossAxisSpacing: 24.sp,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  getStageView() {
    if (isEmpty(stageMap)) {
      return const SizedBox();
    }
    List<Widget> children = [
      Padding(
        padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 0, 16.sp),
        child: MyText(Lang.progress_stage, Colors.black, 32.sp, FontWeight.w500),
      ),
      Divider(height: 1.sp, color: colorE1),
      Padding(
        padding: EdgeInsets.all(24.sp),
        child: Wrap(
          children: stageMap.values.map((stage) => _getStageItem(stage)).toList(),
        ),
      ),
    ];
    return Container(
      key: _keys[3],
      width: 1.sw - 80.sp,
      margin: EdgeInsets.only(top: 32.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _getTagItem(String tag) {
    bool isSelected = currentRecord.tagList.contains(tag);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (currentRecord.tagList.contains(tag)) {
            currentRecord.tagList.remove(tag);
          } else {
            currentRecord.tagList.add(tag);
          }
        });
      },
      child: Container(
        constraints: BoxConstraints(minHeight: 52.sp),
        margin: EdgeInsets.only(right: 20.sp, bottom: 20.sp),
        padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          border: Border.all(color: isSelected ? colorBrandBorder : colorE1),
          color: isSelected ? colorPurpleLavender : Colors.transparent,
        ),
        child: MyText(
          getTagText(tag),
          isSelected ? colorBlueDeep : color2B,
          24.sp,
          isSelected ? FontWeight.w500 : FontWeight.w400,
        ),
      ),
    );
  }

  String getTagText(String tag) {
    if (isNotEmpty(convertList)) {
      for (dynamic mapList in convertList) {
        dynamic list = mapList["projectNames"] as List;
        for (dynamic map in list) {
          if (map.values.contains(tag) && isNotEmpty(map[lang])) {
            // logger("getTagsText 1 $tag -> ${map[lang]}");
            return map[lang];
          }
        }
      }
    }
    return tag;
  }

  Widget _getStageItem(Map stage) {
    bool isSelected = currentRecord.progressStage == stage["code"];

    return GestureDetector(
      onTap: () {
        setState(() {
          currentRecord.progressStage = stage["code"];
        });
      },
      child: Container(
        constraints: BoxConstraints(minHeight: 48.sp),
        margin: EdgeInsets.only(right: 24.sp),
        padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          border: Border.all(color: isSelected ? colorBrandBorder : colorE1),
          color: isSelected ? colorPurpleLavender : Colors.transparent,
        ),
        child: MyText(
          stage["data-$lang"] ?? "",
          isSelected ? colorBlueDeep : color2B,
          24.sp,
          currentRecord.progressStage == stage["code"] ? FontWeight.w500 : FontWeight.w400,
        ),
      ),
    );
  }

  Future<bool> checkRecordSizeMax() async {
    List<ScanInfo> recordList = await getUploadRecordList(caseId, widget.activity.singleCampaign);
    int outSize = 1;
    if (isNotEmpty(recordList)) {
      for (int i = 0; i < recordList.length; i++) {
        if (currentRecord.recordId == recordList[i].recordId && isNotEmpty(recordList[i].recordId) ||
            currentRecord.customTime == recordList[i].customTime && currentRecord.customTime > 0) {
          outSize = 0;
          break;
        }
      }
      return recordList.length + outSize >= MAX_SCAN_SIZE;
    }
    return false;
  }

  int saveTimestamp = 0;

  saveCurrentRecordToList() async {
    // int time = DateTime.now().millisecondsSinceEpoch;
    // if (time - saveTimestamp < 2000) {
    //   return;
    // }
    // saveTimestamp = time;
    // List<ScanInfo> recordList = await loadUploadRecordList(caseId);
    // setState(() {
    //   bool exist = false;
    //   currentRecord.caseId = caseId;
    //   currentRecord.status = currentRecord.hasAllRequest() ? RecordStatus.waitingUpload : RecordStatus.recording;
    //   logger(
    //       "saveCurrentRecordToList ${currentRecord.recordName} ${currentRecord.recordId} ${currentRecord.status} ${currentRecord.customTime}");
    //   if (isNotEmpty(recordList)) {
    //     for (int i = 0; i < recordList.length; i++) {
    //       logger("recordList $i: ${recordList[i].recordName} ${recordList[i].recordId} ${recordList[i].customTime}");
    //       if (currentRecord.recordId == recordList[i].recordId && isNotEmpty(recordList[i].recordId) ||
    //           currentRecord.customTime == recordList[i].customTime && currentRecord.customTime > 0) {
    //         recordList[i] = currentRecord;
    //         exist = true;
    //         break;
    //       }
    //     }
    //   }
    //   if (!exist) {
    //     currentRecord.recordId = const Uuid().v4().replaceAll("-", "");
    //     currentRecord.customTime = DateTime.now().millisecondsSinceEpoch;
    //     recordList.add(currentRecord);
    //   }
    //   Set<int> uniqueItems = {};
    //   recordList.removeWhere((item) => !uniqueItems.add(item.customTime)); // 删除重复记录
    // });
    await saveRecordCache();
  }

  void scrollToPosition(double offset) {
    try {
      if (offset != 0) {
        _scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } catch (ex) {
      //
    }
  }

  void getChildrenHeights() {
    try {
      double pos = 0;
      for (int i = 0; i < _keys.length; i++) {
        RenderBox? box = _keys[i].currentContext?.findRenderObject() as RenderBox?;
        if (box != null) {
          double height = box.size.height;
          pos += height;
          childrenPos[i + 1] = pos;
          childrenHeight[i] = height;
        }
      }
      logger("scrollController getChildrenHeights $childrenHeight $childrenPos");
    } catch (ex) {
      //
    }
  }

  setCurrentPos(int position) {
    hideKeyboard();
    setState(() {
      currentPos = position;
    });
    double pos = 1;
    for (int j = 0; j < position; j++) {
      pos += childrenHeight[j];
    }
    noScrollPos = true;
    scrollToPosition(pos);
  }

  getNavigatorView() {
    List<String> titles = [
      Lang.base_info,
      Lang.scan_intraoral_photo,
      Lang.convert_project,
      // Lang.progress_stage,
      Lang.remark,
    ];
    List<Widget> children = [
      Click(
        onTap: pop,
        child: Container(
          height: 56.sp,
          margin: EdgeInsets.only(bottom: 16.sp),
          padding: EdgeInsets.all(8.sp),
          decoration: BoxDecoration(color: colorBlueDeep, borderRadius: BorderRadius.circular(8.sp)),
          child: Row(
            children: [
              Image.asset(
                "res/icons/icon_back_white.png",
                height: 32.sp,
              ),
              SizedBox(width: 4.sp),
              MyText(Lang.back, Colors.white, 24.sp)
            ],
          ),
        ),
      ),
    ];
    for (int i = 0; i < titles.length; i++) {
      if (i != 0) {
        children.add(
          Container(
            width: 1.sp,
            height: 37.sp,
            color: colorE1,
            margin: EdgeInsets.only(left: 16.sp),
          ),
        );
      }
      children.add(
        Click(
          onTap: () {
            setCurrentPos(i);
          },
          child: Container(
            width: 216.sp,
            height: 56.sp,
            // padding: EdgeInsets.only(left: 16.sp),
            alignment: Alignment.centerLeft,
            color: Colors.transparent,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 32.sp,
                  height: 32.sp,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: currentPos == i ? colorBlueDeep : colorF3,
                  ),
                  child: MyText("${i + 1}", currentPos == i ? Colors.white : color7C, 20.sp, FontWeight.w500),
                ),
                SizedBox(width: 8.sp),
                MyText(titles[i], currentPos == i ? colorBlueDeep : color2B, 24.sp,
                    currentPos == i ? FontWeight.w500 : FontWeight.w400),
              ],
            ),
          ),
        ),
      );
    }
    return Container(
      height: 1.sh,
      decoration: BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
      margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
      padding: EdgeInsets.all(16.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    caseId = widget.activity.campaignId;
    if (isEmpty(caseId) && isNotEmpty(Global.getRouterParams(context))) {
      caseId = Global.getRouterParams(context)["caseId"];
    }
    if (isNotEmpty(Global.getRouterParams(context)) && isNotEmpty(Global.getRouterParams(context)["recordData"])) {
      editMode = true;
    } else if (isNotEmpty(widget.recordData)) {
      editMode = true;
    }
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: colorPurpleLilac,
          image: const DecorationImage(image: AssetImage("res/imgs/my_page_content_bg.png"), fit: BoxFit.fill),
        ),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            // 触摸收起键盘
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: getNavigatorView(),
                ),
                Container(
                  width: 1328.sp,
                  height: 1.sh,
                  margin: EdgeInsets.fromLTRB(8.sp, 16.sp, 8.sp, 24.sp),
                  child: NotificationListener<ScrollNotification>(
                    onNotification: (ScrollNotification notification) {
                      if (notification is ScrollStartNotification) {
                        logger("scrollController 滚动开始 $childrenPos ${notification.dragDetails}");
                        if (notification.dragDetails != null) {
                          hideKeyboard();
                        }
                      } else if (notification is ScrollUpdateNotification) {
                        // logger("scrollController 滚动中: 偏移量 ${notification.metrics.pixels}");
                        double scrollY = notification.metrics.pixels;
                        // if (scrollY > childrenPos[2] && scrollY + 0.9.sh >= childrenPos[5]) {
                        //   if (currentPos != 4) {
                        //     setState(() {
                        //       currentPos = 4;
                        //     });
                        //     logger("scrollController ==============> 4 $scrollY ${childrenPos[4]}");
                        //   }
                        // } else if (scrollY > childrenPos[2] && scrollY + 0.8.sh >= childrenPos[4]) {
                        //   if (currentPos != 3) {
                        //     setState(() {
                        //       currentPos = 3;
                        //     });
                        //     logger("scrollController ==============> 3 $scrollY ${1.sh}");
                        //   }
                        // } else {
                        if (!noScrollPos) {
                          for (int i = 0; i < childrenHeight.length; i++) {
                            if (scrollY > childrenPos[i] && scrollY < childrenPos[i + 1]) {
                              if (currentPos != i) {
                                setState(() {
                                  currentPos = i;
                                });
                                logger("scrollController ==============> $i $scrollY ${1.sh}");
                              }
                            }
                          }
                        }
                        // }
                      } else if (notification is ScrollEndNotification) {
                        noScrollPos = false;
                        double scrollY = notification.metrics.pixels;
                        logger("scrollController 滚动停止 ${scrollY > childrenPos[2]}");
                      }
                      return true;
                    },
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          getUserInputView(),
                          Container(
                            key: _keys[1],
                            width: 1328.sp,
                            margin: EdgeInsets.only(top: 32.sp),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16.sp),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                getScanView(),
                              ],
                            ),
                          ),
                          // getSnView(),
                          getTagView(),
                          // getStageView(),
                          Container(
                            key: _keys[3],
                            margin: EdgeInsets.only(top: 32.sp),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16.sp),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 0, 16.sp),
                                  child: MyText(Lang.remark, Colors.black, 32.sp, FontWeight.w500),
                                ),
                                Divider(height: 1.sp, color: colorE1),
                                Padding(
                                  padding: EdgeInsets.all(24.sp),
                                  child: TextFormField(
                                    minLines: 10,
                                    maxLines: 20,
                                    controller: remarkController,
                                    maxLength: 100,
                                    textAlign: TextAlign.start,
                                    onChanged: (value) {
                                      currentRecord.note = value;
                                    },
                                    style: TextStyle(fontSize: 24.sp, color: color2B),
                                    decoration: InputDecoration(
                                      counterText: '',
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero,
                                      border: InputBorder.none,
                                      hintText: Lang.input_remark,
                                      hintStyle: TextStyle(fontSize: 24.sp, color: colorB8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 900.sp),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                      width: 248.sp,
                      height: 1.sh,
                      decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
                      margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
                      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
                      child: Column(
                        children: [
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            radius: 8.sp,
                            onPress: () {
                              pop();
                            },
                            bgColor: colorPurpleLavender,
                            child: MyText(Lang.save_and_exit,
                                colorBlueDeep.withOpacity(currentRecord.hasData() ? 1 : 0.5), 24.sp),
                          ),
                          const Spacer(),
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            fixedBottom: false,
                            radius: 8.sp,
                            onPress: () async {
                              hideKeyboard();
                              bool lost = await currentRecord.checkPhotoLost(removeLost: true);
                              if (lost) {
                                setState(() {});
                                await saveRecordCache();
                                Global.toast(Lang.record_image_lost);
                                return;
                              }
                              if (isNotEmpty(currentRecord.recordName)) {
                                // if (currentRecord.hasAllRequest()) {
                                if (isEmpty(currentRecord.age)) {
                                  currentRecord.age = "0";
                                }
                                if (currentRecord.age.endsWith(".")) {
                                  setState(() {
                                    currentRecord.age = currentRecord.age.substring(0, currentRecord.age.length - 1);
                                    ageController.text = currentRecord.age;
                                  });
                                }
                                saveCurrentRecordToList();
                                if (await checkRecordSizeMax()) {
                                  // Global.showBottomModal(
                                  //   context,
                                  //   getWaitingUploadDialog(),
                                  //   maxHeight: 0.8.sh,
                                  // );
                                  Global.toast(Lang.upload_records_first);
                                } else {
                                  Global.toast(Lang.saved_to_upload_list);
                                  showCurrentRecord(ScanInfo());
                                  scrollToPosition(0.5.sp);
                                }
                                await saveRecordCache();
                              } else {
                                Global.toast(Lang.request_data_lost);
                                if (isEmpty(currentRecord.recordName)) {
                                  scrollToPosition(90.sp);
                                  // } else if (isEmpty(currentRecord.gender)) {
                                  //   scrollToPosition(100.sp);
                                  // } else {
                                  //   scrollToPosition(670.sp);
                                }
                              }
                            },
                            bgColor: colorBlueDeep.withOpacity(currentRecord.hasData() ? 1 : 0.5),
                            child: MyText(Lang.save_and_add_next, Colors.white, 24.sp),
                          ),
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            fixedBottom: false,
                            radius: 8.sp,
                            onPress: () async {
                              if (currentRecord.hasAllRequest()) {
                                hideKeyboard();
                                await saveCurrentRecordToList();
                                eventBus.fire(EventUploadSingleRecord(currentRecord));
                                pop();
                              }
                            },
                            bgColor: colorBlueDeep.withOpacity(currentRecord.hasAllRequest() ? 1 : 0.5),
                            child: MyText(Lang.upload, Colors.white, 24.sp),
                          ),
                        ],
                      )),
                ),
                // Align(
                //   alignment: Alignment.bottomCenter,
                //   child: getBottomButtons(),
                // ),
                // isNotEmpty(showList)
                //     ? Positioned(
                //         right: 0,
                //         bottom: 100.sp,
                //         child: GestureDetector(
                //           onTap: () {
                //             (listKey.currentState as RefreshGridState).scrollToPosition(0);
                //           },
                //           child: Image.asset(
                //             "res/icons/icon_to_top.png",
                //             width: 80.sp,
                //           ),
                //         ),
                //       )
                //     : const SizedBox(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  showCurrentRecord(ScanInfo record) {
    logger("showCurrentRecord ${record.age}");
    setState(() {
      currentRecord = record;
      ageController.text = currentRecord.age;
      nameController.text = currentRecord.recordName;
      remarkController.text = currentRecord.note;
      phoneController.text = currentRecord.patientPhone;
      if (currentRecord.progressStage == "NONE" && isNotEmpty(stageMap)) {
        currentRecord.progressStage = stageMap.values.first["code"];
      }
      currentRecord.checkPhotoLost(removeLost: true);
    });
    delay(200, checkIsCanPostAndReBuildList);
  }

  bool exitTip = false;
  bool submitTip = false;

  showCloseUltraDialog(bool isExit) {
    //强提示用户关闭ultra
    String content = isExit ? Lang.shut_off_ultra_exit : Lang.shut_off_ultra_submit;
    String subContent = (isExit && exitTip || !isExit && submitTip)
        ? "Q: ${Lang.device_dead_question}\nA: ${Lang.device_dead_answer}"
        : "";
    Global.showAlertDialog(
      Lang.friendly_tip,
      content,
      subContent: subContent,
      contentStyle: TextStyle(
        fontSize: 14.sp,
        color: const Color(0xffEF8666),
        fontWeight: FontWeight.w500,
      ),
      subContentStyle: TextStyle(
        fontSize: 14.sp,
        height: 1.8,
        color: color7C,
        fontWeight: FontWeight.w400,
      ),
      okText: Lang.ok,
      isHideCancelBtn: true,
    );
    delay(500, () {
      if (isExit) {
        exitTip = true;
      } else {
        submitTip = true;
      }
    });
  }

  getIsHaveErrorInfoScanImg() {
    bool isHave = false;
    for (int i = 0; i < swiperScanList.length; i++) {
      int errorCode = getErrorCode(swiperScanList[i]["img"]);
      if (isUltraLightError(errorCode, swiperScanList[i]["img"])) {
        errorCode = 0;
      }
      if (errorCode != 0) {
        isHave = true;
        break;
      }
    }
    return isHave;
  }

  @override
  void onRoutePause(Route nextRoute) {
    super.onRoutePause(nextRoute);
    if (currentRecord.hasData()) {
      saveCurrentRecordToList();
    }
    hideKeyboard();
  }

  @override
  void onRouteResume(Route nextRoute) {
    logger("ScanInfoRecord onRouteResume");
    super.onRouteResume(nextRoute);
    hideKeyboard();
    AudioPlayerUtil.stopSound();
    setScreenVertiacl();

    delay(100, () {
      setScreenVertiacl();
      AudioPlayerUtil.stopSound();
      eventBus.fire(EventPopScan(fromRecord: true));
    });
    checkShowCloseUltra();
  }

  checkShowCloseUltra() async {
    // if (await isUltraIp()) {
    //   setState(() {
    //     showCloseUltraTip = true;
    //   });
    // }
  }
}
