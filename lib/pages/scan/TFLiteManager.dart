import 'dart:math';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli_ffi/bindings_ffi.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';

calculateRect(context, resultObj) {
  Struct2LastTFLite showObject = resultObj["lastObjects"][0];
  Float32List results = pointerFloat2FloatList(showObject.rects, showObject.rectsLength);
  int rectEleCount = showObject.rectEleCount;
  int importNetOriImgW = showObject.importNetOriImgW;
  int importNetOriImgH = showObject.importNetOriImgH;
  if (isNotEmpty(results) && results[0] != 0.0) {
    loggerHttp("rects eleCount: ${showObject.rectEleCount}, size: ${results.length}, data: $results");
  }

  List<Rect> allRects = [];
  List<Point> allPoints = [];

  double tempMaxUpperRectWidthHeight = 0;

  double tempMaxDownRectWidthHeight = 0;

  double tempMaxUlRectWidthHeight = 0;

  for (int i = 0; i < results.length / rectEleCount; i++) {
    //因为新模型对类牙齿形状不太鲁棒，因此正常牙齿口扫图 包含012 大框中两个+至少暴露5颗牙齿
    if (results.length <= 7 * rectEleCount) {
      break;
    }

    double left = results[i * rectEleCount];
    if (left.isNaN) {
      left = 0;
    }
    double top = results[i * rectEleCount + 1];
    if (top.isNaN) {
      top = 0;
    }
    double right = results[i * rectEleCount + 2];
    if (right.isNaN) {
      right = 0;
    }
    double bottom = results[i * rectEleCount + 3];
    if (bottom.isNaN) {
      bottom = 0;
    }
    // double sorce = results[i*6+4];if(sorce.isNaN){sorce = 0;}
    double tIndex = results[i * rectEleCount + 6];
    if (tIndex.isNaN) {
      tIndex = 0;
    }
    int index = tIndex.toInt();
    //只保留最大的 0/1/2
    if (tIndex <= 2) {
      double tnWidth = right - left;
      double tnHeight = bottom - top;
      if (tIndex == 0) {
        if (tnWidth * tnHeight >= tempMaxUpperRectWidthHeight) {
          tempMaxUpperRectWidthHeight = tnWidth * tnHeight;
        } else {
          //剔除较小的大框
          continue;
        }
      } else if (tIndex == 1) {
        if (tnWidth * tnHeight >= tempMaxDownRectWidthHeight) {
          tempMaxDownRectWidthHeight = tnWidth * tnHeight;
        } else {
          //剔除较小的大框
          continue;
        }
      } else if (tIndex == 2) {
        if (tnWidth * tnHeight >= tempMaxUlRectWidthHeight) {
          tempMaxUlRectWidthHeight = tnWidth * tnHeight;
        } else {
          //剔除较小的大框
          continue;
        }
      }
    }

    //去除所有大框
    if (index <= 4) {
      continue;
    }
    if (index >= 3) {
      index = index % 10;
    }

    //如果输入的原图宽高超出屏幕（页面会自动根据宽、高适应，对应也要适应网络算出来的坐标）
    Size screenSize = MediaQuery.of(context).size;

    double scale = importNetOriImgH / screenSize.height;
    left /= scale;
    top /= scale;
    right /= scale;
    bottom /= scale;

    double showWidth = importNetOriImgW / scale;
    double halfSpace = (screenSize.width - showWidth) / 2;
    left += halfSpace;
    right += halfSpace;

    if (left < 0 || top < 0 || right < 0 || bottom < 0) {
      // print("推算出来的Rect有<0的情况(均设为0)：left:$left  top:$top  right:$right  bottom:$bottom");
      left = 0;
      top = 0;
      right = 0;
      bottom = 0;
    }

    allRects.add(Rect.fromLTRB(left, top, right, bottom));
    allPoints.add(Point((left + right) ~/ 2, (top + bottom) ~/ 2));
  }

  if (allRects.isNotEmpty) {
    loggerHttp("rects result: $allRects");
    loggerHttp("points result: $allPoints");
  }
}
