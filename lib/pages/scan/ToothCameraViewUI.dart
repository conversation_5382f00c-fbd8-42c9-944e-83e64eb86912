import 'dart:async';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraStepAniCircle.dart';
import 'package:mooeli/pages/scan/ToothCameraTaskWidget.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/pages/video_player/HVideoPlayer.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/flushbar/flushbar.dart';
import 'package:mooeli/widget/my_widget.dart';

class ToothCameraViewUI extends BasePage {
  final ToothCameraStepEnum toothCameraStep;
  final ToothCameraTypeEnum toothCameraType;
  final dynamic tfliteResultObj;
  final dynamic funMap;
  final dynamic updateStateByType;
  final bool isLastStep;
  final DeviceOrientation currentOrientation;
  final int topStepCircleNum;
  final ToothCameraTypeEnum initDirectionType;

  const ToothCameraViewUI(
      {Key? key,
      required this.toothCameraStep,
      required this.initDirectionType,
      required this.toothCameraType,
      required this.updateStateByType,
      required this.isLastStep,
      required this.currentOrientation,
      required this.topStepCircleNum,
      this.tfliteResultObj,
      required this.funMap})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => ToothCameraViewUIState();
}

class ToothCameraViewUIState extends BasePageState<ToothCameraViewUI> with WidgetsBindingObserver {
  bool isShowAlert = false;
  String showAlertTitle = "";
  dynamic showAlertTitleIcon;
  bool showAlertTitleAlignLeft = false;
  String showAlertContent = "";
  String showAlertCancelBtnText = "";
  dynamic showAlertCancelBtnCB = "";
  String showAlertOkBtnText = "";
  dynamic showAlertOkBtnCB = "";

  //是否显示露出了5号牙齿
  bool isShow5Tootching = false;

  //允许继续检测5号牙齿
  bool isCanContinueCheck6Tooth = true;
  int firstCheck5ToothMillseconds = 0;

  //是否镜像操作
  bool isShowMirrorUI = false;

  //是否弹过一次模糊窗口提醒
  bool isShowAlertOnce = false;

  //是否弹过一次检查灯光的提醒
  bool isShowAlertLightOnce = false;

  //是否双击过一次了
  bool isDoublePressProcessing = false;
  ToothCameraStepEnum? doublePressProcessStepEnum;

  //一秒检测一次
  Timer? checkLightTimer;

  //自动焦点校准中
  bool isAutoModifyFoucs = false;

  int scanTool = 1;

  GlobalKey videoPlayerKey = GlobalKey();

  int adjustCameraAlertType = 1;

  @override
  initState() {
    setScreenHorizontal();
    scanTool = getScanTool();
    if (selectScanByIscan(scanTool)) {
      bool? isFlipY = Global.sharedPrefs.getBool(Global.localStorageEnum["USER_SCAN_TEXT_IS_MIRROR"]);
      if (isFlipY != null) {
        isShowMirrorUI = isFlipY;
      }
    }
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    addEventListener<EventPopScan>((event) {
      doDispose();
    });
    addEventListener<EventExitScan>((event) {
      doDispose();
    });
    addEventListener<EventCameraAdjust>((event) {
      if (adjustCameraAlertType != event.type) {
        logger("adjustCameraAlert  $adjustCameraAlertType -> ${event.type}");
        setState(() {
          adjustCameraAlertType = event.type;
        });
      }
    });
    delay(5000, () async {
      if (!await isUltraIp()) {
        showBatteryAlert = 100;
      }
    });
  }

  late Flushbar flushbar;

  showLowBatteryAlert(bool isRed) {
    flushbar = Flushbar(
        maxWidth: 1.sw - 400.sp,
        flushbarPosition: FlushbarPosition.TOP,
        animationDuration: const Duration(milliseconds: 300),
        duration: const Duration(seconds: 3),
        margin: EdgeInsets.fromLTRB(64.sp, 32.sp, 64.sp, 0),
        padding: EdgeInsets.all(32.sp),
        icon: Container(
          padding: EdgeInsets.only(left: 32.sp, right: 16.sp),
          child: SizedBox(
            width: 48.sp,
            height: 48.sp,
            child: Image.asset(
              isRed ? "res/scan/low_battery_red.png" : "res/scan/low_battery_yellow.png",
              width: 48.sp,
            ),
          ),
        ),
        borderRadius: BorderRadius.circular(16.sp),
        backgroundGradient: LinearGradient(
          colors: isRed
              ? const [
                  Color(0xFFFFBBA7),
                  Color(0xFFEA605B),
                ]
              : const [
                  Color(0xFFF3D684),
                  Color(0xFFEF8666),
                  Color(0xFFFFA286),
                ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        messageText: MyText(Lang.low_battery_alert, Colors.white, 28.sp),
        mainButton: IconButton(
          icon: Icon(
            Icons.close,
            size: 36.sp,
            color: Colors.white,
          ),
          onPressed: () {
            flushbar.dismiss();
          },
        ));
    flushbar.show(context);
  }

  doDispose() {
    if (!isDisposed) {
      isDisposed = true;
      logger("ToothCameraViewUI dispose");
      if (checkLightTimer != null) {
        checkLightTimer!.cancel();
        checkLightTimer = null;
      }
      if (videoPlayerKey.currentState != null) {
        (videoPlayerKey.currentState as CustomVideoPlayerState).stopPlay();
      }
      //设置强制竖屏
      setScreenVertiacl();
    }
  }

  @override
  dispose() {
    doDispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  //获取自定义的按钮
  getCustomButtom(titleText, {dynamic onPressed}) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        padding: EdgeInsets.only(bottom: 16.sp),
        child: HCustomButton(
            onPress: () {
              if (onPressed != null) {
                onPressed();
              } else {
                widget.funMap["intoNextStepEnum"]();
              }
            },
            fontSize: 28.sp,
            text: titleText,
            width: 328.sp,
            height: 56.sp,
            ms: 5000,
            bgColor: colorCyan),
      ),
    );
  }

  //获取进入双击进入下一步的按钮
  callBackDoubleTapEvent(doubleTapCallBack) {
    if (doublePressProcessStepEnum == null) {
      doublePressProcessStepEnum = widget.toothCameraStep;
      isDoublePressProcessing = false;
    } else {
      if (doublePressProcessStepEnum != widget.toothCameraStep) {
        isDoublePressProcessing = false;
      }
    }
    if (!isDoublePressProcessing) {
      isDoublePressProcessing = true;
      if (doubleTapCallBack != null) {
        doubleTapCallBack();
      }
    }
  }

  seDoublePressProcessStepEnum() {
    doublePressProcessStepEnum = widget.toothCameraStep;
    isDoublePressProcessing = false;
  }

  getDoubleTapButton(doubleTapCallBack, {String text = "", bool isHaveIcon = true}) {
    return Stack(
      children: [
        Align(
          alignment: Alignment.bottomCenter,
          child: GestureDetector(
            onTap: doubleTapCallBack,
              // onDoubleTap: () {
              //   callBackDoubleTapEvent(doubleTapCallBack);
              // },
              child: Padding(
                padding: EdgeInsets.only(bottom: 64.sp),
                child: Global.getBlurWidget(
                    Container(
                      padding: EdgeInsets.fromLTRB(48.sp, 28.sp, 48.sp, 28.sp),
                      child: Row(mainAxisSize: MainAxisSize.min, children: [
                        isHaveIcon
                            ? Image.asset("res/scan/doubletap.png", width: 37.5.sp, height: 37.5.sp)
                            : const SizedBox(),
                        SizedBox(width: 12.sp),
                        Text(text,
                            style: TextStyle(
                              fontSize: 25.sp,
                              color: Colors.white,
                            ))
                      ]),
                    ),
                    radius: 48.sp),
              )),
        ),
        GestureDetector(
          onDoubleTap: () {
            callBackDoubleTapEvent(doubleTapCallBack);
          },
        ),
      ],
    );
  }

  //显示弹窗
  showToothStepAlert(showAlertTitle, showAlertContent,
      {bool showAlertTitleAlignLeft = false,
      dynamic showAlertTitleIcon,
      String showAlertOkBtnText = "",
      dynamic showAlertOkBtnCB,
      String showAlertCancelBtnText = "",
      dynamic showAlertCancelBtnCB}) {
    showAlertOkBtnText = (showAlertOkBtnText != "" ? showAlertOkBtnText : Lang.confirm);
    showAlertCancelBtnText = (showAlertCancelBtnText != "" ? showAlertCancelBtnText : Lang.cancel);

    setState(() {
      isShowAlert = true;
      this.showAlertTitle = showAlertTitle;
      this.showAlertTitleIcon = showAlertTitleIcon;
      this.showAlertTitleAlignLeft = showAlertTitleAlignLeft;
      this.showAlertContent = showAlertContent;

      this.showAlertOkBtnText = showAlertOkBtnText;
      this.showAlertOkBtnCB = showAlertOkBtnCB;
      this.showAlertCancelBtnText = showAlertCancelBtnText;
      this.showAlertCancelBtnCB = showAlertCancelBtnCB;
    });
  }

  //重置检测模糊的弹窗
  resetBlurCanShowAlert() {
    setState(() {
      isShowAlertOnce = false;
    });
  }

  //在旋转屏幕等按钮之前
  // getWidgetUIByTypeFront() {
  //   Widget targetWidget;
  //   switch (widget.toothCameraStep) {
  //     case ToothCameraStepEnum.check5TootchClose:
  //     case ToothCameraStepEnum.check5TootchOpen:
  //     case ToothCameraStepEnum.check5TootchMask:
  //       dynamic check56ToothResult =
  //       Global.check56TootchLogic(widget.tfliteResultObj, widget.currentOrientation, isCheckBringDirLeft: true);
  //
  //       if (!isCanContinueCheck6Tooth) {
  //         check56ToothResult["isHave6Tootch"] = true;
  //       }
  //       bool isHave6Tooth = check56ToothResult["isHave6Tootch"];
  //       if (isHave6Tooth) {
  //         if (firstCheck5ToothMillseconds == 0) {
  //           firstCheck5ToothMillseconds = DateTime
  //               .now()
  //               .millisecondsSinceEpoch;
  //         } else {
  //           if (DateTime
  //               .now()
  //               .millisecondsSinceEpoch - firstCheck5ToothMillseconds >= 2000) {
  //             isCanContinueCheck6Tooth = false;
  //           }
  //         }
  //       }
  //       String showImg = ToothCameraStepEnum.check5TootchClose == widget.toothCameraStep
  //           ? "res/scan/leftest_close.png"
  //           : ToothCameraStepEnum.check5TootchOpen == widget.toothCameraStep
  //           ? "res/scan/leftest_open.png"
  //           : "res/scan/leftest_mask.png";
  //       targetWidget = Stack(children: [
  //         widget.tfliteResultObj != null && widget.tfliteResultObj.containsKey("lastObjects")
  //             ? ToothCameraRect(
  //             currentOrientation: widget.currentOrientation,
  //             isShowMirrorUI: widget.toothCameraStep == ToothCameraStepEnum.checkPoint ||
  //                 widget.toothCameraStep == ToothCameraStepEnum.alertMaskUp ||
  //                 widget.toothCameraStep == ToothCameraStepEnum.alertMaskDown
  //                 ? true
  //                 : isShowMirrorUI,
  //             funMap: widget.funMap,
  //             toothCameraStep: widget.toothCameraStep,
  //             checkPointRectSize: Size(96.sp, 96.sp),
  //             resultObj: widget.tfliteResultObj,
  //             isShowMsBoxInfo: false,
  //             showObjIndex: 0,
  //             fitType: ToothCameraRectFitEnum.fitByScreenHeight,
  //             alignType: ToothCameraRectImgAlignEnum.alignByTopCenter)
  //             : const SizedBox(),
  //         Align(
  //           alignment: Alignment.centerLeft,
  //           child: Global.getBlurWidget(
  //             SafeArea(
  //                 child: SizedBox(
  //                     height: double.infinity,
  //                     width: 153.sp,
  //                     child: Padding(
  //                       padding: EdgeInsets.fromLTRB(10.sp, 30.sp, 10.sp, 10.sp),
  //                       child: Column(
  //                         mainAxisAlignment: MainAxisAlignment.center,
  //                         crossAxisAlignment: CrossAxisAlignment.center,
  //                         children: [
  //                           Transform(
  //                             alignment: Alignment.center,
  //                             transform: isShowMirrorUI
  //                                 ? Matrix4.diagonal3Values(1.0, 1.0, 1.0)
  //                                 : Matrix4.diagonal3Values(-1.0, 1.0, 1.0),
  //                             child: Image.asset(showImg),
  //                           ),
  //                         ],
  //                       ),
  //                     ))),
  //           ),
  //         ),
  //         getDoubleTapButton(
  //             !isCanContinueCheck6Tooth
  //                 ? () {
  //               widget.funMap["intoNextStepEnum"]();
  //             }
  //                 : null,
  //             text: !isCanContinueCheck6Tooth
  //                 ? Lang.toothUIViewDoubleTapScan
  //                 : Lang.toothUIViewFollowArrowMove,
  //             isHaveIcon: !isCanContinueCheck6Tooth),
  //         ToothCameraLRArrow(isShowMirrorUI: isShowMirrorUI),
  //       ]);
  //       break;
  //     case ToothCameraStepEnum.noCheck5TootchUp:
  //     case ToothCameraStepEnum.noCheck5TootchDown:
  //       String showImg = ToothCameraStepEnum.noCheck5TootchUp == widget.toothCameraStep
  //           ? "res/scan/checkBlur2Up.png"
  //           : "res/scan/checkBlur2Down.png";
  //       targetWidget = Stack(children: [
  //         Align(
  //           alignment: Alignment.centerLeft,
  //           child: Global.getBlurWidget(
  //             SafeArea(
  //                 child: SizedBox(
  //                     height: double.infinity,
  //                     width: 153.sp,
  //                     child: Padding(
  //                       padding: EdgeInsets.fromLTRB(10.sp, 30.sp, 10.sp, 10.sp),
  //                       child: Column(
  //                         mainAxisAlignment: MainAxisAlignment.center,
  //                         crossAxisAlignment: CrossAxisAlignment.center,
  //                         children: [
  //                           Text(Lang.toothUIViewUpDownNormalScan,
  //                               textAlign: TextAlign.center,
  //                               style: TextStyle(
  //                                 fontSize: 22.sp,
  //                                 color: Colors.white,
  //                                 fontWeight: FontWeight.w500,
  //                               )),
  //                           SizedBox(height: 17.sp),
  //                           Container(
  //                             decoration: BoxDecoration(
  //                               color: const Color(0xffc9c9c9),
  //                               borderRadius: BorderRadius.circular(8.sp),
  //                             ),
  //                             child: Image.asset(showImg),
  //                           ),
  //                           SizedBox(height: 17.sp),
  //                           Text(Lang.toothUIViewUpDownNormalOpenMouth,
  //                               textAlign: TextAlign.center,
  //                               style: TextStyle(
  //                                 fontSize: 20.sp,
  //                                 color: Colors.white,
  //                                 fontWeight: FontWeight.w500,
  //                               )),
  //                         ],
  //                       ),
  //                     ))),
  //           ),
  //         ),
  //         getDoubleTapButton(() {
  //           widget.funMap["intoNextStepEnum"]();
  //         }, text: Lang.toothUIViewDoubleTapScan),
  //       ]);
  //       break;
  //
  //     default:
  //       targetWidget = const SizedBox();
  //       break;
  //   }
  //   return targetWidget;
  // }

  //设置是否可以->强制不再继续检测5号牙齿并要显示出双击下一步按钮

  void setForceCheck5Tooth(bool isCan) {
    setState(() {
      firstCheck5ToothMillseconds = 0;
      isCanContinueCheck6Tooth = isCan;
    });
  }

  //最后的覆盖
  getWidgetUIByTypeLast() {
    Widget targetWidget;
    switch (widget.toothCameraStep) {
      default:
        targetWidget = const SizedBox();
        break;
    }
    return targetWidget;
  }

  // checkIsBlur({dynamic callBack}) {
  //   CameraImage newest = widget.funMap["getStreamImage"]();
  //   TFLiteUtils.callTfliteLogic(newest, 3).then((obj) {
  //     bool isBlur = false;
  //     if (Global.isGlobalUseNewModal) {
  //       isBlur = Global.checkIsBlurByNewModal(struct2lastTFLite: obj["lastObjects"][0]);
  //     } else {
  //       isBlur = obj["lastObjects"][0].blurInfo.isBlur == 1;
  //     }
  //     logger("checkIsBlur: $isBlur");
  //     if (callBack != null) {
  //       callBack(isBlur);
  //     }
  //     if (!isBlur) {
  //       if (widget.toothCameraStep == ToothCameraStepEnum.modifyBlur) {
  //         widget.funMap["intoNextStepEnum"](offsetNum: 1);
  //       } else {
  //         widget.funMap["intoNextStepEnum"](offsetNum: 2);
  //       }
  //     } else {
  //       if (widget.toothCameraStep != ToothCameraStepEnum.modifyBlur) {
  //         widget.funMap["intoNextStepEnum"](offsetNum: 1);
  //       }
  //     }
  //   });
  // }

  setIsAutoFoucsing(bool isAutoFoucsing) {
    setState(() {
      isAutoModifyFoucs = isAutoFoucsing;
    });
  }

  checkIsLight() {
    // CameraImage newest = widget.funMap["getStreamImage"]();
    // dynamic objData = HImageUtils.cameraImg2Uint8ListBringWH(newest);
    // ffiCheckImgHaveLightByData(objData).then((isOk) {
    //   if (isOk) {
    //     if (widget.toothCameraStep == ToothCameraStepEnum.modifyLight) {
    //       widget.funMap["intoNextStepEnum"](offsetNum: 1);
    //     } else {
    //       widget.funMap["intoNextStepEnum"](offsetNum: 2);
    //     }
    //     setState(() {
    //       isShowAlert = false;
    //     });
    //     if (checkLightTimer != null) {
    //       checkLightTimer!.cancel();
    //       checkLightTimer = null;
    //     }
    //   } else {
    //     if (checkLightTimer == null) {
    //       widget.funMap["intoNextStepEnum"](offsetNum: 1);
    //       checkLightTimer = Timer.periodic(const Duration(seconds: 1), (_) {
    //         checkIsLight();
    //       });
    //     }
    //   }
    // });
  }

  mirrorUI() {
    setState(() {
      isShowMirrorUI = !isShowMirrorUI;
      Global.sharedPrefs.setBool(Global.localStorageEnum["USER_SCAN_TEXT_IS_MIRROR"], isShowMirrorUI);
    });
    sendScanEvent("mirror", getRecordEventStepEnumTitle(widget.toothCameraType),
        {"value3": isShowMirrorUI ? "myself" : "others"});
  }

  //获取当前处于哪一组的圆形提示
  getFlashCirclesByStep(int stepNum) {
    int index = stepNum - 1;
    List<Widget> circleWidgets = [];
    int allCircleCount = widget.funMap["getStepCount"]();
    for (int i = 0; i < allCircleCount; i++) {
      circleWidgets.add(Row(
        children: [
          index == i
              ? ToothCameraStepAniCircle(topSpace: 76.sp, leftSpace: 8.sp)
              : Container(
                  margin: EdgeInsets.only(top: 76.sp, left: 8.sp),
                  width: 30.sp,
                  height: 30.sp,
                  decoration: BoxDecoration(
                    color: i <= index ? Colors.white : Colors.white.withOpacity(0.22),
                    borderRadius: BorderRadius.circular(20.sp),
                  ),
                ),
          i >= allCircleCount - 1
              ? const SizedBox()
              : Container(
                  margin: EdgeInsets.only(top: 76.sp, left: 8.sp),
                  width: 72.sp,
                  height: 5.sp,
                  color: i <= index - 1 ? Colors.white : Colors.white.withOpacity(0.22),
                ),
        ],
      ));
    }

    return Align(
      alignment: Alignment.topCenter,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: circleWidgets,
      ),
    );
  }

  Widget getMirrorButton() {
    return selectScanByUltra(scanTool)
        ? const SizedBox()
        : Align(
            alignment: Alignment.bottomRight,
            child: GestureDetector(
                onTap: mirrorUI,
                child: Padding(
                    padding: EdgeInsets.only(top: 24.sp, right: 24.sp, bottom: 24.sp),
                    child: Image.asset("res/icons/scan_mirror.png", width: 42.sp, height: 42.sp))),
          );
  }

  Widget getCloseButton() {
    return Align(
        alignment: Alignment.topRight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // batteryPercent > 0
            //     ? Container(
            //         margin: EdgeInsets.only(top: 64.sp, right: 48.sp),
            //         child: BatteryView(batteryPercent),
            //       )
            //     : const SizedBox(),
            GestureDetector(
              onTap: () {
                sendScanEvent("close", getRecordEventStepEnumTitle(widget.toothCameraType));
                AudioPlayerUtil.stopSound();
                eventBus.fire(EventPopScan());
                pop();
              },
              child: Padding(
                  padding: EdgeInsets.only(top: 64.sp, right: 48.sp),
                  child: Image.asset("res/icons/modal_close.png", width: 64.sp, height: 64.sp)),
            ),
          ],
        ));
  }

  Widget getQAButton() {
    return selectScanByUltra(scanTool)
        ? Align(
            alignment: Alignment.bottomRight,
            child: GestureDetector(
                onTap: () {
                  Global.showCustomDialog(getUltraQADialog());
                  sendScanEvent("encounter_problem", getRecordEventStepEnumTitle(widget.toothCameraType));
                },
                child: Padding(
                    padding: EdgeInsets.only(top: 24.sp, right: 48.sp, bottom: 64.sp),
                    child: Image.asset("res/icons/scan_question.png", width: 64.sp, height: 64.sp))))
        : const SizedBox();
  }

  getUltraQADialog() {
    return Center(
      child: Container(
        width: 656.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                width: 656.sp,
                height: 96.sp,
                padding: EdgeInsets.fromLTRB(32.sp, 0, 32.sp, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      Lang.have_question,
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w500,
                        color: color2B,
                      ),
                    ),
                    Click(
                      onTap: () {
                        BotToast.cleanAll();
                      },
                      child: Image.asset(
                        "res/imgs/icon_close.png",
                        width: 24.sp,
                      ),
                    ),
                  ],
                )),
            Divider(color: colorE1),
            SizedBox(height: 32.sp),
            // MyText(Lang.scan_ultra_tip_content31, color2B, 24.sp),
            // SizedBox(height: 8.sp),
            MyText(Lang.scan_ultra_tip_content32, color2B, 24.sp),
            SizedBox(height: 16.sp),
            Image.asset(
              "res/scan/mooeli_service.png",
              width: 240.sp,
              height: 240.sp,
              fit: BoxFit.contain,
            ),
            SizedBox(height: 32.sp),
          ],
        ),
      ),
    );
  }

  Widget getTextItem(String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 12.sp),
          child: Image.asset("res/icons/item_round.png", width: 6.sp),
        ),
        SizedBox(width: 5.65.sp),
        Text(text,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w400,
              color: color7C,
            )),
      ],
    );
  }

  Widget getAlertDialog({
    required String icon,
    required String title,
    required String content,
    required String cancelText,
    required String okText,
    dynamic cancelCallBack,
    dynamic okCallBack,
  }) {
    Widget iconWidget = Image.asset(icon, width: 20.sp, height: 20.sp);
    return Container(
      alignment: Alignment.center,
      color: Colors.black.withOpacity(0.6),
      child: SafeArea(
        child: Container(
          width: 360.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.sp),
          ),
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            Padding(
              padding: EdgeInsets.fromLTRB(34.sp, 24.sp, 34.sp, 16.sp),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      iconWidget,
                      SizedBox(width: 6.sp),
                      Text(title,
                          textAlign: TextAlign.left,
                          style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w500, color: color2B)),
                    ],
                  ),
                  SizedBox(height: content == "" ? 0 : 8.sp),
                  content == ""
                      ? const SizedBox()
                      : Text(content, textAlign: TextAlign.left, style: TextStyle(fontSize: 20.sp, color: color2B)),
                ],
              ),
            ),
            SizedBox(height: content == "" ? 0 : 10.sp),
            Divider(
              height: 1.sp,
              color: const Color(0xffe5e5e5),
            ),
            Container(
              height: 56.sp,
              width: double.infinity,
              padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 0),
              child: Row(
                mainAxisAlignment: cancelText == "" ? MainAxisAlignment.center : MainAxisAlignment.spaceAround,
                children: [
                  cancelText == ""
                      ? const SizedBox()
                      : GestureDetector(
                          onTap: () {
                            setState(() {
                              isShowAlert = false;
                            });
                            if (cancelCallBack != null) {
                              cancelCallBack();
                            }
                          },
                          child: Text(
                            cancelText,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w500,
                              color: color2B,
                            ),
                          ),
                        ),
                  cancelText == ""
                      ? const SizedBox()
                      : Container(
                          width: 0.5.sp,
                          height: 56.sp,
                          color: const Color(0xffe5e5e5),
                        ),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        isShowAlert = false;
                      });
                      if (okCallBack != null) {
                        okCallBack();
                      }
                    },
                    child: Text(
                      okText,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        color: color2B,
                      ),
                    ),
                  ),
                ],
              ),
            )
          ]),
        ),
      ),
    );
  }

  Widget getAlertMaskUI({bool mask = true}) {
    // 戴上or取下
    bool isMirror = isShowMirrorUI && mask && widget.initDirectionType == ToothCameraTypeEnum.all;
    return Transform(
      alignment: Alignment.center,
      transform: isMirror ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0) : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
      child: Stack(
        children: [
          Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(48.sp),
              child: SizedBox(
                width: 1200.sp,
                height: 675.sp,
                child: HVideoPlayer(
                    targetFilePathName: mask ? "res/videos/mask_up.mp4" : "res/videos/mask_off.mp4",
                    isLoop: true,
                    volume: 0,
                    fitType: HVideoFitTYpe.cover),
              ),
            ),
          ),
          Center(
            child: Container(
                width: 1200.sp,
                height: 675.sp,
                color: Colors.black.withOpacity(0.0),
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.topCenter,
                      child: Container(
                        margin: EdgeInsets.fromLTRB(100.sp, 30.sp, 100.sp, 0),
                        padding: EdgeInsets.all(10.sp),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(8.sp),
                        ),
                        child: Text(mask ? Lang.pickup_mask_ware_mouth_prop2 : Lang.pickoff_mask_ware_mouth_prop2,
                            style: TextStyle(
                              fontSize: 24.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            )),
                      ),
                    ),
                    getCustomButtom(Lang.continue_step, onPressed: () {
                      if (selectScanByUltra(scanTool) || mask && widget.initDirectionType == ToothCameraTypeEnum.all) {
                        widget.funMap["intoNextStepEnum"]();
                      } else {
                        checkIsLight();
                      }
                    }),
                  ],
                )),
          ),
          getCloseButton(),
          mask && widget.initDirectionType == ToothCameraTypeEnum.all ? getMirrorButton() : const SizedBox(),
          getQAButton(),
        ],
      ),
    );
  }

  Widget getScanHelpVideoUI() {
    bool isMirror = isShowMirrorUI;
    return Transform(
      alignment: Alignment.center,
      transform: isMirror ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0) : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
      child: Stack(children: [
        Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(48.sp),
            child: SizedBox(
              width: 1200.sp,
              height: 675.sp,
              child: HVideoPlayer(
                key: videoPlayerKey,
                targetFilePathName: getScanHelpVideo(widget.toothCameraType, selectScanByUltra(scanTool)),
                isLoop: true,
                volume: 0,
                fitType: HVideoFitTYpe.cover,
              ),
            ),
          ),
        ),
        getDoubleTapButton(
          () {
            widget.funMap["intoNextStepEnum"]();
          },
          text: Lang.double_click_scan,
        ),
        getCloseButton(),
        getMirrorButton(),
      ]),
    );
  }

  Widget getUltraCheckToothUI() {
    return Stack(children: [
      ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: 1.sh,
          minWidth: 1.sw,
        ),
        child: SizedBox(width: 1.sw, height: 1.sh),
      ),
      Align(
        alignment: Alignment.center,
        child: Global.getBlurWidget(
          SizedBox(
            width: 488.sp,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 24.sp),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.sp),
                    child: Image.asset("res/scan/ultra_check_tooth.gif", width: 440.sp),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 20.sp),
                  child: Text(
                    Lang.put_ultra_power_up,
                    style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.w400, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          radius: 8.sp,
        ),
      ),
      getCloseButton(),
      getQAButton(),
    ]);
  }

  Widget getUltraDirectionUI() {
    return Stack(children: [
      ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: 1.sh,
          minWidth: 1.sw,
        ),
        child: SizedBox(width: 1.sw, height: 1.sh),
      ),
      getDoubleTapButton(
        () {
          widget.funMap["intoNextStepEnum"]();
        },
        text: Lang.double_click_continue,
      ),
      getCloseButton(),
      getQAButton(),
    ]);
  }

  Widget getIntoScanUI() {
    bool isMirror = isShowMirrorUI;
    return Transform(
      alignment: Alignment.center,
      transform: isMirror ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0) : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
      child: Stack(children: [
        ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: 1.sh,
            minWidth: 1.sw,
          ),
          child: SizedBox(width: 1.sw, height: 1.sh),
        ),
        getFlashCirclesByStep(widget.topStepCircleNum),
        ToothCameraTaskWidget(
          showToothStepAlert: showToothStepAlert,
          mirrorUI: mirrorUI,
          currentOrientation: widget.currentOrientation,
          isShowMirrorUI: isShowMirrorUI,
          isLastStep: widget.isLastStep,
          getDoubleTapButton: getDoubleTapButton,
          toothCameraStep: widget.toothCameraStep,
          toothCameraType: widget.toothCameraType,
          directionEnum: widget.toothCameraType == ToothCameraTypeEnum.occlusion ||
                  widget.toothCameraType == ToothCameraTypeEnum.openMaskOn ||
                  widget.toothCameraType == ToothCameraTypeEnum.openMaskOff
              ? ScanTaskDirEnum.lr
              : widget.toothCameraType == ToothCameraTypeEnum.maxillary
                  ? ScanTaskDirEnum.toUp
                  : ScanTaskDirEnum.toDown,
          funMap: widget.funMap,
          updateStateByType: widget.updateStateByType,
        ),
        Align(
          alignment: Alignment.topLeft,
          child: GestureDetector(
            onTap: () {
              if (skilledMode) {
                widget.funMap["intoNextStepEnum"](offsetNum: 0, rescan: true);
              } else {
                widget.funMap["intoNextStepEnum"](offsetNum: -1);
              }
              sendScanEvent("back", getRecordEventStepEnumTitle(widget.toothCameraType));
            },
            child: Padding(
                padding: EdgeInsets.only(top: 64.sp, left: 48.sp),
                child: Image.asset("res/scan/circle_back.png", width: 64.sp, height: 64.sp)),
          ),
        ),
        getCloseButton(),
        getMirrorButton(),
        getQAButton(),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.toothCameraStep) {
      case ToothCameraStepEnum.ultraCheckTooth:
        return getUltraCheckToothUI();
      case ToothCameraStepEnum.ultraCheckDirection:
        return getUltraDirectionUI();
      // case ToothCameraStepEnum.checkPoint:
      //   return getCheckPointUI();
      // case ToothCameraStepEnum.modifyLight:
      //   return getModifyLightUI();
      case ToothCameraStepEnum.alertMaskUp:
        return getAlertMaskUI(mask: true);
      case ToothCameraStepEnum.alertMaskDown:
        return getAlertMaskUI(mask: false);
      case ToothCameraStepEnum.scanHelpVideo:
        return getScanHelpVideoUI();
      // case ToothCameraStepEnum.modifyBlur:
      //   return getModifyBlurUI();
      // case ToothCameraStepEnum.check5TootchClose:
      //   return getCheck5ToothUI();
      // case ToothCameraStepEnum.check5TootchOpen:
      //   return getCheck5ToothUI();
      // case ToothCameraStepEnum.check5TootchMask:
      //   return getCheck5ToothUI();
      // case ToothCameraStepEnum.noCheck5TootchUp:
      //   return getCheck5ToothUI();
      // case ToothCameraStepEnum.noCheck5TootchDown:
      //   return getCheck5ToothUI();
      case ToothCameraStepEnum.intoScan:
        return getIntoScanUI();
      default:
        return const SizedBox();
    }
  }

  getDialogTipContent(String content, double rowWidth, {String? boldText, bool onlyText = false}) {
    return Padding(
      padding: EdgeInsets.only(top: 6.sp),
      child: onlyText
          ? Container(
              constraints: BoxConstraints(maxWidth: rowWidth),
              child: Text(
                content,
                style: TextStyle(
                  fontSize: 14.sp,
                  height: 1.5,
                  color: color7C,
                ),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 4.sp,
                  height: 4.sp,
                  margin: EdgeInsets.all(8.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2.sp),
                    color: const Color(0xff000000),
                  ),
                ),
                Container(
                  constraints: BoxConstraints(maxWidth: rowWidth - 20.sp),
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: boldText ?? "",
                          style: TextStyle(
                            fontSize: 14.sp,
                            height: 1.5,
                            fontWeight: FontWeight.w400,
                            color: color2B,
                          ),
                        ),
                        TextSpan(
                          text: content,
                          style: TextStyle(
                            fontSize: 14.sp,
                            height: 1.5,
                            fontWeight: FontWeight.w400,
                            color: color7C,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  getQuestionText(String content, bool isQuestion) {
    TextStyle textStyle = isQuestion
        ? TextStyle(
            fontSize: 14.sp,
            height: 1.5,
            fontWeight: FontWeight.w500,
            color: color2B,
          )
        : TextStyle(
            fontSize: 14.sp,
            height: 1.5,
            fontWeight: FontWeight.w400,
            color: color7C,
          );
    return Padding(
      padding: EdgeInsets.only(top: isQuestion ? 16.sp : 4.sp),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(isQuestion ? "Q: " : "A: ", style: textStyle),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: (1.sw - 156.sp) / 3 - 19.sp),
            child: Text(content, style: textStyle, maxLines: 10),
          ),
        ],
      ),
    );
  }
}
