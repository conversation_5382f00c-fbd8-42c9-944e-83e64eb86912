import 'dart:async';

import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:wifi_iot/wifi_iot.dart';


class CheckUltraConnectTask {
  dynamic resultCallback;
  dynamic batteryCallback;
  Timer? checkIsConnectedCameraTimer;
  bool isRunning = false;
  int counter = 0;

  CheckUltraConnectTask(this.resultCallback, this.batteryCallback);

  start() async {
    // if (checkIsConnectedCameraTimer != null) {
    //   checkIsConnectedCameraTimer!.cancel();
    //   checkIsConnectedCameraTimer = null;
    // }
    // isRunning = true;
    // checkIsConnectedCameraTimer = Timer.periodic(const Duration(milliseconds: 1000), (_) async {
    //   counter++;
    //   bool checkIp = await isUltraIp();
    //   bool checkView = false;
    //   if (checkIp) {
    //     bool hasImage = true;
    //     if (ultraType == 2) {
    //       hasImage = hasRecentImage();
    //     }
    //     checkView = await checkUltraConnected() && hasImage;
    //   }
    //   bool connected = checkView && checkIp;
    //   resultCallback(connected);
    //   if (scanShowIp) {
    //     String? ip = await WiFiForIoTPlugin.getIP();
    //     eventBus.fire(EventIp("IP: $ip"));
    //   }
    //   if (connected) {
    //     int battery = await getUltraBattery();
    //     batteryCallback(battery);
    //   }
    // });
  }

  stop() {
    // logger("CheckUltraConnectTask stop");
    // if (checkIsConnectedCameraTimer != null) {
    //   checkIsConnectedCameraTimer!.cancel();
    //   checkIsConnectedCameraTimer = null;
    // }
    // isRunning = false;
  }
}
