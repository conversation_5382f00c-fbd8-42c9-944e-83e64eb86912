import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/scan_record_list_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:sprintf/sprintf.dart';

class ScanActivityItem extends BasePage {
  ScanActivity activity;
  bool selectMode = false;
  bool inSelect = false;

  ScanActivityItem(this.activity, {Key? key, this.selectMode = false, this.inSelect = false}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanActivityItemState();
  }
}

class ScanActivityItemState extends BasePageState<ScanActivityItem> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isFront = true;
  int uploadCount = 0;

  @override
  void initState() {
    super.initState();
    addEventListener<EventUploadRecordResult>((event) {
      getUploadCount();
    });
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _animation = Tween(begin: 0.0, end: 1.0).animate(_controller)
      ..addListener(() {
        setState(() {});
      });
    getUploadCount();
  }

  void _flip() {
    hideKeyboard();
    if (_isFront) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
    _isFront = !_isFront;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Transform(
      alignment: FractionalOffset.center,
      transform: Matrix4.identity()
        ..setEntry(3, 2, 0.001) // Perspective
        ..rotateY(3.14 * _animation.value),
      child: _animation.value <= 0.5
          ? _getActivityItem(widget.activity)
          : Transform(
              alignment: Alignment.center,
              transform: Matrix4.diagonal3Values(-1.0, 1.0, 1.0),
              child: _getBackItem(widget.activity),
            ),
    );
  }

  getUploadCount() async {
    List<ScanInfo> list = await getUploadRecordList(widget.activity.campaignId, widget.activity.singleCampaign);
    setState(() {
      uploadCount = list.length;
    });
  }

  _getActivityItem(ScanActivity activity) {
    return GestureDetector(
      onTap: widget.selectMode ? null : () {
        hideKeyboard();
        push(ScanRecordListPage(activity));
      },
      child: Container(
        width: 427.sp,
        height: 268.sp,
        padding: EdgeInsets.fromLTRB(24.sp, 0.sp, 0.sp, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
          border: widget.selectMode
              ? Border.all(
              width: 4.sp,
              color: widget.inSelect
                  ? const Color(0xFF8473D1)
                  : const Color(0xFFD8D3F0))
              : null,
          boxShadow: [
            BoxShadow(
              color: const Color(0x0A000000),
              offset: Offset(0, 4.sp),
              blurRadius: 16.sp,
            ),
            BoxShadow(
              color: const Color(0x05000000),
              offset: Offset(0, 8.sp),
              blurRadius: 16.sp,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(top: 18.sp),
                    child: Text(
                      activity.campaignName,
                      style: TextStyle(
                        fontSize: 32.sp,
                        color: color2B,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  ),
                ),
                // Click(
                //     child: Container(
                //       padding: EdgeInsets.symmetric(horizontal: 20.sp),
                //       child: Image.asset("res/icons/icon_activity_star.png", width: 32.sp),
                //     ),
                //     onTap: () {}),
                widget.selectMode ? _selectedCircle() : GestureDetector(
                  onTap: _flip,
                  child: Padding(
                    padding: EdgeInsets.all(24.sp),
                    child: Image.asset("res/icons/icon_activity_more.png", width: 32.sp),
                  ),
                ),
              ],
            ),
            // MyText(activity.creatorName, color7C, 20.sp),
            const Spacer(),
            MyText(sprintf(Lang.activity_upload_status, [activity.counter, uploadCount]), color7C, 20.sp),
            SizedBox(height: 12.sp),
            Padding(
              padding: EdgeInsets.only(right: 10.sp),
              child: LinearProgressIndicator(
                minHeight: 5.sp,
                value: (uploadCount + activity.counter) > 0
                    ? (activity.counter * 1.0 / (uploadCount + activity.counter))
                    : 0.0, // 60% 的进度
                backgroundColor: colorD9,
                color: colorGreen,
              ),
            ),
            SizedBox(height: 12.sp),
            MyText(
                "${activity.creatorName} ${Global.getDateByTimestamp(milliseconds: activity.createTime)}${Lang.create_case}",
                colorA4,
                20.sp),
            SizedBox(height: 20.sp),
          ],
        ),
      ),
    );
  }

  _getBackItem(ScanActivity activity) {
    return Container(
      width: 427.sp,
      height: 268.sp,
      padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  activity.campaignName,
                  style: TextStyle(
                    fontSize: 32.sp,
                    color: color2B,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                ),
              ),
              GestureDetector(
                onTap: () {
                  hideKeyboard();
                  eventBus.fire(EventDeleteActivity(activity));
                },
                child: Builder(
                  builder: (context) {
                    return Image.asset("res/icons/icon_delete.png", width: 32.sp);
                  },
                ),
              ),
              SizedBox(width: 20.sp),
              GestureDetector(
                onTap: () {
                  hideKeyboard();
                  eventBus.fire(EventEditActivity(activity));
                },
                child: Builder(
                  builder: (context) {
                    return Image.asset("res/icons/icon_edit.png", width: 32.sp);
                  },
                ),
              ),
              SizedBox(width: 20.sp),
              GestureDetector(
                onTap: _flip,
                child: Builder(
                  builder: (context) {
                    return Image.asset("res/icons/icon_reset.png", width: 32.sp);
                  },
                ),
              ),
            ],
          ),
          // MyText(activity.creatorName, color7C, 20.sp),
        ],
      ),
    );
  }

  Widget _selectedCircle() {
    return Padding(
      padding: EdgeInsets.all(24.sp),
      child: SizedBox(
        width: 32.sp,
        height: 32.sp,
        child: ClipOval(
          child: Image.asset(widget.inSelect ? 'res/icons/icon_selected.png' : 'res/icons/icon_unselected.png'),
        ),

      ),
    );
  }
}
