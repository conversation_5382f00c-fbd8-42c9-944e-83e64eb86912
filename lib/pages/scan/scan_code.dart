import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class ScanCode extends BasePage {
  const ScanCode({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ScanCodeState();
}

class _ScanCodeState extends BasePageState<ScanCode> with SingleTickerProviderStateMixin {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  bool hasInit = false;
  String caseId = "";

  Barcode? result;
  QRViewController? controller;
  bool hasCode = false;

  late AnimationController _animationController;
  double _animMarginTop = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 1000))
      ..repeat(reverse: false);
    _animationController!.addListener(() {
      setState(() {
        _animMarginTop = _animationController!.value * 133.sp;
      });
    });
  }

  @override
  void dispose() {
    controller?.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  void onRoutePause(Route nextRoute) {
    super.onRoutePause(nextRoute);
    controller?.pauseCamera();
  }

  @override
  void onRouteResume(Route nextRoute) {
    super.onRouteResume(nextRoute);
    controller?.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    if (!hasInit) {
      dynamic routerParams = Global.getRouterParams(context);
      caseId = routerParams["caseId"] ?? "";
      hasInit = true;
    }
    return Scaffold(
      body: Stack(
        children: [
          SizedBox(
            width: 1.sw,
            height: 1.sh,
            child: QRView(
              key: qrKey,
              overlay: QrScannerOverlayShape(
                borderColor: colorBrand,
                borderWidth: 0.1.sp,
                overlayColor: const Color.fromRGBO(0, 0, 0, 80),
                borderRadius: 8.sp,
                borderLength: 30.sp,
                cutOutWidth: 335.sp,
                cutOutHeight: 133.sp,
                // cutOutBottomOffset: 0.15.sh,
              ),
              onQRViewCreated: _onQRViewCreated,
            ),
          ),
          Align(
            alignment: Alignment.center,
            child: Image.asset(
              "res/imgs/scan_code_border.png",
              width: 340.sp,
            ),
          ),
          Align(
            alignment: Alignment.center,
            child: Container(
              width: 335.sp,
              height: 133.sp,
              alignment: Alignment.topCenter,
              padding: EdgeInsets.fromLTRB(10.sp, _animMarginTop, 0, 0),
              child: Image.asset("res/imgs/scan_code_line.png", width: 315.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 4.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: pop,
                  icon: Icon(
                    Platform.isAndroid ? Icons.arrow_back : Icons.arrow_back_ios_new,
                    color: Colors.white,
                  ),
                  iconSize: 24.sp,
                ),
                MyText(Lang.scan_bind_code, Colors.white, 18.sp, FontWeight.w500),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(
                    Icons.space_bar,
                    color: Colors.transparent,
                  ),
                  iconSize: 24.sp,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) async {
      if (!hasCode) {
        hasCode = true;
        String code = scanData.code ?? "";
        List<ScanInfo> list = await getAllRecordList(caseId);
        for (ScanInfo info in list) {
          if (info.recordSn == code) {
            Global.toast(Lang.code_has_been_bound, marginBottom: 0.4.sh);
            delay(2000, () {
              hasCode = false;
            });
            return;
          }
        }
        Global.toast(Lang.bind_code_success, marginBottom: 0.4.sh);
        eventBus.fire(EventQrCode(code));
        pop();
      }
    });
  }
}
