import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_uvc_camera/flutter_uvc_camera.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraViewUI.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/TFLiteUtils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:orientation/orientation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wakelock/wakelock.dart';

enum ToothCameraTypeEnum {
  occlusion, //咬合
  openMaskOff, //微张(取下牙套)
  maxillary, //上颌
  mandibular, //下颌
  openMaskOn, //微张(带上牙套)
  all,
}

enum ToothCameraStepEnum {
  checkPoint, //检测中心点
  modifyLight, //手动修复手电筒的光是否在开口器内部

  alertMaskUp, //牙套提醒(带上)
  alertMaskDown, //牙套提醒(取下)

  //******* 如下在队列中，模糊 step+1,清晰 step+2 (如果从模糊页跳 step+1)
  // checkBlur2Close, //检测模糊(咬合)
  // checkBlur2Open, //检测模糊(微张)
  // checkBlur2Up, //检测模糊(上扫)
  // checkBlur2Down, //检测模糊(下扫)
  // checkBlur2Mask, //检测模糊(带上牙套)
  //*******

  modifyBlur, //手动修复模糊
  check5TootchClose, //检测5号牙齿(咬合)
  check5TootchOpen, //检测5号牙齿(微张)
  check5TootchMask, //检测5号牙齿(牙套)
  noCheck5TootchUp, //检测5号牙齿(上)
  noCheck5TootchDown, //检测5号牙齿(下)

  scanHelpVideo, //口扫教学视频

  intoScan, //开始扫描

  ultraConnect, //连接ultra
  ultraCheckTooth, //检测牙齿
  ultraCheckDirection, //提示ultra方向
  //
  // intoScanClose, //扫描咬合
  // intoScanOpen,
  // intoScanMask,
  // intoScanUp,
  // intoScanDown,
  rescan,
}

class ToothCameraView extends BasePage {
  final ToothCameraTypeEnum initDirectionType; //全部重拍
  final dynamic initRouterParamas;
  bool isLight; //是否扫描牙菌斑

  ToothCameraView({
    Key? key,
    this.initDirectionType = ToothCameraTypeEnum.all,
    this.initRouterParamas,
    this.isLight = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => ToothCameraViewState();
}

class ToothCameraViewState extends BasePageState<ToothCameraView> with WidgetsBindingObserver {
  int cameraId = 61850;
  UVCCameraController? cameraController;

  ToothCameraStepEnum currentStepEnum = ToothCameraStepEnum.checkPoint;
  late ToothCameraTypeEnum currentTypeEnum;
  List<ToothCameraTypeEnum> scanAllSteps = []; //扫描全部的组数流程
  List stepArray = [];
  int stepIndex = 0;

  //两点之间的长度 imgW / (two points dis) * Y
  double calcDevInfoValue = -1;

  // //校准的十字准心坐标归一化(相对整个屏幕所在的位置且归一化)
  // Offset centerOffset = const Offset(0, 0);
  //
  // //十字准心位置(相对处理图的位置，不是整个屏幕的位置)
  Offset centerPoint = const Offset(0, 0);

  //
  // List<CameraDescription>? cameras;
  // CameraController? cameraController;
  // bool flashOpen = false;
  //
  // //画质
  // ResolutionPreset rpType = ResolutionPreset.high;
  //
  // //记录当前方向
  DeviceOrientation currentOrientation = DeviceOrientation.landscapeRight;

  //
  // //缩放
  // double minAvailableZoom = 1.0;
  // double maxAvailableZoom = 1.0;
  // double currentScale = 1.1;
  // double baseScale = 1.0;
  // int fingerCounts = 0;
  //
  // //临时存放相机流的最新一张
  // late CameraImage newestCameraImg;
  late Directory tempDir; //记录临时文件夹路径
  int lastCameraTime = 0;

  //监听
  // late StreamSubscription<DeviceOrientation> subscription;

  //是否正在进行ffi预测逻辑中
  bool isPredicting = false;

  //call ttf result
  dynamic tfliteResultObj;

  //防止流处理太多
  int counter = 0;

  //处理聚焦
  GlobalKey globalKeyFingerUI = GlobalKey();
  GlobalKey globalKeyCameraUI = GlobalKey();

  //顶部显示第几步 [1,5]
  int topStepCircleNum = 1;

  //模型是否加载完成
  bool modalIsLoadOk = false;

  int scanTool = 1;

  String scanIp = "IP: ";

  bool isCameraOpened = false;

  String scanLog = "";

  @override
  void initState() {
    setScreenHorizontal();
    //隐藏android 的状态栏
    if (Platform.isAndroid) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.bottom]); //隐藏状态栏，保留底部按钮栏
    }

    super.initState();
    Wakelock.enable();
    WidgetsBinding.instance.addObserver(this);
    addEventListener<EventPopScan>((event) {
      doDispose();
    });
    addEventListener<EventExitScan>((event) {
      doDispose();
    });
    addEventListener<EventScanPause>((event) {
      cancelFocusTimer();
    });
    addEventListener<EventRescanUltra>((event) {
      rescanUltra();
    });
    addEventListener<EventScanLog>((event) {
      setState(() {
        scanLog += "\n${event.message}";
      });
    });
    addEventListener<EventHideMask>((event) {
      setState(() {
        initStepArray();
      });
    });
    addEventListener<EventUltraType>((event) {
      setState(() {});
      delay(300, connectUltraDevice);
    });
    addEventListener<EventIp>((event) {
      setState(() {
        scanIp = event.ip;
      });
    });
    AudioPlayerUtil.stopSound();
    scanLight = widget.isLight;
    initStepArray();

    // 记录第一次，然后根据记录的强制对应的方向逻辑 ----- start
    // bool? isFirstOpenToothCamera = Global.sharedPrefs
    //     .getBool(Global.localStorageEnum["FIRST_OPEN_TOOTH_CAMERA"]);
    // bool? isOpenToothCameraDirLeft = Global.sharedPrefs
    //     .getBool(Global.localStorageEnum["OPEN_TOOTH_CAMERA_LEFT"]);
    // if (isFirstOpenToothCamera != null && isOpenToothCameraDirLeft != null) {
    //   if (isOpenToothCameraDirLeft) {
    //     currentOrientation = DeviceOrientation.landscapeLeft;
    //   } else {
    //     currentOrientation = DeviceOrientation.landscapeRight;
    //   }
    //   OrientationPlugin.setPreferredOrientations([currentOrientation]);
    //   OrientationPlugin.forceOrientation(currentOrientation);
    // } else {
    //   OrientationPlugin.setPreferredOrientations([
    //     DeviceOrientation.landscapeLeft,
    //     DeviceOrientation.landscapeRight
    //   ]).then((value) {
    //     //如果不通过lock camera 方向的话，android camera会有竖屏显示的问题，因此lock camera方向，配合监听方向变化动态更改left right
    //     subscription = OrientationPlugin.onOrientationChange.listen((value) {
    //       if (value == DeviceOrientation.landscapeLeft ||
    //           value == DeviceOrientation.landscapeRight) {
    //         currentOrientation = value;
    //         subscription.cancel();
    //         Global.sharedPrefs.setBool(
    //             Global.localStorageEnum["FIRST_OPEN_TOOTH_CAMERA"], false);
    //         Global.sharedPrefs.setBool(
    //             Global.localStorageEnum["OPEN_TOOTH_CAMERA_LEFT"],
    //             value == DeviceOrientation.landscapeLeft);
    //         OrientationPlugin.setPreferredOrientations([value]);
    //         OrientationPlugin.forceOrientation(value).then((value) {
    //           if (Platform.isIOS) {
    //             _lockCameraOrientation(currentOrientation);
    //           }
    //         });
    //       }
    //     });
    //   });
    // }
    // 记录第一次，然后根据记录的强制对应的方向逻辑 ----- end

    initStateAsync();
  }

  //从ui上的视图顺序转化到口扫顺序
  //ui顺序:上颌，咬合，微张，牙套，下颌
  //口扫顺序：咬合，微张，上颌，下颌，牙套
  int getStepFromUi(int uiIndex) {
    switch (uiIndex) {
      case 1:
        return 3;
      case 2:
        return 1;
      case 3:
        return 2;
      case 4:
        return 5;
      case 5:
        return 4;
      default:
        return 1;
    }
  }

  initStepArray() async {
    scanTool = getScanTool();
    if (widget.isLight) {
      scanAllSteps = [
        ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
        ToothCameraTypeEnum.maxillary, //上颌
        ToothCameraTypeEnum.mandibular, //下颌
      ];
    } else if (scanMode == 1) {
      scanAllSteps = [
        ToothCameraTypeEnum.occlusion, //咬合
        ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
        ToothCameraTypeEnum.maxillary, //上颌
        ToothCameraTypeEnum.mandibular, //下颌
      ];
    } else if (scanMode == 2) {
      scanAllSteps = [
        ToothCameraTypeEnum.occlusion, //咬合
        ToothCameraTypeEnum.maxillary, //上颌
        ToothCameraTypeEnum.mandibular, //下颌
      ];
    } else if (hideMask) {
      scanAllSteps = [
        ToothCameraTypeEnum.occlusion, //咬合
        ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
        ToothCameraTypeEnum.maxillary, //上颌
        ToothCameraTypeEnum.mandibular, //下颌
      ];
    } else {
      scanAllSteps = [
        ToothCameraTypeEnum.occlusion, //咬合
        ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
        ToothCameraTypeEnum.maxillary, //上颌
        ToothCameraTypeEnum.mandibular, //下颌
        ToothCameraTypeEnum.openMaskOn, //微张(带上牙套)
      ];
    }

    prepareScanSteps();

    if (widget.initDirectionType == ToothCameraTypeEnum.all) {
      //扫描全部
      topStepCircleNum = 1;
      currentTypeEnum = scanAllSteps[0];
      for (ToothCameraTypeEnum step in scanAllSteps) {
        if (step == ToothCameraTypeEnum.openMaskOn) {
          stepArray.add(ToothCameraStepEnum.alertMaskUp);
        }
        addSingleScanSteps();
      }
    } else {
      addSingleScanSteps();
      currentTypeEnum = widget.initDirectionType;
      topStepCircleNum = scanAllSteps.indexOf(currentTypeEnum) + 1;
    }

    currentStepEnum = stepArray[stepIndex];

    logger("stepArray: $stepArray", key: "initStepArray");
    logger("initStepArray topStepCircleNum: $topStepCircleNum");
    logger("initStepArray currentTypeEnum: $currentTypeEnum");
    logger("initStepArray currentStepEnum: $currentStepEnum");
  }

  prepareScanSteps() {
    //准备步骤
    if (selectScanByUltra(scanTool)) {
      stepArray = [
        // ToothCameraStepEnum.ultraConnect,
        ToothCameraStepEnum.ultraCheckTooth,
        ToothCameraStepEnum.ultraCheckDirection,
      ];
      if (widget.initDirectionType == ToothCameraTypeEnum.openMaskOn && !hideMask) {
        stepArray.add(ToothCameraStepEnum.alertMaskUp);
      }
    } else {
      stepArray = [
        ToothCameraStepEnum.checkPoint, //光检测
        ToothCameraStepEnum.modifyLight, //光有问题进入
      ];
      if (!hideMask) {
        stepArray.insert(
            1,
            widget.initDirectionType == ToothCameraTypeEnum.openMaskOn
                ? ToothCameraStepEnum.alertMaskUp
                : ToothCameraStepEnum.alertMaskDown); //模糊检测放在了这里
      }
    }
  }

  addSingleScanSteps() {
    if (selectScanByIscan(scanTool)) {
      stepArray.add(ToothCameraStepEnum.modifyBlur);
    }
    if (!skilledMode) {
      stepArray.add(ToothCameraStepEnum.scanHelpVideo);
    }
    stepArray.add(ToothCameraStepEnum.intoScan);
  }

  void retryConnectCamera() {
    delay(5000, () {
      if (!isCameraOpened) {
        cameraController?.initializeCamera(cameraId);
        retryConnectCamera();
      }
    });
  }

  void initStateAsync() async {
    cameraController = UVCCameraController();
    cameraController?.cameraStateCallback = (state) {
      if (state == UVCCameraState.opened) {
        setState(() {
          isCameraOpened = true;
        });
        stepIndex = 0;
        currentStepEnum = stepArray[stepIndex];
        playSoundByStepEnum(currentStepEnum);
        ultraCheckTooth();
      }
    };
    retryConnectCamera();
    WidgetsBinding.instance.addObserver(this);
    // if (selectScanByUltra(scanTool)) {
    //根据用户方向自动调节方向
    // if (Platform.isAndroid) {
    //   OrientationPlugin.setPreferredOrientations(
    //       [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    // } else {
    //   OrientationPlugin.setPreferredOrientations(
    //       [DeviceOrientation.landscapeRight]).then((value) {
    //     OrientationPlugin.setPreferredOrientations([
    //       DeviceOrientation.landscapeLeft,
    //       DeviceOrientation.landscapeRight
    //     ]);
    //   });
    // }
    await TFLiteUtils.init();
    TFLiteUtils.resetCreateTFLiteInstance(ToothTaskTypeEnum.toothDetection);

    // playSoundByStepEnum(currentStepEnum);
    // ultraCheckTooth();
    // } else {
    //   if (Platform.isAndroid) {
    //     PermissionStatus storageStatus = await Permission.camera.status;
    //     if (storageStatus != PermissionStatus.granted) {
    //       Global.toast(Lang.request_camera_scan, marginBottom: 0.8.sh);
    //     }
    //   }
    //   initializeCamera();
    //   await TFLiteUtils.init();
    //   TFLiteUtils.resetCreateTFLiteInstance(ToothTaskTypeEnum.checkPoint);
    // }
    tempDir = await getTemporaryDirectory();
  }

  //防止部分android机器第一次启动或从后台过久回来会出现摄像机画面不动的问题
  // onListenCameraState() {
  //   if (mounted) {
  //     setState(() {});
  //   }
  //   if (cameraController!.value.hasError) {
  //     if (kDebugMode) {
  //       print('Camera error ${cameraController!.value.errorDescription}');
  //     }
  //   }
  // }
  //
  // void initializeCamera() async {
  //   int useWideAngleCameraIndex = 0;
  //   if (Global.deviceInfo["name"] == "iPhone14,2" ||
  //       Global.deviceInfo["name"] == "iPhone14,3" ||
  //       Global.deviceInfo["name"] == "iPhone15,2" ||
  //       Global.deviceInfo["name"] == "iPhone15,3") {
  //     currentScale = 2;
  //     useWideAngleCameraIndex = 3;
  //   }
  //
  //   //适配华为部分机型(无需延长器)
  //   try {
  //     if (Global.deviceInfo["name"].toLowerCase().contains("huawei")) {
  //       String deviceName = Global.deviceInfo["name"].toLowerCase();
  //       //华为 Mate 40 Pro 口扫采用广角
  //       if (deviceName.contains("noh") && deviceName.contains("an00")) {
  //         useWideAngleCameraIndex = 4;
  //       }
  //       //华为 P40 Pro 口扫采用广角
  //       if (deviceName.contains("els") && deviceName.contains("an00")) {
  //         currentScale = 1.5;
  //         useWideAngleCameraIndex = 4;
  //       }
  //       //华为 Mate 40
  //       if (deviceName.contains("oce") && deviceName.contains("an10")) {
  //         currentScale = 1.5;
  //         useWideAngleCameraIndex = 3;
  //       }
  //       //华为 Mate 50 Pro
  //       if (deviceName.contains("dco") && deviceName.contains("al00")) {
  //         currentScale = 1.0;
  //       }
  //       //华为 P40
  //       if (deviceName.contains("ana") && deviceName.contains("an00")) {
  //         useWideAngleCameraIndex = 4;
  //         currentScale = 1.5;
  //       }
  //     }
  //   } catch (ex) {
  //     //
  //   }
  //   bool isAndroid() => Theme.of(context).platform == TargetPlatform.android;
  //   cameras = await availableCameras();
  //   //防止出现因为android某些型号升级系统导致没有权限调用其他摄像头的处理
  //   if (useWideAngleCameraIndex >= cameras!.length) {
  //     useWideAngleCameraIndex = 0;
  //     currentScale = 1.1;
  //   }
  //   if (kDebugMode) {
  //     print(
  //         "\n设备信息：deviceName:${Global.deviceInfo["name"]}\n使用摄像头序号下标(总摄像头个数):$useWideAngleCameraIndex(${cameras!.length})\n摄像头缩放倍率:$currentScale");
  //   }
  //   cameraController = CameraController(cameras![useWideAngleCameraIndex], rpType,
  //       enableAudio: false, imageFormatGroup: isAndroid() ? ImageFormatGroup.yuv420 : ImageFormatGroup.bgra8888);
  //
  //   //防止部分android机器第一次启动或从后台过久回来会出现摄像机画面不动的问题
  //   cameraController?.addListener(onListenCameraState);
  //   cameraController!.initialize().then((_) async {
  //     //根据用户方向自动调节方向
  //     OrientationPlugin.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight])
  //         .then((value) {
  //       rotateScreen(targetDir: currentOrientation);
  //       //如果不通过lock camera 方向的话，android camera会有竖屏显示的问题，因此lock camera方向，配合监听方向变化动态更改left right
  //       subscription = OrientationPlugin.onOrientationChange.listen((value) {
  //         if (value == DeviceOrientation.landscapeLeft || value == DeviceOrientation.landscapeRight) {
  //           if (currentOrientation != value) {
  //             if (kDebugMode) {
  //               print("当前方向：$currentOrientation ，目标方向：$value");
  //             }
  //             rotateScreen();
  //           }
  //         }
  //       });
  //     });
  //     //设置自动对焦和曝光(注意手动要设置对焦时，Android需要先设置为locked)
  //     setFocusMode(FocusMode.auto);
  //     setExposureMode(ExposureMode.auto);
  //     //打开手电筒模式
  //     _setFlashMode(FlashMode.torch);
  //     //设置默认的聚焦
  //     await cameraController!.setZoomLevel(currentScale);
  //
  //     if (!cameraController!.value.isStreamingImages) {
  //       await Future.wait(<Future<Object?>>[
  //         cameraController!.getMaxZoomLevel().then((double value) => maxAvailableZoom = value),
  //         cameraController!.getMinZoomLevel().then((double value) => minAvailableZoom = value),
  //       ]);
  //
  //       await cameraController!.startImageStream(onImageStream);
  //
  //       setState(() {});
  //     }
  //   });
  // }

  playSoundByStepEnum(stepEnum) {
    if (kDebugMode) {
      print("playSoundByStepEnum: $stepEnum");
    }
    switch (stepEnum) {
      case ToothCameraStepEnum.modifyLight:
        AudioPlayerUtil.playSound("${lang}_check_light.mp3");
        break;
      case ToothCameraStepEnum.ultraCheckTooth:
        AudioPlayerUtil.playSound("${lang}_setup_ultra_direction.mp3");
        break;
      case ToothCameraStepEnum.ultraCheckDirection:
        AudioPlayerUtil.playSound("${lang}_check_ultra_image.mp3");
        break;
      case ToothCameraStepEnum.alertMaskUp:
        AudioPlayerUtil.playSound("${lang}_scan_mask_up.mp3", repeat: true);
        break;
      case ToothCameraStepEnum.alertMaskDown:
        AudioPlayerUtil.playSound("${lang}_scan_mask_off.mp3", repeat: true);
        break;
      case ToothCameraStepEnum.scanHelpVideo:
        AudioPlayerUtil.playSound(getScanHelpAudio(currentTypeEnum, selectScanByUltra(scanTool)), repeat: true);
        break;
      default:
        AudioPlayerUtil.stopSound();
        break;
    }
  }

  intoNextStepEnum({int offsetNum = 1, bool rescan = false}) {
    logger("intoNextStepEnum from  $currentStepEnum $stepIndex / ${stepArray.length}");
    if (globalKeyCameraUI.currentState == null || isDisposed) {
      return;
    }
    if (rescan) {
      AudioPlayerUtil.stopSound();
      setState(() {
        currentStepEnum = ToothCameraStepEnum.rescan;
      });
      delay(100, () {
        intoNextStepEnum(offsetNum: offsetNum);
      });
      return;
    }
    tfliteResultObj = null;
    cancelFocusTimer();
    // if (sdFocusTimer != null) {
    //   sdFocusTimer!.cancel();
    //   sdFocusTimer = null;
    // }

    if (stepIndex + offsetNum >= stepArray.length) {
      //关闭手电筒模式
      // _setFlashMode(FlashMode.auto);

      setScreenVertiacl();
      if (selectScanByUltra(scanTool)) {
        disconnectUltra();
      }

      AudioPlayerUtil.stopSound();
      eventBus.fire(EventPopScan());
      pop();
      return;
    }

    //打开手电筒模式
    // if (selectScanByIscan(scanTool)) {
    //   _setFlashMode(FlashMode.torch);
    // }
    OrientationPlugin.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);

    //如果返回上一步是检测模糊，就继续再往前一步
    if (offsetNum == -1 && stepArray[stepIndex - 1] == ToothCameraStepEnum.modifyBlur) {
      offsetNum = -2;
    }
    setState(() {
      stepIndex = stepIndex + offsetNum;

      if (currentStepEnum == ToothCameraStepEnum.checkPoint && stepArray[stepIndex] != ToothCameraStepEnum.checkPoint) {
        TFLiteUtils.resetCreateTFLiteInstance(ToothTaskTypeEnum.toothDetection);
      }

      isPredicting = false;
      currentStepEnum = stepArray[stepIndex];
      logger("ToothCameraView intoNextStepEnum: $currentStepEnum");
      if (currentStepEnum == ToothCameraStepEnum.modifyLight) {
        sendScanEvent("brightness_detection");
      }

      //重置模糊可弹窗态&&重置上方进度圆形
      if (offsetNum > 0) {
        if (stepArray[stepIndex - 1] == ToothCameraStepEnum.intoScan) {
          (globalKeyCameraUI.currentState as ToothCameraViewUIState).resetBlurCanShowAlert();
        }
      }

      playSoundByStepEnum(currentStepEnum);

      (globalKeyCameraUI.currentState as ToothCameraViewUIState).seDoublePressProcessStepEnum();

      if (currentStepEnum == ToothCameraStepEnum.modifyBlur) {
        // if (Platform.isAndroid) {
        //   //对于Android，您的焦点模式应该设置为锁定，以便setFocusPoint产生效果
        //   setFocusMode(FocusMode.locked);
        // }
        // startAutoFoucs();
      } else if (currentStepEnum == ToothCameraStepEnum.ultraCheckTooth) {
        timerAutoListIndex = 0;
        ultraCheckTooth();
      }
    });
  }

  rotateScreen({dynamic targetDir}) {
    // if (targetDir != null) {
    //   currentOrientation = targetDir;
    // } else {
    //   if (currentOrientation == DeviceOrientation.landscapeLeft) {
    //     currentOrientation = DeviceOrientation.landscapeRight;
    //   } else {
    //     currentOrientation = DeviceOrientation.landscapeLeft;
    //   }
    // }
    //
    // Global.sharedPrefs.setBool(
    //     Global.localStorageEnum["OPEN_TOOTH_CAMERA_LEFT"], currentOrientation == DeviceOrientation.landscapeLeft);
    // OrientationPlugin.setPreferredOrientations([currentOrientation]);
    // OrientationPlugin.forceOrientation(currentOrientation).then((value) {
    //   if (Platform.isIOS) {
    //     _lockCameraOrientation(currentOrientation);
    //   }
    //   //打开手电筒模式
    //   if (mounted && !isDisposed) {
    //     _setFlashMode(FlashMode.torch);
    //   }
    // });
  }

  // onImageStream(CameraImage cameraImage) async {
  //   if (isDisposed) {
  //     return;
  //   }
  //
  //   // counter++;
  //   // if (counter % 4 != 0) {
  //   //   if (counter >= 100000) {
  //   //     counter = 0;
  //   //   }
  //   //   return;
  //   // }
  //
  //   newestCameraImg = cameraImage;
  //   lastCameraTime = DateTime.now().millisecondsSinceEpoch;
  //
  //   if (isPredicting) {
  //     return;
  //   } else {
  //     setState(() {
  //       isPredicting = true;
  //     });
  //   }
  //
  //   if (currentStepEnum == ToothCameraStepEnum.checkPoint ||
  //       currentStepEnum == ToothCameraStepEnum.check5TootchClose ||
  //       currentStepEnum == ToothCameraStepEnum.check5TootchOpen ||
  //       currentStepEnum == ToothCameraStepEnum.check5TootchMask) {
  //     TFLiteUtils.callTfliteLogic(cameraImage, 3).then((obj) {
  //       if (kDebugMode) {
  //         // print("ms:${DateTime.now().millisecondsSinceEpoch - timestamp}");
  //       }
  //       //销毁时，避免继续运行刷新逻辑
  //       if (!isDisposed) {
  //         setState(() {
  //           tfliteResultObj = obj;
  //           isPredicting = false;
  //         });
  //       }
  //     });
  //   }
  // }

  // dver代表 第几代 mooeli 硬件产品
  // fver代表设备版本号 flag version (算法版本标识号)
  // width代表图片宽度对应的真实毫米数
  // deviceInfo
  // camera counts
  // app version
  getPicOtherInfo() {
    String deviceStr = Global.deviceInfo["name"] ?? "phone";
    deviceStr = deviceStr.replaceAll(' ', '_');
    deviceStr = deviceStr.replaceAll('-', '_');
    // return "dver_1-fver_1-width_${calcDevInfoValue.toStringAsFixed(4)}-$deviceStr-${cameras != null ? cameras!.length : 0}-${Global.appVersion}";
    return "dver_1-fver_1-width_${calcDevInfoValue.toStringAsFixed(4)}-$deviceStr-0-${Global.appVersion}";
  }

  getCenterPoint() {
    return centerPoint;
  }

  getModalIsLoadOk() {
    return modalIsLoadOk;
  }

  setModalLoadOk() {
    modalIsLoadOk = true;
  }

  setPicOtherInfo(double dis, Offset offset, Offset point) {
    // calcDevInfoValue = dis;
    // centerOffset = offset;
    // centerPoint = point;
  }

  // CameraImage getStreamImage() {
  //   return newestCameraImg;
  // }
  //
  // Future<dynamic> isolateCamera2Img(CameraImage cameraImage, {String saveImgName = ""}) {
  //   //Android landscapeLeft camera stream need rotate 90
  //   if (currentOrientation == DeviceOrientation.landscapeLeft && Platform.isAndroid) {
  //     return TFLiteUtils.callTfliteLogic(cameraImage, -1, saveImgName: saveImgName);
  //   } else {
  //     return TFLiteUtils.callTfliteLogic(cameraImage, 1, saveImgName: saveImgName);
  //   }
  // }
  //
  // //监听后台返回事件
  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) async {
  //   logger("ToothCameraView didChangeAppLifecycleState: $state");
  //   if (selectScanByIscan(scanTool)) {
  //     switch (state) {
  //       case AppLifecycleState.paused:
  //         //关闭摄像头
  //         _setFlashMode(FlashMode.auto);
  //         if (cameraController!.value.isStreamingImages) {
  //           cameraController!.stopImageStream();
  //         }
  //         break;
  //       case AppLifecycleState.resumed:
  //         if (!cameraController!.value.isStreamingImages) {
  //           await cameraController!.startImageStream(onImageStream);
  //         }
  //         //打开手电筒模式(为了防止ios 后台时间过长会引起无法点亮手电筒的情况，所以这里后台回来延迟2秒再打开)
  //         delay(2000, () {
  //           int time = DateTime.now().millisecondsSinceEpoch;
  //           if (time - lastCameraTime > 10000) {
  //             //判断为后台时间太久，相机死机，需要重新初始化
  //             logger("ToothCameraView ImageStream error for ms: ${time - lastCameraTime}");
  //             lastCameraTime = time;
  //             initializeCamera();
  //           } else {
  //             logger("ToothCameraView ImageStream ok");
  //             _setFlashMode(FlashMode.torch);
  //           }
  //         });
  //         break;
  //       default:
  //     }
  //   }
  // }
  //
  // //设置对焦
  // Future<void> setFocusMode(FocusMode mode) async {
  //   if (cameraController == null) {
  //     return;
  //   }
  //
  //   try {
  //     await cameraController!.setFocusMode(mode);
  //     if (mode == FocusMode.auto) {
  //       await cameraController!.setFocusPoint(null);
  //     }
  //   } on CameraException catch (e) {
  //     if (kDebugMode) {
  //       print(e.toString());
  //     }
  //     rethrow;
  //   }
  // }
  //
  // //设置曝光
  // Future<void> setExposureMode(ExposureMode mode) async {
  //   if (cameraController == null) {
  //     return;
  //   }
  //   try {
  //     await cameraController!.setExposureMode(mode);
  //     if (mode == FocusMode.auto) {
  //       //null = 重置曝光
  //       cameraController!.setExposurePoint(null);
  //     }
  //   } on CameraException catch (e) {
  //     Global.toast(e.toString());
  //     rethrow;
  //   }
  // }
  //
  // //设置闪光灯模式(屏幕旋转会自动关掉手电筒模式)
  // Future<void> _setFlashMode(FlashMode mode) async {
  //   if (cameraController == null || selectScanByUltra(scanTool)) {
  //     return;
  //   }
  //   try {
  //     //由于Android机除0之外摄像头无闪光灯开启权限，所以这里原生拉起服务开手电筒
  //     if (Platform.isAndroid) {
  //       if (mode == FlashMode.torch || mode == FlashMode.always) {
  //         if (!flashOpen) {
  //           flashOpen = true;
  //           callMcByFunName("openFlash");
  //           await cameraController!.setFlashMode(mode);
  //         }
  //       }
  //
  //       if (mode == FlashMode.auto || mode == FlashMode.off) {
  //         if (flashOpen) {
  //           flashOpen = false;
  //           callMcByFunName("closeFlash");
  //           await cameraController!.setFlashMode(mode);
  //         }
  //       }
  //     } else {
  //       await cameraController!.setFlashMode(mode);
  //     }
  //   } on CameraException catch (e) {
  //     //CameraException(setFlashModeFailed, Torch mode is currently not available)
  //     //Unhandled Exception: CameraException(setFlashModeFailed, Torch mode is currently not available)
  //     if (kDebugMode) {
  //       print(e.toString());
  //     }
  //     rethrow;
  //   }
  // }
  //
  // Timer? sdFocusTimer;
  //
  // //点击设置曝光和焦点
  // void fingerTap(TapDownDetails details, BoxConstraints constraints) {
  //   if (currentStepEnum != ToothCameraStepEnum.modifyBlur) {
  //     return;
  //   }
  //   //自动对焦时，无法手动
  //   if (autoFoucsTimer != null) {
  //     return;
  //   }
  //
  //   (globalKeyFingerUI.currentState as ToothCameraTouchFoucsState)
  //       .setFingerFocus(details.localPosition.dx, details.localPosition.dy);
  //   sendScanEvent("click_screen_focus", getRecordEventStepEnumTitle(currentTypeEnum));
  //
  //   //3秒内没出判断结果前不再响应用户点击行为
  //   if (sdFocusTimer == null) {
  //     final Offset offset = Offset(
  //       details.localPosition.dx / constraints.maxWidth,
  //       details.localPosition.dy / constraints.maxHeight,
  //     );
  //     if (kDebugMode) {
  //       print("设置焦点:$offset");
  //     }
  //     // 不在动态调整曝光而是一开始就设定固定值null(重置曝光)
  //     // cameraController!.setExposurePoint(offset);
  //     // if (kDebugMode) {
  //     //   print("设置曝光点:$offset");
  //     // }
  //     cameraController!.setFocusPoint(offset);
  //     sdFocusTimer = Timer(const Duration(seconds: 3), () {
  //       if (kDebugMode) {
  //         print("检测清晰度");
  //       }
  //       if (currentStepEnum == ToothCameraStepEnum.modifyBlur) {
  //         (globalKeyCameraUI.currentState as ToothCameraViewUIState).checkIsBlur();
  //       }
  //       if (sdFocusTimer != null) {
  //         sdFocusTimer!.cancel();
  //         sdFocusTimer = null;
  //       }
  //     });
  //   }
  // }

  rescanUltra() {
    //扫描全部需要重新设置步骤，加上ultra检测等，单步则直接从头开始
    if (widget.initDirectionType == ToothCameraTypeEnum.all) {
      prepareScanSteps();
      int currentStep = scanAllSteps.indexOf(currentTypeEnum);
      if (currentStep < 0) {
        currentStep = 0;
      }
      for (int i = currentStep; i < scanAllSteps.length; i++) {
        if (scanAllSteps[i] == ToothCameraTypeEnum.openMaskOn) {
          stepArray.add(ToothCameraStepEnum.alertMaskUp);
        }
        addSingleScanSteps();
      }
    }
    setState(() {
      stepIndex = 0;
    });
    intoNextStepEnum(offsetNum: 0);
  }

  eachScanFinishIntoNextAction() {
    if (widget.initDirectionType == ToothCameraTypeEnum.all && stepIndex + 1 < stepArray.length) {
      currentTypeEnum = scanAllSteps[topStepCircleNum];
      topStepCircleNum++;
      // if (isScanByIscan(scanTool)) {
      //   (globalKeyCameraUI.currentState as ToothCameraViewUIState).checkIsBlur();
      // } else {
      if (skilledMode && selectScanByUltra(scanTool)) {
        //ultra老手模式需要直接进入下一步口扫
        intoNextStepEnum(rescan: true);
      } else {
        intoNextStepEnum();
      }
      // }
    } else {
      intoNextStepEnum();
    }
  }

  Timer? autoFoucsTimer;
  int timerAutoListIndex = 0;
  List<Offset> autoOffsetList = [
    // const Offset(0.3, 0.3),
    // const Offset(0.4, 0.4),
    // const Offset(0.5, 0.5),
    // const Offset(0.6, 0.7),
    // const Offset(0.7, 0.7),
    // //
    // const Offset(0.7, 0.3),
    // const Offset(0.6, 0.4),
    // const Offset(0.5, 0.5),
    // const Offset(0.4, 0.6),
    // const Offset(0.3, 0.7),
    const Offset(0.2, 0.2),
    const Offset(0.8, 0.8),
    const Offset(0.5, 0.5),
  ];

  void startAutoFoucs() {
    timerAutoListIndex = 0;

    (globalKeyCameraUI.currentState as ToothCameraViewUIState).setIsAutoFoucsing(true);

    autoCheckNextFoucs();
  }

  void autoCheckNextFoucs() async {
    // //意外判断
    // if (currentStepEnum != ToothCameraStepEnum.modifyBlur) {
    //   cancelFocusTimer();
    //   return;
    // }
    //
    // //调整焦点
    // Offset targetOffset = Offset(autoOffsetList[timerAutoListIndex].dx + centerOffset.dx,
    //     autoOffsetList[timerAutoListIndex].dy + centerOffset.dy);
    // if (kDebugMode) {
    //   print("自动校准:$targetOffset,old:${autoOffsetList[timerAutoListIndex]} center:$centerOffset");
    // }
    // cameraController!.setFocusPoint(targetOffset);
    //
    // //定时检测结果
    // cancelFocusTimer();
    // autoFoucsTimer = Timer(const Duration(milliseconds: 3000), () {
    //   (globalKeyCameraUI.currentState as ToothCameraViewUIState).checkIsBlur(callBack: (bool isBlur) {
    //     //检测模糊
    //     timerAutoListIndex++;
    //     if (timerAutoListIndex >= autoOffsetList.length || !isBlur) {
    //       cancelFocusTimer();
    //       (globalKeyCameraUI.currentState as ToothCameraViewUIState).setIsAutoFoucsing(false);
    //
    //       if (isBlur) {
    //         if (currentStepEnum == ToothCameraStepEnum.modifyBlur) {
    //           AudioPlayerUtil.playSound("${lang}_click_focus.mp3");
    //           sendScanEvent("manual_focus");
    //         }
    //       }
    //     } else {
    //       if (timerAutoListIndex <= 1) {
    //         AudioPlayerUtil.playSound("${lang}_tooth_blur.mp3");
    //         sendScanEvent("vague", getRecordEventStepEnumTitle(currentTypeEnum));
    //       }
    //       autoCheckNextFoucs();
    //     }
    //   });
    // });
  }

  ultraCheckTooth() {
    logger("ToothCameraViewUI ultraCheckTooth");
    //定时检测结果
    cancelFocusTimer();
    autoFoucsTimer = Timer(const Duration(milliseconds: 3000), () async {
      logger("ultraCheckTooth currentStepEnum $currentStepEnum");
      if (currentStepEnum != ToothCameraStepEnum.ultraCheckTooth) {
        cancelFocusTimer();
        return;
      }
      try {
        Directory parentDir = await getTemporaryDirectory();
        String fullPath = "${parentDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg";
        logger("ultraCheckTooth fullPath $fullPath");
        saveScanImage(fullPath, () {
          logger("ToothCameraViewUI ultraCheckTooth: $currentStepEnum");
          TFLiteUtils.callTfliteLogic([fullPath], 2).then((obj) {
            logger("ultraCheckTooth obj $obj");
            bool isBlur = false;
            if (Global.isGlobalUseNewModal) {
              isBlur = Global.checkIsBlurByNewModal(struct2lastTFLite: obj["lastObjects"][0]);
            } else {
              isBlur = obj["lastObjects"][0].blurInfo.isBlur == 1;
            }
            logger("ultraCheckTooth isBlur $isBlur");
            //检测模糊
            timerAutoListIndex++;
            if (timerAutoListIndex >= 3 || !isBlur) {
              cancelFocusTimer();
              intoNextStepEnum();
            } else {
              ultraCheckTooth();
            }
          });
        });
      } catch (ex) {
        timerAutoListIndex++;
        if (timerAutoListIndex >= 3) {
          cancelFocusTimer();
          intoNextStepEnum();
        } else {
          ultraCheckTooth();
        }
      }
    });
  }

  void saveScanImage(String savePath, [dynamic callback]) {
    if (cameraController == null) {
      logger("saveUvcImage break");
      return;
    }
    try {
      logger("saveUvcImage try $savePath");
      cameraController!.takePicture(
        path: savePath,
        onSuccess: (path) {
          logger("saveUvcImage $path");
          if (isNotEmpty(path)) {
            File file = File(path!);
            if (file.existsSync()) {
              ffiFlipImage(path, savePath);
              // await file.copy(savePath);
              logger("saveUvcImage copy to $savePath");
              // file.delete();
              logger("saveUvcImage delete $path");
              if (callback != null) {
                callback();
              }
            }
          }
        },
      );
    } catch (ex) {
      logger("saveUvcImage exception: $ex");
    }
  }

  //锁定摄像机指定视角（不指定则默认为视角）
  Future<void> _lockCameraOrientation(DeviceOrientation orientation) async {
    // try {
    //   if (cameraController != null && !isDisposed) {
    //     await cameraController!.lockCaptureOrientation(orientation);
    //   }
    // } on CameraException catch (e) {
    //   Global.toast('Error: ${e.code}\n${e.description}');
    // }
  }

  // //缩放
  // void scaleStart(ScaleStartDetails details) {
  //   return;
  //
  //   if (currentStepEnum != ToothCameraStepEnum.modifyBlur) {
  //     return;
  //   }
  //   baseScale = currentScale;
  // }
  //
  // //缩放更新
  // Future<void> scaleUpdate(ScaleUpdateDetails details) async {
  //   return;
  //
  //   if (currentStepEnum != ToothCameraStepEnum.modifyBlur) {
  //     return;
  //   }
  //   if (cameraController == null || fingerCounts != 2) {
  //     return;
  //   }
  //
  //   currentScale = (baseScale * details.scale).clamp(minAvailableZoom, maxAvailableZoom);
  //
  //   // print(
  //   //     "当前缩放值:${currentScale.toStringAsFixed(2)} [$minAvailableZoom,$maxAvailableZoom]");
  //
  //   await cameraController!.setZoomLevel(currentScale);
  // }

  @override
  Widget build(BuildContext context) {
    logger("ToothCameraView build ${widget.isLight} ${Global.getRouterParams(context)}");
    // if (selectScanByIscan(scanTool) && (cameraController == null || !cameraController!.value.isInitialized)) {
    //   return Scaffold(body: Container());
    // }

    return WillPopScope(
      onWillPop: () {
        doDispose();
        return Future.value(true);
      },
      child: Scaffold(
        backgroundColor: Colors.black54,
        body: Stack(children: [
          UVCCameraView(
            cameraController: cameraController!,
            cameraId: cameraId,
            width: 1.sw,
            height: 1.sh,
          ),
          isCameraOpened
              ? const SizedBox()
              : Align(
                  alignment: Alignment.topCenter,
                  child: Padding(
                    padding: EdgeInsets.only(top: 0.5.sh - 240.sp),
                    child: MyText(Lang.conencting_camera, Colors.white, 28.sp, FontWeight.w500),
                  ),
                ),
          ToothCameraViewUI(
              isLastStep: stepIndex == stepArray.length - 1,
              initDirectionType: widget.initDirectionType,
              key: globalKeyCameraUI,
              toothCameraStep: currentStepEnum,
              toothCameraType: currentTypeEnum,
              tfliteResultObj: tfliteResultObj,
              currentOrientation: currentOrientation,
              topStepCircleNum: topStepCircleNum,
              funMap: {
                "intoNextStepEnum": intoNextStepEnum,
                "getStepCount": () {
                  return scanAllSteps.length;
                },
                "rotateScreen": rotateScreen,
                "setPicOtherInfo": setPicOtherInfo,
                // "getStreamImage": getStreamImage,
                // "isolateCamera2Img": isolateCamera2Img,
                "saveScanImage": saveScanImage,
                "getPicOtherInfo": getPicOtherInfo,
                "getCenterPoint": getCenterPoint,
                "getModalIsLoadOk": getModalIsLoadOk,
                "setModalLoadOk": setModalLoadOk,
                "eachScanFinishIntoNextAction": eachScanFinishIntoNextAction,
              },
              updateStateByType: widget.initRouterParamas != null
                  ? widget.initRouterParamas["updateStateByType"]
                  : Global.getRouterParams(context)["updateStateByType"]),
          // Container(
          //   width: 0.8.sw,
          //   padding: EdgeInsets.all(16.sp),
          //   color: Colors.black12,
          //   child: SingleChildScrollView(
          //     child: HeightText(scanLog, Colors.white, 18.sp, 1.4),
          //   ),
          // ),
        ]),
      ),
    );
  }

  cancelFocusTimer() {
    if (autoFoucsTimer != null) {
      autoFoucsTimer!.cancel();
      autoFoucsTimer = null;
    }
  }

  doDispose() {
    if (!isDisposed) {
      logger("ToothCameraView dispose");
      //显示回 android 的状态栏
      if (Platform.isAndroid) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
            overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]); //隐藏状态栏，保留底部按钮栏
      }
      //自动对焦timer
      cancelFocusTimer();
      isDisposed = true;

      // if (selectScanByUltra(scanTool)) {
      disconnectUltra();
      setScreenVertiacl();
      // } else {
      //   _setFlashMode(FlashMode.off);
      //   //先取消方向监听,再转竖屏
      //   try {
      //     subscription?.cancel().then((value) {
      //       //设置强制竖屏
      //       OrientationPlugin.setPreferredOrientations([DeviceOrientation.portraitUp]);
      //       OrientationPlugin.forceOrientation(DeviceOrientation.portraitUp);
      //     });
      //   } on Exception catch (e) {
      //     //设置强制竖屏
      //     OrientationPlugin.setPreferredOrientations([DeviceOrientation.portraitUp]);
      //     OrientationPlugin.forceOrientation(DeviceOrientation.portraitUp);
      //   }
      // }

      WidgetsBinding.instance.removeObserver(this);

      TFLiteUtils.dispose();

      // if (cameraController != null) {
      //   cameraController!.removeListener(onListenCameraState);
      //   if (cameraController!.value.isRecordingVideo || cameraController!.value.isRecordingPaused) {
      //     cameraController!.stopVideoRecording();
      //   }
      //   if (cameraController!.value.isStreamingImages) {
      //     cameraController!.stopImageStream().then((_) {
      //       cameraController!.dispose();
      //     });
      //   }
      // }
    }
  }

  @override
  void dispose() {
    ultraType = 0;
    doDispose();
    Wakelock.disable();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
