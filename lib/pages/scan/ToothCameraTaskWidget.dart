import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraNewModalTask.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/GreatView.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/swiper/swiper.dart';
import 'package:mooeli/widget/swiper/swiper_controller.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:path_provider/path_provider.dart';

enum ScanTaskDirEnum {
  lr,
  toUp,
  toDown,
}

class ToothCameraTaskWidget extends BasePage {
  final ToothCameraStepEnum toothCameraStep;
  final ToothCameraTypeEnum toothCameraType;
  final dynamic funMap;
  final dynamic mirrorUI;
  final dynamic updateStateByType;
  final dynamic showToothStepAlert;
  final dynamic getDoubleTapButton;
  final ScanTaskDirEnum directionEnum;
  final bool isLastStep;
  final DeviceOrientation currentOrientation;
  final bool isShowMirrorUI;

  const ToothCameraTaskWidget(
      {Key? key,
      required this.updateStateByType,
      required this.showToothStepAlert,
      required this.mirrorUI,
      required this.isShowMirrorUI,
      required this.funMap,
      required this.isLastStep,
      required this.currentOrientation,
      required this.getDoubleTapButton,
      required this.toothCameraStep,
      required this.toothCameraType,
      required this.directionEnum})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ToothCameraTaskWidgetState();
  }
}

int isGetTaskResult = -1; //-1 还没有结果 0=模糊等要提醒 1=清晰正常页

class _ToothCameraTaskWidgetState extends BasePageState<ToothCameraTaskWidget>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late ToothCameraNewModalTask task;
  bool isCounterEnd = false;
  Timer? counterTimer;
  int counter = 3;

  double eachBottomRectImgWidth = 100.sp;
  double eachBottomRectArrowWidth = 63.sp;

  late Animation<double> aniUI;
  late AnimationController aniControlUI;

  bool isPlayedStart = false;
  bool isPlayedLeft = false;
  bool isPlayedCenter = false;
  bool isPlayedEnd = false;

  //动画参数
  int aniTotalSec = 0; //设定周期
  double startSp = 0; //开始
  double centerSp = 0; //开始
  double endSp = 0; //结束
  double progressRate = 0; //进度条

  //扫描结束返回结果了
  bool isAnimateDone = false;
  List resImages = [];
  List errorCodes = [];
  SwiperController swiperController = SwiperController();

  int curShowImage = -1;

  int scanTool = 1;

  bool isZipError = false;

  @override
  void initState() {
    setScreenHorizontal();
    super.initState();
    isGetTaskResult = -1;
    WidgetsBinding.instance.addObserver(this);
    addEventListener<EventPopScan>((event) {
      doDispose();
    });
    addEventListener<EventExitScan>((event) {
      doDispose();
    });

    scanTool = getScanTool();

    aniTotalSec = widget.directionEnum == ScanTaskDirEnum.lr ? 13 : 5;

    aniControlUI = AnimationController(duration: Duration(seconds: aniTotalSec), vsync: this);
    aniUI = CurvedAnimation(parent: aniControlUI, curve: Curves.linear);

    //从左到右
    if (widget.directionEnum == ScanTaskDirEnum.lr) {
      startSp = 170.sp;
      endSp = 1.sw - 170.sp;
      centerSp = 0.5.sw;

      aniUI = TweenSequence([
        //这是一个动画序列，weight表示权重
        TweenSequenceItem(
            tween: Tween(begin: centerSp, end: centerSp).chain(CurveTween(curve: Curves.linear)),
            weight: 1 / 14.5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: centerSp, end: endSp).chain(CurveTween(curve: Curves.linear)),
            weight: 3.5 / 14.5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: endSp, end: endSp).chain(CurveTween(curve: Curves.linear)),
            weight: 1 / 14.5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: endSp, end: centerSp).chain(CurveTween(curve: Curves.linear)),
            weight: 3.5 / 14.5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: centerSp, end: centerSp).chain(CurveTween(curve: Curves.linear)),
            weight: 1 / 14.5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: centerSp, end: startSp).chain(CurveTween(curve: Curves.linear)),
            weight: 3.5 / 14.5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: startSp, end: startSp).chain(CurveTween(curve: Curves.linear)),
            weight: 1 / 14.5 * aniTotalSec),
      ]).animate(aniControlUI);
    } else {
      startSp = widget.directionEnum == ScanTaskDirEnum.toUp ? 1.sh - 114.sp : 114.sp;
      endSp = widget.directionEnum == ScanTaskDirEnum.toUp ? 114.sp : 1.sh - 114.sp;

      aniUI = TweenSequence([
        //这是一个动画序列，weight表示权重
        TweenSequenceItem(
            tween: Tween(begin: startSp, end: startSp).chain(CurveTween(curve: Curves.linear)),
            weight: 1 / 5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: startSp, end: endSp).chain(CurveTween(curve: Curves.linear)),
            weight: 3 / 5 * aniTotalSec),
        TweenSequenceItem(
            tween: Tween(begin: endSp, end: endSp).chain(CurveTween(curve: Curves.linear)),
            weight: 1 / 5 * aniTotalSec),
      ]).animate(aniControlUI);
    }

    aniUI.addListener(animateListenFun);
    aniUI.addStatusListener(animateListenStatusFun);

    //restartTimer();

    // if (Global.isGlobalUseNewModal) {
    task = ToothCameraNewModalTask(
        toothCameraStep: widget.toothCameraStep,
        toothCameraType: widget.toothCameraType,
        funMap: widget.funMap,
        currentOrientation: widget.currentOrientation,
        taskDirection: widget.directionEnum,
        totalSecond: aniTotalSec);
    // } else {
    //   /*如果确认旧的版本不再使用，可以移除 assets/mm8.ml 文件
    //   备注:旧的版本从左往右扫描的过程中产生的unuse图应该为：2张45度角照片，但是旧版本用的仍旧是非最左2张，非最中2张，非最右2张，如果重启旧的方案unuse.zip图片逻辑要相应的修改掉*/
    //   task = ToothCameraTask(
    //       toothCameraStep: widget.toothCameraStep,
    //       toothCameraType: widget.toothCameraType,
    //       funMap: widget.funMap,
    //       currentOrientation: widget.currentOrientation,
    //       taskDirection: widget.directionEnum,
    //       totalSecond: aniTotalSec);
    // }
    // AudioPlayerUtil.playSound("counter.wav");

    String getScanStartMp3() {
      if (skilledMode) {
        return widget.toothCameraType == ToothCameraTypeEnum.occlusion
            ? "${lang}_scan_bat_start1.mp3"
            : "${lang}_scan_open_start1.mp3";
      } else {
        return "${lang}_scan_lr_start1.mp3";
      }
    }

    intoScanPage() {
      setState(() {
        if (!skilledMode) {
          isCounterEnd = true;
        }
        isPlayedStart = true;
      });
      logger("startScan toothCameraType: ${widget.toothCameraType}");
      switch (widget.toothCameraType) {
        case ToothCameraTypeEnum.occlusion:
        case ToothCameraTypeEnum.openMaskOff:
        case ToothCameraTypeEnum.openMaskOn:
          AudioPlayerUtil.playSound(getScanStartMp3(), onCompleteCallback: () {
            if (!isDisposed) {
              setState(() {
                isCounterEnd = true;
              });
              AudioPlayerUtil.playSound("${lang}_scan_lr_start2.mp3");
              aniControlUI.forward();
              task.start(taskFinishCallBack);
            }
          });
          break;
        case ToothCameraTypeEnum.maxillary:
          AudioPlayerUtil.playSound("${lang}_scan_up1.mp3", onCompleteCallback: () {
            if (!isDisposed) {
              AudioPlayerUtil.playSound("${lang}_scan_up2.mp3");
              aniControlUI.forward();
              task.start(taskFinishCallBack);
            }
          });
          break;
        case ToothCameraTypeEnum.mandibular:
          AudioPlayerUtil.playSound("${lang}_scan_down1.mp3", onCompleteCallback: () {
            if (!isDisposed) {
              AudioPlayerUtil.playSound("${lang}_scan_down2.mp3");
              aniControlUI.forward();
              task.start(taskFinishCallBack);
            }
          });
          break;
      }
    }

    if (skilledMode) {
      intoScanPage();
    } else {
      switch (widget.toothCameraType) {
        case ToothCameraTypeEnum.occlusion:
          AudioPlayerUtil.playSound("${lang}_bat_prepare.mp3", onCompleteCallback: intoScanPage);
          break;
        case ToothCameraTypeEnum.openMaskOff:
        case ToothCameraTypeEnum.openMaskOn:
          AudioPlayerUtil.playSound("${lang}_open_prepare.mp3", onCompleteCallback: intoScanPage);
          break;
        case ToothCameraTypeEnum.maxillary:
        case ToothCameraTypeEnum.mandibular:
          intoScanPage();
          break;
      }
    }
  }

  bool animatePaused = false;

  animateListenFun() {
    if (widget.directionEnum == ScanTaskDirEnum.lr) {
      if (aniUI.value == endSp) {
        if (!isPlayedLeft) {
          isPlayedLeft = true;
          AudioPlayerUtil.playSound("takephoto.wav", onCompleteCallback: () {
            if (!isDisposed) {
              AudioPlayerUtil.playSound(widget.toothCameraType == ToothCameraTypeEnum.occlusion
                  ? "${lang}_scan_lr_right_bat.mp3"
                  : "${lang}_scan_lr_right_open.mp3");
            }
          }, callBackMillsec: 500);
        }
      }
      if (isPlayedLeft) {
        if (aniUI.value <= centerSp) {
          if (!isPlayedCenter) {
            isPlayedCenter = true;
            AudioPlayerUtil.playSound("takephoto.wav", onCompleteCallback: () {
              if (!isDisposed) {
                AudioPlayerUtil.playSound("${lang}_scan_lr_center.mp3");
              }
            }, callBackMillsec: 500);
          }
        }
      }
      if (isPlayedLeft) {
        if (aniUI.value <= startSp) {
          if (!isPlayedEnd) {
            isPlayedEnd = true;
            AudioPlayerUtil.playSound("takephoto.wav");
          }
        }
      }
      if (!isPlayedLeft) {
        progressRate = (aniUI.value - centerSp) / (endSp - centerSp);
      } else {
        progressRate = (aniUI.value - startSp) / (endSp - startSp);
      }
    } else if (widget.directionEnum == ScanTaskDirEnum.toUp) {
      if (aniUI.value <= endSp) {
        if (!isPlayedEnd) {
          isPlayedEnd = true;
          AudioPlayerUtil.playSound("takephoto.wav");
        }
      }
      progressRate = (aniUI.value - startSp) / (endSp - startSp);
    } else {
      if (aniUI.value >= endSp) {
        if (!isPlayedEnd) {
          isPlayedEnd = true;
          AudioPlayerUtil.playSound("takephoto.wav");
        }
      }
      progressRate = (aniUI.value - startSp) / (endSp - startSp);
    }
    logger("startScan progressRate: $progressRate");
    setState(() {});
  }

  animateListenStatusFun(state) {
    if (state == AnimationStatus.completed) {
      setState(() {
        isAnimateDone = true;
        if (isGetTaskResult == -1) {
          Global.showGlobalLoading(seconds: 20);
        }
      });
    }
  }

  taskFinishCallBack(resultObj) async {
    logger("taskFinishCallBack $resultObj", key: "[CameraImg]");
    uploadZip(resultObj);
  }

  uploadZip(resultObj) {
    //组装图片和zip并上传
    List imgPaths = resultObj["imgPaths"];
    List useIndexArray = resultObj["useIndexArray"];
    List useErrorInfos = resultObj["useErrorInfos"];
    List noUseIndexArray = resultObj["noUseIndexArray"];
    List useFrameIndexArray = resultObj["useFrameIndexArray"];
    List noUseFrameIndexArray = resultObj["noUseFrameIndexArray"];
    if (kDebugMode) {
      print("uploadZip imgPaths:$imgPaths");
      print("uploadZip useIndexArray:$useIndexArray");
      print("uploadZip useErrorInfos:$useErrorInfos");
      print("uploadZip noUseIndexArray:$noUseIndexArray");
      print("uploadZip useFrameIndexArray:$useFrameIndexArray");
      print("uploadZip noUseFrameIndexArray:$noUseFrameIndexArray");
    }

    //显示所有扫描出来的结果图片
    //Global.showBottomListImageByPaths(context, resultObj["imgPaths"]);

    getTemporaryDirectory().then((Directory appDocDirectory) async {
      // String dirPath = "${appDocDirectory.path}/${getCurrentTimelineId()}";
      // File file = File("$dirPath/");
      // if (!file.existsSync()) {
      //   file.create(recursive: true);
      // }
      //
      // //use 打包
      String zipNameFlag = Map.from({
        ToothCameraTypeEnum.occlusion: "lr_close",
        ToothCameraTypeEnum.openMaskOff: "lr_open",
        ToothCameraTypeEnum.openMaskOn: "lr_mask",
        ToothCameraTypeEnum.maxillary: "up",
        ToothCameraTypeEnum.mandibular: "down",
      })[widget.toothCameraType];
      // if (scanLight) {
      //   zipNameFlag = "plaque_$zipNameFlag";
      // }

      String picOtherInfo = widget.funMap["getPicOtherInfo"]();

      // String useZiperName = "${zipNameFlag}_use.zip";
      // String useZiperPath = "$dirPath/$useZiperName";
      //
      // String unUseZipName = "${zipNameFlag}_unuse.zip";
      // String unUseZiperPath = "$dirPath/$unUseZipName";
      //
      // var useZiper = ZipFileEncoder();
      // useZiper.create(useZiperPath);
      List tempShowUseImgs = [];
      List tempShowUnuseImgs = [];

      // if (isScanByUltra(scanTool)) {
      //   await TFLiteUtils.resetCreateTFLiteInstance(ToothTaskTypeEnum.clarityIncrease);
      // }

      for (int i = 0; i < useIndexArray.length; i++) {
        String picOtherInfo = widget.funMap["getPicOtherInfo"](); //之前下标标识-zip标识-截取帧数下标-错误记录-设备信息相关-时间戳
        int firstFlag = i + 1;
        if (widget.directionEnum != ScanTaskDirEnum.lr) {
          firstFlag = 2;
        }
        String picNewName = "$firstFlag-$zipNameFlag-${useFrameIndexArray[i]}-${useErrorInfos[i]}-$picOtherInfo";
        eventBus.fire(EventScanLog("uploadZip picNewName:$picNewName"));
        String newPath = await Global.renameImgFile(imgPaths[useIndexArray[i]], picNewName);

        if (isEmpty(newPath)) {
          setState(() {
            isZipError = true;
          });
          Global.hideGlobalLoading();
          return;
        }

        logger("uploadZip ffiColorTransform:$newPath");
        //色差处理
        ffiColorTransform(newPath);

        // if (isScanByUltra(scanTool)) {
        //   await TFLiteUtils.clarityIncrease(newPath);
        // }

        //压缩
        //await Global.compressImgByPath(newPath);
        tempShowUseImgs.add(newPath);
        // useZiper.addFile(File(newPath));
      }
      // await TFLiteUtils.resetCreateTFLiteInstance(ToothTaskTypeEnum.toothDetection);

      //配合算法组这里我们up down的时候use & unuse两张都放在一起
      if (widget.directionEnum != ScanTaskDirEnum.lr) {
        int firstFlag = 1;
        String picNewName = "$firstFlag-$zipNameFlag-${noUseFrameIndexArray[0]}-x-$picOtherInfo";
        String newPath = await Global.renameImgFile(imgPaths[noUseIndexArray[0]], picNewName);
        if (isEmpty(newPath)) {
          setState(() {
            isZipError = true;
          });
          Global.hideGlobalLoading();
          return;
        }

        //色差处理
        ffiColorTransform(newPath);
        //压缩
        // await Global.compressImgByPath(newPath);

        // tempShowUseImgs.add(newPath);
        tempShowUnuseImgs.add(newPath);
        // useZiper.addFile(File(newPath));
      }

      // useZiper.close();

      //配合算法组这里我们up down的时候use & unuse两张都放在一起
      if (widget.directionEnum == ScanTaskDirEnum.lr) {
        // var unUseZiper = ZipFileEncoder();
        // unUseZiper.create(unUseZiperPath);
        for (int i = 0; i < noUseIndexArray.length; i++) {
          //之前下标标识-zip标识-截取帧数下标-错误记录-设备信息相关-时间戳
          int firstFlag = i + 1;
          if (widget.directionEnum != ScanTaskDirEnum.lr) {
            firstFlag = 1;
          }
          String picNewName = "$firstFlag-$zipNameFlag-${noUseFrameIndexArray[i]}-x-$picOtherInfo";
          String newPath = await Global.renameImgFile(imgPaths[noUseIndexArray[i]], picNewName);
          if (isEmpty(newPath)) {
            setState(() {
              isZipError = true;
            });
            Global.hideGlobalLoading();
            return;
          }

          //色差处理
          ffiColorTransform(newPath);
          //压缩
          // await Global.compressImgByPath(newPath);
          tempShowUnuseImgs.add(newPath);
          // unUseZiper.addFile(File(newPath));
        }
        // unUseZiper.close();
      }
      eventBus.fire(EventScanLog("tempShowUseImgs: $tempShowUseImgs, tempShowUnuseImgs: $tempShowUnuseImgs"));
      widget.updateStateByType("updateScanImgs", {
        "tempShowUseImgs": tempShowUseImgs,
        "tempShowUnuseImgs": tempShowUnuseImgs,
        "uploadFinishCb": () {
          setState(() {
            bool isOk = true;
            eventBus.fire(EventScanLog("useErrorInfos: $useErrorInfos"));
            resImages = tempShowUseImgs;
            errorCodes = useErrorInfos;
            for (int i = 0; i < errorCodes.length; i++) {
              if (isUltraLightError(errorCodes[i], resImages[i])) {
                errorCodes[i] = 0;
              }
            }
            for (int i = 0; i < errorCodes.length; i++) {
              if (errorCodes[i] != 0) {
                isOk = false;
                break;
              }
            }
            isGetTaskResult = isOk ? 1 : 0; //1= 没问题，0=有问题
            AudioPlayerUtil.playSound("confirm.wav", onCompleteCallback: () {
              if (widget.isLastStep && isOk && !isDisposed) {
                AudioPlayerUtil.playSound("${lang}_congratulations.mp3");
              }
            }, callBackMillsec: 1000);
            Global.hideGlobalLoading();
          });
        },
        // "useZiperName": useZiperName,
        // "useZiperPath": useZiperPath,
        // "unUseZipName": unUseZipName,
        // "unUseZiperPath": unUseZiperPath,
        "isLight": scanLight,
        "toothCameraStep": widget.toothCameraStep,
        "toothCameraType": widget.toothCameraType,
      });
    });
  }

  doDispose() {
    if (!isDisposed) {
      isDisposed = true;
      logger("ToothCameraTaskWidget dispose");
      Global.hideGlobalLoading();
      AudioPlayerUtil.stopSound();
      aniUI.removeListener(animateListenFun);
      aniUI.removeStatusListener(animateListenStatusFun);
      aniControlUI.dispose();
      task.stop();

      if (counterTimer != null) {
        counterTimer!.cancel();
        counterTimer = null;
      }
    }
  }

  @override
  void dispose() {
    isGetTaskResult = -1;
    doDispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  restartScan() {
    // isPlayedStart = false;
    // isPlayedCenter = false;
    // isPlayedEnd = false;
    //
    // AudioPlayerUtil.playSound("counter.wav");
    // setState(() {
    //   isAnimateDone = false;
    //   isGetTaskResult = -1;
    //   aniControllUI.reset();
    //   isCounterEnd = false;
    //   restartTimer();
    // });

    // List<String> errorIndexReason = [];
    // for (int i = 0; i < resImages.length; i++) {
    //   if (errorCodes[i] != 0) {
    //     errorIndexReason
    //         .add("${i + 1} - ${Global.getAlertTextByScanErrorCode(errorCodes[i], resImages[i])["content"]}");
    //   }
    // }
    // Map<String, dynamic> param = {"value5": "process"};
    // if (isNotEmpty(errorIndexReason)) {
    //   param["value3"] = "$errorIndexReason";
    // }
    // sendScanEvent("rescan", getRecordEventStepEnumTitle(widget.toothCameraType), param);
    //给用户更多的时间准备返回上一页
    if (skilledMode) {
      widget.funMap["intoNextStepEnum"](offsetNum: 0, rescan: true);
    } else {
      widget.funMap["intoNextStepEnum"](offsetNum: -1);
    }
  }

  Widget getCountDownUI() {
    return Global.getBlurWidget(Container(
        alignment: Alignment.center,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(47.sp),
              width: 136.sp,
              height: 136.sp,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(600.sp),
                border: Border.all(color: Colors.white, width: 6.sp),
              ),
            ),
            Text("$counter",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 64.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                )),
          ],
        )));
  }

  Widget getZipErrorUi() {
    return isZipError
        ? Container(
            width: 1.sw,
            height: 1.sh,
            color: Colors.black.withOpacity(0.4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  Lang.zip_error,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 32.sp),
                HCustomButton(
                    fontSize: 20.sp,
                    onPress: restartScan,
                    text: Lang.retake_current_record,
                    textColor: color2B,
                    width: 224.sp,
                    height: 54.sp,
                    bgColor: Colors.white),
              ],
            ),
          )
        : const SizedBox();
  }

  Widget getWarmUpUI() {
    String showImage;
    String showText;
    switch (widget.toothCameraType) {
      case ToothCameraTypeEnum.occlusion:
        showImage = "res/scan/checkBlur2Close.png";
        showText = Lang.natural_bite;
        break;
      case ToothCameraTypeEnum.openMaskOff:
        showImage = "res/scan/checkBlur2Open.png";
        showText = Lang.gently_open_maxillary;
        break;
      case ToothCameraTypeEnum.openMaskOn:
        showImage = "res/scan/checkBlur2Mask.png";
        showText = Lang.wear_mask_open_maxillary;
        break;
      default:
        return const SizedBox();
    }

    return Container(
      width: 1.sw,
      height: 1.sh,
      color: Colors.black.withOpacity(0.4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            showImage,
            width: 560.sp,
            height: 350.sp,
            fit: BoxFit.contain,
          ),
          SizedBox(height: 6.sp),
          Text(showText,
              style: TextStyle(
                fontSize: 30.sp,
                color: Colors.white,
                fontWeight: FontWeight.w500,
                height: 2,
              )),
        ],
      ),
    );
  }

  Widget getTextItem({
    required String text,
    bool reversed = false,
    bool isRead = false,
    int quarterTurns = 0,
  }) {
    // List<Widget> widgets = [
    //   Container(
    //     width: 8.sp,
    //     height: 8.sp,
    //     decoration: BoxDecoration(
    //       shape: BoxShape.circle,
    //       color: !isRead ? color7C : Colors.white,
    //     ),
    //   ),
    //   SizedBox(width: 4.sp),
    //   Text(text,
    //       style: TextStyle(
    //         fontSize: 18.sp,
    //         fontWeight: FontWeight.w400,
    //         color: !isRead ? Colors.black : Colors.white,
    //       )),
    // ];
    // return Transform(
    //   alignment: Alignment.center,
    //   transform: (isScanByUltra(scanTool) || widget.isShowMirrorUI) && quarterTurns == 0
    //       ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0)
    //       : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
    //   child: RotatedBox(
    //     quarterTurns: quarterTurns,
    //     child: Column(
    //       mainAxisSize: MainAxisSize.min,
    //       crossAxisAlignment: CrossAxisAlignment.center,
    //       children: !reversed ? widgets : widgets.reversed.toList(),
    //     ),
    //   ),
    // );
    return Container(
      width: 20.sp,
      height: 20.sp,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: !isRead ? color7C : Colors.white,
      ),
    );
  }

  Widget getProgressIndicator({
    required double value,
    required double width,
    required double height,
    required String icon,
    bool reverse = false,
    dynamic leftLabel,
    dynamic rightLabel,
    dynamic centerLabel,
    int labelQuarterTurns = 0,
  }) {
    assert(value >= 0 && value <= 1);
    bool isReadLeft = !reverse ? true : false;
    bool isReadRight = !reverse ? false : true;
    bool isReadCenter = !reverse ? value >= 0.5 : value <= 0.5;
    return Stack(
      children: [
        Container(
          height: height,
          width: width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.sp),
            color: Colors.white.withOpacity(0.5),
          ),
        ),
        Positioned(
          left: reverse ? null : 0,
          right: reverse ? 0 : null,
          bottom: 0,
          child: Container(
            height: height,
            width: (width - height) * (!reverse ? value : (1 - value)) + height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50.sp),
              color: colorCyan,
            ),
          ),
        ),
        leftLabel == null
            ? const SizedBox()
            : Positioned.fill(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: EdgeInsets.only(left: 40.sp),
                    child: getTextItem(text: leftLabel, isRead: isReadLeft, quarterTurns: labelQuarterTurns),
                  ),
                ),
              ),
        rightLabel == null
            ? const SizedBox()
            : Positioned.fill(
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: 40.sp),
                    child: getTextItem(text: rightLabel, isRead: isReadRight, quarterTurns: labelQuarterTurns),
                  ),
                ),
              ),
        centerLabel == null
            ? const SizedBox()
            : Positioned.fill(
                child: Align(
                  alignment: Alignment.center,
                  child: getTextItem(text: centerLabel, isRead: isReadCenter, quarterTurns: labelQuarterTurns),
                ),
              ),
        Positioned(
          left: (width - height) * value,
          bottom: 0,
          child: Image.asset(icon, height: height),
        ),
      ],
    );
  }

  Widget getScanHorizontalUI() {
    double lineWidth = 8.sp;
    double lineLeft = aniUI.value - lineWidth / 2;

    return Center(
      child: SizedBox(
        width: 1.sw,
        height: 1.sh,
        child: Transform(
          alignment: Alignment.center,
          transform: selectScanByIscan(scanTool) && widget.isShowMirrorUI || selectScanByUltra(scanTool)
              ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0)
              : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
          child: Stack(
            children: [
              SizedBox(width: 1.sw, height: 1.sh),
              Positioned(
                left: lineLeft,
                top: 0,
                child: Image.asset(
                  "res/scan/scan_line_vertical.png",
                  height: 1.sh,
                  width: lineWidth,
                  fit: BoxFit.fill,
                ),
              ),
              !isPlayedLeft
                  ? Positioned(
                      right: 120.sp,
                      bottom: 64.sp,
                      child: getProgressIndicator(
                        icon: "res/scan/scan_arrow_right.png",
                        value: progressRate,
                        width: 0.5.sw - 70.sp,
                        height: 100.sp,
                        rightLabel: Lang.left_tooth,
                      ),
                    )
                  : Positioned(
                      right: 120.sp,
                      bottom: 64.sp,
                      child: getProgressIndicator(
                        icon: "res/scan/scan_arrow_left.png",
                        value: progressRate,
                        width: 1.sw - 240.sp,
                        height: 100.sp,
                        reverse: true,
                        leftLabel: Lang.right_tooth,
                        rightLabel: Lang.left_tooth,
                        centerLabel: Lang.center_tooth,
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getScanVerticalUI({bool isScanUp = true}) {
    double lineWidth = 1.sw - 320.sp;
    double lineHeight = 8.sp;
    double lineTop = aniUI.value - lineHeight / 2;

    return Center(
      child: SizedBox(
        width: 1.sw,
        height: 1.sh,
        child: Stack(
          children: [
            Container(color: Colors.transparent, width: 1.sw, height: 1.sh),
            Center(
              child: Image.asset(
                isScanUp ? "res/scan/maxillary_outline.png" : "res/scan/mandibular_outline.png",
                width: 840.sp,
                height: 502.sp,
              ),
            ),
            Positioned(
              left: 168.sp,
              top: lineTop,
              child: Image.asset(
                "res/scan/scan_line_horizontal.png",
                width: lineWidth,
                height: lineHeight,
                fit: BoxFit.fill,
              ),
            ),
            Positioned(
              left: 160.sp,
              bottom: 64.sp,
              child: RotatedBox(
                quarterTurns: isScanUp ? 3 : 1,
                child: getProgressIndicator(
                  icon: "res/scan/scan_arrow_right.png",
                  value: progressRate,
                  width: 1.sh - 128.sp,
                  height: 100.sp,
                  rightLabel: isScanUp ? Lang.maxillary : Lang.mandibular,
                  labelQuarterTurns: isScanUp ? 1 : 3,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget getImageRejectHelpUI(String title, String content, String image) {
    return StatefulBuilder(builder: (BuildContext context, StateSetter setSheetState) {
      bool isMirror = widget.isShowMirrorUI;
      return Transform(
        alignment: Alignment.center,
        transform: isMirror ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0) : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 20.sp, width: double.infinity),
                  Padding(
                    padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                    child: Text(title,
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w500,
                        )),
                  ),
                  SizedBox(height: 12.sp),
                  Padding(
                    padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                    child: Text(content,
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w400,
                          color: color7C,
                        )),
                  ),
                  SizedBox(height: 12.sp),
                  Image.asset(
                    image,
                    width: 400.sp,
                    height: 1.sh - 150.sp,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(height: 20.sp),
                ],
              ),
            ),
            Positioned(
              top: 24.sp,
              right: 24.sp,
              child: GestureDetector(
                onTap: () => {Navigator.pop(context)},
                child: Image.asset("res/icons/close_help.png", width: 20.sp),
              ),
            ),
            selectScanByIscan(scanTool)
                ? Positioned(
                    right: 25.sp,
                    bottom: 0.sp,
                    child: GestureDetector(
                      onTap: () {
                        widget.mirrorUI();
                        setSheetState(() {
                          isMirror = !isMirror;
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.only(top: 24.sp, right: 24.sp, bottom: 24.sp),
                        child: Container(
                          decoration: BoxDecoration(boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              spreadRadius: 0,
                              blurRadius: 21.sp,
                              offset: const Offset(0, 1),
                            )
                          ]),
                          child: Image.asset("res/icons/scan_mirror.png", width: 42.sp, height: 42.sp),
                        ),
                      ),
                    ),
                  )
                : const SizedBox(),
          ],
        ),
      );
    });
  }

  Widget getShowResultUI() {
    return Stack(
      children: [
        Container(
          width: 1.sw,
          height: 1.sh,
          decoration: const BoxDecoration(
            color: Color(0xFF333333),
          ),
        ),
        Container(
          width: double.infinity,
          padding: EdgeInsets.only(top: 21.sp, bottom: 96.sp),
          child: Swiper(
            controller: swiperController,
            itemBuilder: (BuildContext context, int index) {
              Map alertObj = Global.getAlertTextByScanErrorCode(errorCodes[index], resImages[index]);
              showRejectHelp() {
                List<String> errorIndexReason = [];
                for (int i = 0; i < resImages.length; i++) {
                  if (errorCodes[i] != 0) {
                    errorIndexReason.add("${i + 1} - ${alertObj["content"]}");
                  }
                }
                Map<String, dynamic> param = {};
                if (isNotEmpty(errorIndexReason)) {
                  param["value3"] = "$errorIndexReason";
                }
                sendScanEvent("error_help", getRecordEventStepEnumTitle(widget.toothCameraType), param);

                Global.showBottomModal(
                    context,
                    getImageRejectHelpUI(
                      Lang.resolve_action,
                      alertObj["fun"],
                      getScanErrorTeachImg(errorCodes[index], widget.toothCameraType, selectScanByUltra(scanTool)),
                    ),
                    maxHeight: 1.sh - 33.sp);
              }

              return Container(
                width: 1077.sp,
                height: 606.sp,
                margin: EdgeInsets.fromLTRB(0, 180.sp, 0, 150.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.sp),
                  image: DecorationImage(
                    image: FileImage(
                      File(resImages[index]),
                    ),
                    fit: BoxFit.fill,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.sp),
                  child: Stack(
                    children: [
                      // Container(
                      //   width: double.infinity,
                      //   height: 64.sp,
                      //   decoration: const BoxDecoration(
                      //       gradient: LinearGradient(
                      //     colors: [Color(0x80283143), Color(0x00000000)],
                      //     begin: Alignment.topCenter,
                      //     end: Alignment.bottomCenter,
                      //   )),
                      // ),
                      checkScanQuality && !scanLight
                          ? Positioned(
                              top: 32.sp,
                              right: 32.sp,
                              child: GestureDetector(
                                onTap: () {
                                  if (errorCodes[index] == 0) return;
                                  // showRejectHelp();
                                },
                                child: Image.asset(
                                  errorCodes[index] == 0 ? "res/icons/pic_accept.png" : "res/icons/pic_reject.png",
                                  width: 80.sp,
                                  height: 80.sp,
                                ),
                              ),
                            )
                          : const SizedBox(),
                      errorCodes[index] == 0
                          ? const SizedBox()
                          : Positioned(
                              child: Container(
                                padding: EdgeInsets.fromLTRB(48.sp, 28.sp, 48.sp, 18.sp),
                                child: Align(
                                  alignment: Alignment.topCenter,
                                  child: Text(alertObj["content"],
                                      style: TextStyle(
                                        fontSize: 40.sp,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ),
                      // errorCodes[index] == 0
                      //     ? const SizedBox()
                      //     : Positioned(
                      //         right: 16.sp,
                      //         bottom: 16.sp,
                      //         child: GestureDetector(
                      //           onTap: () {
                      //             showRejectHelp();
                      //           },
                      //           child: Image.asset("res/icons/scan_help.png",
                      //               width: 32.sp, height: 32.sp),
                      //         ),
                      //       ),
                    ],
                  ),
                ),
              );
            },
            onTap: (index) {
              swiperController.move(index);
            },
            itemCount: resImages.length,
            index: curShowImage != -1 ? curShowImage : 0,
            viewportFraction: 0.6,
            scale: 0.84,
            loop: false,
            onIndexChanged: (index) {
              curShowImage = index;
            },
          ),
        ),
        showGreat() ? const GreatView() : const SizedBox(),
        Padding(
          padding: EdgeInsets.only(bottom: 48.sp),
          child: Align(
              alignment: Alignment.bottomCenter,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  HCustomButton(
                      fontSize: 40.sp,
                      onPress: restartScan,
                      text: Lang.retake_current_record,
                      textColor: color2B,
                      width: 360.sp,
                      height: 100.sp,
                      bgColor: Colors.white),
                  SizedBox(width: 20.sp),
                  HCustomButton(
                      fontSize: 40.sp,
                      onPress: () {
                        widget.funMap["eachScanFinishIntoNextAction"]();

                        List<String> errorIndexReason = [];
                        for (int i = 0; i < resImages.length; i++) {
                          if (errorCodes[i] != 0) {
                            errorIndexReason.add(
                                "${i + 1} - ${Global.getAlertTextByScanErrorCode(errorCodes[i], resImages[i])["content"]}");
                          }
                        }
                        Map param = {};
                        if (isNotEmpty(errorIndexReason)) {
                          param["value3"] = "$errorIndexReason";
                        }
                        sendScanEvent("next_step", getRecordEventStepEnumTitle(widget.toothCameraType), param);
                      },
                      borderColor: Colors.white,
                      borderWidth: 2.sp,
                      text: widget.isLastStep ? Lang.complete_scan : Lang.direct_to_next,
                      width: 360.sp,
                      height: 100.sp,
                      ms: 5000,
                      bgColor: Colors.transparent),
                ],
              )),
        ),
      ],
    );
  }

  bool showGreat() {
    if (!checkScanQuality || scanLight) {
      return false;
    }
    for (int i = 0; i < errorCodes.length; i++) {
      if (errorCodes[i] != 0) {
        return false;
      }
    }
    logger("isResultAllRight $errorCodes");
    return true;
  }

  Widget getScanUI() {
    switch (widget.directionEnum) {
      case ScanTaskDirEnum.lr:
        return getScanHorizontalUI();
      case ScanTaskDirEnum.toUp:
        return getScanVerticalUI(isScanUp: true);
      case ScanTaskDirEnum.toDown:
        return getScanVerticalUI(isScanUp: false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isGetTaskResult != -1) return getShowResultUI();

    // bool step1ImgCanShow = isCounterEnd;
    // bool step2ImgCanShow = aniUI.value >=
    //     startSp + eachBottomRectImgWidth * 1.5 + eachBottomRectArrowWidth;
    // bool step3ImgCanShow = aniUI.value >=
    //     startSp + eachBottomRectImgWidth * 3 + eachBottomRectArrowWidth * 2;
    //
    // String stepShowImgFlag =
    //     widget.toothCameraStep == ToothCameraStepEnum.intoScanClose
    //         ? "close"
    //         : widget.toothCameraStep == ToothCameraStepEnum.intoScanOpen
    //             ? "open"
    //             : "mask";

    //if (!isCounterEnd) return getCountDownUI();
    if (skilledMode) {
      return isCounterEnd
          ? Stack(
              children: [
                getScanUI(),
                getZipErrorUi(),
              ],
            )
          : Stack(
              children: [
                getScanUI(),
                getWarmUpUI(),
                getZipErrorUi(),
              ],
            );
    } else {
      return isCounterEnd
          ? Stack(
              children: [
                getScanUI(),
                getZipErrorUi(),
              ],
            )
          : getWarmUpUI();
    }
  }
}
