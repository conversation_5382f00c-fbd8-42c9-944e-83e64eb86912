import 'dart:convert';
import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/db/record_db.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/xianzong_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/highlight_text.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/usb_save_view.dart';
import 'package:mooeli_ffi/bindings_ffi.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:path_provider/path_provider.dart';
import 'package:synchronized/synchronized.dart';
import 'package:uuid/uuid.dart';

bool hideMask = true; //是否隐藏牙套组

bool scanLight = false; //是否使用Ultra Light
bool scanLocalPlaque = false;

bool skilledMode = true;

bool checkScanQuality = true;

bool scanShowIp = false;

int showBatteryAlert = 100;

int MAX_SCAN_SIZE = 50;

///1 - 口采,  2 - 初诊
int scanMode = 0;
String scanPageName = "ScanInfoRecord";

initScanMode(int mode) {
  scanMode = mode;
  switch (mode) {
    case 1:
      scanPageName = "ScanInfoRecord";
      break;
    case 2:
      scanPageName = "DiagnoseInfoRecord";
      break;
  }
}

const int SCAN_TOOL_ULTRA = 1;
const int SCAN_TOOL_ISCAN = 2;
const int SCAN_TOOL_LIGHT = 3;

String localSingleCampaignId = "ISCANBOT123456";

bool selectScanByUltra(int? tool) {
  return true; //tool == SCAN_TOOL_ULTRA || tool == SCAN_TOOL_LIGHT;
}

bool selectScanByIscan(int? tool) {
  return tool == SCAN_TOOL_ISCAN;
}

bool selectScanByLight(int? tool) {
  return tool == SCAN_TOOL_LIGHT;
}

Future<List<ScanActivity>> getLocalActivityList() async{
  final result = await recordDbHelper.getScanActivityAll();
  // List<String> listCache = Global.sharedPrefs.getStringList("local_activity_list") ?? [];
  // List<ScanActivity> list = listCache.map((str) => ScanActivity.fromJson(jsonDecode(str))).toList();
  logger("getLocalActivityList ${result.map((e) => "${e.toDb()}").join(', ')}");
  return result;
}

Future<ScanActivity?> getLocalActivity(String campaignId) async{
  // List<ScanActivity> list = getLocalActivityList();
  // for (int i = 0; i < list.length; i++) {
  //   if (list[i].campaignId == campaignId) {
  //     return list[i];
  //   }
  // }
  // return null;
  return await recordDbHelper.getScanActivityById(campaignId);
}

addLocalActivity(ScanActivity activity) {
  activity.campaignId = const Uuid().v4().replaceAll("-", "");
  recordDbHelper.insertScanActivity(activity);
  // List<ScanActivity> list = getLocalActivityList();
  // list.insert(0, activity);
  // List<String> saveCache = list.map((activity) => jsonEncode(activity.toJson())).toList();
  // Global.sharedPrefs.setStringList("local_activity_list", saveCache);
  // logger("addLocalActivity $saveCache");
}

updateLocalActivity(ScanActivity activity) {
  // List<ScanActivity> list = getLocalActivityList();
  // for (int i = 0; i < list.length; i++) {
  //   if (list[i].campaignId == activity.campaignId) {
  //     list[i] = activity;
  //     logger("updateLocalActivity ${activity.campaignName} - ${list[i].campaignName}");
  //     break;
  //   }
  // }
  // List<String> saveCache = list.map((activity) => jsonEncode(activity.toJson())).toList();
  // Global.sharedPrefs.setStringList("local_activity_list", saveCache);
  recordDbHelper.updateScanActivity(activity);
  logger("updateLocalActivity ${activity.toDb()}");
}

deleteLocalActivity(ScanActivity activity) async{
  // List<ScanActivity> list = getLocalActivityList();
  // logger("deleteLocalActivity ${activity.toJson()}");
  // for (int i = list.length - 1; i >= 0; i--) {
  //   if (list[i].campaignId == activity.campaignId) {
  //     list.removeAt(i);
  //     break;
  //   }
  // }
  // List<String> saveCache = list.map((activity) => jsonEncode(activity.toJson())).toList();
  return recordDbHelper.deleteScanActivityByCampaignIds([activity.campaignId]);
}

deleteLocalActivities(List<ScanActivity> activities) async{
  // List<ScanActivity> list = getLocalActivityList();
  // logger("deleteLocalActivity ${activity.toJson()}");
  // for (int i = list.length - 1; i >= 0; i--) {
  //   if (list[i].campaignId == activity.campaignId) {
  //     list.removeAt(i);
  //     break;
  //   }
  // }
  // List<String> saveCache = list.map((activity) => jsonEncode(activity.toJson())).toList();
  logger("updateLocalActivity ${activities.map((e) => e.toDb()).join(',')}");
  return recordDbHelper.deleteScanActivityByCampaignIds(activities.map((activity) => activity.campaignId).toList());
}

Future<List<ScanInfo>> getAllRecordList(String caseId, [bool singmeCampaign = false]) async {
  List<ScanInfo> list = await recordDbHelper.getRecordList(caseId, includeComplete: true);
  if (singmeCampaign && caseId != localSingleCampaignId) {
    List<ScanInfo> localList = await recordDbHelper.getRecordList(localSingleCampaignId);
    if (isNotEmpty(localList)) {
      list.addAll(localList);
    }
  }
  list.sort((a, b) {
    return b.id - a.id;
  });
  logger("getAllRecordList recordList: ${list.map((e) => "${e.recordName}-${e.status}").toList()}");
  return list;
}

Future<List<ScanInfo>> getSearchRecordList(String caseId, String keyword, [bool singmeCampaign = false]) async {
  List<ScanInfo> list = [];
  if (singmeCampaign && caseId != localSingleCampaignId) {
    List<ScanInfo> localList = await recordDbHelper.getRecordList(localSingleCampaignId, searchWord: keyword);
    if (isNotEmpty(localList)) {
      list.addAll(localList);
    }
  }
  List<ScanInfo> recordingList = await recordDbHelper.getRecordList(caseId, searchWord: keyword);
  if (isNotEmpty(recordingList)) {
    list.addAll(recordingList);
  }
  list.sort((a, b) {
    return b.id - a.id;
  });
  logger("getSearchRecordList recordList: ${list.map((e) => "${e.recordName}-${e.status}").toList()}");
  return list;
}

Future<List<ScanInfo>> getUploadRecordList(String caseId, [bool singmeCampaign = false]) async {
  List<ScanInfo> list = [];
  if (singmeCampaign && caseId != localSingleCampaignId) {
    List<ScanInfo> localList = await recordDbHelper.getRecordList(localSingleCampaignId);
    if (isNotEmpty(localList)) {
      list.addAll(localList);
      for (ScanInfo localRecord in localList) {
        saveRecordToDb(caseId, localRecord);
      }
    }
  }
  List<ScanInfo> recordingList = await recordDbHelper.getRecordList(caseId);
  if (isNotEmpty(recordingList)) {
    list.addAll(recordingList);
  }
  list.sort((a, b) {
    return b.id - a.id;
  });
  logger("getUploadRecordList recordList: ${list.map((e) => "${e.recordName}-${e.status}").toList()}");
  return list;
}

saveArrayStatusInAllRecord(String caseId, List<String> recordIds, RecordStatus status) async {
  List<ScanInfo> list = await getAllRecordList(caseId, true);
  for (ScanInfo r in list) {
    if (recordIds.contains(r.recordId)) {
      r.status = status;
      saveRecordToDb(caseId, r);
      if (status == RecordStatus.completeUpload) {
        Global.deleteExternalImages("scan_${r.recordId}");
      }
    }
  }
}

deleteRecords(List<String> recordIds) async {
  for (String recordId in recordIds) {
    await recordDbHelper.deleteRecord(recordId);
    Global.deleteExternalImages("scan_$recordId");
  }
}

ScanInfo saveRecord = ScanInfo();
int saveTimestamp = 0;

Future<ScanInfo?> getRecordById(String recordId) async {
  ScanInfo? record = await recordDbHelper.getRecordById(recordId);
  return record;
}

saveRecordToDb(String caseId, ScanInfo record) async {
  if (!record.hasData()) {
    return;
  }
  int time = DateTime.now().millisecondsSinceEpoch;
  if (time - saveTimestamp < 1000 && saveRecord.toDbJson() == record.toDbJson()) {
    return;
  }
  saveRecord = record;
  saveTimestamp = time;
  record.campaignId = caseId;
  if (isEmpty(record.recordId)) {
    record.recordId = const Uuid().v4().replaceAll("-", "");
    record.customTime = DateTime.now().millisecondsSinceEpoch;
    recordDbHelper.insertRecord(record);
  } else {
    ScanInfo? r = await recordDbHelper.getRecordById(record.recordId);
    if (r != null) {
      recordDbHelper.updateRecord(record);
    } else {
      recordDbHelper.insertRecord(record);
    }
  }
  saveAllRecordFile(caseId, [record]);
  saveAllRecordFile(caseId, [record], isDocument: true);
}

int saveAllFileTime = 0;

void saveAllRecordFile(String caseId, List<ScanInfo> list, {bool isDocument = false}) async {
  int time = DateTime.now().millisecondsSinceEpoch;
  if (time - saveAllFileTime < 2000) {
    return;
  }
  saveAllFileTime = time;
  try {
    Directory? externalDir =
        isDocument ? await getApplicationDocumentsDirectory() : await getApplicationSupportDirectory();
    Directory saveDir = Directory('${externalDir!.path}/scan_data');
    if (!saveDir.existsSync()) {
      await saveDir.create(recursive: true);
    }
    File file = File('${saveDir!.path}/${currentUser.personId}_$caseId.txt');
    List originList = [];
    if (file.existsSync()) {
      String str = await file.readAsString();
      Map json = jsonDecode(str);
      originList = json["list"];
      logger("saveAllRecordFile read length: ${originList.length}");
    }
    for (ScanInfo record in list) {
      bool contain = false;
      for (int i = 0; i < originList.length; i++) {
        if (record.recordId == ScanInfo.fromJson(originList[i]).recordId) {
          originList[i] = record.toJson();
          logger("saveAllRecordFile modify: ${record.recordName} ${record.status}");
          contain = true;
          break;
        }
      }
      if (!contain) {
        logger("saveAllRecordFile add: ${record.recordName}");
        originList.add(record.toJson());
      }
    }

    logger("saveAllRecordFile save length: ${originList.length}");
    Map map = {
      "caseId": caseId,
      "list": originList,
    };
    file.writeAsStringSync(jsonEncode(map));
  } catch (ex) {
    logger("saveAllRecordFile error: ${ex.toString()}");
  }
}

Future<List<ScanInfo>> readAllRecordFile(String caseId, {bool isDocument = false}) async {
  List<ScanInfo> list = [];
  try {
    Directory? externalDir =
        isDocument ? await getApplicationDocumentsDirectory() : await getApplicationSupportDirectory();
    Directory saveDir = Directory('${externalDir!.path}/scan_data');
    File file = File('${saveDir!.path}/${currentUser.personId}_$caseId.txt');
    if (file.existsSync()) {
      String str = await file.readAsString();
      Map json = jsonDecode(str);
      list = (json["list"] as List).map((e) => ScanInfo.fromJson(e)).toList();
      logger("readAllRecordFile read length: $isDocument ${list.length}");
    }
  } catch (ex) {
    logger("readAllRecordFile error: $isDocument ${ex.toString()}");
  }
  return list;
}

void filterRecord(List<ScanInfo> records, List<ScanInfo> list) {
  if (isNotEmpty(records) && isNotEmpty(list)) {
    for (int i = records.length - 1; i >= 0; i--) {
      for (int j = 0; j < list.length; j++) {
        if (list[j].recordId == records[i].recordId || list[j].customTime == records[i].customTime) {
          records.removeAt(i);
          break;
        }
      }
    }
  }
}

bool checkUltraIsLight() {
  UltraModel model = ffiGetUltraDeviceInfo();
  logger("checkUltraIsLight ${model.manufacturer} ${model.hardware_version} ${model.software_version}");
  return model.hardware_version > 80; //TODO chenxb
}

String getTypeByFile(String fileName) {
  Map typeMap = {
    "2-up": "108",
    "1-lr_close": "105",
    "2-lr_close": "103",
    "3-lr_close": "107",
    "1-lr_open": "152",
    "2-lr_open": "151",
    "3-lr_open": "153",
    "2-down": "106",
    "2-lr_mask": "154",
    "1-lr_mask": "155",
    "3-lr_mask": "156",
    "1-face100": "100",
    "1-face101": "101",
    "1-face102": "102",
    "1-face113": "113",
    "1-face116": "116",
    "1-face118": "118",
    "1-face119": "119",
  };
  for (String key in typeMap.keys) {
    if (fileName.startsWith(key)) {
      return typeMap[key];
    }
  }
  return "";
}

List<String> uploadingCaseIds = [];
Map<String, int> uploadTimeMap = {};
String uploadRecordId = "";
Map<String, int> confirmTimeMap = {};
Map<String, int> createCountMap = {};
Map<String, int> uploadIdMap = {};

final Lock lock = Lock();
int uploadTime = 0;

startUploadScanRecords(ScanActivity scanActivity, [int lastOffset = 0, bool auto = false]) async {
  int time = DateTime.now().millisecondsSinceEpoch;
  if (time - uploadTime < 3000 && !auto) {
    return;
  }
  ScanActivity? activity = await getLocalActivity(scanActivity.campaignId);
  if (activity != null && await HHttp.checkInternetValid()) {
    HHttp.request(
      "/v3/quickscan/campaign/createById",
      "POST",
      (data) async {
        deleteLocalActivity(activity);
        uploadingCaseIds.remove(scanActivity.campaignId);
        startUploadScanRecords(scanActivity, lastOffset, auto);
      },
      isShowErrorToast: false,
      params: {
        "id": activity.campaignId,
        "campaignName": activity.campaignName,
      },
    );
    return;
  }
  uploadTime = time;
  await lock.synchronized(() async {
    bool isHaveNetWork = await HHttp.checkInternetValid();
    if (isHaveNetWork) {
      if (!uploadingCaseIds.contains(scanActivity.campaignId)) {
        uploadingCaseIds.add(scanActivity.campaignId);
        List<ScanInfo> uploadList = await getUploadRecordList(scanActivity.campaignId, scanActivity.singleCampaign);
        uploadList = uploadList.where((element) => element.status == RecordStatus.uploading).toList();
        if (isNotEmpty(uploadList)) {
          int index = uploadList.length - 1 - lastOffset;
          if (index < 0) {
            index = 0;
          }
          ScanInfo record = uploadList[index];
          if (record.status == RecordStatus.completeUpload ||
              await record.checkPhotoLost(removeLost: true) ||
              !record.hasAllRequest()) {
            if (!record.hasAllRequest()) {
              record.status == RecordStatus.recording;
              saveRecordToDb(scanActivity.campaignId, record);
              eventBus.fire(EventRefreshRecordList());
              logger("startUploadScanRecord [${record.recordName}] lost!");
            }
            uploadingCaseIds.remove(scanActivity.campaignId);
            if (index >= 1) {
              logger("startUploadScanRecord [${record.recordName}] next!");
              startUploadScanRecords(scanActivity, lastOffset + 1, true);
            } else {
              logger("startUploadScanRecord [${record.recordName}] finish!");
            }
            return;
          }
          logger("startUploadScanRecord [${record.recordName}] start");

          List photos = record.getAllPhotos();
          List<String> uploadOkList = []; //已上传的图片

          checkUploadResult() {
// logger("startUploadScanRecord checkUploadResult ${uploadOkList.length} ${photos.length}");
            if (uploadOkList.length == photos.length) {
              confirmTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
              HHttp.request(
                "/v3/quickscan/scanRecord/confirm",
                "POST",
                (data) {
                  logger("startUploadScanRecord [${record.recordName}] success!");
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  record.status = RecordStatus.completeUpload;
                  eventBus.fire(EventUploadRecordResult(record));
                  saveRecordToDb(scanActivity.campaignId, record);
                  uploadingCaseIds.remove(scanActivity.campaignId);
                  startUploadScanRecords(scanActivity, lastOffset, true);
                  addEventTrace({
                    "domainName": "quickScan",
                    "operationName": "confirmRecord",
                    "value1": record.recordId,
                  });
                },
                errCallBack: (err) {
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  if (err["code"] == "1_2_1x3_9" || err["code"] == "1_2_6x3_6") {
//已经上传成功
                    logger("startUploadScanRecord [${record.recordName}] confirm repeat!");
                    record.status = RecordStatus.completeUpload;
                    saveRecordToDb(scanActivity.campaignId, record);
                    eventBus.fire(EventUploadRecordResult(record));
                    if (uploadList.length - 2 - lastOffset >= 0) {
                      uploadingCaseIds.remove(scanActivity.campaignId);
                      startUploadScanRecords(scanActivity, lastOffset + 1, true);
                    }
                  } else {
                    logger("startUploadScanRecord [${record.recordName}] fail!");
                    uploadingCaseIds.remove(scanActivity.campaignId);
                    record.status = RecordStatus.failUpload;
                    eventBus.fire(EventUploadRecordResult(record));
                    if (uploadList.length - 2 - lastOffset >= 0) {
                      uploadingCaseIds.remove(scanActivity.campaignId);
                      startUploadScanRecords(scanActivity, lastOffset + 1, true);
                    }
                  }
                },
                isShowNetWorkLoading: false,
                isShowErrorToast: false,
                params: {
                  "scanRecordId": record.recordId,
                  "categoryList": photos.map((path) {
                    String fileName = Global.getFileNameByPath(path);
                    return getTypeByFile(fileName);
                  }).toList(),
                },
              );
            }
          }

          uploadSinglePhoto(String path, [dynamic okCallback, dynamic errCallback]) async {
            int time = 5;
            String fileName = Global.getFileNameByPath(path);

            upload() {
// logger("startUploadScanRecord [${record.recordName}] $fileName");
              uploadScanPhoto(
                path,
                {
                  "name": fileName,
                  "sourceId": record.recordId,
                  "fileType": 1,
                  "sourceType": "RECORD_QUICKSCAN",
                  "persionId": currentUser.personId,
                  "category": getTypeByFile(fileName),
                  "analysisFile": "FALSE"
                },
                okCallback: (data) {
                  logger("startUploadScanRecord [${record.recordName}] $fileName ok!");
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  if (!uploadOkList.contains(path)) {
                    uploadOkList.add(path);
                    checkUploadResult();
                  }
                  if (okCallback != null) {
                    okCallback();
                  }
                },
                errCallback: (err) {
                  logger("startUploadScanRecord [${record.recordName}] $fileName error!");
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  if (time >= 0) {
                    time--;
                    upload();
                  } else {
                    if (!uploadOkList.contains(path)) {
                      uploadOkList.add(path);
                      checkUploadResult();
                    }
                    if (errCallback != null) {
                      errCallback();
                    }
                  }
                },
                showLoading: false,
              );
            }

            upload();
          }

          preUpload(List paths, [dynamic onFinish]) {
            int time = 0;

            uploadOnePhoto() {
              uploadSinglePhoto(
                paths[time],
                () {
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  logger("startUploadScanRecord preUpload ok");
                  if (onFinish != null) {
                    onFinish();
                  }
                },
                () {
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  logger("startUploadScanRecord preUpload error ");
                  if (time < paths.length - 1) {
                    time++;
                    uploadOnePhoto();
                  } else {
                    if (onFinish != null) {
                      onFinish();
                    }
                  }
                },
              );
            }

            uploadOnePhoto();
          }

          uploadRecordPhotos() async {
            List files = record.getAllPhotos();

            record.status = RecordStatus.uploading;
            eventBus.fire(EventUploadRecordResult(record));
            uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
            preUpload(files, () {
              for (String path in files) {
                uploadSinglePhoto(path);
              }
            });
          }

          startCreateRecord() {
            logger("startUploadScanRecords startCreateRecord ");
            HHttp.request(
              "/v3/quickscan/scanRecord/create",
              "POST",
              (data) {
                logger("startUploadScanRecord [${record.recordName}] create record ok!");
                uploadRecordId = record.recordId;
                uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                record.recordId = data["scanRecordId"];
// record.phaseId = data["phaseId"];
// saveUploadRecordList(caseId, uploadList);
                uploadRecordPhotos();
              },
              errCallBack: (err) {
                if (err["code"] == "1_2_1x3_10" || err["code"] == "1_2_6x14_1") {
//绑定的二维码已被使用
                  record.status = RecordStatus.waitingUpload;
                  record.repeatSn = true;
                  saveRecordToDb(scanActivity.campaignId, record);
                  eventBus.fire(EventRefreshRecordList());
                  if (uploadList.length - 1 - lastOffset >= 0) {
                    uploadingCaseIds.remove(scanActivity.campaignId);
                    startUploadScanRecords(scanActivity, lastOffset, true);
                  }
                } else if (err["code"] == "1_2_1x3_9" || err["code"] == "1_2_6x3_6") {
//已经上传成功
                  logger("startUploadScanRecord [${record.recordName}] create success repeat!");
                  record.status = RecordStatus.completeUpload;
                  saveRecordToDb(scanActivity.campaignId, record);
                  eventBus.fire(EventUploadRecordResult(record));
                  if (uploadList.length - 2 - lastOffset >= 0) {
                    uploadingCaseIds.remove(scanActivity.campaignId);
                    startUploadScanRecords(scanActivity, lastOffset + 1, true);
                  }
                } else {
                  HHttp.toastError(err);
                  logger("startUploadScanRecord [${record.recordName}] create record ${err["code"]}");
                  record.status = RecordStatus.failUpload;
                  saveRecordToDb(scanActivity.campaignId, record);
                  eventBus.fire(EventUploadRecordResult(record));
                  uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
                  if (uploadList.length - 2 - lastOffset >= 0) {
                    uploadingCaseIds.remove(scanActivity.campaignId);
                    startUploadScanRecords(scanActivity, lastOffset + 1, true);
                  }
                }
              },
              params: {
                "scanRecordId": record.recordId,
                "campaignId": record.campaignId,
                "name": record.recordName,
                "gender": record.gender,
                "age": num.parse(record.age),
                "recordCreateTime": record.customTime,
                "tagList": record.tagList,
                "note": record.note,
                "phone": record.patientPhone,
                "progressStage": record.progressStage,
                // "qrCodeId": record.recordSn,
              },
              isShowNetWorkLoading: false,
              isShowErrorToast: false,
            );
          }

          HHttp.request(
            "/v3/quickscan/scanRecord/checkConfirm",
            "POST",
            (data) {
              if (data) {
                //已经上传成功
                logger("startUploadScanRecord [${record.recordName}] create repeat!");
                record.status = RecordStatus.completeUpload;
                saveRecordToDb(scanActivity.campaignId, record);
                eventBus.fire(EventUploadRecordResult(record));
                if (uploadList.length - 2 - lastOffset >= 0) {
                  uploadingCaseIds.remove(scanActivity.campaignId);
                  startUploadScanRecords(scanActivity, lastOffset + 1, true);
                }
                return;
              }
              startCreateRecord();
            },
            errCallBack: (err) {
              uploadTimeMap[scanActivity.campaignId] = DateTime.now().millisecondsSinceEpoch;
              if (uploadList.length - 2 - lastOffset >= 0) {
                uploadingCaseIds.remove(scanActivity.campaignId);
                startUploadScanRecords(scanActivity, lastOffset + 1, true);
              }
            },
            params: {
              "scanRecordId": record.recordId,
            },
          );
        } else {
          uploadingCaseIds.remove(scanActivity.campaignId);
        }
      }
    } else {
      List<ScanInfo> uploadList = await getUploadRecordList(scanActivity.campaignId, scanActivity.singleCampaign);
      uploadList = uploadList.where((element) => element.status == RecordStatus.uploading).toList();
      if (isNotEmpty(uploadList)) {
        for (ScanInfo r in uploadList) {
          r.status = RecordStatus.failUpload;
          eventBus.fire(EventUploadRecordResult(r));
        }
      }
      uploadingCaseIds.remove(scanActivity.campaignId);
    }
  });
}

uploadScanPhoto(
  String filePath,
  dynamic params, {
  Function? okCallback,
  Function? errCallback,
  bool showLoading = false,
}) {
  onError(err) {
    if (errCallback != null) {
      try {
        errCallback(err);
      } catch (e) {
        errCallback();
      }
    }
  }

  if (!File(getLocalPath(filePath)).existsSync()) {
    onError({});
    return;
  }
  String fileName = Global.getFileNameByPath(filePath);

  HHttp.request(
    "/v3/file/getUploadPath?sourceType=RECORD_QUICKSCAN",
    "POST",
    (data) async {
// logger("startUploadScanRecord $fileName get-upload-path");
      try {
        if (isNotEmpty(data["list"])) {
          String id = data["fileId"];
          List<String> urls = []; //已上传的url

          String url = data["list"][0]["url"];
          for (dynamic info in data["list"]) {
            if (info["fileType"] == 1) {
// logger("startUploadScanRecord $fileName getFileType == 1 !");
              url = info["url"];
            }
          }

          uploadIdMap[id] = DateTime.now().millisecondsSinceEpoch ~/ 10 * 10 + 5;
          checkUploadCount() {
// if (urls.length == data["list"].length) {
// logger("startUploadScanRecord $fileName confirm-upload-file");
            HHttp.request(
              "/v3/file/confirmWithCrop",
              "POST",
              (data) {
                if (okCallback != null) {
                  okCallback(data);
                }
                uploadIdMap.remove(id);
              },
              errCallBack: onError,
              params: {
                "fileId": id,
                "sourceType": "RECORD_QUICKSCAN",
                "category": getTypeByFile(fileName),
              },
              isShowNetWorkLoading: false,
              isShowErrorToast: false,
            );
// }
          }

// for (dynamic info in data["list"]) {
          int time = 3;
          uploadFile(String url, okCallback, errCallback) {
// logger("startUploadScanRecord uploadFile $fileName");
            HHttp.request(
              url,
              "PUT",
              cbFullResponse: true,
              isFullUrl: true,
              isShowErrorToast: false,
              useUploadDio: true,
              (data) {
                okCallback();
              },
              errCallBack: (err) {
                errCallback();
              },
              isShowNetWorkLoading: showLoading,
              params: File(getLocalPath(filePath)).readAsBytesSync(),
            );
          }

          uploadSingleFile(String url) {
// logger("startUploadScanRecord uploadSingleFile $fileName");
            uploadFile(url, () {
              if (!urls.contains(url)) {
                urls.add(url);
                checkUploadCount();
              }
            }, () {
              time--;
              if (time >= 0) {
                uploadSingleFile(url);
              } else {
                if (!urls.contains(url)) {
                  urls.add(url);
                  checkUploadCount();
                }
              }
            });
          }

// String url = info["url"];
          uploadSingleFile(url);
// }
        } else {
          onError({});
        }
      } catch (ex) {
        onError({});
      }
    },
    errCallBack: (err) {
      logger("startUploadScanRecord $fileName upload ${err["code"]}");
      if (err["code"] == "1_2_1x3_8") {
//已经上传过了，直接成功回调
        if (okCallback != null) {
          okCallback(err);
        }
      } else {
        onError(err);
      }
    },
    params: params,
    isShowNetWorkLoading: showLoading,
  );
}

void showSwitchLightDialog(BuildContext context) {
  if (scanLocalPlaque) {
    Global.showCustomDialog(
      Center(
        child: Container(
          width: 327.sp,
          height: 222.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.sp),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 20.sp),
                child: Text(
                  Lang.friendly_tip,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: color2B,
                  ),
                ),
              ),
              Container(
                height: 100.sp,
                padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        Lang.check_your_device_ultra_home,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: color2B,
                          height: 1.3,
                        ),
                      ),
                    ),
                    SizedBox(width: 6.sp),
                    Image.asset(
                      "res/scan/scan_tool_ultra.png",
                      width: 40.sp,
                      height: 40.sp,
                      fit: BoxFit.contain,
                    ),
                  ],
                ),
              ),
              Divider(height: 1.sp, color: const Color(0xffe5e5e5)),
              Click(
                onTap: () {
                  disconnectUltra(disconnectWifi: true);
                  Navigator.popUntil(context, (route) => route.isFirst);
// Navigator.popUntil(context, ModalRoute.withName('HomePage'));
                  BotToast.cleanAll();
                  Future.delayed(const Duration(milliseconds: 500), () {
                    eventBus.fire(EventSelectTool());
                  });
                },
                ms: 1000,
                child: Container(
                  width: 327.sp,
                  height: 56.sp,
                  alignment: Alignment.center,
                  child: Text(
                    Lang.ok,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: color2B,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      backButtonBehavior: BackButtonBehavior.ignore,
      clickBgCallback: () {},
      isBringXBtn: false,
    );
  } else {
    Global.showCustomDialog(
      Center(
        child: Container(
          width: 327.sp,
          height: 258.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.sp),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 20.sp),
                child: Text(
                  Lang.friendly_tip,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: color2B,
                  ),
                ),
              ),
              Container(
                height: 56.sp,
                padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        Lang.check_your_device_ultra,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: color2B,
                          height: 1.3,
                        ),
                      ),
                    ),
                    SizedBox(width: 6.sp),
                    Image.asset(
                      "res/scan/scan_tool_ultra.png",
                      width: 40.sp,
                      height: 40.sp,
                      fit: BoxFit.contain,
                    ),
                  ],
                ),
              ),
              Container(
                height: 80.sp,
                padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        Lang.switch_device_to_light,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: colorCyan,
                          height: 1.3,
                        ),
                      ),
                    ),
                    SizedBox(width: 6.sp),
                    Image.asset(
                      "res/scan/scan_tool_light.png",
                      width: 40.sp,
                      height: 40.sp,
                      fit: BoxFit.contain,
                    ),
                  ],
                ),
              ),
              Divider(height: 1.sp, color: const Color(0xffe5e5e5)),
              Click(
                onTap: () {
                  disconnectUltra(disconnectWifi: true);
                  Navigator.popUntil(context, ModalRoute.withName(scanPageName));
                  BotToast.cleanAll();
                },
                ms: 1000,
                child: Container(
                  width: 327.sp,
                  height: 56.sp,
                  alignment: Alignment.center,
                  child: Text(
                    Lang.ok,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: color2B,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      backButtonBehavior: BackButtonBehavior.ignore,
      clickBgCallback: () {},
      isBringXBtn: false,
    );
  }
}

//根据url获取对应Image 的组件，
getImgByUrlOrPath(String? pathOrUrl, double imgSize) {
  if (pathOrUrl == null || pathOrUrl == "") {
    return const SizedBox();
  }
  return pathOrUrl.contains("http")
      ? Image.network(pathOrUrl, width: imgSize, height: imgSize, fit: BoxFit.cover)
      : Image.file(File(getLocalPath(pathOrUrl)), width: imgSize, height: imgSize, fit: BoxFit.cover);
}

getScanInfoItem(ScanInfo record, bool isSelect, [bool isChild = false, String highLightText = ""]) {
  return Container(
      width: 335.sp,
      height: 112.sp,
      padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 0),
      margin: EdgeInsets.only(bottom: isChild ? 0 : 16.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
        border: isSelect ? Border.all(color: colorBrand) : const Border(),
        boxShadow: isChild
            ? []
            : [
                BoxShadow(
                  color: colorShader,
                  blurRadius: 1.sp,
                  spreadRadius: 1.sp,
                ),
              ],
      ),
      child: Row(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 220.sp),
                child: LightText(
                  text: isNotEmpty(record.recordName) ? record.recordName : "-",
                  lightText: highLightText,
                  textStyle: TextStyle(
                    color: color2B,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  lightStyle: TextStyle(color: colorPink, fontSize: 16.sp, fontWeight: FontWeight.w500),
                  maxLines: 1,
                ),
              ),
              SizedBox(height: 8.sp),
              SingleText(Global.getShowTime(milliseconds: record.customTime, ignoreSameDay: false), color7C, 14.sp),
              SizedBox(height: 8.sp),
              record.getStatusView(),
            ],
          ),
          const Spacer(),
          isNotEmpty(record.smilePhoto)
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(40.sp),
                  child: Image.file(
                    File(getLocalPath(record.smilePhoto)),
                    width: 64.sp,
                    height: 64.sp,
                    fit: BoxFit.cover,
                  ),
                )
              : Image.asset("res/imgs/record_avatar.png", width: 64.sp, height: 64.sp, fit: BoxFit.cover),
        ],
      ));
}

setCurrentContactId(String contractId) {
  Global.sharedPrefs.setString("current_contact_${User.instance.getUserData("UserId")}", contractId);
}

String getCurrentContactId() {
  return Global.sharedPrefs.getString("current_contact_${User.instance.getUserData("UserId")}") ?? "";
}

setCurrentTimelineId(String timelineId) {
  Global.sharedPrefs.setString("current_timeline_${User.instance.getUserData("UserId")}", timelineId);
}

String getCurrentTimelineId() {
  return Global.sharedPrefs.getString("current_timeline_${User.instance.getUserData("UserId")}") ?? "";
}

getSkilledMode() {
  skilledMode =
      Global.sharedPrefs.getBool("skilled_mode_${User.instance.getUserData("UserId")}_${getCurrentContactId()}") ??
          false;
}

setSkilledMode(bool mode) {
  Global.sharedPrefs.setBool("skilled_mode_${User.instance.getUserData("UserId")}_${getCurrentContactId()}", mode);
  skilledMode = mode;
}

bool getSkilledNewTagClicked() {
  return Global.sharedPrefs.getBool("skilled_tag_${User.instance.getUserData("UserId")}_${getCurrentContactId()}") ??
      false;
}

setSkilledNewTagClicked() {
  Global.sharedPrefs.setBool("skilled_tag_${User.instance.getUserData("UserId")}_${getCurrentContactId()}", true);
}

void deleteScanFile(String path) async {
  try {
    logger("deleteScanFile start: $path");
    File file = File(path);
    await file.delete();
    logger("deleteScanFile success: $path");
  } catch (ex) {
    logger("deleteScanFile error: $path");
  }
}

String getRecordEventStepEnumTitle(ToothCameraTypeEnum toothCameraType) {
  String title = "";
  switch (toothCameraType) {
    case ToothCameraTypeEnum.maxillary:
      title = "upper";
      break;
    case ToothCameraTypeEnum.occlusion:
      title = "occlusion";
      break;
    case ToothCameraTypeEnum.openMaskOff:
      title = "open";
      break;
    case ToothCameraTypeEnum.openMaskOn:
      title = "open_aligner";
      break;
    case ToothCameraTypeEnum.mandibular:
      title = "lower";
      break;
  }
  return title;
}

sendScanEvent(String operation, [String? content, Map? param]) {
  param ??= {};
  param["value1"] = getCurrentContactId();
  param["value2"] = getCurrentTimelineId();
  if (isNotEmpty(content)) {
    param["content"] = content!;
  }
  sendEventPoint(selectScanByUltra(getScanTool()) ? "intraoral_ultra" : "intraoral_iscan", operation, param);
}

sendRecordEvent(String operation, [String? content, Map? param]) {
  param ??= {};
  param["value1"] = getCurrentContactId();
  param["value2"] = getCurrentTimelineId();
  if (isNotEmpty(content)) {
    param["content"] = content!;
  }
  sendEventPoint("input_page", operation, param);
}

int getScanTool([String? contractId]) {
// contractId ??= Global.sharedPrefs.getString("current_contact_${User.instance.getUserData("UserId")}") ?? "";
  int result = 0;
// if (Global.sharedPrefs.containsKey("scan_tool_${User.instance.getUserData("UserId")}_$contractId")) {
//   result = Global.sharedPrefs.getInt("scan_tool_${User.instance.getUserData("UserId")}_$contractId") ?? 0;
// } else {
  result = Global.sharedPrefs.getInt("scan_tool_${User.instance.getUserData("UserId")}") ?? SCAN_TOOL_ULTRA;
// }
  return result;
}

setScanTool(int type) {
  Global.sharedPrefs.setInt("scan_tool_${User.instance.getUserData("UserId")}", type);
}

String getScanTime(timeline) {
  String result = "";
  if (result == "") {
    try {
      if (timeline.containsKey("TimelineCommittedTime") && timeline["TimelineCommittedTime"].length >= 13) {
        result = DateFormat('yyyy.MM.dd HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(int.parse(timeline["TimelineCommittedTime"])));
      }
    } catch (ex) {
//
    }
  }
  if (result == "") {
    result = getScanDate(timeline);
  }
  return result;
}

String getScanDate(timeline) {
  String result = "";
  if (result == "") {
    if (timeline.containsKey("TimelineCommittedDate") && timeline["TimelineCommittedDate"].length >= 8) {
      result =
          "${timeline["TimelineCommittedDate"].substring(0, 4)}.${timeline["TimelineCommittedDate"].substring(4, 6)}.${timeline["TimelineCommittedDate"].substring(6, 8)}";
    }
  }
  if (result == "") {
    result = timeline.containsKey("TimelineDate") && timeline["TimelineDate"].length >= 8
        ? ("${timeline["TimelineDate"].substring(0, 4)}.${timeline["TimelineDate"].substring(4, 6)}.${timeline["TimelineDate"].substring(6, 8)}")
        : "";
  }
  return result;
}

//ultra检测如果报亮度问题，则视为正常
bool isUltraLightError(int errorCode, String imgName) {
// logger("isUltraLightError $errorCode $imgName");
  if (!checkScanQuality || scanLight) {
    return true;
  }
//ultra扫描的，图片宽度传默认的-1
  if (isUltraImage(imgName)) {
    if (errorCode >= 71 && errorCode <= 79) {
      return true;
    }
  }
  return false;
}

getErrorCode(String path) {
  if (!path.contains("dver") && !path.contains("fver")) {
    return -1;
  }
  if (Global.getFileNameByPath(path).split('-').length < 8) {
    return -1;
  }
  if (!checkScanQuality || scanLight) {
    return 0;
  }
  try {
    return int.parse(Global.getFileNameByPath(path).split("-")[3]);
  } catch (ex) {
    return 0;
  }
}

bool isUltraImage(String imgName) {
  return imgName.contains("-width_-1");
}

getTitleByImgRow(ToothCameraTypeEnum type, {bool isPlaque = false}) {
  String title = "";
  switch (type) {
    case ToothCameraTypeEnum.maxillary:
      title = hideMask || isPlaque ? Lang.no_mask_scan_up : Lang.pickoff_mask_scan_up;
      break;
    case ToothCameraTypeEnum.occlusion:
      title = hideMask || isPlaque ? Lang.no_mask_natural_bite : Lang.pickoff_mask_natural_bite;
      break;
    case ToothCameraTypeEnum.openMaskOff:
      title = hideMask || isPlaque ? Lang.no_mask_open_bimaxillary : Lang.pickoff_mask_open_bimaxillary;
      break;
    case ToothCameraTypeEnum.openMaskOn:
      title = Lang.pickup_mask_open_bimaxillary;
      break;
    case ToothCameraTypeEnum.mandibular:
      title = hideMask || isPlaque ? Lang.no_mask_scan_down : Lang.pickoff_mask_scan_down;
      break;
  }
  return title;
}

getTitlePlaque(ToothCameraTypeEnum type) {
  String title = "";
  switch (type) {
    case ToothCameraTypeEnum.maxillary:
      title = Lang.plaque_scan_up;
      break;
    case ToothCameraTypeEnum.openMaskOff:
      title = Lang.plaque_open_bimaxillary;
      break;
    case ToothCameraTypeEnum.mandibular:
      title = Lang.plaque_scan_down;
      break;
  }
  return title;
}

//疗程类型：1-天使，2-灵芽口腔全科，3-灵芽隐形矫正，4-灵芽固定矫正
int getContractType(contract) {
  if (isNotEmpty(contract) && isNotEmpty(contract["Data"]) && isNotEmpty(contract["Data"]["Title"])) {
    if (contract["Data"]["Title"] == "clearaligner" && contract["ProviderType"] == "aa") {
      return 1;
    } else if (contract["Data"]["Title"] == "generaldentistry" && contract["ProviderType"] == "ly") {
      return 2;
    } else if (contract["Data"]["Title"] == "clearaligner" && contract["ProviderType"] == "ly") {
      return 3;
    } else if (contract["Data"]["Title"] == "fixedappliance" && contract["ProviderType"] == "ly") {
      return 4;
    }
  }
  return 0;
}

String getScanHelpVideo(ToothCameraTypeEnum type, bool isUltra) {
  return "res/videos/guide_${type.name}_ultra.mp4";
}

String getScanHelpAudio(ToothCameraTypeEnum type, bool isUltra) {
  return "${lang}_guide_${type.name}_ultra.mp3";
}

String getScanErrorTeachImg(int errorCode, ToothCameraTypeEnum type, bool isUltra) {
  String gifName = "";
  String tag = isUltra ? "ultra" : "iscan";
  if (errorCode == 11) {
//1、牙齿<=5个
    gifName = "scan_error_${tag}_no_tooth.gif"; //未检测到牙齿
  } else if (errorCode == 21) {
//2、牙齿不清晰
    if (isUltra) {
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_blur.gif"; //咬合画面模糊
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_blur.gif"; //微张画面模糊
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_blur.gif"; //牙套画面模糊
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    } else {
      gifName = "scan_error_${tag}_blur.gif"; //画面模糊
    }
  } else if (errorCode >= 300 && errorCode <= 399) {
//3、扫描方向错了
    int shouldCode = int.parse(("$errorCode").substring(1, 2));
    int currentCode = int.parse(("$errorCode").substring(2, 3));
    if (shouldCode == 1 || shouldCode == 3) {
//应该扫左/右，但扫的是其他方向
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_lr.gif"; //咬合左右方向错误
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_lr.gif"; //微张左右方向错误
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_lr.gif"; //牙套左右方向错误
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    } else if (shouldCode == 2 || currentCode == 7) {
//应该扫中，但扫的是其他方向  || 应该扫中间但扫在临界区间
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_center.gif"; //咬合门牙出错
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_center.gif"; //微张门牙出错
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_center.gif"; //牙套门牙出错
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    } else if (shouldCode == 4) {
//应该扫上颌但扫的不是上颌，且没露下牙
      gifName = "scan_error_${tag}_up.gif"; //上颌出错
    } else if (shouldCode == 5) {
//应该扫下颌但扫的不是下颌，且没露上牙
      gifName = "scan_error_${tag}_down.gif"; //下颌出错
    }

    if (currentCode == 6) {
//应该扫左/右但扫在临界区间
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_angle.gif"; //咬合左右角度不足
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_angle.gif"; //微张左右角度不足
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_angle.gif"; //牙套左右角度不足
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    }
  } else if (errorCode >= 41 && errorCode <= 49) {
//4、没有露出5号牙
    if (errorCode == 41) {
//没露左5  ||  没露右5
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_lr.gif"; //咬合左右方向错误
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_lr.gif"; //微张左右方向错误
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_lr.gif"; //牙套左右方向错误
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    } else if (errorCode == 44) {
//没露上5
      gifName = "scan_error_${tag}_up.gif"; //上颌出错
    } else if (errorCode == 45) {
//没露下5
      gifName = "scan_error_${tag}_down.gif"; //下颌出错
    }
  } else if (errorCode >= 51 && errorCode <= 59) {
//5、露5没但露6号牙
    if (errorCode == 51) {
//没露左6  ||  没露右6
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_lr.gif"; //咬合左右方向错误
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_lr.gif"; //微张左右方向错误
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_lr.gif"; //牙套左右方向错误
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    } else if (errorCode == 54) {
//没露上6
      gifName = "scan_error_${tag}_up.gif"; //上颌出错
    } else if (errorCode == 55) {
//没露下6
      gifName = "scan_error_${tag}_down.gif"; //下颌出错
    }
  } else if (errorCode >= 61 && errorCode <= 69) {
//6、上下颌是否微张
    if (errorCode == 61) {
//该咬合但没咬合
      gifName = "scan_error_${tag}_close_bat.gif"; //咬合出错
    } else if (errorCode == 62 || errorCode == 63) {
//该微张但没张开 ||  该微张但张开幅度太大
      switch (type) {
        case ToothCameraTypeEnum.occlusion:
          gifName = "scan_error_${tag}_close_bat.gif"; //咬合出错
          break;
        case ToothCameraTypeEnum.openMaskOff:
          gifName = "scan_error_${tag}_open_bat.gif"; //微张微张幅度出错
          break;
        case ToothCameraTypeEnum.openMaskOn:
          gifName = "scan_error_${tag}_mask_bat.gif"; //牙套微张幅度出错
          break;
        case ToothCameraTypeEnum.maxillary:
          gifName = "scan_error_${tag}_up.gif"; //上颌出错
          break;
        case ToothCameraTypeEnum.mandibular:
          gifName = "scan_error_${tag}_down.gif"; //下颌出错
          break;
      }
    }
  } else if (errorCode >= 71 && errorCode <= 79) {
//7、牙齿亮度不够
    if (errorCode == 71) {
      gifName = "scan_pre_2.png"; //亮度不够
    }
  } else if (errorCode >= 81 && errorCode <= 89) {
//8、上下颌弓形不够
    if (errorCode == 81) {
//上颌弓形不够
      gifName = "scan_error_${tag}_up.gif"; //上颌出错
    } else if (errorCode == 82) {
//下颌弓形不够
      gifName = "scan_error_${tag}_down.gif"; //下颌出错
    }
  }
  return "res/scan/$gifName";
}

String getLocalPath(String path) {
  if (Platform.isAndroid) {
    return path;
  }
  try {
    if (isNotEmpty(currentTempDir) && !path.contains(currentTempDir)) {
//1、根据长度直接替换
      if (path.length > currentTempDir.length + 1 &&
          path.substring(currentTempDir.length, currentTempDir.length + 1) == "/") {
        String result = currentTempDir + path.substring(currentTempDir.length);
        logger("getLocalPath 1: $path -> $result");
        return result;
      }

//2、根据分隔符替换
// String result = currentTempDir + path.substring(path.lastIndexOf("/"));
// logger("getLocalPath 2: $path -> $result");
// return result;
    }
  } catch (ex) {}
  return path;
}

Future<int> checkZipExist(String zipPath, List files) async {
  if (await File(zipPath).exists()) {
    logger("uploadFileByServer checkZipExist: $zipPath exist!");
    List contents = await Global.unZip(zipPath);
    if (contents.length == files.length) {
      logger("uploadFileByServer checkZipExist: $zipPath length = ${contents.length}");
      return contents.length;
    }
  }

  logger("uploadFileByServer checkZipExist: $zipPath recreate ${files.length}");
  var ziper = ZipFileEncoder();
  ziper.create(zipPath);
  for (String file in files) {
    logger("uploadFileByServer checkZipExist: $zipPath create with $file");
    ziper.addFile(File(getLocalPath(file)));
  }
  ziper.close();

  List contents = await Global.unZip(zipPath);
  logger("uploadFileByServer checkZipExist: $zipPath file count: ${contents.length}");
  return contents.length;
}

Map<String, List<ScanInfo>> usbSaveMap = {};
Map<String, int> usbCompleteMap = {};

addListToUsbSave(
    String caseId, List<ScanInfo> records, void Function(double) onProgress, void Function() onComplete) async {
  List<ScanInfo> usbSaveList = usbSaveMap[caseId] ?? [];
  if (await Global.checkStorePermission()) {
    showCustomDialog(UsbPathScreen((path) {
      for (ScanInfo record in records) {
        bool flag = false;
        for (ScanInfo item in usbSaveList) {
          if (item.recordId == record.recordId) {
            flag = true;
            break;
          }
        }
        if (!flag) {
          usbSaveList.add(record);
        }
      }
      usbSaveMap[caseId] = usbSaveList;
      usbSave(caseId, path, onProgress, onComplete);
    }));
  }
}

usbSave(String caseId, String path, void Function(double) onProgress, void Function() onComplete) async {
  List<ScanInfo> usbSaveList = usbSaveMap[caseId] ?? [];
  int usbSaveCompleteCount = usbCompleteMap[caseId] ?? 0;
  if (usbSaveList.isNotEmpty) {
    Directory root = await getApplicationDocumentsDirectory();
    logger("uploadv2 progress: ${root.path}");
    copySpecificFolderWithProgress(
        sourcePath: root.path,
        destinationPath: "$path/looper",
        targetFolderName: "${usbSaveList[0].recordName}_${usbSaveList[0].recordId}",
        onComplete: () {
          if (usbSaveList.isNotEmpty) {
            usbSaveList.removeAt(0);
          }
          usbSaveMap[caseId] = usbSaveList;
          logger("progress: $usbSaveCompleteCount, total: ${usbSaveCompleteCount + usbSaveList.length}");
          usbSaveCompleteCount++;
          usbCompleteMap[caseId] = usbSaveCompleteCount;
          onProgress(usbSaveCompleteCount / (usbSaveCompleteCount + usbSaveList.length));
          usbSave(caseId, path, onProgress, onComplete);
        });
  } else {
    usbSaveCompleteCount = 0;
    usbCompleteMap[caseId] = usbSaveCompleteCount;
    onComplete();
  }
// Directory dir = Directory("${root.path}/${currentRecord.recordName}_${currentRecord.recordId}");
}

/// 复制指定子文件夹及其内容
Future<void> copySpecificFolderWithProgress({
  required String sourcePath,
  required String destinationPath,
  required String targetFolderName, // 需要复制的子文件夹名称
  required void Function() onComplete,
}) async {
  final sourceDir = Directory(sourcePath);
  final destinationDir = Directory(destinationPath);

  if (!await sourceDir.exists()) {
    throw Exception("Source directory does not exist: $sourcePath");
  }

// 查找目标文件夹
  final List<Directory> matchingFolders = [];
  await for (var entity in sourceDir.list(recursive: false)) {
    logger("uploadv2 progress: ${entity.path}");
    if (entity is Directory && entity.path.endsWith(targetFolderName)) {
      matchingFolders.add(entity);
    }
  }
  logger("uploadv2 progress: $matchingFolders");

  if (matchingFolders.isEmpty) {
    onComplete();
// throw Exception("No matching folder found: $targetFolderName");
  }

  int totalFiles = 0;
  int copiedFiles = 0;

// 遍历匹配的文件夹并复制内容
  for (var folder in matchingFolders) {
    final entities = await folder.list(recursive: true).toList();
    totalFiles += entities.whereType<File>().length;

    for (var entity in entities) {
      if (entity is File) {
        logger("uploadv2 file: ${entity.path}");
// 计算目标路径
        final relativePath = entity.path.substring(folder.path.length + 1);
        final newPath = '${destinationDir.path}/$targetFolderName/$relativePath';

// 创建目标文件夹
        await File(newPath).parent.create(recursive: true);

// 复制文件
        await entity.copy(newPath);

// 更新进度
        copiedFiles++;
        double progress = copiedFiles / totalFiles;
// onProgress(progress, entity.path);
      }
    }
  }
// await Future.delayed(const Duration(seconds: 1));
  onComplete();
}
