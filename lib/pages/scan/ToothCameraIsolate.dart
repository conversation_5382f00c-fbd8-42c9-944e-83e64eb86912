import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';

class ToothCameraIsolate {
  late Isolate _isolate;
  late ReceivePort _receivePort;
  late SendPort _sendPort;

  SendPort get sendPort => _sendPort;

  Future<void> start() async {
    _receivePort = ReceivePort();
    _isolate = await Isolate.spawn(entryPoint, _receivePort.sendPort);
    _sendPort = await _receivePort.first;
  }

  static void entryPoint(SendPort sendPort) async {
    final port = ReceivePort();
    sendPort.send(port.sendPort);
    await for (final dynamic msgObj in port) {
      if (msgObj != null) {
        try {
          logicProcess(msgObj);
        } catch (ex) {
          //
        }
      } else {
        if (kDebugMode) {
          print("Isolate process error: recvice msg obj is null!");
        }
      }
    }
  }

  //imgDatasType:
  //0 = rgb 格式
  //1 = rbga/rbga8888 格式
  //2 = yuv420 格式(暂时没测试完成)
  static void logicProcess(dynamic msgObj) {
    int? type = msgObj["type"];
    // if (type == 3) {
    //   //获取结果
    //   //以下这种利用 imgLib.Image 来转bytes的release后效率巨慢，这里我们该传入原始数据用直接opencv处理的方式来转换
    //   // dynamic obj = ffiPredictByImgData(HImageUtils.cameraImg2Uint8ListBringWH(msgObj["obj"]));
    //   CameraImage image = msgObj["obj"];
    //   Uint8List bytes;
    //   int imgDatasType;
    //   if (msgObj["obj"].format.group == ImageFormatGroup.bgra8888) {
    //     bytes = image.planes[0].bytes;
    //     imgDatasType = ImageType.rbga8888;
    //   } else if (msgObj["obj"].format.group == ImageFormatGroup.yuv420) {
    //     bytes = image.planes[0].bytes;
    //     imgDatasType = ImageType.yuv420;
    //   } else {
    //     bytes = Uint8List.fromList([]);
    //     imgDatasType = ImageType.rgb;
    //   }
    //   dynamic obj = ffiPredictByImgData({
    //     "Uint8List": bytes,
    //     "width": image.width,
    //     "height": image.height,
    //     "imgDatasType": imgDatasType,
    //     "plane1": msgObj["obj"].format.group == ImageFormatGroup.yuv420 ? image.planes[1].bytes : [],
    //     "plane2": msgObj["obj"].format.group == ImageFormatGroup.yuv420 ? image.planes[2].bytes : [],
    //     "bytesPerRow": msgObj["obj"].format.group == ImageFormatGroup.yuv420 ? image.planes[1].bytesPerRow : 0,
    //     "bytesPerPixel": msgObj["obj"].format.group == ImageFormatGroup.yuv420 ? image.planes[1].bytesPerPixel : 0,
    //     "yRowStride": msgObj["obj"].format.group == ImageFormatGroup.yuv420 ? image.planes[0].bytesPerRow : 0,
    //   }, isUseNewModal: Global.isGlobalUseNewModal);
    //   msgObj["responseSendPort"].send(obj);
    // } else
    if (type == 2) {
      //根据多张图返回预测结果
      dynamic obj = ffiPredictByPaths(msgObj["obj"], isUseNewModal: Global.isGlobalUseNewModal);
      logger("ffiPredictByPaths: $obj");
      msgObj["responseSendPort"].send(obj);
    // } else if (type == 1) {
    //   //ffi保存图片处理
    //   HImageUtils.saveImageByFfiTempDir(msgObj["tempDir"], msgObj["obj"], 0, picName: msgObj["saveImgName"])
    //       .then((path) {
    //     msgObj["responseSendPort"].send(path);
    //   });
    // } else if (type == -1) {
    //   //旋转180度的图片处理
    //   HImageUtils.saveImageByFfiTempDir(msgObj["tempDir"], msgObj["obj"], 180, picName: msgObj["saveImgName"])
    //       .then((path) {
    //     msgObj["responseSendPort"].send(path);
    //   });
    } else if (type == 0) {
      //初始化tflite
      ffiCreateTFLite(msgObj["modelData"], msgObj["isEncrypt"] ? 1 : 0, msgObj["modelType"]);
    // } else if (type == 4) {
    //   dynamic obj = ffiClarityIncrease(msgObj["obj"]);
    //   msgObj["responseSendPort"].send(obj);
    }
  }

  dispose() {
    _receivePort.close();
    _isolate.kill();
    ffiDeleteTFLite();
  }
}
