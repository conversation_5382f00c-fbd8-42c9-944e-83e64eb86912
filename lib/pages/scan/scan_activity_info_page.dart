import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

class ScanActivityInfoPage extends BasePage {
  ScanActivity? activity;

  ScanActivityInfoPage({this.activity, Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanActivityInfoPageState();
  }
}

class ScanActivityInfoPageState extends BasePageState<ScanActivityInfoPage> {
  GlobalKey globalButtonKey = GlobalKey();
  bool isPosting = false;
  ScanActivity scanActivity = ScanActivity();
  final picker = ImagePicker();
  bool editMode = false;
  bool updateQrCode = false;

  TextEditingController nameController = TextEditingController();
  TextEditingController workerController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController codeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.activity != null) {
      editMode = true;
      scanActivity = widget.activity!.copy();
      setActivityText();
      logger("scanActivity ${scanActivity.toJson()}");
      _getActivityInfo();
    }
  }

  setActivityText() {
    nameController.text = scanActivity.campaignName;
    workerController.text = scanActivity.contactName;
    phoneController.text = scanActivity.contactPhone;
    addressController.text = scanActivity.contactAddress;
    codeController.text = scanActivity.qrCodeDescription;
    checkButtonState();
  }

  checkButtonState() {
    if (globalButtonKey.currentState != null) {
      (globalButtonKey.currentState as HCustomButtonState).setVisible(isNotEmpty(scanActivity.campaignName) && !isPosting);
    }
  }

  _getActivityInfo() {
    HHttp.request(
      "/v3/quickscan/campaign/get",
      "POST",
      (data) {
        setState(() {
          scanActivity = ScanActivity.fromJson(data);
        });
        setActivityText();

        if (isNotEmpty(scanActivity.qrCodeFileId)) {
          downloadFile(
            [scanActivity.qrCodeFileId],
            1,
            (id, path) {
              setState(() {
                scanActivity.qrcodePath = path;
              });
            },
            "RECORD_QUICKSCAN",
          );
        }
      },
      params: {"campaignId": scanActivity.campaignId},
    );
  }

  _buildForm() {
    return SingleChildScrollView(
      child: Container(
        width: 1.sw - 40.sp,
        margin: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 40.sp),
        padding: EdgeInsets.all(20.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.sp),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                MyText("* ", Colors.red, 16.sp, FontWeight.w500),
                MyText(Lang.collect_data_name, color2B, 16.sp, FontWeight.w500),
              ],
            ),
            editField(
              Lang.input_collect_name,
              nameController,
              (text) {
                setState(() {
                  scanActivity.campaignName = text;
                });
                checkButtonState();
              },
              maxLength: 50,
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(0, 8.sp, 0, 16.sp),
              child: MyText(Lang.data_in_cover, color7C, 12.sp),
            ),
            MyText(Lang.tenant_worker_name, color2B, 16.sp, FontWeight.w500),
            editField(
              Lang.input_tenant_worker_name,
              workerController,
              (text) {
                setState(() {
                  scanActivity.contactName = text;
                });
              },
              maxLength: 50,
            ),
            MyText(Lang.tenant_worker_phone, color2B, 16.sp, FontWeight.w500),
            editField(
              Lang.input_tenant_worker_phone,
              phoneController,
              (text) {
                setState(() {
                  scanActivity.contactPhone = text;
                });
              },
              inputType: TextInputType.phone,
            ),
            MyText(Lang.tenant_worker_address, color2B, 16.sp, FontWeight.w500),
            editField(Lang.input_tenant_worker_address, addressController, (text) {
              setState(() {
                scanActivity.contactAddress = text;
              });
            }, maxLength: 50),
            MyText(Lang.tenant_worker_qrcode, color2B, 16.sp, FontWeight.w500),
            SizedBox(height: 8.sp),
            Row(
              children: [
                GestureDetector(
                  onTap: () async {
                    XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);
                    if (pickedFile != null) {
                      if (File(pickedFile.path).lengthSync() > 2 * 1024 * 1024) {
                        Global.toast(Lang.tenant_qrcode_size_limit);
                      } else {
                        updateQrCode = true;
                        setState(() {
                          scanActivity.qrcodePath = pickedFile.path;
                        });
                      }
                    }
                  },
                  child: isEmpty(scanActivity.qrcodePath)
                      ? Image.asset("res/icons/add_image.png", width: 80.sp)
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(4.sp),
                          child: getImgByUrlOrPath(scanActivity.qrcodePath, 80.sp),
                        ),
                ),
                isEmpty(scanActivity.qrcodePath)
                    ? const SizedBox()
                    : GestureDetector(
                        onTap: () async {
                          updateQrCode = true;
                          setState(() {
                            scanActivity.qrcodePath = "";
                          });
                        },
                        child: Padding(
                            padding: EdgeInsets.all(12.sp),
                            child: Image.asset("res/icons/icon_delete.png", width: 20.sp)),
                      ),
              ],
            ),
            SizedBox(height: 8.sp),
            MyText(Lang.tenant_worker_qrcode_tip, color7C, 12.sp),
            SizedBox(height: 16.sp),
            MyText(Lang.tenant_worker_qrcode_desc, color2B, 16.sp, FontWeight.w500),
            editField(
              Lang.input_tenant_worker_qrcode,
              codeController,
              (text) {
                setState(() {
                  scanActivity.qrCodeDescription = text;
                });
              },
              maxLength: 9,
            ),
            getLoginArea() != 0 ? const SizedBox() : MyText(Lang.logo_setting, color2B, 16.sp, FontWeight.w500),
            getLoginArea() != 0
                ? const SizedBox()
                : Container(
                    padding: EdgeInsets.fromLTRB(16.sp, 20.sp, 6.sp, 18.sp),
                    margin: EdgeInsets.only(top: 8.sp),
                    decoration: BoxDecoration(
                      border: Border.all(color: colorE1),
                      borderRadius: BorderRadius.circular(8.sp),
                    ),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(
                              width: 200.sp,
                              child: HeightText(Lang.report_show_ts_logo, color2B, 14.sp, 1.5),
                            ),
                            SizedBox(
                              height: 20.sp,
                              child: Switch(
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                activeColor: colorBrand,
                                value: scanActivity.showPdfLogo,
                                onChanged: (value) {
                                  setState(() {
                                    scanActivity.showPdfLogo = value;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                        Container(
                          width: 263.sp,
                          height: 41.sp,
                          margin: EdgeInsets.only(top: 8.sp, right: 10.sp),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: const Color(0xFFF8F7FC),
                            borderRadius: BorderRadius.circular(4.sp),
                          ),
                          child: Image.asset("res/imgs/pdf_logo.png", height: 24.sp),
                        ),
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  editField(
    String hintText,
    TextEditingController controller,
    dynamic onText, {
    TextInputType inputType = TextInputType.text,
    maxLength = 10000,
  }) {
    return Container(
      constraints: BoxConstraints(minHeight: 36.sp),
      margin: EdgeInsets.only(top: 8.sp, bottom: 16.sp),
      padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: colorE1,
        ),
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: TextFormField(
        maxLines: null,
        controller: controller,
        textAlign: TextAlign.start,
        maxLength: maxLength,
        keyboardType: inputType,
        onChanged: onText,
        style: TextStyle(fontSize: 14.sp, color: color2B, height: 1.5),
        decoration: InputDecoration(
          counterText: '',
          isDense: true,
          contentPadding: EdgeInsets.zero,
          border: InputBorder.none,
          hintText: hintText,
          hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var isKeyboardShow = MediaQuery.of(context).viewInsets.bottom > 5.sp;
    // logger("isKeyboardShow: $isKeyboardShow");
    return Scaffold(
      appBar: AppBar(
        title: Text(editMode ? Lang.edit_mouth_data_collect : Lang.add_mouth_data_collect),
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          // 触摸收起键盘
          hideKeyboard();
        },
        child: Column(
          children: [
            Expanded(
              child: _buildForm(),
            ),
            Container(
              height: isKeyboardShow ? 0 : 90.sp,
              decoration: BoxDecoration(
                border: Border(top: BorderSide(color: color2B.withOpacity(0.05))),
              ),
              padding: EdgeInsets.fromLTRB(20.sp, 14.sp, 20.sp, 34.sp),
              child: HCustomButton(
                key: globalButtonKey,
                initVisible: false,
                onPress: () {
                  hideKeyboard();
                  isPosting = true;
                  checkButtonState();
                  if (editMode) {
                    HHttp.request(
                      "/v3/quickscan/campaign/update",
                      "POST",
                      (data) {
                        postQrCode();
                      },
                      errCallBack: (err) {
                        isPosting = false;
                        checkButtonState();
                      },
                      params: {
                        "campaignId": scanActivity.campaignId,
                        "campaignName": scanActivity.campaignName,
                        "contactName": scanActivity.contactName,
                        "contactPhone": scanActivity.contactPhone,
                        "contactAddress": scanActivity.contactAddress,
                        "qrCodeDescription": scanActivity.qrCodeDescription,
                        "showPdfLogo": scanActivity.showPdfLogo,
                      },
                    );
                  } else {
                    HHttp.request(
                      "/v3/quickscan/campaign/create",
                      "POST",
                      (data) {
                        scanActivity.campaignId = data["campaignId"];
                        postQrCode();
                      },
                      errCallBack: (err) {
                        isPosting = false;
                        checkButtonState();
                      },
                      params: {
                        "campaignName": scanActivity.campaignName,
                        "contactName": scanActivity.contactName,
                        "contactPhone": scanActivity.contactPhone,
                        "contactAddress": scanActivity.contactAddress,
                        "qrCodeDescription": scanActivity.qrCodeDescription,
                        "showPdfLogo": scanActivity.showPdfLogo,
                      },
                    );
                  }
                },
                width: 335.sp,
                height: 42.sp,
                text: Lang.complete,
                ms: 3000,
              ),
            ),
          ],
        ),
      ),
    );
  }

  postQrCode() {
    if (updateQrCode) {
      if (isNotEmpty(scanActivity.qrcodePath)) {
        String fileName = scanActivity.qrcodePath.split("/").last;
        HHttp.uploadFileByServer(
          scanActivity.qrcodePath,
          "CAMPAIGN",
          {
            "name": fileName,
            "sourceId": scanActivity.campaignId,
            "sourceType": "CAMPAIGN",
            "fileType": 1,
            "persionId": currentUser.personId,
            "category": "810",
            "analysisFile": "FALSE",
          },
          okCallback: (data) {
            logger("uploadFileByServer $fileName success");
            pop();
          },
          errCallback: (err) {
            isPosting = false;
            checkButtonState();
            HHttp.toastError(err);
          },
        );
      } else if (isNotEmpty(scanActivity.qrCodeFileId)) {
        //删除二维码
        HHttp.request(
          "/v3/file/delete",
          "POST",
          (data) {
            pop();
          },
          params: {
            "id": scanActivity.qrCodeFileId,
          },
        );
      } else {
        pop();
      }
    } else {
      pop();
    }
  }
}
