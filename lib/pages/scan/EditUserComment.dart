import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';

class EditUserComment extends StatefulWidget {
  const EditUserComment({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _EditUserCommentState();
}

class _EditUserCommentState extends State<EditUserComment> {
  GlobalKey globalKey = GlobalKey();
  String? remarkTextOld;
  String? remarkText;

  @override
  initState() {
    super.initState();
    initAsyncState();
  }

  initAsyncState() async {}

  @override
  dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (remarkText == null) {
      remarkText = Global.getRouterParams(context)["data"] ?? "";
      remarkTextOld = remarkText;
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.remark),
        actions: [
          Padding(
              padding: EdgeInsets.only(right: 10.sp),
              child: Align(
                child: HCustomButton(
                  key: globalKey,
                  onPress: () async {
                    if (await isUltraIp()) {
                      eventBus.fire(EventUserComment(remarkText!));
                    } else {
                      Global.getRouterParams(context)["updateStateByType"]("newUserComment", remarkText);
                    }

                    Navigator.popUntil(context, ModalRoute.withName(scanPageName));
                  },
                  text: Lang.save,
                  width: 80.sp,
                  height: 30.sp,
                  fontSize: 14.sp,
                  initVisible: false,
                ),
              )),
        ],
      ),
      body: SafeArea(
          child: SingleChildScrollView(
        child: Padding(
            padding: EdgeInsets.fromLTRB(24.sp, 10.sp, 24.sp, 24.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  maxLines: null,
                  initialValue: remarkText,
                  textAlign: TextAlign.left,
                  onChanged: (value) {
                    setState(() {
                      remarkText = value;
                      if (remarkTextOld == remarkText) {
                        (globalKey.currentState as HCustomButtonState).setVisible(false);
                      } else {
                        (globalKey.currentState as HCustomButtonState).setVisible(true);
                      }
                    });
                  },
                  autofocus: true,
                  style: TextStyle(fontSize: 16.sp, color: color2B),
                  decoration: InputDecoration(
                    counterText: '',
                    border: InputBorder.none,
                    hintText: Lang.input_remark,
                    hintStyle: TextStyle(fontSize: 16.sp, color: const Color(0xFF999999)),
                  ),
                ),
              ],
            )),
      )),
    );
  }
}
