import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ToothCameraLRArrow extends StatefulWidget {
  const ToothCameraLRArrow({Key? key, required this.isShowMirrorUI})
      : super(key: key);
  final dynamic isShowMirrorUI;

  @override
  State<StatefulWidget> createState() => _ToothCameraLRArrowState();
}

class _ToothCameraLRArrowState extends State<ToothCameraLRArrow>
    with TickerProviderStateMixin {
  late Animation<double> aniUI;
  late AnimationController aniControllUI;

  late Animation<double> aniOpacity;
  late AnimationController aniOpacityControll;

  int aniTotalSec = 0;

  @override
  initState() {
    super.initState();
    aniTotalSec = 2;
    double startLeft = 0.5.sw - 150.sp;
    double endRight = 0.5.sw + 150.sp;

    aniControllUI = AnimationController(
        duration: Duration(seconds: aniTotalSec), vsync: this)
      ..addListener(() {
        setState(() {});
      });
    aniUI = CurvedAnimation(parent: aniControllUI, curve: Curves.linear);
    aniUI = TweenSequence([
      //这是一个动画序列，weight表示权重
      TweenSequenceItem(
          tween: Tween(begin: startLeft, end: endRight)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 1),
    ]).animate(aniControllUI)
      ..addListener(() {
        setState(() {});
      });

    aniOpacityControll = AnimationController(
        duration: Duration(seconds: aniTotalSec), vsync: this);
    aniOpacity =
        CurvedAnimation(parent: aniOpacityControll, curve: Curves.bounceInOut);
    aniOpacity = TweenSequence([
      //这是一个动画序列，weight表示权重
      TweenSequenceItem(
          tween: Tween(begin: 0.0, end: 1.0)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 1),
      TweenSequenceItem(
          tween: Tween(begin: 1.0, end: 1.0)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 4),
      TweenSequenceItem(
          tween: Tween(begin: 1.0, end: 0.0)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 1),
    ]).animate(aniOpacityControll)
      ..addListener(() {
        setState(() {});
      });

    playAnimate();
  }

  playAnimate() {
    aniControllUI.reset();
    aniControllUI.repeat();

    aniOpacityControll.reset();
    aniOpacityControll.repeat();
  }

  @override
  dispose() {
    aniControllUI.dispose();
    aniOpacityControll.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
        left: widget.isShowMirrorUI
            ? (1.sw - aniUI.value - 25.sp)
            : (aniUI.value - 25.sp),
        bottom: 80.sp + ScreenUtil().bottomBarHeight,
        width: 48.sp,
        child: Opacity(
          opacity: aniOpacity.value,
          child: Transform(
              alignment: Alignment.center,
              transform: widget.isShowMirrorUI
                  ? Matrix4.diagonal3Values(-1.0, 1.0, 1.0)
                  : Matrix4.diagonal3Values(1.0, 1.0, 1.0),
              child: Image.asset("res/scan/scanline_arrow.png")),
        ));
  }
}
