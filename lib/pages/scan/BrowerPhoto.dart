import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class BrowerPhoto extends StatefulWidget {
  final List imgPathList;
  final int defaultShowIndex;
  final String title;
  final String content;
  final List titles;
  final List contents;
  final dynamic deleteCalllBack;
  final double initScale;
  Widget lbWidget;
  Widget cbWidget;
  TypeCallback<int>? onPageChanged;

  BrowerPhoto({
    Key? key,
    required this.imgPathList,
    this.defaultShowIndex = 0,
    this.deleteCalllBack,
    this.title = "",
    this.content = "",
    this.initScale = 1,
    this.titles = const [],
    this.contents = const [],
    this.lbWidget = const SizedBox(),
    this.cbWidget = const SizedBox(),
    this.onPageChanged,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _BrowerPhotoState();
}

class _BrowerPhotoState extends State<BrowerPhoto> {
  int curShowIndex = 0;
  late PageController pageController;

  @override
  initState() {
    super.initState();
    curShowIndex = widget.defaultShowIndex;
    pageController = PageController(initialPage: widget.defaultShowIndex);
  }

  @override
  dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Click(
      child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.2),
          ),
          child: Stack(
            children: [
              PhotoViewGallery.builder(
                scrollPhysics: const BouncingScrollPhysics(),
                builder: (BuildContext context, int index) {
                  dynamic provider = widget.imgPathList[index].contains("http")
                      ? NetworkImage(widget.imgPathList[index])
                      : FileImage(File(getLocalPath(widget.imgPathList[index])));
                  return PhotoViewGalleryPageOptions(
                    filterQuality: FilterQuality.high, //默认低画质有马赛克
                    imageProvider: provider,
                    initialScale: PhotoViewComputedScale.contained * widget.initScale,
                    heroAttributes: PhotoViewHeroAttributes(tag: "galleryItems_id_$index"),
                  );
                },
                itemCount: widget.imgPathList.length,
                loadingBuilder: (context, event) => Center(
                  child: SizedBox(
                    width: 20.0,
                    height: 20.0,
                    child: CircularProgressIndicator(
                      value: event == null
                          ? 0
                          : (event.expectedTotalBytes == null)
                              ? 0
                              : (event.cumulativeBytesLoaded / event.expectedTotalBytes!),
                    ),
                  ),
                ),
                pageController: pageController,
                backgroundDecoration: const BoxDecoration(
                  color: Colors.transparent,
                ),
                onPageChanged: (index) {
                  setState(() {
                    curShowIndex = index;
                  });
                  if (widget.onPageChanged != null) {
                    widget.onPageChanged!(index);
                  }
                },
              ),
              Align(
                alignment: Alignment.topLeft,
                child: Padding(
                    padding: EdgeInsets.fromLTRB(0.sp, 24.sp, 48.sp, 20.sp),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            IconButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              icon: Icon(Icons.arrow_back, color: Colors.white, size: 48.sp),
                            ),
                            MyText(getTitle(), Colors.white, 36.sp, FontWeight.w600),
                            widget.deleteCalllBack == null
                                ? SizedBox(width: 32.sp)
                                : GestureDetector(
                                    onTap: widget.deleteCalllBack,
                                    child: Image.asset("res/icons/delete.png", height: 32.sp)),
                          ],
                        ),
                        SizedBox(height: 18.sp),
                        MyText(
                            isNotEmpty(widget.contents) && widget.contents.length >= widget.imgPathList.length
                                ? widget.contents[curShowIndex]
                                : widget.content,
                            Colors.white,
                            24.sp),
                      ],
                    )),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 50.sp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      widget.lbWidget,
                      widget.cbWidget,
                      const SizedBox(),
                      // GestureDetector(
                      //   onTap: () async {
                      //     await Global.saveImageFileFromCache(widget.imgPathList[curShowIndex]);
                      //   },
                      //   child: Image.asset("res/icons/icon_download.png", width: 24.sp),
                      // ),
                    ],
                  ),
                ),
              ),
              widget.imgPathList.length > 1
                  ? Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 80.sp),
                        child: Click(
                          onTap: () {
                            if (curShowIndex > 0) {
                              pageController.animateToPage(
                                curShowIndex - 1,
                                duration: const Duration(milliseconds: 200),
                                curve: Curves.linear,
                              );
                              setState(() {
                                curShowIndex = curShowIndex - 1;
                                if (widget.onPageChanged != null) {
                                  widget.onPageChanged!(curShowIndex);
                                }
                              });
                            }
                          },
                          child: Image.asset(
                            curShowIndex == 0 ? "res/imgs/big_left_disable.png" : "res/imgs/big_left_enable.png",
                            width: 320.sp,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
              widget.imgPathList.length > 1
                  ? Align(
                      alignment: Alignment.centerRight,
                      child: Padding(
                        padding: EdgeInsets.only(right: 80.sp),
                        child: Click(
                          onTap: () {
                            if (curShowIndex < widget.imgPathList.length - 1) {
                              pageController.animateToPage(
                                curShowIndex + 1,
                                duration: const Duration(milliseconds: 200),
                                curve: Curves.linear,
                              );
                              setState(() {
                                curShowIndex = curShowIndex + 1;
                                if (widget.onPageChanged != null) {
                                  widget.onPageChanged!(curShowIndex);
                                }
                              });
                            }
                          },
                          child: Image.asset(
                            curShowIndex == widget.imgPathList.length - 1
                                ? "res/imgs/big_right_disable.png"
                                : "res/imgs/big_right_enable.png",
                            width: 320.sp,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
            ],
          )),
      onTap: () {
        Navigator.pop(context);
      },
    );
  }

  String getTitle() {
    if (isNotEmpty(widget.titles) && widget.titles.length > curShowIndex) {
      return widget.titles[curShowIndex];
    } else if (isNotEmpty(widget.title)) {
      return widget.title;
    } else {
      return widget.imgPathList.length <= 1 ? "" : "${curShowIndex + 1} / ${widget.imgPathList.length}";
    }
  }
}
