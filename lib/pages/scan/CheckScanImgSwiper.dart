import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/swiper/swiper.dart';
import 'package:mooeli/widget/swiper/swiper_controller.dart';

class CheckScanImgSwiper extends StatefulWidget {
  final String initChooseImgPath;
  final List scanImgInfoList;
  final dynamic updateStateByType;
  final bool isCanReScan;

  const CheckScanImgSwiper({
    Key? key,
    required this.updateStateByType,
    required this.isCanReScan,
    required this.initChooseImgPath,
    required this.scanImgInfoList,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _CheckScanImgSwiperState();
  }
}

class _CheckScanImgSwiperState extends State<CheckScanImgSwiper> {
  List<Widget> allSwiperChildrenWidgets = [];

  int currentIndex = 0;

  SwiperController swiperController = SwiperController();

  @override
  void initState() {
    for (int i = 0; i < widget.scanImgInfoList.length; i++) {
      if (widget.scanImgInfoList[i]["img"] == widget.initChooseImgPath) {
        currentIndex = i;
      }
    }
    getAllWidget();
    super.initState();
  }

  @override
  void dispose() {
    AudioPlayerUtil.stopSound();
    BotToast.cleanAll();
    super.dispose();
  }

  getExampleImgByImgInfo(Map imgInfo) {
    ToothCameraTypeEnum type = imgInfo["type"];
    int index = imgInfo["index"];
    String imgName = "";
    switch (type) {
      case ToothCameraTypeEnum.maxillary:
        imgName = "print_scan_up.png";
        break;
      case ToothCameraTypeEnum.occlusion:
        imgName = "print_scan_close_${index == 0 ? "left" : index == 1 ? "mid" : "right"}.png";
        break;
      case ToothCameraTypeEnum.openMaskOff:
        imgName = "print_scan_open_${index == 0 ? "left" : index == 1 ? "mid" : "right"}.png";
        break;
      case ToothCameraTypeEnum.openMaskOn:
        imgName = "print_scan_mask_${index == 0 ? "left" : index == 1 ? "mid" : "right"}.png";
        break;
      case ToothCameraTypeEnum.mandibular:
        imgName = "print_scan_down.png";
        break;
    }
    return "res/scan/$imgName";
  }

  getExampleVideoName(Map eachScanInfo) {
    bool isUltra = isUltraImage(eachScanInfo["img"]);
    ToothCameraTypeEnum type = eachScanInfo["type"];
    return getScanHelpVideo(type, isUltra);
  }

  getExampleAudioName(Map eachScanInfo) {
    bool isUltra = isUltraImage(eachScanInfo["img"]);
    ToothCameraTypeEnum type = eachScanInfo["type"];
    return getScanHelpAudio(type, isUltra);
  }

  //有可能存在txt 和 img
  //示例:  1.axxasdfasd[img]res/icons/xx.jpg\n2.asdfasdfasak[img]res/scan/yy.png\n3.asdfasdf
  getScanFunWidgetsByText(String fun) {
    List<Widget> listWidgets = [];
    List allListStrs = [];
    List<String> splitList = fun.split("\n");
    for (int i = 0; i < splitList.length; i++) {
      if (splitList[i].contains("[img]")) {
        allListStrs.addAll(splitList[i].split("[img]"));
      } else {
        allListStrs.add(splitList[i]);
      }
    }
    for (int i = 0; i < allListStrs.length; i++) {
      if (allListStrs[i].contains(".jpg") || allListStrs[i].contains(".gif") || allListStrs[i].contains(".png")) {
        listWidgets.add(Padding(
            padding: EdgeInsets.only(bottom: 6.sp, top: 2.sp),
            child: ClipRRect(borderRadius: BorderRadius.circular(8.sp), child: Image.asset(allListStrs[i]))));
      } else {
        listWidgets.add(Padding(
            padding: EdgeInsets.only(bottom: 1.sp, top: 1.sp),
            child: Text(allListStrs[i], style: TextStyle(fontSize: 14.sp, color: color7C))));
      }
    }
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: listWidgets);
  }

  getAllWidget() {
    allSwiperChildrenWidgets = [];
    for (int i = 0; i < widget.scanImgInfoList.length; i++) {
      Map eachScanInfo = widget.scanImgInfoList[i];
      logger("eachScanInfo: $eachScanInfo", key: "getAllWidget");
      String title = getTitleByImgRow(eachScanInfo["type"]);
      bool isPlaque = widget.scanImgInfoList[i]["light"] ?? false;
      if (isPlaque) {
        title = getTitlePlaque(eachScanInfo["type"]);
      }
      String videoName = getExampleVideoName(eachScanInfo);
      logger("videoName $videoName");
      int errorCode = getErrorCode(eachScanInfo["img"]);
      if (isUltraLightError(errorCode, eachScanInfo["img"])) {
        errorCode = 0;
      }
      Map errorInfo = Global.getAlertTextByScanErrorCode(errorCode, eachScanInfo["img"]);
      String exampleImg = getExampleImgByImgInfo(eachScanInfo);
      allSwiperChildrenWidgets.add(
        Container(
          width: 725.sp,
          margin: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 46.sp),
          padding: EdgeInsets.fromLTRB(32.sp, 24.sp, 32.sp, 32.sp),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              title == "" ? const SizedBox() : MyText("${i + 1}. $title", color2B, 28.sp, FontWeight.w500),
              Container(
                width: 647.sp,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(12.sp, 8.sp, 12.sp, 8.sp),
                decoration: BoxDecoration(
                  color: errorCode != 0 ? const Color(0xffC84C4C).withOpacity(0.1) : colorCyan.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.sp),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                        padding: EdgeInsets.only(right: 8.sp),
                        child: Image.asset(
                            errorCode != 0 ? "res/scan/scan_img_swiper_error.png" : "res/scan/scan_img_swiper_ok.png",
                            height: 36.sp)),
                    SizedBox(
                      width: 568.sp,
                      child: Text(
                        " ${errorInfo["content"]}",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.w500,
                            color: errorCode == 0 ? colorCyan : const Color(0xFFC84C4C)),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.sp),
              GestureDetector(
                onTap: () {
                  Global.showPhotoBrowerModal(
                    context,
                    imgPathList: widget.scanImgInfoList.map((e) => e["img"]).toList(),
                    titles: widget.scanImgInfoList.map((e) => getTitleByImgRow(e["type"])).toList(),
                    defaultShowIndex: i,
                    initScale: 0.75,
                  );
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.sp),
                  child: Stack(
                    children: [
                      Image.file(
                        File(getLocalPath(widget.scanImgInfoList[i]["img"])),
                        width: 660.sp,
                        height: 370.sp,
                        fit: BoxFit.cover,
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: color2B.withOpacity(0.4),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(16.sp),
                          ),
                        ),
                        padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 16.sp),
                        child: Text(Lang.your_scan_result, style: TextStyle(fontSize: 26.sp, color: Colors.white)),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 12.sp),
              ClipRRect(
                borderRadius: BorderRadius.circular(16.sp),
                child: Stack(
                  children: [
                    Image.asset(
                      exampleImg,
                      width: 660.sp,
                      height: 370.sp,
                      fit: BoxFit.cover,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: color2B.withOpacity(0.4),
                        borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(16.sp),
                        ),
                      ),
                      padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 16.sp),
                      child: Text(Lang.standard_scan, style: TextStyle(fontSize: 26.sp, color: Colors.white)),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white54,
      appBar: AppBar(
        title: MyText(Lang.scan_detail, Colors.white, 36.sp, FontWeight.w600),
        backgroundColor: Colors.transparent,
        iconTheme: IconThemeData(size: 48.sp, color: Colors.white),
      ),
      body: Swiper(
        controller: swiperController,
        index: currentIndex,
        onIndexChanged: (index) {
          setState(() {
            currentIndex = index;
          });
        },
        onTap: (index) {
          swiperController.move(index);
        },
        viewportFraction: 0.4,
        scale: 0.84,
        loop: false,
        itemBuilder: (BuildContext context, int index) {
          return allSwiperChildrenWidgets[index];
        },
        itemCount: allSwiperChildrenWidgets.length,
      ),
    );
  }
}
