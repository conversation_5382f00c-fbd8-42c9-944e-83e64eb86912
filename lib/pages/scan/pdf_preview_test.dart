import 'dart:collection';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/utils/WidgetToImageConverter.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/ZoomableView.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

class ScanPdfPreviewPage extends BasePage {
  MooeliItem mooeli;
  ScanInfo? scanInfo;
  String? recordId;

  ScanPdfPreviewPage(
    this.mooeli, {
    Key? key,
    this.recordId,
    this.scanInfo,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanPdfPreviewPageState();
  }
}

class ScanPdfPreviewPageState extends BasePageState<ScanPdfPreviewPage> {
  final GlobalKey _buttonKey = GlobalKey<HCustomButtonState>();
  final GlobalKey _bodyKey = GlobalKey();

  final double headerHeight = 32.sp, itemHeight = 48.sp;

  bool loaded = false;

  List<MooeliPhase> phaseList = [];
  MooeliRecord selectedRecord = MooeliRecord();
  Map<String, List<MooeliRecord>> recordMap = {};

  List<MooeliFile> fileList = [];
  int status = 0, algorithmStatus = 0;
  String? diseaseYati, diseaseYazhou, diseaseYalie, patientFeedback;
  bool isProviderFeedback = false;
  bool hasAiResult = false;
  String batText = "";
  Widget? fitWidget;

  String stageText = "";

  ScanInfo scanInfo = ScanInfo();

  @override
  initState() {
    super.initState();
    if (widget.scanInfo != null) {
      scanInfo = widget.scanInfo!;
      addEventListener<EventUploadRecordResult>((event) {
        if (scanInfo.recordId == event.record.recordId) {
          setState(() {
            scanInfo = event.record;
          });
        }
      });
      getScanConfigJson((json) {
        if (isNotEmpty(json) && isNotEmpty(json["ProgressStage"])) {
          Map stageMap = json["ProgressStage"];
          for (Map stage in stageMap.values) {
            if (stage["code"] == scanInfo.progressStage) {
              setState(() {
                stageText = stage["data"];
              });
            }
          }
        }
      });

      for (String key in faceMap.keys) {
        addFaceFile(int.parse(key), scanInfo.getPhoto(key));
      }
      addMouthFile(108, scanInfo.upPhotos, 0);
      addMouthFile(105, scanInfo.closePhotos, 0);
      addMouthFile(103, scanInfo.closePhotos, 1);
      addMouthFile(107, scanInfo.closePhotos, 2);
      addMouthFile(152, scanInfo.openPhotos, 0);
      addMouthFile(151, scanInfo.openPhotos, 1);
      addMouthFile(153, scanInfo.openPhotos, 2);
      addMouthFile(155, scanInfo.maskPhotos, 0);
      addMouthFile(154, scanInfo.maskPhotos, 1);
      addMouthFile(156, scanInfo.maskPhotos, 2);
      addMouthFile(106, scanInfo.downPhotos, 0);
      logger("recordId: ${widget.recordId}");
      if (scanInfo.status != RecordStatus.completeUpload) {
        loaded = true;
      } else {
        selectedRecord.recordId = widget.recordId!;
        getFileList();
      }
    } else {
      addEventListener<EventFeedback>((event) {
        if (event.recordId == selectedRecord.recordId) {
          setState(() {
            isProviderFeedback = event.feedback;
          });
          if (_buttonKey.currentState != null) {
            (_buttonKey.currentState as HCustomButtonState).setText(isProviderFeedback ? "查看反馈" : "发送反馈");
          }
        }
      });
      getPhaseList();
    }
  }

  addFaceFile(int type, String path) {
    if (isNotEmpty(path)) {
      MooeliFile file = MooeliFile();
      file.id = "${scanInfo.recordId}_$type";
      file.attribute = type.toString();
      file.imagePath = path;
      fileList.add(file);
    }
  }

  addMouthFile(int type, List list, int index) {
    if (isNotEmpty(list) && list.length > index) {
      MooeliFile file = MooeliFile();
      file.id = "${scanInfo.recordId}_$type";
      file.attribute = type.toString();
      file.imagePath = list[index];
      fileList.add(file);
    }
  }

  getPhaseList() {
    HHttp.request(
      "/v3/quickscan/scanRecord/get",
      "POST",
      (data) {
        setState(() {
          phaseList = (data['phaseList'] as List).map((i) => MooeliPhase.fromJson(i)).toList();
        });
        if (isNotEmpty(phaseList)) {
          getRecordList(phaseList.first, true);
        }
      },
      params: {
        "caseId": widget.mooeli.caseId,
        "pageCount": 50,
        "pageIndex": 0,
      },
    );
  }

  getRecordList(MooeliPhase phase, bool selected) {
    HHttp.request(
      "/v3/monitor/record/page",
      "POST",
      (data) {
        setState(() {
          List<MooeliRecord> recordList = (data['recordList'] as List).map((i) => MooeliRecord.fromJson(i)).toList();
          if (isNotEmpty(recordList)) {
            recordMap[phase.phaseId] = recordList;

            if (isNotEmpty(widget.recordId)) {
              for (MooeliRecord record in recordList) {
                if (record.recordId == widget.recordId) {
                  selectedRecord = record;
                  selectedRecord.phaseName = phase.phaseName;
                  getFileList();
                  break;
                }
              }
            }

            if (selected) {
              if (isEmpty(widget.recordId)) {
                selectedRecord = recordList.first;
                selectedRecord.phaseName = phase.phaseName;
                getFileList();
              }
              for (int i = 1; i < phaseList.length; i++) {
                getRecordList(phaseList[i], false);
              }
            }
          } else {
            setState(() {
              loaded = true;
            });
          }
        });
      },
      params: {
        "phaseId": phase.phaseId,
        "pageCount": 50,
        "pageIndex": 0,
      },
    );
  }

  getFileList([bool onlyShowStatus = false]) {
    HHttp.request(
      "/v3/quickscan/scanRecord/get",
      "POST",
      (data) {
        setState(() {
          status = getJsonInt(data, "status");
          loaded = status == 7 || status == 8;
          fileList = (data["fileInfoList"] as Map).values.map((i) => MooeliFile.fromJson(i, true)).toList();
          if (isNotEmpty(data["algorithmInfoMap"]) && isNotEmpty((data["algorithmInfoMap"] as Map).values)) {
            String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
            getAlgorithmResult(algorithmId, (json) {
              getDiseaseAnalyticsResult(json);
              getFitAnalyticsResult(json);
              setState(() {
                hasAiResult = true;
              });
            });
          }
          algorithmStatus = getJsonInt(data, "algorithmStatus");
          // if (algorithmStatus == 5 || algorithmStatus == 7) {
          //   HHttp.request(
          //     "/v2/analysis/forceReplayByRecordAndSource",
          //     "POST",
          //     (data) {
          //       delay(1000, () {
          //         getFileList(true);
          //       });
          //     },
          //     params: {
          //       "id": selectedRecord.recordId,
          //       "sourceType": "5006" // 字符串类型
          //     },
          //   );
          // }
          if (onlyShowStatus) {
            if (algorithmStatus == 6) {
              delay(1000, () {
                getFileList(true);
              });
            }
          } else {
            if (loaded) {
              if (algorithmStatus == 6) {
                delay(1000, () {
                  getFileList(true);
                });
              }
            } else {
              delay(1000, () {
                getFileList();
              });
            }
            scanInfo.tagList = data["tagList"] as List;
            scanInfo.note = getJsonString(data, "note");
            patientFeedback = data["patientFeedback"];
            isProviderFeedback = data["isProviderFeedback"];
          }
          if (_buttonKey.currentState != null) {
            (_buttonKey.currentState as HCustomButtonState).setText(isProviderFeedback ? "查看反馈" : "发送反馈");
          }
        });
        if (isNotEmpty(fileList)) {
          List<String> ids = [];
          for (var file in fileList) {
            if (isNotEmpty(file.id)) {
              ids.add(file.id);
            }
          }
          downloadOrigin(ids, (id, path) {
            for (var file in fileList) {
              if (file.id == id) {
                setState(() {
                  file.imagePath = path;
                });
              }
            }
          }, "");
        }
      },
      params: {
        "id": selectedRecord.recordId,
        "onlyShowStatus": onlyShowStatus,
      },
    );
  }

  void getDiseaseAnalyticsResult(json) {
    String result = "";
    Map<int, Set> map = {};
    try {
      for (dynamic direction in (json["image"] as Map).values) {
        for (dynamic descriptions in (direction["disease"] as Map).values) {
          if (isNotEmpty(descriptions["map"] as List)) {
            if (map[descriptions["label"]] == null) {
              map[descriptions["label"]] = <dynamic>{};
            }
            map[descriptions["label"]]!.addAll(descriptions["map"]);
          }
        }
      }
    } catch (ex) {
      //
    }
    if (isNotEmpty(map)) {
      setState(() {
        diseaseYati = getDiseaseText(map, [1, 2, 3, 4, 5, 6, 11, 9]);
        diseaseYazhou = getDiseaseText(map, [8, 7]);
        diseaseYalie = getDiseaseText(map, [10, 12, 13]);
      });
    }
  }

  String getDiseaseText(Map<int, Set> map, List<int> ids) {
    String result = "";
    for (int id in ids) {
      if (diseaseNameMap.containsKey(id.toString()) && map.containsKey(id)) {
        if (isNotEmpty(result)) {
          result = "$result\n";
        }
        result =
            "$result疑似${diseaseNameMap[id.toString()]}：${SplayTreeSet.from(map[id] as Set, (int a, int b) => a - b).join("、")}";
      }
    }
    return result;
  }

  void getFitAnalyticsResult(json) {
    List<Widget> children = [];
    fitText(String text) {
      return HeightText(text, color2B, 14.sp, 1.6);
    }

    batText = "";
    try {
      if (json["image"]["left"]["info"]["molar"]["type"] != null ||
          json["image"]["right"]["info"]["molar"]["type"] != null) {
        String result = "";
        if (json["image"]["left"]["info"]["molar"]["type"] != null) {
          result = "左侧 ${getMolarDesc(json["image"]["left"]["info"]["molar"]["type"])}";
        }
        if (json["image"]["right"]["info"]["molar"]["type"] != null) {
          result =
              "${isNotEmpty(result) ? "$result、" : ""}右侧 ${getMolarDesc(json["image"]["right"]["info"]["molar"]["type"])}";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("磨牙关系："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
        batText += "磨牙关系：$result\n";
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["left"]["info"]["canine"]["type"] != null ||
          json["image"]["right"]["info"]["canine"]["type"] != null) {
        String result = "";
        if (json["image"]["left"]["info"]["canine"]["type"] != null) {
          result = "左侧 ${getMolarDesc(json["image"]["left"]["info"]["canine"]["type"], true)}";
        }
        if (json["image"]["right"]["info"]["canine"]["type"] != null) {
          result =
              "${isNotEmpty(result) ? "$result、" : ""}右侧 ${getMolarDesc(json["image"]["right"]["info"]["canine"]["type"], true)}";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("尖牙关系："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
        batText += "尖牙关系：$result\n";
      }
    } catch (ex) {
      //
    }
    try {
      children.add(
        Padding(
          padding: EdgeInsets.only(top: 8.sp),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              fitText("中线关系："),
              Expanded(
                child: fitText(getMidDesc(json["image"]["front"]["info"]["middle_line"]["type"])),
              ),
            ],
          ),
        ),
      );
      batText += "中线关系：${getMidDesc(json["image"]["front"]["info"]["middle_line"]["type"])}\n";
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["front"]["info"]["bite"]["type"] > -5) {
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("覆𬌗关系："),
                Expanded(
                  child: fitText(getBiteDesc(json["image"]["front"]["info"]["bite"]["type"])),
                ),
              ],
            ),
          ),
        );
        batText += "中线关系：${getBiteDesc(json["image"]["front"]["info"]["bite"]["type"])}\n";
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["left"]["info"]["overjet"]["type"] > -3) {
        String result = getOverjetDesc(json["image"]["left"]["info"]["overjet"]["type"]);
        if (json["image"]["left"]["info"]["overjet"]["ref"] != null) {
          double ref = json["image"]["left"]["info"]["overjet"]["ref"];
          result = "$result、覆盖 ${ref.toStringAsFixed(2)} mm";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("覆盖关系："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
        batText += "覆盖关系：$result\n";
      } else if (json["image"]["right"]["info"]["overjet"]["type"] > -3) {
        String result = getOverjetDesc(json["image"]["right"]["info"]["overjet"]["type"]);
        if (json["image"]["right"]["info"]["overjet"]["ref"] != null) {
          double ref = json["image"]["right"]["info"]["overjet"]["ref"];
          result = "$result、覆盖 ${ref.toStringAsFixed(2)} mm";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("覆盖关系："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
        batText += "覆盖关系：$result\n";
      }
    } catch (ex) {
      //
    }
    if (isNotEmpty(children)) {
      setState(() {
        fitWidget = Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorF3,
                ),
                child: MyText(Lang.bat_relation, color2B, 14.sp, FontWeight.w500),
              ),
              ...children,
            ],
          ),
        );
      });
    }
  }

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        leading: Builder(
          builder: (BuildContext context) {
            return IconButton(
              icon: Platform.isAndroid ? const Icon(Icons.arrow_back) : const Icon(Icons.arrow_back_ios_new),
              onPressed: pop,
            );
          },
        ),
        title: Text(Lang.patient_detail),
        actions: [
          IconButton(
            onPressed: () async {
              final pdf = pw.Document(
                pageMode: PdfPageMode.fullscreen,
                theme: pw.ThemeData.withFont(
                  base: pw.Font.ttf(
                    await rootBundle.load("assets/fonts/PdfFont.ttf"),
                  ),
                  bold: pw.Font.ttf(
                    await rootBundle.load("assets/fonts/PdfFont.ttf"),
                  ),
                ),
              );
              pdf.addPage(
                pw.Page(
                  orientation: pw.PageOrientation.portrait,
                  pageFormat: const PdfPageFormat(210 * PdfPageFormat.mm, 1000 * PdfPageFormat.mm),
                  margin: pw.EdgeInsets.zero,
                  build: (pw.Context context) {
                    return pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: _getPdfListView(),
                    );
                  },
                ),
              );
              // Uint8List? bytes = await captureWidgetToImage(_bodyKey);
              // if (bytes != null) {
              //   final pdf = pw.Document();
              //   pdf.addPage(
              //     pw.Page(
              //       orientation: pw.PageOrientation.portrait,
              //       pageFormat: const PdfPageFormat(210 * PdfPageFormat.mm, 3500 * PdfPageFormat.mm),
              //       margin: pw.EdgeInsets.zero,
              //       build: (pw.Context context) {
              //         return pw.Image(pw.MemoryImage(bytes!));
              //       },
              //     ),
              //   );
              // }
              Directory directory = await getTemporaryDirectory();
              final file = File('${directory.path}/${scanInfo.recordName}.pdf');
              await file.writeAsBytes(await pdf.save());
              OpenFile.open(file.path);
            },
            icon: Icon(
              Icons.preview,
              color: color7C,
              size: 20.sp,
            ),
          ),
          IconButton(
            onPressed: () async {
              Directory directory = await getTemporaryDirectory();
              final file = File('${directory.path}/${scanInfo.recordName}.pdf');
              Share.shareXFiles([XFile(file.path)]);
            },
            icon: Icon(
              Icons.share,
              color: color7C,
              size: 20.sp,
            ),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 0),
        child: ZoomableView(
          child: WidgetToImageConverter(
            key: _bodyKey,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _getListView(),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void onRouteResume(Route nextRoute) {
    //这里是防止图片浏览魔法效果返回后白屏的现象
    setState(() {});
  }

  //是否包含types中任意一种
  bool containTypes(List<int> types) {
    if (isNotEmpty(fileList)) {
      for (MooeliFile file in fileList) {
        for (int type in types) {
          if (file.attribute == type.toString()) {
            return true;
          }
        }
      }
    }
    return false;
  }

  List<MooeliFile> getFilesByType(List<int> types) {
    List<MooeliFile> list = [];
    for (int type in types) {
      MooeliFile? file = getFileByType(type);
      if (file != null) {
        list.add(file);
      }
    }
    return list;
  }

  MooeliFile? getFileByType(int type) {
    if (isNotEmpty(fileList)) {
      for (MooeliFile file in fileList) {
        if (file.attribute == type.toString()) {
          return file;
        }
      }
    }
    return null;
  }

  getMouthPhotoView(int type, String defaultImage) {
    MooeliFile? file = getFileByType(type);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 12.sp),
        ClipRRect(
          borderRadius: BorderRadius.circular(8.sp),
          child: SizedBox(
            width: 94.sp,
            height: 65.sp,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  "res/imgs/$defaultImage",
                  width: 94.sp,
                  height: 65.sp,
                  fit: BoxFit.cover,
                ),
                file != null
                    ? Image.file(
                        File(file.imagePath),
                        width: 94.sp,
                        height: 65.sp,
                        fit: BoxFit.cover,
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ),
        SizedBox(height: 4.sp),
        MyText(categoryMap[type.toString()]!, color2B, 12.sp),
      ],
    );
  }

  Widget getFaceItemView(MooeliFile file) {
    String title = faceMap[file.attribute] ?? "";
    return SizedBox(
      height: 128.sp,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          MyText(title, color7C, 14.sp, FontWeight.w500),
          SizedBox(height: 8.sp),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.sp),
            child: isNotEmpty(file.imagePath)
                ? getImgByUrlOrPath(file.imagePath, 80.sp)
                : Image.asset("res/scan/default_face_${file.attribute}.png", width: 80.sp, height: 80.sp),
          ),
          SizedBox(height: 16.sp),
        ],
      ),
    );
  }

  getFacePhotoView() {
    List<int> types = faceMap.keys.map((e) => int.parse(e)).toList();
    List<MooeliFile> faceFiles = [];
    for (int type in types) {
      MooeliFile? file = getFileByType(type);
      if (file != null) {
        faceFiles.add(file);
      }
    }
    if (isEmpty(faceFiles)) {
      return const SizedBox();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 0.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyText(Lang.face_photo, color2B, 16.sp, FontWeight.w500),
          SizedBox(height: 18.sp),
          SizedBox(
            height: 128.sp * (faceFiles.length / 3.0).ceil(),
            child: GridView(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 0.75,
              ),
              children: faceFiles.map((file) => getFaceItemView(file)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  PdfColor pwColor(Color color) {
    return PdfColor.fromInt(color.value);
  }

  pw.Text pwMyText(String text, Color color, double size, [pw.FontWeight? weight]) {
    return pw.Text(
      text,
      style: pw.TextStyle(
        fontSize: size,
        color: pwColor(color),
        fontWeight: weight ?? pw.FontWeight.normal,
        fontFallback: [pw.Font()],
      ),
      softWrap: true,
    );
  }

  pw.Text pwHeightText(String text, Color color, double size, double height,
      [pw.FontWeight? weight, pw.TextAlign? align]) {
    return pw.Text(
      text,
      textAlign: align ?? pw.TextAlign.left,
      style: pw.TextStyle(
        fontSize: size,
        color: pwColor(color),
        height: height,
        fontWeight: weight ?? pw.FontWeight.normal,
      ),
      softWrap: true,
    );
  }

  pw.MemoryImage pwImage(String path) {
    File imageFile = File(getLocalPath(path));
    Uint8List imageData = imageFile.readAsBytesSync();
    return pw.MemoryImage(imageData);
  }

  pw.Widget getPdfFacePhotoView() {
    List<int> types = faceMap.keys.map((e) => int.parse(e)).toList();
    List<pw.Widget> faceViews = [];
    for (int type in types) {
      MooeliFile? file = getFileByType(type);
      if (file != null) {
        faceViews.add(getPdfFaceItemView(file));
      }
    }

    return pw.Column(
      mainAxisSize: pw.MainAxisSize.min,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pwMyText(Lang.face_photo, color2B, 16.sp, pw.FontWeight.bold),
        pw.SizedBox(height: 18.sp),
        pw.GridView(
          crossAxisCount: 3,
          childAspectRatio: 0.75,
          children: faceViews,
        ),
      ],
    );
  }

  pw.Widget getPdfFaceItemView(MooeliFile file) {
    String title = faceMap[file.attribute] ?? "";
    pw.MemoryImage? image;
    if (isNotEmpty(file.imagePath)) {
      image = pwImage(file.imagePath);
    }
    return pw.Column(
      mainAxisSize: pw.MainAxisSize.min,
      children: [
        pwMyText(title, color7C, 14.sp, pw.FontWeight.bold),
        pw.SizedBox(height: 8.sp),
        pw.ClipRRect(
          horizontalRadius: 8.sp,
          verticalRadius: 8.sp,
          child: image != null
              ? pw.Image(image, width: 100.sp, height: 100.sp, fit: pw.BoxFit.cover)
              : pw.SizedBox(width: 100.sp, height: 100.sp),
        ),
        pw.SizedBox(height: 16.sp),
      ],
    );
  }

  pw.Widget getPdfScanStepView() {
    double width = 160.sp, height = 90.sp;
    List<pw.Widget> listWidgets = [];
    List<ToothCameraTypeEnum> scanSteps = [
      ToothCameraTypeEnum.maxillary, //上颌
      ToothCameraTypeEnum.occlusion, //咬合
      ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
      ToothCameraTypeEnum.openMaskOn, //微张(带上牙套)
      ToothCameraTypeEnum.mandibular, //下颌
    ];
    for (int i = 0; i < 5; i++) {
      List targetList = [];
      if (i == 0) {
        targetList = getFilesByType([108]).map((e) => e.imagePath).toList();
      } else if (i == 1) {
        targetList = getFilesByType([105, 103, 107]).map((e) => e.imagePath).toList();
      } else if (i == 2) {
        targetList = getFilesByType([152, 151, 153]).map((e) => e.imagePath).toList();
      } else if (i == 3) {
        targetList = getFilesByType([155, 154, 156]).map((e) => e.imagePath).toList();
      } else if (i == 4) {
        targetList = getFilesByType([106]).map((e) => e.imagePath).toList();
      }
      if (isEmpty(targetList)) {
        continue;
      }
      String title = getTitleByImgRow(scanSteps[i]);
      List<pw.Widget> rowChilds = [];
      for (int j = 0; j < (i == 0 || i == 4 ? 1 : 3); j++) {
        rowChilds.add(
          pw.Container(
            margin: pw.EdgeInsets.only(bottom: i == 4 ? 0 : 16.sp),
            width: width,
            height: height,
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.circular(8.sp),
              color: pwColor(const Color(0xffe6e6e6)),
            ),
            child: pw.ClipRRect(
              horizontalRadius: 8.sp,
              verticalRadius: 8.sp,
              child: j < targetList.length ? pw.Image(pwImage(getLocalPath(targetList[j]))) : pw.SizedBox(),
            ),
          ),
        );
      }
      listWidgets.add(pw.Column(
        children: [
          pw.Container(
            width: double.infinity,
            padding: pw.EdgeInsets.fromLTRB(8.sp, 3.sp, 8.sp, 3.sp),
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.circular(4.sp),
              color: pwColor(const Color(0xffececec)),
            ),
            child: pwMyText(title, color7C, 12.sp),
          ),
          pw.SizedBox(height: 8.sp),
          pw.Row(
            mainAxisAlignment: (i == 0 || i == 4 ? pw.MainAxisAlignment.center : pw.MainAxisAlignment.spaceBetween),
            children: rowChilds,
          )
        ],
      ));
    }
    return pw.Padding(
        padding: pw.EdgeInsets.only(top: 8.sp),
        child: pw.Column(
          children: listWidgets,
        ));
  }

  List<pw.Widget> _getPdfListView() {
    List<pw.Widget> children = [];

    List<pw.Widget> aiList = [];
    if (algorithmStatus == 8 && hasAiResult) {
      aiList.add(
          pw.Container(height: 1.sp, margin: pw.EdgeInsets.only(top: 20.sp, bottom: 22.sp), color: pwColor(colorE1)));
      aiList.add(pwHeightText(Lang.ai_analytics, color2B, 16.sp, 2.0, pw.FontWeight.bold));

      bool hasResult = false;
      if (isNotEmpty(diseaseYati)) {
        hasResult = true;
        aiList.add(
          pw.Container(
            width: double.infinity,
            margin: pw.EdgeInsets.only(top: 16.sp),
            padding: pw.EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.all(pw.Radius.circular(4.sp)),
              color: pwColor(colorF3),
            ),
            child: pwMyText(Lang.tooth_body, color2B, 14.sp, pw.FontWeight.bold),
          ),
        );
        aiList.add(pwHeightText(diseaseYati!, color2B, 14.sp, 2.0));
      }
      if (isNotEmpty(diseaseYazhou)) {
        hasResult = true;
        aiList.add(
          pw.Container(
            width: double.infinity,
            margin: pw.EdgeInsets.only(top: 16.sp),
            padding: pw.EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.all(pw.Radius.circular(4.sp)),
              color: pwColor(colorF3),
            ),
            child: pwMyText(Lang.tooth_around, color2B, 14.sp, pw.FontWeight.bold),
          ),
        );
        aiList.add(pwHeightText(diseaseYazhou!, color2B, 14.sp, 2.0));
      }
      if (isNotEmpty(diseaseYalie)) {
        hasResult = true;
        aiList.add(
          pw.Container(
            width: double.infinity,
            margin: pw.EdgeInsets.only(top: 16.sp),
            padding: pw.EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.all(pw.Radius.circular(4.sp)),
              color: pwColor(colorF3),
            ),
            child: pwMyText(Lang.tooth_list, color2B, 14.sp, pw.FontWeight.bold),
          ),
        );
        aiList.add(pwHeightText(diseaseYalie!, color2B, 14.sp, 2.0));
      }
      if (isNotEmpty(batText)) {
        aiList.add(
          pw.Container(
            width: double.infinity,
            margin: pw.EdgeInsets.only(top: 16.sp),
            padding: pw.EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
            decoration: pw.BoxDecoration(
              borderRadius: pw.BorderRadius.all(pw.Radius.circular(4.sp)),
              color: pwColor(colorF3),
            ),
            child: pwMyText(Lang.bat_relation, color2B, 14.sp, pw.FontWeight.bold),
          ),
        );
        aiList.add(pwHeightText(batText, color2B, 14.sp, 2.0));
      }
      if (!hasResult) {
        aiList.add(
          pw.Container(
            alignment: pw.Alignment.topCenter,
            child:
                pwHeightText(Lang.ai_status_no_result, color7C, 14.sp, 2.0, pw.FontWeight.normal, pw.TextAlign.center),
          ),
        );
      }
    }

    children.add(
      pw.Container(
        padding: pw.EdgeInsets.all(20.sp),
        margin: pw.EdgeInsets.only(bottom: 20.sp),
        decoration: pw.BoxDecoration(
          borderRadius: pw.BorderRadius.circular(8.sp),
          color: pwColor(Colors.white),
        ),
        child: pw.Column(children: [
          pw.Padding(
            padding: pw.EdgeInsets.only(top: 16.sp),
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pwMyText(Lang.name, color2B, 16.sp, pw.FontWeight.bold),
                pw.SizedBox(width: 16.sp),
                pw.Expanded(
                  child: pwMyText(scanInfo.recordName, color2B, 16.sp),
                ),
              ],
            ),
          ),
          isNotEmpty(scanInfo.gender)
              ? pw.Padding(
                  padding: pw.EdgeInsets.only(top: 16.sp),
                  child: pw.Row(
                    children: [
                      pwMyText(Lang.gender, color2B, 16.sp, pw.FontWeight.bold),
                      pw.SizedBox(width: 16.sp),
                      pw.Expanded(
                        child: pwMyText(scanInfo.getGender(), color2B, 16.sp),
                      ),
                    ],
                  ),
                )
              : pw.SizedBox(),
          pw.SizedBox(height: 16.sp),
          pw.Row(
            children: [
              pwMyText(Lang.age, color2B, 16.sp, pw.FontWeight.bold),
              pw.SizedBox(width: 16.sp),
              pw.Expanded(
                child: pwMyText(scanInfo.getAge(), color2B, 16.sp),
              ),
            ],
          ),
          isEmpty(scanInfo.patientPhone)
              ? pw.SizedBox()
              : pw.Padding(
                  padding: pw.EdgeInsets.only(top: 16.sp),
                  child: pw.Row(
                    children: [
                      pwMyText(Lang.mobile, color2B, 16.sp, pw.FontWeight.bold),
                      pw.SizedBox(width: 16.sp),
                      pw.Expanded(
                        child: pwMyText(scanInfo.patientPhone, color2B, 16.sp),
                      ),
                    ],
                  ),
                ),
        ]),
      ),
    );

    children.add(
      pw.Container(
        width: double.infinity,
        padding: pw.EdgeInsets.all(20.sp),
        margin: pw.EdgeInsets.only(bottom: 20.sp),
        decoration: pw.BoxDecoration(
          borderRadius: pw.BorderRadius.circular(8.sp),
          color: pwColor(Colors.white),
        ),
        child: pw.Column(
          children: [
            getPdfFacePhotoView(),
            pw.Align(
              alignment: pw.Alignment.topLeft,
              child: pwMyText(Lang.intraoral_photo, color2B, 16.sp, pw.FontWeight.bold),
            ),
            getPdfScanStepView(),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: aiList,
            ),
          ],
        ),
      ),
    );

    if (isNotEmpty(scanInfo.tagList)) {
      children.add(
        pw.Container(
          width: double.infinity,
          padding: pw.EdgeInsets.all(20.sp),
          margin: pw.EdgeInsets.only(bottom: 20.sp),
          decoration: pw.BoxDecoration(
            borderRadius: pw.BorderRadius.circular(8.sp),
            color: pwColor(Colors.white),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pwMyText(Lang.convert_project, color2B, 16.sp, pw.FontWeight.bold),
              pw.SizedBox(height: 8.sp),
              pwHeightText(scanInfo.tagList.join("   "), color7C, 14.sp, 1.5),
            ],
          ),
        ),
      );
    }
    if (isNotEmpty(stageText) && scanInfo.progressStage != "NONE") {
      children.add(
        pw.Container(
          width: double.infinity,
          padding: pw.EdgeInsets.all(20.sp),
          margin: pw.EdgeInsets.only(bottom: 20.sp),
          decoration: pw.BoxDecoration(
            borderRadius: pw.BorderRadius.circular(8.sp),
            color: pwColor(Colors.white),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pwMyText(Lang.progress_stage, color2B, 16.sp, pw.FontWeight.bold),
              pw.SizedBox(height: 8.sp),
              pwHeightText(stageText, color7C, 14.sp, 1.5),
            ],
          ),
        ),
      );
    }
    if (isNotEmpty(scanInfo.note)) {
      children.add(
        pw.Container(
          width: double.infinity,
          padding: pw.EdgeInsets.all(20.sp),
          margin: pw.EdgeInsets.only(bottom: 20.sp),
          decoration: pw.BoxDecoration(
            borderRadius: pw.BorderRadius.circular(8.sp),
            color: pwColor(Colors.white),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pwMyText(Lang.remark, color2B, 16.sp, pw.FontWeight.bold),
              pw.SizedBox(height: 16.sp),
              pwMyText(scanInfo.note, color7C, 14.sp),
            ],
          ),
        ),
      );
    }
    children.add(
      pw.Align(
        alignment: pw.Alignment.topCenter,
        child: pw.Container(
          margin: pw.EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 60.sp),
          child: pwMyText(Lang.get_report_in_web, colorB8, 14.sp),
        ),
      ),
    );

    return children;
  }

  List<Widget> _getListView() {
    List<Widget> children = [
      SizedBox(height: 10.sp),
    ];

    List<Widget> aiList = [];
    if (algorithmStatus >= 5 && algorithmStatus <= 8) {
      aiList.add(Container(height: 1.sp, margin: EdgeInsets.only(top: 20.sp, bottom: 22.sp), color: colorE1));
      aiList.add(MyText(Lang.ai_analytics, color2B, 16.sp, FontWeight.w500));
      switch (algorithmStatus) {
        case 5:
        case 6:
          aiList.add(
            Container(
              alignment: Alignment.topCenter,
              child: HeightText(Lang.ai_status_analyzing, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
            ),
          );
          break;
        case 7:
          aiList.add(
            Container(
              alignment: Alignment.topCenter,
              child: HeightText(Lang.ai_status_fail, color7C, 14.sp, 2.0, FontWeight.w400, TextAlign.center),
            ),
          );
          break;
        case 8:
          if (!hasAiResult) {
            aiList.add(
              Container(
                alignment: Alignment.topCenter,
                child: HeightText(Lang.loading, color7C, 14.sp, 2.0, FontWeight.w400, TextAlign.center),
              ),
            );
            break;
          }
          bool hasResult = false;
          if (isNotEmpty(diseaseYati)) {
            hasResult = true;
            aiList.add(
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorF3,
                ),
                child: MyText(Lang.tooth_body, color2B, 14.sp, FontWeight.w500),
              ),
            );
            aiList.add(HeightText(diseaseYati!, color2B, 14.sp, 2.0));
          }
          if (isNotEmpty(diseaseYazhou)) {
            hasResult = true;
            aiList.add(
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorF3,
                ),
                child: MyText(Lang.tooth_around, color2B, 14.sp, FontWeight.w500),
              ),
            );
            aiList.add(HeightText(diseaseYazhou!, color2B, 14.sp, 2.0));
          }
          if (isNotEmpty(diseaseYalie)) {
            hasResult = true;
            aiList.add(
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(8.sp, 4.sp, 8.sp, 4.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorF3,
                ),
                child: MyText(Lang.tooth_list, color2B, 14.sp, FontWeight.w500),
              ),
            );
            aiList.add(HeightText(diseaseYalie!, color2B, 14.sp, 2.0));
          }
          if (fitWidget != null) {
            hasResult = true;
            aiList.add(fitWidget!);
          }
          if (!hasResult) {
            aiList.add(
              Container(
                alignment: Alignment.topCenter,
                child: HeightText(Lang.ai_status_no_result, color7C, 14.sp, 2.0, FontWeight.w400, TextAlign.center),
              ),
            );
          }
          break;
      }
    }

    switch (scanInfo.status) {
      case RecordStatus.uploading:
        children.add(
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(bottom: 16.sp),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset("res/icons/icon_cloud_uploading.png", width: 16.sp),
                Padding(
                  padding: EdgeInsets.only(left: 4.sp),
                  child: MyText(Lang.upload_to_cloud_ing, color7C, 12.sp),
                ),
              ],
            ),
          ),
        );
        break;
      case RecordStatus.failUpload:
        children.add(
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(bottom: 16.sp),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset("res/icons/icon_cloud_fail.png", width: 16.sp),
                Padding(
                  padding: EdgeInsets.only(left: 4.sp),
                  child: MyText(Lang.status_fail_upload, color7C, 12.sp),
                ),
              ],
            ),
          ),
        );
        break;
      case RecordStatus.completeUpload:
        children.add(
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(bottom: 16.sp),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset("res/icons/icon_cloud_success.png", width: 18.sp),
                Padding(
                  padding: EdgeInsets.only(left: 4.sp),
                  child: MyText(Lang.upload_to_cloud_success, color7C, 12.sp),
                ),
              ],
            ),
          ),
        );
        break;
    }
    children.add(
      Container(
        padding: EdgeInsets.all(20.sp),
        margin: EdgeInsets.only(bottom: 20.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          color: Colors.white,
        ),
        child: Column(children: [
          Container(
            constraints: BoxConstraints(maxWidth: 1.sw - 80.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MyText(Lang.name, color2B, 16.sp, FontWeight.w500),
                SizedBox(width: 16.sp),
                Expanded(
                  child: MyText(scanInfo.recordName, color2B, 16.sp),
                ),
              ],
            ),
          ),
          isNotEmpty(scanInfo.gender)
              ? Padding(
                  padding: EdgeInsets.only(top: 16.sp),
                  child: Row(
                    children: [
                      MyText(Lang.gender, color2B, 16.sp, FontWeight.w500),
                      SizedBox(width: 16.sp),
                      Expanded(
                        child: MyText(scanInfo.getGender(), color2B, 16.sp),
                      ),
                    ],
                  ),
                )
              : const SizedBox(),
          SizedBox(height: 16.sp),
          Row(
            children: [
              MyText(Lang.age, color2B, 16.sp, FontWeight.w500),
              SizedBox(width: 16.sp),
              Expanded(
                child: MyText(scanInfo.getAge(), color2B, 16.sp),
              ),
            ],
          ),
          isEmpty(scanInfo.patientPhone)
              ? const SizedBox()
              : Padding(
                  padding: EdgeInsets.only(top: 16.sp),
                  child: Row(
                    children: [
                      MyText(Lang.mobile, color2B, 16.sp, FontWeight.w500),
                      SizedBox(width: 16.sp),
                      Expanded(
                        child: MyText(scanInfo.patientPhone, color2B, 16.sp),
                      ),
                    ],
                  ),
                ),
        ]),
      ),
    );

    if (loaded) {
      children.add(
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(20.sp),
          margin: EdgeInsets.only(bottom: 20.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: Colors.white,
          ),
          child: Column(
            children: status == 7
                ? [
                    SizedBox(height: 10.sp),
                    Image.asset("res/imgs/image_error.png", width: 100.sp),
                    SizedBox(height: 4.sp),
                    MyText(Lang.loading_image_error, color7C, 12.sp),
                    SizedBox(height: 10.sp),
                  ]
                : [
                    getFacePhotoView(),
                    Align(
                      alignment: Alignment.topLeft,
                      child: MyText(Lang.intraoral_photo, color2B, 16.sp, FontWeight.w500),
                    ),
                    getScanStepView(),
                    // containTypes([108]) ? getMouthPhotoView(108, "default_up.png") : const SizedBox(),
                    // containTypes([105, 103, 107])
                    //     ? Row(
                    //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //         children: [
                    //           getMouthPhotoView(105, "default_close1.png"),
                    //           getMouthPhotoView(103, "default_close2.png"),
                    //           getMouthPhotoView(107, "default_close3.png"),
                    //         ],
                    //       )
                    //     : const SizedBox(),
                    // containTypes([152, 151, 153])
                    //     ? Row(
                    //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //         children: [
                    //           getMouthPhotoView(152, "default_open1.png"),
                    //           getMouthPhotoView(151, "default_open2.png"),
                    //           getMouthPhotoView(153, "default_open3.png"),
                    //         ],
                    //       )
                    //     : const SizedBox(),
                    // containTypes([155, 154, 156])
                    //     ? Row(
                    //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //         children: [
                    //           getMouthPhotoView(155, "default_mask1.png"),
                    //           getMouthPhotoView(154, "default_mask2.png"),
                    //           getMouthPhotoView(156, "default_mask3.png"),
                    //         ],
                    //       )
                    //     : const SizedBox(),
                    // containTypes([108]) ? getMouthPhotoView(106, "default_down.png") : const SizedBox(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: aiList,
                    ),
                  ],
          ),
        ),
      );
    } else {
      children.add(
        Container(
          width: double.infinity,
          padding: EdgeInsets.fromLTRB(20.sp, 56.sp, 20.sp, 56.sp),
          margin: EdgeInsets.only(bottom: 20.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: Colors.white,
          ),
          child: Column(
            children: [
              SizedBox(
                width: 32.sp,
                height: 32.sp,
                child: CircularProgressIndicator(
                  strokeWidth: 4.sp, // 设置进度圈的宽度
                  backgroundColor: colorBrand.withOpacity(0.3), // 设置进度圈的背景颜色
                  valueColor: AlwaysStoppedAnimation<Color>(colorBrand), // 设置进度圈的前景颜色
                ),
              ),
              SizedBox(height: 8.sp),
              HeightText(Lang.loading_images, color7C, 12.sp, 2.0, FontWeight.w400, TextAlign.center),
            ],
          ),
        ),
      );
    }

    if (isNotEmpty(scanInfo.tagList)) {
      children.add(
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(20.sp),
          margin: EdgeInsets.only(bottom: 20.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.convert_project, color2B, 16.sp, FontWeight.w500),
              SizedBox(height: 8.sp),
              HeightText(scanInfo.tagList.join("   "), color7C, 14.sp, 1.5),
            ],
          ),
        ),
      );
    }
    if (isNotEmpty(stageText) && scanInfo.progressStage != "NONE") {
      children.add(
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(20.sp),
          margin: EdgeInsets.only(bottom: 20.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.progress_stage, color2B, 16.sp, FontWeight.w500),
              SizedBox(height: 8.sp),
              HeightText(stageText, color7C, 14.sp, 1.5),
            ],
          ),
        ),
      );
    }
    if (isNotEmpty(scanInfo.note)) {
      children.add(
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(20.sp),
          margin: EdgeInsets.only(bottom: 20.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.remark, color2B, 16.sp, FontWeight.w500),
              SizedBox(height: 16.sp),
              MyText(scanInfo.note, color7C, 14.sp),
            ],
          ),
        ),
      );
    }
    children.add(
      Align(
        alignment: Alignment.topCenter,
        child: Container(
          margin: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 60.sp),
          child: MyText(Lang.get_report_in_web, colorB8, 14.sp),
        ),
      ),
    );

    return children;
  }

  addChildScanImgByRowIndex(parentList, targetList, ToothCameraTypeEnum type, [bool isLight = false]) {
    for (int i = 0; i < targetList.length; i++) {
      parentList.add({
        "img": targetList[i], //每组口扫图片
        "type": type, //口扫步骤 ToothCameraTypeEnum
        "index": i, //每组图片序号，0-左，1-中，2-右
        "light": isLight,
      });
    }
  }

  List swiperScanList = [];

  getScanStepView() {
    double width = 90.sp, height = 50.625.sp;
    List<Widget> listWidgets = [];
    List defaultScanImgs = [
      ["default_up.png"],
      ["default_close1.png", "default_close2.png", "default_close3.png"],
      ["default_open1.png", "default_open2.png", "default_open3.png"],
      ["default_mask1.png", "default_mask2.png", "default_mask3.png"],
      ["default_down.png"],
    ];
    List<ToothCameraTypeEnum> scanSteps = [
      ToothCameraTypeEnum.maxillary, //上颌
      ToothCameraTypeEnum.occlusion, //咬合
      ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
      ToothCameraTypeEnum.openMaskOn, //微张(带上牙套)
      ToothCameraTypeEnum.mandibular, //下颌
    ];
    swiperScanList = [];
    for (int i = 0; i < 5; i++) {
      List targetList = [];
      if (i == 0) {
        targetList = getFilesByType([108]).map((e) => e.imagePath).toList();
      } else if (i == 1) {
        targetList = getFilesByType([105, 103, 107]).map((e) => e.imagePath).toList();
      } else if (i == 2) {
        targetList = getFilesByType([152, 151, 153]).map((e) => e.imagePath).toList();
      } else if (i == 3) {
        targetList = getFilesByType([155, 154, 156]).map((e) => e.imagePath).toList();
      } else if (i == 4) {
        targetList = getFilesByType([106]).map((e) => e.imagePath).toList();
      }
      if (isEmpty(targetList)) {
        continue;
      }
      addChildScanImgByRowIndex(swiperScanList, targetList, scanSteps[i]);
      String title = getTitleByImgRow(scanSteps[i]);
      List<Widget> rowChilds = [];
      List<String> errorIndexReason = [];
      for (int j = 0; j < (i == 0 || i == 4 ? 1 : 3); j++) {
        bool isHaveErrorInfo = Global.isHaveErrorInfoByScanImgPath(j < targetList.length ? targetList[j] : "");
        rowChilds.add(
          Container(
            margin: EdgeInsets.only(bottom: i == 4 ? 0 : 16.sp),
            width: width,
            height: height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.sp),
              color: const Color(0xffe6e6e6),
            ),
            child: Stack(alignment: Alignment.center, children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.sp),
                child: j < targetList.length
                    ? Image.file(File(getLocalPath(targetList[j])))
                    : Image.asset("res/scan/${defaultScanImgs[i][j]}"),
              ),
              !isHaveErrorInfo
                  ? const SizedBox()
                  : Positioned(
                      top: 4.sp,
                      right: 4.sp,
                      child: Image.asset("res/scan/scan_img_error.png", width: 16.sp),
                    ),
            ]),
          ),
        );
        if (isHaveErrorInfo) {
          errorIndexReason.add(
              "${j + 1} - ${Global.getAlertTextByScanErrorCode(getErrorCode(targetList[j]), targetList[j])["content"]}");
        }
      }
      listWidgets.add(Column(
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.fromLTRB(8.sp, 3.sp, 8.sp, 3.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.sp),
              color: const Color(0xffececec),
            ),
            child: Text(title,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: color7C,
                )),
          ),
          SizedBox(height: 8.sp),
          Row(
            mainAxisAlignment: (i == 0 || i == 4 ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween),
            children: rowChilds,
          )
        ],
      ));
    }
    return Padding(
        padding: EdgeInsets.only(top: 8.sp),
        child: Column(
          children: listWidgets,
        ));
  }
}
