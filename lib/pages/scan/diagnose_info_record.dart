import 'dart:convert';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/common/local_account.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/login_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/diagnose_data.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/CheckScanImgSwiper.dart';
import 'package:mooeli/pages/scan/ScanInfoRecord.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/pages/scan/diagnose_import_search_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/AudioPlayerUtil.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/save_qrcode_view.dart';
import 'package:permission_handler/permission_handler.dart';

enum CaseCategory {
  adult_default,
  child_default,
}

class DiagnoseInfoRecord extends BasePage {
  const DiagnoseInfoRecord({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => DiagnoseInfoRecordState();
}

class DiagnoseInfoRecordState extends BasePageState<DiagnoseInfoRecord>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  int scanTool = SCAN_TOOL_ULTRA;

  bool statusInit = false; //用于第一步，加载基本信息
  bool statusNormal = false; //用于第二步，加载图片
  bool fromScan = false;

  bool hideTag = false;
  bool hideQuestion = false;
  Map<String, List<String>> questionMap = {};
  Map<String, List<String>> intentTagMap = {};
  Map<String, List<String>> aiTagMap = {};

  List questionList = [];
  List intentTagList = [];
  List aiTagList = [];

  String title = "";
  bool init = false;
  String keyword = "";
  DiagnoseInfo savedRecord = DiagnoseInfo();
  DiagnoseInfo currentRecord = DiagnoseInfo();
  DiagnoseInfo createdRecord = DiagnoseInfo();
  DiagnoseInfo importedRecord = DiagnoseInfo();

  List swiperScanList = [];

  List<int> widgetIndexs = [0, 1, 2, 3];

  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController ageController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  TextEditingController appealController = TextEditingController();
  TextEditingController adviceController = TextEditingController();

  final ScrollController _scrollController = ScrollController();

  Map faceTypes = {};

  int submitStatus = 0; //0：正常，1：连接互联网，2：提交资料
  int caseTypeIndex = 0;
  List caseTypeList = [
    {
      "title": Lang.case_general,
      "needFace": false,
      "category": CaseCategory.adult_default,
    },
    // {
    //   "title": Lang.case_orthodontics,
    //   "needFace": true,
    // },
    {
      "title": Lang.case_children,
      "desc": Lang.case_children_desc,
      "needFace": false,
      "category": CaseCategory.child_default,
    },
  ];

  List<String> recordFileIds = ["102", "100", "101", "108", "106", "105", "103", "107"];

  int step = 0;

  int reportId = 0;
  bool showReportDialog = false;

  List convertList = [];

  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    setScreenVertiacl();
    addEventListener<EventSmilePhoto>((event) {
      setState(() {
        currentRecord.setPhoto(event.type, event.path);
      });
    });
    addEventListener<EventReportDialogClose>((event) {
      showReportDialog = false;
    });
    addEventListener<EventSearchScan>((event) {
      setState(() {
        keyword = event.keyword;
        importedRecord.name = event.record.name;
        importedRecord.gender = event.record.gender;
        importedRecord.birthday =
            DateTime(DateTime.now().year - event.record.age ~/ 1).millisecondsSinceEpoch.toString();
        importedRecord.avatarFilePath = event.record.avatarFilePath;
        importedRecord.phone = event.record.phone;
        importedRecord.scanRecordId = event.record.quickScanRecordId;
        importedRecord.scanCreateFrom = event.record.scanCreateFrom;
        importedRecord.scanCreateTime = event.record.scanCreateTime.toString();
        importedRecord.scanTagList = event.record.tagList;
        showCurrentRecord(importedRecord);
      });
    });
    addEventListener<EventUploadRecord>((event) {
      pop();
    });
    initScanMode(2);
    initAsync();
    for (String key in faceMap.keys) {
      if (key == "102" || key == "100" || key == "101") {
        faceTypes[key] = faceMap[key];
      }
    }
    imageCache.clear(); // 清除缓存
    imageCache.clearLiveImages(); // 清理实时图像缓存
  }

  initAsync() async {
    await Global.initGlobalAsync();

    delay(100, () {
      getScanConfigJson((json) {
        if (isNotEmpty(json) && isNotEmpty(json["V3ConversionProject"])) {
          Map map = json["V3ConversionProject"] as Map;
          List list = [];
          for (String key in map.keys) {
            if (isNotEmpty(map[key]["projectNames"])) {
              list.addAll(map[key]["projectNames"]);
            }
          }
          setState(() {
            convertList = list;
          });
        }
      });
      getDiagnoseConfigJson((json) {
        if (isNotEmpty(json) && isNotEmpty(json["IntentProject"])) {
          Map map = json["IntentProject"] as Map;
          setState(() {
            List list = [];
            for (String key in map.keys) {
              if (isNotEmpty(map[key]["title"]) && isNotEmpty(map[key]["projectNames"])) {
                String title = map[key]["title"][getLangKey()];
                list.addAll(map[key]["projectNames"]);
                try {
                  List<String> value = [];
                  for (Map name in map[key]["projectNames"]) {
                    value.add(name[getLangKey()]);
                  }
                  intentTagMap[title] = value;
                } catch (ex) {
                  //
                }
              }
            }
            intentTagList = list;
          });
        }
        if (isNotEmpty(json) && isNotEmpty(json["RecommendedTreatmentProgram"])) {
          Map map = json["RecommendedTreatmentProgram"] as Map;
          setState(() {
            List list = [];
            for (String key in map.keys) {
              if (isNotEmpty(map[key]["title"]) && isNotEmpty(map[key]["projectNames"])) {
                String title = map[key]["title"][getLangKey()];
                list.addAll(map[key]["projectNames"]);
                try {
                  List<String> value = [];
                  for (Map name in map[key]["projectNames"]) {
                    value.add(name[getLangKey()]);
                  }
                  aiTagMap[title] = value;
                } catch (ex) {
                  //
                }
              }
            }
            aiTagList = list;
          });
        }
        if (isNotEmpty(json) && isNotEmpty(json["Questionnaire"])) {
          Map map = json["Questionnaire"] as Map;
          setState(() {
            List list = [];
            for (String key in map.keys) {
              if (isNotEmpty(map[key]["title"]) && isNotEmpty(map[key]["projectNames"])) {
                list.add(map[key]["title"]);
                String title = map[key]["title"][getLangKey()];
                list.addAll(map[key]["projectNames"]);
                try {
                  List<String> value = [];
                  for (Map name in map[key]["projectNames"]) {
                    value.add(name[getLangKey()]);
                  }
                  questionMap[title] = value;
                } catch (ex) {
                  //
                }
              }
            }
            questionList = list;
            logger("getQuestionView: $questionList");
          });
        }
      });
      logger(currentRecord.toJson().toString(), key: "currentRecord init");
    });
  }

  getFileList() {
    HHttp.request(
      "/v3/doctor/record/listFile",
      "POST",
      (data) {
        setState(() {
          List<LingyaFile> fileList = (data as List)
              .map((i) => LingyaFile.fromJson(i))
              .where((file) => recordFileIds.contains(file.category))
              .toList();
          if (isNotEmpty(fileList)) {
            List<String> ids = [];
            List closePhotos = ["", "", ""];
            for (var file in fileList) {
              if (isNotEmpty(file.fileId)) {
                ids.add(file.fileId);
              }
            }
            downloadAnalytics(
              ids,
              (id, path) {
                for (var file in fileList) {
                  if (file.fileId == id) {
                    setState(() {
                      file.imagePath = path;
                      switch (file.category) {
                        case "102":
                          savedRecord.smilePhoto = path;
                          currentRecord.smilePhoto = path;
                          break;
                        case "100":
                          savedRecord.frontPhoto = path;
                          currentRecord.frontPhoto = path;
                          break;
                        case "101":
                          savedRecord.right90Photo = path;
                          currentRecord.right90Photo = path;
                          break;
                        case "108":
                          savedRecord.upPhotos = [path];
                          currentRecord.upPhotos = [path];
                          break;
                        case "106":
                          savedRecord.downPhotos = [path];
                          currentRecord.downPhotos = [path];
                          break;
                        case "105":
                          closePhotos[0] = path;
                          savedRecord.closePhotos = closePhotos;
                          currentRecord.closePhotos = closePhotos;
                          break;
                        case "103":
                          closePhotos[1] = path;
                          savedRecord.closePhotos = closePhotos;
                          currentRecord.closePhotos = closePhotos;
                          break;
                        case "107":
                          closePhotos[2] = path;
                          savedRecord.closePhotos = closePhotos;
                          currentRecord.closePhotos = closePhotos;
                          break;
                      }
                      logger("currentRecord download photo: ${file.category}");
                    });
                  }
                }
              },
              "RECORD",
            );
          }
        });
      },
      params: {
        "recordId": savedRecord.recordId,
      },
    );
  }

  getRecordInfo() {
    HHttp.request(
      "/v3/doctor/initial/get",
      "POST",
      (data) {
        setState(() {
          savedRecord = DiagnoseInfo.fromJson(data);
          statusInit = true;
        });
        if (isNotEmpty(savedRecord.avatarFileId)) {
          downloadThumbnail(
            [savedRecord.avatarFileId],
            (id, path) {
              if (savedRecord.avatarFileId == id) {
                setState(() {
                  savedRecord.smilePhoto = path;
                  currentRecord.smilePhoto = path;
                });
              }
            },
            "RECORD",
          );
        }
        showCurrentRecord(savedRecord.clone());
        checkRecordStatus();
      },
      errCallBack: (err) {
        delay(1000, checkRecordStatus);
      },
      params: {
        "recordId": savedRecord.recordId,
      },
      isShowErrorToast: false,
    );
  }

  checkRecordStatus() {
    HHttp.request(
      "/v3/doctor/initial/getStatus",
      "POST",
      (data) {
        if (data["status"] == 0) {
          statusNormal = true;
          getFileList();
        } else {
          delay(1000, checkRecordStatus);
        }
      },
      errCallBack: (err) {
        delay(1000, checkRecordStatus);
      },
      params: {
        "recordId": savedRecord.recordId,
      },
      isShowErrorToast: false,
    );
  }

  createNewRecord(TypeCallback<bool> callback) {
    currentRecord.setProcessStep(1);
    HHttp.request(
      "/v3/doctor/initial/create",
      "POST",
      (data) {
        currentRecord.recordId = data["recordId"];
        savedRecord = currentRecord.clone();
        eventBus.fire(EventUpdateDiagnose(savedRecord));
        callback(true);
        checkRecordStatus();
      },
      errCallBack: (err) {
        callback(false);
      },
      params: {
        "quickScanRecordId": currentRecord.scanRecordId,
        "name": currentRecord.name,
        "birthday": currentRecord.birthday,
        "gender": currentRecord.gender,
        "phone": currentRecord.phone,
        "chiefComplaint": currentRecord.chiefComplaint,
        "note": currentRecord.note,
        "category": currentRecord.getCategory(),
        "scanCreateTime": currentRecord.scanCreateTime,
        "scanCreateFrom": currentRecord.scanCreateFrom,
        "intentTagList": currentRecord.intentTagList,
        "metadata": currentRecord.getMetaDataJson()
      },
    );
  }

  updateBaseInfo(TypeCallback<bool> callback) {
    List<String> addTagList = [];
    for (String tag in currentRecord.intentTagList) {
      if (!savedRecord.intentTagList.contains(tag)) {
        addTagList.add(tag);
      }
    }
    List<String> delTagList = [];
    for (String tag in savedRecord.intentTagList) {
      if (!currentRecord.intentTagList.contains(tag)) {
        delTagList.add(tag);
      }
    }
    HHttp.request(
      "/v3/doctor/initial/update",
      "POST",
      (data) {
        savedRecord.copyBaseInfo(currentRecord);
        eventBus.fire(EventUpdateDiagnose(savedRecord));
        callback(true);
      },
      errCallBack: (err) {
        callback(false);
      },
      params: {
        "recordId": savedRecord.recordId,
        "name": currentRecord.name,
        "birthday": currentRecord.birthday,
        "gender": currentRecord.gender,
        "phone": currentRecord.phone,
        "chiefComplaint": currentRecord.chiefComplaint,
        "note": currentRecord.note,
        "addIntentTagList": addTagList,
        "delIntentTagList": delTagList,
        "category": currentRecord.getCategory(),
        "metadata": currentRecord.getMetaDataJson()
      },
    );
  }

  updatePhotos(TypeCallback<bool> callback) async {
    if (!currentRecord.hasPhotosChanged(savedRecord)) {
      callback(true);
      return;
    }
    setSubmitState(2);

    onUploadFail(err) {
      setSubmitState(0);
      toast(Lang.upload_result_fail);
      callback(false);
    }

    await disconnectUltra(disconnectWifi: true, atOnce: true);
    HHttp.request(
      "/v3/doctor/record/listFile",
      "POST",
      (data) {
        List<LingyaFile> fileList = (data as List)
            .map((i) => LingyaFile.fromJson(i))
            .where((file) => recordFileIds.contains(file.category))
            .toList();
        List<String> deleteFileIds = [];
        if (isNotEmpty(fileList)) {
          for (LingyaFile file in fileList) {
            switch (file.category) {
              case "102":
                if (isEmpty(currentRecord.smilePhoto)) {
                  deleteFileIds.add(file.fileId);
                }
                break;
              case "100":
                if (isEmpty(currentRecord.frontPhoto)) {
                  deleteFileIds.add(file.fileId);
                }
                break;
              case "101":
                if (isEmpty(currentRecord.right90Photo)) {
                  deleteFileIds.add(file.fileId);
                }
                break;
            }
          }
        }
        List updatePhotos = currentRecord.getUpdatePhotoList(savedRecord);
        logger("updatePhotos deleteIds: $deleteFileIds, updatePhotos: $updatePhotos");
        List uploadOkList = [];

        checkUploadResult() {
          if (uploadOkList.length == updatePhotos.length + deleteFileIds.length) {
            setSubmitState(0);
            toast(Lang.upload_result_success);
            savedRecord.copyPhotos(currentRecord);
            eventBus.fire(EventUpdateDiagnose(savedRecord));
            callback(true);
          }
        }

        uploadSinglePhoto(String path, [dynamic okCallback]) async {
          int time = 5;
          String fileName = Global.getFileNameByPath(path);

          upload() {
            HHttp.uploadFileByServer(
              path,
              "RECORD",
              {
                "name": fileName,
                "sourceId": currentRecord.recordId,
                "sourceType": "RECORD",
                "fileType": 1,
                "persionId": currentUser.personId,
                "category": getTypeByFile(fileName),
                "analysisFile": "FALSE"
              },
              okCallback: (data) {
                logger("startUploadScanRecord $fileName ok!");
                if (!uploadOkList.contains(path)) {
                  uploadOkList.add(path);
                  checkUploadResult();
                }
                if (okCallback != null) {
                  okCallback();
                }
                Global.deleteExternalImages("initial_${currentRecord.recordId}");
              },
              errCallback: (err) {
                logger("startUploadScanRecord $fileName error!");
                if (time >= 0) {
                  time--;
                  upload();
                } else {
                  onUploadFail(err);
                }
              },
              showLoading: false,
            );
          }

          upload();
        }

        if (isNotEmpty(updatePhotos)) {
          uploadSinglePhoto(updatePhotos.last, () {
            delay(500, () {
              for (String path in updatePhotos) {
                if (path != updatePhotos.last) {
                  uploadSinglePhoto(path);
                }
              }
            });
          });
        }
        for (String fileId in deleteFileIds) {
          HHttp.deleteFileById(
            fileId,
            "RECORD",
            okCallback: (data) {
              if (!uploadOkList.contains(fileId)) {
                uploadOkList.add(fileId);
                checkUploadResult();
              }
            },
            errCallback: (err) {
              onUploadFail(err);
            },
          );
        }
      },
      errCallBack: onUploadFail,
      params: {
        "recordId": savedRecord.recordId,
      },
    );
  }

  makeReport() {
    onFail(err) {
      eventBus.fire(EventReportStatus(reportId, false));
    }

    reportId++;
    showReportDialog = true;
    Global.showCustomDialog(
      SizedBox(
        width: 1.sw,
        height: 1.sh,
        child: Center(
          child: ReportStateDialog(reportId, makeReport),
        ),
      ),
      isBringXBtn: false,
    );

    HHttp.request(
      "/v3/doctor/record/listFile",
      "POST",
      (data) {
        List<LingyaFile> fileList = (data as List).map((i) => LingyaFile.fromJson(i)).toList();
        if (isNotEmpty(fileList)) {
          for (LingyaFile file in fileList) {
            if (isNotEmpty(file.analysisInfoMap) && (file.analysisInfoMap as Map).containsKey("5008")) {
              String id = file.analysisInfoMap["5008"]["id"];
              if (currentRecord.recommendBySelf) {
                Map<String, List<String>> tagMap = {};
                for (String tag in currentRecord.recommendTagList) {
                  for (String key in aiTagMap.keys) {
                    if (aiTagMap[key]!.contains(tag)) {
                      if (tagMap.containsKey(key)) {
                        tagMap[key]!.add(tag);
                      } else {
                        tagMap[key] = [tag];
                      }
                    }
                  }
                }
                currentRecord.recommdation = jsonEncode(tagMap);
              } else {
                currentRecord.recommdation = "auto";
              }
              HHttp.request(
                "/v3/analysis/update",
                "POST",
                (data) {
                  HHttp.request(
                    "/v3/analysis/forceReplay",
                    "POST",
                    (data) {
                      bool updateMeta = true;
                      getAnalyticsResult() {
                        getAnalyticsInfo(
                          id,
                          (data) {
                            if (data["status"] == 8) {
                              eventBus.fire(EventReportStatus(reportId, true));
                              currentRecord.setProcessStep(3, updateMeta);
                              updateMeta = false;
                            } else if (data["status"] == 7) {
                              eventBus.fire(EventReportStatus(reportId, false));
                            } else {
                              if (data["status"] == 6) {
                                currentRecord.setProcessStep(3, updateMeta);
                                updateMeta = false;
                              }
                              if (showReportDialog) {
                                delay(1000, getAnalyticsResult);
                              }
                            }
                          },
                          onlyShowStatus: true,
                        );
                      }

                      getAnalyticsResult();
                    },
                    errCallBack: onFail,
                    params: {
                      "id": id,
                    },
                  );
                },
                errCallBack: onFail,
                params: {
                  "id": id,
                  "inputJson": {
                    "birthday_or_age": currentRecord.getAge().toString(),
                    "custom_info": {
                      "recommdation": currentRecord.recommdation,
                      "advice": currentRecord.advice,
                      "questionaire_list": currentRecord.questionaire_list,
                      "region": getLoginArea() != 0 ? "Overseas" : "China",
                    },
                    "mode": currentRecord.isChild() ? "infant" : "general",
                    "inner_options": jsonEncode({
                      "onlyCutTooth": true,
                    }),
                  },
                },
              );
              break;
            }
          }
        } else {
          onFail({});
        }
      },
      errCallBack: onFail,
      params: {
        "recordId": savedRecord.recordId,
      },
    );
  }

  @override
  void dispose() {
    SmartDialog.dismiss();
    disconnectUltra();
    WidgetsBinding.instance.removeObserver(this);
    setSubmitState(0);
    controller?.dispose();
    super.dispose();
  }

  //别的页面数据回来或更新数据均走这里
  updateStateByType(String typeStr, dynamic params, {dynamic callback}) async {
    switch (typeStr) {
      case "newSmilePhoto": //微笑照返回
        setState(() {
          currentRecord.setPhoto(params["type"], params["path"]);
        });
        break;
      case "deleteSmilePhoto": //删除了微笑照
        setState(() {
          currentRecord.setPhoto(params["type"], "");
        });
        break;
      case "newUserComment": //修改了文字备注
        setState(() {
          currentRecord.note = params;
        });
        break;
      case "deleteUserComment": //删除了文字备注
        setState(() {
          currentRecord.note = "";
        });
        break;
      case "selectNewTag": //选中新标签
      case "deleteNewTag": //删除选中标签
        setState(() {
          if (typeStr == "selectNewTag") {
            currentRecord.intentTagList.add(params);
          } else {
            for (int i = 0; i < currentRecord.intentTagList.length; i++) {
              if (params.toString() == currentRecord.intentTagList[i].toString()) {
                currentRecord.intentTagList.removeAt(i);
                break;
              }
            }
          }
        });
        break;
      case "updateScanImgs": //更新口扫照片
        ToothCameraTypeEnum toothCameraType = params["toothCameraType"];
        List tempShowUseImgs = params["tempShowUseImgs"] ?? [];
        List tempShowUnuseImgs = params["tempShowUnuseImgs"] ?? [];
        dynamic uploadFinishCb = params["uploadFinishCb"];

        for (dynamic path in tempShowUseImgs) {
          await Global.copyImageDocument(path, "initial_${currentRecord.recordId}", save: true);
        }
        // for (dynamic path in tempShowUnuseImgs) {
        //   await Global.copyImageDocument(path, "initial_${currentRecord.recordId}", save: true);
        // }

        switch (toothCameraType) {
          case ToothCameraTypeEnum.occlusion:
            setState(() {
              currentRecord.closePhotos = tempShowUseImgs;
            });
            break;
          case ToothCameraTypeEnum.maxillary:
            setState(() {
              currentRecord.upPhotos = tempShowUseImgs;
            });
            break;
          case ToothCameraTypeEnum.mandibular:
            setState(() {
              currentRecord.downPhotos = tempShowUseImgs;
            });
            break;
        }
        uploadFinishCb();
        break;
    }
  }

  addChildScanImgByRowIndex(parentList, targetList, ToothCameraTypeEnum type, [bool isLight = false]) {
    for (int i = 0; i < targetList.length; i++) {
      if (File(getLocalPath(targetList[i])).existsSync()) {
        parentList.add({
          "img": targetList[i], //每组口扫图片
          "type": type, //口扫步骤 ToothCameraTypeEnum
          "index": i, //每组图片序号，0-左，1-中，2-右
          "light": isLight,
        });
      }
    }
  }

  checkIsCanPostAndReBuildList() {
    swiperScanList = [];

    if (widgetIndexs.contains(ScanStep.IntraoralPhoto.index)) {
      addChildScanImgByRowIndex(swiperScanList, currentRecord.upPhotos, ToothCameraTypeEnum.maxillary);
      addChildScanImgByRowIndex(swiperScanList, currentRecord.closePhotos, ToothCameraTypeEnum.occlusion);
      addChildScanImgByRowIndex(swiperScanList, currentRecord.downPhotos, ToothCameraTypeEnum.mandibular);
    }
  }

  //获取每个步骤完成后的页面
  getScanStepView() {
    double width = 90.sp, height = 50.625.sp;
    List<Widget> listWidgets = [];
    List defaultScanImgs = [
      ["default_up.png"],
      ["default_close1.png", "default_close2.png", "default_close3.png"],
      ["default_down.png"],
    ];
    List<ToothCameraTypeEnum> scanSteps = [
      ToothCameraTypeEnum.maxillary, //上颌
      ToothCameraTypeEnum.occlusion, //咬合
      ToothCameraTypeEnum.mandibular, //下颌
    ];
    for (int i = 0; i < scanSteps.length; i++) {
      List targetList = [];
      if (i == 0) {
        targetList = currentRecord.upPhotos;
      } else if (i == 1) {
        targetList = currentRecord.closePhotos;
      } else if (i == 2) {
        targetList = currentRecord.downPhotos;
      }
      String title = getTitleByImgRow(scanSteps[i]);
      List<Widget> rowChilds = [];
      List<String> errorIndexReason = [];
      for (int j = 0; j < (i == 1 ? 3 : 1); j++) {
        bool isHaveErrorInfo = Global.isHaveErrorInfoByScanImgPath(j < targetList.length ? targetList[j] : "");
        rowChilds.add(Container(
          margin: EdgeInsets.only(bottom: i == 4 ? 0 : 16.sp),
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.sp),
            color: const Color(0xffe6e6e6),
          ),
          child: GestureDetector(
            onTap: () {
              if (j < targetList.length) {
                checkIsCanPostAndReBuildList();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return CheckScanImgSwiper(
                          isCanReScan: true,
                          updateStateByType: updateStateByType,
                          initChooseImgPath: targetList[j],
                          scanImgInfoList: swiperScanList);
                    },
                  ),
                );
              }
            },
            child: Stack(alignment: Alignment.center, children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.sp),
                child: j < targetList.length && File(getLocalPath(targetList[j])).existsSync()
                    ? Image.file(File(getLocalPath(targetList[j])))
                    : Image.asset("res/scan/${defaultScanImgs[i][j]}"),
              ),
              !isHaveErrorInfo
                  ? const SizedBox()
                  : Positioned(
                      top: 4.sp,
                      right: 4.sp,
                      child: Image.asset("res/scan/scan_img_error.png", width: 16.sp),
                    ),
            ]),
          ),
        ));
        if (isHaveErrorInfo) {
          errorIndexReason.add(
              "${j + 1} - ${Global.getAlertTextByScanErrorCode(getErrorCode(targetList[j]), targetList[j])["content"]}");
        }
      }
      listWidgets.add(Column(
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(8.sp, 3.sp, 8.sp, 3.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.sp),
              color: const Color(0xffececec),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(title,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: color7C,
                      )),
                ),
                SizedBox(width: 8.sp),
                currentRecord.hasScanPhoto()
                    ? GestureDetector(
                        onTap: () async {
                          Map<String, dynamic> param = {"value5": "input"};
                          if (isNotEmpty(errorIndexReason)) {
                            param["value3"] = "$errorIndexReason";
                          }
                          sendScanEvent("rescan", getRecordEventStepEnumTitle(scanSteps[i]), param);
                          Navigator.push(context, MaterialPageRoute(builder: (context) {
                            return ToothCameraView(initDirectionType: scanSteps[i], initRouterParamas: {
                              "updateStateByType": updateStateByType,
                            });
                          }));
                        },
                        child: Text(Lang.retake_current_record,
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: color2B,
                            )))
                    : const SizedBox(),
              ],
            ),
          ),
          SizedBox(height: 8.sp),
          Row(
            mainAxisAlignment: (i == 1 ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center),
            children: rowChilds,
          )
        ],
      ));
    }
    return Padding(
        padding: EdgeInsets.only(top: 8.sp),
        child: Column(
          children: listWidgets,
        ));
  }

  getUserInputView() {
    return Container(
      padding: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              MyText("* ", colorRed, 14.sp, FontWeight.w500),
              MyText(
                Lang.name,
                color2B,
                16.sp,
                FontWeight.w500,
              ),
            ],
          ),
          Container(
            height: 36.sp,
            margin: EdgeInsets.only(top: 8.sp),
            padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              border: Border.all(
                color: colorE1,
              ),
              borderRadius: BorderRadius.circular(4.sp),
            ),
            child: TextFormField(
              controller: nameController,
              maxLines: 1,
              maxLength: 30,
              textAlign: TextAlign.start,
              onChanged: (value) {
                currentRecord.name = value;
              },
              style: TextStyle(fontSize: 14.sp, color: color2B),
              decoration: InputDecoration(
                counterText: '',
                isDense: true,
                contentPadding: EdgeInsets.zero,
                border: InputBorder.none,
                hintText: Lang.input_name,
                hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
              ),
            ),
          ),
          SizedBox(height: 16.sp),
          Row(
            children: [
              MyText("* ", colorRed, 14.sp, FontWeight.w500),
              MyText(
                Lang.gender,
                color2B,
                16.sp,
                FontWeight.w500,
              ),
            ],
          ),
          SizedBox(height: 8.sp),
          Row(
            children: [
              _getSelectView(Lang.gender_man, currentRecord.gender == "male", () {
                setState(() {
                  currentRecord.gender = "male";
                });
              }),
              SizedBox(width: 12.sp),
              _getSelectView(Lang.gender_woman, currentRecord.gender == "female", () {
                setState(() {
                  currentRecord.gender = "female";
                });
              }),
            ],
          ),
          SizedBox(height: 16.sp),
          Row(
            children: [
              MyText("* ", colorRed, 14.sp, FontWeight.w500),
              MyText(
                Lang.birthday,
                color2B,
                16.sp,
                FontWeight.w500,
              ),
            ],
          ),
          // Container(
          //   height: 36.sp,
          //   margin: EdgeInsets.only(top: 8.sp),
          //   alignment: Alignment.centerLeft,
          //   decoration: BoxDecoration(
          //     border: Border.all(
          //       color: colorE1,
          //     ),
          //     borderRadius: BorderRadius.circular(4.sp),
          //   ),
          //   child: Row(
          //     children: [
          //       GestureDetector(
          //         child: Container(
          //           width: 40.sp,
          //           height: 36.sp,
          //           alignment: Alignment.center,
          //           child: Image.asset("res/icons/icon_minus.png", width: 16.sp),
          //         ),
          //         onTap: () {
          //           _onChangeAge(-1);
          //         },
          //       ),
          //       Expanded(
          //         child: Container(
          //           padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
          //           alignment: Alignment.center,
          //           decoration: BoxDecoration(
          //             border: Border(
          //               left: BorderSide(color: colorE1),
          //               right: BorderSide(color: colorE1),
          //             ),
          //           ),
          //           child: TextFormField(
          //             controller: ageController,
          //             maxLines: 1,
          //             keyboardType: TextInputType.phone,
          //             textAlign: TextAlign.center,
          //             onChanged: _onInputAge,
          //             style: TextStyle(fontSize: 14.sp, color: color2B, height: 1.5),
          //             decoration: InputDecoration(
          //               counterText: '',
          //               isDense: true,
          //               contentPadding: EdgeInsets.zero,
          //               border: InputBorder.none,
          //               hintText: '',
          //               hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
          //             ),
          //           ),
          //         ),
          //       ),
          //       GestureDetector(
          //         child: Container(
          //           width: 40.sp,
          //           height: 36.sp,
          //           alignment: Alignment.center,
          //           child: Image.asset("res/icons/icon_plus.png", width: 16.sp),
          //         ),
          //         onTap: () {
          //           _onChangeAge(1);
          //         },
          //       ),
          //     ],
          //   ),
          // ),
          // SizedBox(height: 16.sp),
          // MyText(Lang.birthday_year, color2B, 16.sp, FontWeight.w500),
          GestureDetector(
            onTap: () async {
              final DateTime? date = await showDatePicker(
                context: context,
                initialDate: isNotEmpty(currentRecord.birthday)
                    ? DateTime.fromMillisecondsSinceEpoch(int.parse(currentRecord.birthday))
                    : DateTime.now(),
                firstDate: DateTime(1900),
                lastDate: DateTime.now(),
                locale: currentLocale,
              );
              if (date != null) {
                DateTime date2 = date.subtract(-date.timeZoneOffset);
                setState(() {
                  currentRecord.birthday = date2.millisecondsSinceEpoch.toString();
                  caseTypeIndex = currentRecord.isChild() ? 1 : 0;
                });
              }
            },
            child: Container(
              height: 36.sp,
              margin: EdgeInsets.only(top: 8.sp),
              padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorE1,
                ),
                borderRadius: BorderRadius.circular(4.sp),
              ),
              child: Row(
                children: [
                  Image.asset("res/icons/icon_calendar.png", height: 20.sp),
                  SizedBox(width: 8.sp),
                  MyText(
                      isEmpty(currentRecord.birthday)
                          ? Lang.select_birthday
                          : currentRecord.getDate(currentRecord.birthday),
                      isEmpty(currentRecord.birthday) ? colorB8 : color2B,
                      14.sp),
                ],
              ),
            ),
          ),
          SizedBox(height: 16.sp),
          MyText(Lang.phone, color2B, 16.sp, FontWeight.w500),
          Container(
            height: 36.sp,
            margin: EdgeInsets.only(top: 8.sp),
            padding: EdgeInsets.fromLTRB(12.sp, 4.sp, 12.sp, 4.sp),
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              border: Border.all(
                color: colorE1,
              ),
              borderRadius: BorderRadius.circular(4.sp),
            ),
            child: TextFormField(
              controller: phoneController,
              maxLines: 1,
              maxLength: 20,
              keyboardType: TextInputType.phone,
              textAlign: TextAlign.start,
              onChanged: (value) {
                currentRecord.phone = value;
              },
              style: TextStyle(fontSize: 14.sp, color: color2B),
              decoration: InputDecoration(
                counterText: '',
                isDense: true,
                contentPadding: EdgeInsets.zero,
                border: InputBorder.none,
                hintText: Lang.phone,
                hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getSelectView(String text, bool isSelected, dynamic onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        width: 104.sp,
        height: 36.sp,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.sp),
          color: isSelected ? colorBrand : Colors.white,
          border: Border.all(color: isSelected ? colorBrand : colorE1, width: 1.0.sp),
        ),
        child: MyText(text, isSelected ? Colors.white : color2B, 12.sp),
      ),
    );
  }

  getFaceView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyText(Lang.face_photo, color2B, 16.sp, FontWeight.w500),
            // caseTypeList[caseTypeIndex]["needFace"] ? const SizedBox() : MyText(Lang.optional, color7C, 14.sp),
            SizedBox(width: 4.sp),
            Expanded(
              child: MyText(Lang.need_clear_smile_photo, color7C, 12.sp),
            ),
          ],
        ),
        SizedBox(height: 18.sp),
        SizedBox(
          height: 128.sp * (faceTypes.keys.length / 3.0).ceil(),
          child: GridView(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 0.75,
            ),
            children: faceTypes.keys.map((key) => getFaceItemView(key, faceTypes[key])).toList(),
          ),
        ),
      ],
    );
  }

  Widget getFaceItemView(String type, String title) {
    return SizedBox(
      height: 128.sp,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              caseTypeList[caseTypeIndex]["needFace"]
                  ? MyText("* ", colorRed, 14.sp, FontWeight.w500)
                  : const SizedBox(),
              MyText(title, color7C, 14.sp),
            ],
          ),
          SizedBox(height: 8.sp),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.sp),
            child: isNotEmpty(currentRecord.getPhoto(type))
                ? GestureDetector(
                    onTap: () {
                      int index = 0;
                      List<String> imgList = [];
                      List<String> typeList = [];
                      List<String> titleList = [];
                      for (String key in faceTypes.keys) {
                        if (isNotEmpty(currentRecord.getPhoto(key))) {
                          if (key == type) {
                            index = typeList.length;
                          }
                          typeList.add(key);
                          titleList.add(faceTypes[key]);
                          imgList.add(getLocalPath(currentRecord.getPhoto(key)));
                        }
                      }
                      Global.showPhotoBrowerModal(
                        context,
                        imgPathList: imgList,
                        onPageChanged: (i) {
                          index = i;
                        },
                        deleteCalllBack: () {
                          Global.showAlertDialog(Lang.warn, Lang.confirm_delete_smile_photo, okCallBack: () {
                            updateStateByType("deleteSmilePhoto", {"type": typeList[index]});
                            Navigator.pop(context);
                          });
                        },
                        cbWidget: GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            openFaceCameraView(typeList[index], titleList[index]);
                          },
                          child: Container(
                            width: 160.sp,
                            height: 42.sp,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white),
                              borderRadius: BorderRadius.circular(24.sp),
                            ),
                            child: MyText(Lang.retake_photo, Colors.white, 16.sp, FontWeight.w500),
                          ),
                        ),
                        titles: titleList,
                        defaultShowIndex: index,
                        initScale: 0.8,
                      );
                    },
                    child: getImgByUrlOrPath(currentRecord.getPhoto(type), 80.sp),
                  )
                : GestureDetector(
                    onTap: () {
                      openFaceCameraView(type, title);
                    },
                    child: Image.asset("res/scan/default_face_$type.png", width: 80.sp, height: 80.sp),
                  ),
          ),
          SizedBox(height: 16.sp),
        ],
      ),
    );
  }

  openFaceCameraView(String type, String title) async {
    List<String> types = [];
    List allTypes = faceTypes.keys.toList();
    for (int i = allTypes.indexOf(type); i < allTypes.length; i++) {
      if (isEmpty(currentRecord.getPhoto(allTypes[i]))) {
        types.add(allTypes[i]);
      }
    }
    // if (Platform.isAndroid) {
    //   PermissionStatus storageStatus = await Permission.camera.status;
    //   if (storageStatus != PermissionStatus.granted) {
    //     Global.showAlertDialog(
    //       Lang.warn,
    //       Lang.request_camera_smile,
    //       okCallBack: () {
    //         Navigator.pushNamed(context, "AiFaceCameraView", arguments: {
    //           "updateStateByType": updateStateByType,
    //           "type": type,
    //           "types": types,
    //         });
    //       },
    //     );
    //     return;
    //   }
    // }

    Navigator.pushNamed(context, "AiFaceCameraView", arguments: {
      "updateStateByType": updateStateByType,
      "type": type,
      "types": types,
    });
  }

  getScanView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            MyText("* ", colorRed, 14.sp, FontWeight.w500),
            Expanded(
              child: MyText(
                Lang.intraoral_photo,
                color2B,
                16.sp,
                FontWeight.w500,
              ),
            ),
            currentRecord.hasScanPhoto()
                ? GestureDetector(
                    onTap: () async {
                      Navigator.pushNamed(context, "ScanPre", arguments: {
                        "updateStateByType": updateStateByType,
                        "scanTool": scanTool,
                      });
                    },
                    child: Text(
                      Lang.retake_all_photos,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: color2B,
                      ),
                    ),
                  )
                : MainButton(
                    onPress: () async {
                      Navigator.pushNamed(context, "ScanPre", arguments: {
                        "updateStateByType": updateStateByType,
                        "scanTool": scanTool,
                      });
                    },
                    text: Lang.start_scan,
                    width: 96.sp,
                    height: 28.sp,
                    fontSize: 14.sp,
                  ),
          ],
        ),
        SizedBox(height: 8.sp),
        getScanStepView(),
      ],
    );
  }

  getQuestionView() {
    if (isNotEmpty(currentRecord.answerMap)) {
      logger("getQuestionView: ${currentRecord.answerMap}");
      List keys = currentRecord.answerMap.keys.toList();
      Map answerMap = {};
      for (String key in keys) {
        String keyLang = getTagText(questionList, key, getLangKey());
        List list = currentRecord.answerMap[key];
        answerMap[keyLang] = list.map((e) => getTagText(questionList, e, getLangKey())).toList();
      }
      currentRecord.answerMap = answerMap;
      logger("getQuestionView: ${currentRecord.answerMap}");
    }
    if (isEmpty(questionMap) || !currentRecord.isChild()) {
      return const SizedBox();
    }
    List<Widget> children = [
      GestureDetector(
        child: Container(
          color: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 1.sw - 110.sp,
                    child: MyText(Lang.children_bad_habit_question, color2B, 16.sp, FontWeight.w500),
                  ),
                  SizedBox(height: 4.sp),
                  SizedBox(
                    width: 1.sw - 110.sp,
                    child: MyText(Lang.suggest_treat_to_fill, color7C, 12.sp),
                  ),
                ],
              ),
              Icon(
                hideQuestion ? Icons.expand_more : Icons.expand_less,
                size: 24.sp,
              ),
            ],
          ),
        ),
        onTap: () {
          setState(() {
            hideQuestion = !hideQuestion;
          });
        },
      ),
    ];
    if (!hideQuestion) {
      List<String> questions = questionMap.keys.toList();
      for (int i = 0; i < questions.length; i++) {
        List<String> answers = questionMap[questions[i]]!.toList();
        children.add(Padding(
          padding: EdgeInsets.only(top: 16.sp),
          child: Container(
            constraints: BoxConstraints(maxWidth: 1.sw - 88.sp),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "${i + 1}、",
                    style: TextStyle(color: color2B, fontSize: 14.sp, fontWeight: FontWeight.w600),
                  ),
                  TextSpan(
                    text: "[${Lang.multiple_select}] ",
                    style: TextStyle(color: colorBrand, fontSize: 14.sp),
                  ),
                  TextSpan(
                    text: questions[i],
                    style: TextStyle(color: color2B, fontSize: 14.sp, fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            ),
          ),
        ));
        for (int j = 0; j < answers.length; j++) {
          children.add(
            Padding(
              padding: EdgeInsets.only(
                  top: 10.sp, bottom: j == answers.length - 1 && i < questions.length - 1 ? 10.sp : 2.sp),
              child: GestureDetector(
                onTap: () {
                  hideKeyboard();
                  setState(() {
                    if (!currentRecord.answerMap.containsKey(questions[i])) {
                      currentRecord.answerMap[questions[i]] = [];
                    }
                    if (!currentRecord.answerMap[questions[i]]!.contains(answers[j])) {
                      List list = currentRecord.answerMap[questions[i]]!;
                      int index = 0;
                      for (int k = 0; k < j; k++) {
                        if (list.contains(answers[k])) {
                          index++;
                        }
                      }
                      list.insert(index, answers[j]);
                      logger("answerMap add ${currentRecord.answerMap[questions[i]]}");
                    } else {
                      currentRecord.answerMap[questions[i]]!.remove(answers[j]);
                      logger("answerMap remove ${currentRecord.answerMap[questions[i]]}");
                    }
                  });
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 18.sp,
                      height: 18.sp,
                      margin: EdgeInsets.only(top: 1.2.sp),
                      child: Checkbox(
                        side: BorderSide(width: 1.sp, color: colorE1),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        fillColor: MaterialStateProperty.resolveWith((Set<MaterialState> states) {
                          if (states.contains(MaterialState.selected)) {
                            return colorBrand;
                          }
                          return Colors.white;
                        }),
                        checkColor: Colors.white,
                        value: currentRecord.answerMap.containsKey(questions[i]) &&
                            currentRecord.answerMap[questions[i]]!.contains(answers[j]),
                        onChanged: (value) {
                          hideKeyboard();
                          setState(() {
                            if (!currentRecord.answerMap.containsKey(questions[i])) {
                              currentRecord.answerMap[questions[i]] = [];
                            }
                            if (value!) {
                              if (!currentRecord.answerMap[questions[i]]!.contains(answers[j])) {
                                List list = currentRecord.answerMap[questions[i]]!;
                                int index = 0;
                                for (int k = 0; k < j; k++) {
                                  if (list.contains(answers[k])) {
                                    index++;
                                  }
                                }
                                list.insert(index, answers[j]);
                                logger("answerMap add ${currentRecord.answerMap[questions[i]]}");
                              }
                            } else {
                              currentRecord.answerMap[questions[i]]!.remove(answers[j]);
                              logger("answerMap remove ${currentRecord.answerMap[questions[i]]}");
                            }
                          });
                        },
                      ),
                    ),
                    SizedBox(width: 4.sp),
                    Expanded(
                      child: HeightText(answers[j], const Color(0xFF283143), 14.sp, 1.4),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      }
    }
    return Container(
      margin: EdgeInsets.only(top: 16.sp),
      padding: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  getIntentTagView() {
    if (isNotEmpty(currentRecord.intentTagList)) {
      currentRecord.intentTagList =
          currentRecord.intentTagList.map((e) => getTagText(intentTagList, e, getLangKey())).toList();
    }
    if (isEmpty(intentTagMap)) {
      return const SizedBox();
    }
    List<Widget> children = [
      GestureDetector(
        child: Container(
          color: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              MyText(Lang.deal_project, color2B, 16.sp, FontWeight.w500),
              Icon(
                hideTag ? Icons.expand_more : Icons.expand_less,
                size: 24.sp,
              ),
            ],
          ),
        ),
        onTap: () {
          setState(() {
            hideTag = !hideTag;
          });
        },
      ),
    ];
    if (!hideTag) {
      for (String key in intentTagMap.keys) {
        List<String> tags = intentTagMap[key]!;
        children.add(Padding(
          padding: EdgeInsets.only(top: 16.sp),
          child: MyText(key, color2B, 14.sp, FontWeight.w600),
        ));
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Wrap(
              children: tags.map((tag) => _getTagItem(tag)).toList(),
            ),
          ),
        );
      }
    }
    return Container(
      margin: EdgeInsets.only(top: 16.sp),
      padding: EdgeInsets.all(20.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  getAiTagView() {
    if (isNotEmpty(currentRecord.recommendTagList)) {
      currentRecord.recommendTagList =
          currentRecord.recommendTagList.map((e) => getTagText(aiTagList, e, getLangKey())).toList();
    }
    if (isEmpty(aiTagMap)) {
      return const SizedBox();
    }
    List<Widget> children = [
      Container(
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            MyText(Lang.recommend_by_self, color2B, 16.sp, FontWeight.w500),
            SizedBox(
              width: 24.sp,
              height: 24.sp,
              child: Material(
                color: Colors.transparent,
                child: Radio<bool>(
                  activeColor: colorBrand,
                  value: true,
                  onChanged: (value) {
                    setState(() {
                      currentRecord.recommendBySelf = true;
                    });
                  },
                  groupValue: currentRecord.recommendBySelf,
                ),
              ),
            ),
          ],
        ),
      ),
    ];
    if (currentRecord.recommendBySelf) {
      for (String key in aiTagMap.keys) {
        List<String> tags = aiTagMap[key]!;
        children.add(Padding(
          padding: EdgeInsets.only(top: 16.sp),
          child: MyText(key, color2B, 14.sp, FontWeight.w600),
        ));
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Wrap(
              children: tags.map((tag) => _getAiTagItem(tag)).toList(),
            ),
          ),
        );
      }
    }
    return GestureDetector(
      onTap: () {
        setState(() {
          currentRecord.recommendBySelf = true;
        });
      },
      child: Container(
        constraints: BoxConstraints(minHeight: 84.sp),
        margin: EdgeInsets.only(top: 12.sp),
        padding: EdgeInsets.fromLTRB(20.sp, 30.sp, 20.sp, 20.sp),
        // alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        ),
      ),
    );
  }

  Widget _getTagItem(String tag) {
    bool isSelected = currentRecord.intentTagList.contains(tag);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (currentRecord.intentTagList.contains(tag)) {
            currentRecord.intentTagList.remove(tag);
          } else {
            currentRecord.intentTagList.add(tag);
          }
        });
        logger(
            "tagList: ${currentRecord.gender} ${currentRecord.intentTagList} ${savedRecord.gender} ${savedRecord.intentTagList}");
      },
      child: Container(
        constraints: BoxConstraints(minHeight: 32.sp),
        margin: EdgeInsets.only(right: 10.sp, bottom: 8.sp),
        padding: EdgeInsets.fromLTRB(10.sp, 4.sp, 10.sp, 4.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          border: Border.all(color: isSelected ? colorBrand : colorE1),
          color: isSelected ? colorBrand : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [MyText(tag, isSelected ? Colors.white : color2B, 14.sp)],
        ),
      ),
    );
  }

  Widget _getAiTagItem(String tag) {
    bool isSelected = currentRecord.recommendTagList.contains(tag);

    return GestureDetector(
      onTap: () {
        setState(() {
          if (currentRecord.recommendTagList.contains(tag)) {
            currentRecord.recommendTagList.remove(tag);
          } else {
            currentRecord.recommendTagList.add(tag);
          }
        });
      },
      child: Container(
        constraints: BoxConstraints(minHeight: 32.sp),
        margin: EdgeInsets.only(right: 10.sp, bottom: 8.sp),
        padding: EdgeInsets.fromLTRB(10.sp, 4.sp, 10.sp, 4.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          border: Border.all(color: isSelected ? colorBrand : colorE1),
          color: isSelected ? colorBrand : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [MyText(tag, isSelected ? Colors.white : color2B, 14.sp)],
        ),
      ),
    );
  }

  void scrollToPosition(double offset) {
    try {
      if (offset != 0) {
        _scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } catch (ex) {
      //
    }
  }

  Widget getStepCircle(int index) {
    Widget child;
    if (index == step) {
      child = Container(
        width: 24.sp,
        height: 24.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.sp),
        ),
        alignment: Alignment.center,
        child: MyText("${index + 1}", colorBrand, 16.sp, FontWeight.w700),
      );
    } else if (index < currentRecord.processStep) {
      child = Image.asset("res/icons/icon_completed.png", width: 24.sp, height: 24.sp);
    } else {
      child = Image.asset("res/icons/icon_incompleted.png", width: 24.sp, height: 24.sp);
    }
    return GestureDetector(
      onTap: () {
        // if (index <= currentRecord.processStep) {
        //   switchStep(index);
        // }
      },
      child: child,
    );
  }

  Widget getStepText(int index, String text) {
    return GestureDetector(
      onTap: () {
        // if (index <= currentRecord.processStep) {
        //   switchStep(index);
        // }
      },
      child: Container(
        margin: EdgeInsets.only(top: 12.sp, bottom: 12.sp),
        width: 116.sp,
        alignment: Alignment.center,
        child: MyText(text, Colors.white, 14.sp),
      ),
    );
  }

  switchStep(int newStep) async {
    toStep() {
      currentRecord.setProcessStep(newStep);
      scrollToPosition(0.5.sp);
      hideKeyboard();
      setState(() {
        step = newStep;
      });
    }

    if (step == newStep) {
      return;
    }
    logger("currentRecord hasBaseInfoChanged: ${currentRecord.hasBaseInfoChanged(savedRecord)}");
    logger("currentRecord hasPhotosChanged: ${currentRecord.hasPhotosChanged(savedRecord)}");

    if (await isUltraIp()) {
      showCloseUltraDialog(true);
      return;
    } else if (newStep < step) {
      toStep();
    } else if (step == 0) {
      if (currentRecord.hasAllBaseInfo()) {
        if (!savedRecord.hasData()) {
          //创建出诊信息
          logger("currentRecord 创建出诊信息");
          createNewRecord((result) {
            if (result) {
              toStep();
            }
          });
        } else if (currentRecord.hasBaseInfoChanged(savedRecord)) {
          //更新基本信息
          logger("currentRecord 更新初诊详情");
          updateBaseInfo((result) {
            if (result) {
              toStep();
            }
          });
        } else {
          logger("currentRecord 初诊详情无变化");
          toStep();
        }
      } else {
        Global.toast(Lang.request_data_lost);
        //新建方式
        double top = savedRecord.hasData() ? 0 : 56.sp;
        //搜索框
        if (!savedRecord.hasData() && fromScan) {
          top += 56.sp;
        }
        //导入卡片
        if (isNotEmpty(currentRecord.scanCreateFrom)) {
          final RenderBox renderBox = _importKey.currentContext?.findRenderObject() as RenderBox;
          final containerHeight = renderBox.size.height;
          top += containerHeight;
        }
        if (isEmpty(currentRecord.name)) {
          scrollToPosition(24.sp + top);
        } else if (isEmpty(currentRecord.gender)) {
          scrollToPosition(104.sp + top);
        } else {
          scrollToPosition(184.sp + top);
        }
      }
    } else if (step == 1) {
      if (currentRecord.hasAllMouthPhotos() &&
          (!caseTypeList[caseTypeIndex]["needFace"] || currentRecord.hasAllFacePhotos())) {
        if (currentRecord.hasPhotosChanged(savedRecord)) {
          updatePhotos(
            (result) {
              if (result) {
                toStep();
              }
            },
          );
        } else {
          toStep();
        }
      } else {
        Global.toast(Lang.request_data_lost);
        double top = 0; //60.sp;
        if (caseTypeList[caseTypeIndex]["needFace"] && !currentRecord.hasAllFacePhotos()) {
          scrollToPosition(56.sp + top);
        } else {
          scrollToPosition(226.sp + top);
        }
      }
    } else {
      toStep();
    }
  }

  Widget getBodyView() {
    switch (step) {
      case 0:
        return !statusInit
            ? Container(
                padding: EdgeInsets.all(16.sp),
                margin: EdgeInsets.only(bottom: 280.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(16.sp),
                    bottomRight: Radius.circular(16.sp),
                  ),
                ),
                child: Column(
                  children: [
                    SizedBox(height: 20.sp),
                    Image.asset("res/icons/icon_loading.gif", width: 32.sp),
                    SizedBox(width: 1.sw - 72.sp, height: 20.sp),
                    MyText(Lang.loading, color7C, 14.sp),
                    SizedBox(height: 20.sp),
                  ],
                ),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  savedRecord.hasData()
                      ? const SizedBox()
                      : Container(
                          height: 46.sp,
                          margin: EdgeInsets.fromLTRB(0, 4.sp, 0, 16.sp),
                          padding: EdgeInsets.all(4.sp),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(28.sp),
                          ),
                          child: Row(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  hideKeyboard();
                                  setState(() {
                                    fromScan = false;
                                    showCurrentRecord(createdRecord);
                                  });
                                },
                                child: Container(
                                  width: 0.5.sw - 64.sp,
                                  height: 38.sp,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: fromScan ? Colors.white : colorBrand,
                                    borderRadius: BorderRadius.circular(28.sp),
                                  ),
                                  child: MyText(Lang.create_patient, fromScan ? color2B : Colors.white, 14.sp),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  hideKeyboard();
                                  setState(() {
                                    fromScan = true;
                                    showCurrentRecord(importedRecord);
                                  });
                                },
                                child: Container(
                                  width: 0.5.sw + 16.sp,
                                  height: 38.sp,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: fromScan ? colorBrand : Colors.white,
                                    borderRadius: BorderRadius.circular(28.sp),
                                  ),
                                  child: MyText(Lang.import_scan, fromScan ? Colors.white : color2B, 14.sp),
                                ),
                              ),
                            ],
                          ),
                        ),
                  !savedRecord.hasData() && fromScan
                      ? GestureDetector(
                          onTap: () {
                            push(DiagnoseSearchPage(keyword: keyword));
                          },
                          child: Container(
                            height: 38.sp,
                            margin: EdgeInsets.only(bottom: 16.sp),
                            padding: EdgeInsets.fromLTRB(16.sp, 0, 16.sp, 0),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20.sp),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                isEmpty(keyword)
                                    ? MyText(Lang.search_scan_hint, colorB8, 14.sp)
                                    : MyText(keyword, color2B, 14.sp),
                                Image.asset("res/imgs/icon_search2.png", height: 20.sp),
                              ],
                            ),
                          ),
                        )
                      : const SizedBox(),
                  savedRecord.hasData() || !fromScan || importedRecord.hasData()
                      ? Column(
                          children: [
                            getImportView(),
                            getUserInputView(),
                            Container(
                              margin: EdgeInsets.only(top: 16.sp),
                              padding: EdgeInsets.all(20.sp),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16.sp),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  MyText(Lang.main_require, color2B, 16.sp, FontWeight.w500),
                                  SizedBox(height: 16.sp),
                                  TextFormField(
                                    maxLines: null,
                                    controller: appealController,
                                    maxLength: 500,
                                    textAlign: TextAlign.start,
                                    onChanged: (value) {
                                      currentRecord.chiefComplaint = value;
                                    },
                                    style: TextStyle(fontSize: 14.sp, color: color2B),
                                    decoration: InputDecoration(
                                      counterText: '',
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero,
                                      border: InputBorder.none,
                                      hintText: Lang.input_appeal,
                                      hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            getQuestionView(),
                            getIntentTagView(),
                            Container(
                              margin: EdgeInsets.only(top: 16.sp, bottom: 42.sp),
                              padding: EdgeInsets.all(20.sp),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16.sp),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  MyText(Lang.remark, color2B, 16.sp, FontWeight.w500),
                                  SizedBox(height: 16.sp),
                                  TextFormField(
                                    maxLines: null,
                                    controller: remarkController,
                                    maxLength: 500,
                                    textAlign: TextAlign.start,
                                    onChanged: (value) {
                                      currentRecord.note = value;
                                    },
                                    style: TextStyle(fontSize: 14.sp, color: color2B),
                                    decoration: InputDecoration(
                                      counterText: '',
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero,
                                      border: InputBorder.none,
                                      hintText: Lang.input_remark,
                                      hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )
                      : const SizedBox(),
                ],
              );
      case 1:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 1.sw - 40.sp,
                  padding: EdgeInsets.all(8.sp),
                  constraints: BoxConstraints(minHeight: 36.sp),
                  margin: EdgeInsets.only(top: 4.sp),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: const Color(0xffE1DDF4),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16.sp),
                      topRight: Radius.circular(16.sp),
                    ),
                  ),
                  child: MyText(Lang.photos_for_report, colorBrand, 12.sp),
                ),
                Container(
                  padding: EdgeInsets.all(16.sp),
                  margin: EdgeInsets.only(bottom: 42.sp),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(16.sp),
                      bottomRight: Radius.circular(16.sp),
                    ),
                  ),
                  child: !statusNormal
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(height: 20.sp),
                            Image.asset("res/icons/icon_loading.gif", width: 32.sp),
                            SizedBox(width: 1.sw - 72.sp, height: 20.sp),
                            MyText(Lang.loading, color7C, 14.sp),
                            SizedBox(height: 20.sp),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            getFaceView(),
                            getScanView(),
                          ],
                        ),
                ),
              ],
            ),
          ],
        );
      case 2:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 1.sw,
              margin: EdgeInsets.only(top: 4.sp, bottom: 16.sp),
              padding: EdgeInsets.all(8.sp),
              constraints: BoxConstraints(minHeight: 34.sp),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0xffE1DDF4),
                borderRadius: BorderRadius.circular(8.sp),
              ),
              child: MyText(Lang.diagnose_report_tip, colorBrand, 12.sp),
            ),
            MyText(Lang.recommend_treat_project, color2B, 16.sp, FontWeight.w500),
            GestureDetector(
              onTap: () {
                setState(() {
                  currentRecord.recommendBySelf = false;
                });
              },
              child: Container(
                constraints: BoxConstraints(minHeight: 84.sp),
                margin: EdgeInsets.only(top: 12.sp),
                padding: EdgeInsets.all(20.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.sp),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 260.sp),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MyText(Lang.recommend_by_ai, color2B, 16.sp, FontWeight.w500),
                          SizedBox(height: 4.sp),
                          MyText(Lang.recommend_by_ai_desc, color80, 12.sp, FontWeight.w500),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 24.sp,
                      height: 24.sp,
                      child: Material(
                        color: Colors.transparent,
                        child: Radio<bool>(
                          activeColor: colorBrand,
                          value: false,
                          onChanged: (value) {
                            setState(() {
                              currentRecord.recommendBySelf = false;
                            });
                          },
                          groupValue: currentRecord.recommendBySelf,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            getAiTagView(),
            SizedBox(height: 24.sp),
            MyText(Lang.other_health_recommend, color2B, 16.sp, FontWeight.w500),
            Container(
              margin: EdgeInsets.only(top: 12.sp, bottom: 76.sp),
              padding: EdgeInsets.all(20.sp),
              constraints: BoxConstraints(minHeight: 102.sp),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.sp),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    maxLines: null,
                    controller: adviceController,
                    maxLength: 300,
                    textAlign: TextAlign.start,
                    onChanged: (value) {
                      currentRecord.advice = adviceController.text;
                    },
                    style: TextStyle(fontSize: 14.sp, color: color2B),
                    decoration: InputDecoration(
                      counterText: '',
                      isDense: true,
                      contentPadding: EdgeInsets.zero,
                      border: InputBorder.none,
                      hintText: Lang.other_health_recommend_hint,
                      hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
    }
    return const SizedBox();
  }

  final GlobalKey _importKey = GlobalKey();

  String getTagText(List list, String tag, String key) {
    if (isNotEmpty(list)) {
      for (dynamic map in list) {
        if (map.values.contains(tag) && isNotEmpty(map[key])) {
          logger("getTagsText $tag -> ${map[key]}");
          return map[key];
        }
      }
    }
    return tag;
  }

  Widget getImportView() {
    // logger("getImportView ${currentRecord.scanCreateFrom} ${currentRecord.scanCreateTime}");
    if (isNotEmpty(currentRecord.scanCreateFrom) && isNotEmpty(currentRecord.scanCreateTime)) {
      Widget avatarView() {
        if (isNotEmpty(currentRecord.smilePhoto)) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(40.sp),
            child: Image.file(
              File(getLocalPath(currentRecord.smilePhoto)),
              width: 54.sp,
              height: 54.sp,
              fit: BoxFit.cover,
            ),
          );
        } else if (isNotEmpty(currentRecord.avatarFilePath)) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(40.sp),
            child: Image.network(
              currentRecord.avatarFilePath,
              width: 54.sp,
              height: 54.sp,
              fit: BoxFit.cover,
            ),
          );
        } else {
          return Image.asset(
            "res/imgs/record_avatar.png",
            width: 54.sp,
            height: 54.sp,
            fit: BoxFit.cover,
          );
        }
      }

      Widget tagView() {
        return isNotEmpty(currentRecord.scanTagList)
            ? Expanded(
                child: Container(
                  margin: EdgeInsets.only(bottom: 22.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(Lang.scan_convert_project, color2B, 14.sp, FontWeight.w500),
                      Wrap(
                        children: currentRecord.scanTagList
                            .map(
                              (tag) => Container(
                                margin: EdgeInsets.only(top: 8.sp, right: 8.sp),
                                padding: EdgeInsets.all(4.sp),
                                decoration: BoxDecoration(
                                  color: colorBrand,
                                  borderRadius: BorderRadius.circular(4.sp),
                                ),
                                child: MyText(getTagText(convertList, tag, lang), colorBrand, 12.sp),
                              ),
                            )
                            .toList(),
                      ),
                    ],
                  ),
                ),
              )
            : const SizedBox();
      }

      Widget timeView() {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyText(Lang.scan_time, color2B, 14.sp, FontWeight.w500),
            SizedBox(height: 8.sp),
            SingleText(currentRecord.getDate(currentRecord.scanCreateTime), color7C, 14.sp),
          ],
        );
      }

      Widget sourceView() {
        return Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.scan_activity_source, color2B, 14.sp, FontWeight.w500),
              SizedBox(height: 8.sp),
              MyText(currentRecord.scanCreateFrom, color7C, 14.sp), //TODO
            ],
          ),
        );
      }

      return Container(
        key: _importKey,
        margin: EdgeInsets.only(top: 6.sp, bottom: 16.sp),
        padding: EdgeInsets.all(20.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: isNotEmpty(currentRecord.scanTagList)
            ? Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      tagView(),
                      avatarView(),
                    ],
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      timeView(),
                      SizedBox(width: 20.sp),
                      sourceView(),
                    ],
                  )
                ],
              )
            : Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  timeView(),
                  SizedBox(width: 20.sp),
                  sourceView(),
                  SizedBox(width: 12.sp),
                  avatarView(),
                ],
              ),
      );
    } else {
      return const SizedBox();
    }
  }

  bool checkOnPop() {
    if (!savedRecord.hasData()) {
      return true;
    }
    currentRecord.setProcessStep(step);
    bool changeBase = currentRecord.hasBaseInfoChanged(savedRecord) && currentRecord.hasAllBaseInfo();
    bool changePhoto = currentRecord.hasPhotosChanged(savedRecord) &&
        currentRecord.hasAllMouthPhotos() &&
        (!caseTypeList[caseTypeIndex]["needFace"] || currentRecord.hasAllFacePhotos());
    if (changeBase || changePhoto) {
      checkResult() {
        if (!changeBase && !changePhoto) {
          toast(Lang.auto_saved);
          pop();
        }
      }

      if (changeBase) {
        updateBaseInfo((result) {
          if (result) {
            changeBase = false;
            checkResult();
          }
        });
      }
      if (changePhoto) {
        updatePhotos((result) {
          if (result) {
            changePhoto = false;
            checkResult();
          }
        });
      }
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!init) {
      init = true;
      Map infoJson = Global.getRouterParams(context)["infoJson"] ?? {};
      if (isNotEmpty(infoJson)) {
        savedRecord = DiagnoseInfo.fromJson(infoJson);
        title = Lang.diagnose_detail;
        showCurrentRecord(savedRecord.clone());
        statusInit = false;
        getRecordInfo();
      } else {
        statusInit = true;
        title = Lang.add_diagnose;
        showCurrentRecord(createdRecord);
      }
    }
    var isKeyboardShow = MediaQuery.of(context).viewInsets.bottom > 5.sp;
    // logger("isKeyboardShow: $isKeyboardShow");
    return WillPopScope(
      onWillPop: () async {
        logger(
            "onWillPop hasBaseInfoChanged:${currentRecord.hasBaseInfoChanged(savedRecord) && currentRecord.hasAllBaseInfo()}");
        logger(
            "onWillPop hasPhotosChanged:${currentRecord.hasPhotosChanged(savedRecord) && currentRecord.hasAllMouthPhotos() && (!caseTypeList[caseTypeIndex]["needFace"] || currentRecord.hasAllFacePhotos())}");
        if (await isUltraIp()) {
          showCloseUltraDialog(true);
          return false;
        } else {
          return checkOnPop();
        }
      },
      child: Scaffold(
        appBar: LingyaAppBar(
          contentHeight: 183.sp - MediaQuery.of(context).padding.top,
          child: Stack(
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  // 触摸收起键盘
                  FocusScope.of(context).requestFocus(FocusNode());
                },
                child: Image.asset("res/scan/diagnose_top_bg.png", width: 1.sw, height: 183.sp, fit: BoxFit.fill),
              ),
              Column(
                children: [
                  SizedBox(height: MediaQuery.of(context).padding.top + 4.sp),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () async {
                          if (await isUltraIp()) {
                            showCloseUltraDialog(true);
                          } else if (checkOnPop()) {
                            pop();
                          }
                        },
                        icon: Icon(
                          Platform.isAndroid ? Icons.arrow_back : Icons.arrow_back_ios_new,
                          color: Colors.white,
                        ),
                        iconSize: 24.sp,
                      ),
                      GestureDetector(
                        onLongPress: () {
                          if (HHttp.httpEnv != HttpEnv.prod || isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
                            setState(() {
                              if (isNotEmpty(localPhotos["la_up_use.zip"])) {
                                currentRecord.upPhotos = localPhotos["la_up_use.zip"]!;
                                for (dynamic path in currentRecord.upPhotos) {
                                  Global.copyImageDocument(path, "initial_${currentRecord.recordId}", save: true);
                                }
                              }
                              if (isNotEmpty(localPhotos["la_lr_close_use.zip"])) {
                                currentRecord.closePhotos = localPhotos["la_lr_close_use.zip"]!;
                                for (dynamic path in currentRecord.closePhotos) {
                                  Global.copyImageDocument(path, "initial_${currentRecord.recordId}", save: true);
                                }
                              }
                              if (isNotEmpty(localPhotos["la_down_use.zip"])) {
                                currentRecord.downPhotos = localPhotos["la_down_use.zip"]!;
                                for (dynamic path in currentRecord.downPhotos) {
                                  Global.copyImageDocument(path, "initial_${currentRecord.recordId}", save: true);
                                }
                              }
                            });
                            hideKeyboard();
                            toast("口扫图片已填充！");
                          }
                        },
                        child: MyText(title, Colors.white, 18.sp, FontWeight.w500),
                      ),
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(
                          Icons.space_bar,
                          color: Colors.transparent,
                        ),
                        iconSize: 24.sp,
                      ),
                    ],
                  ),
                  SizedBox(height: 20.sp),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      getStepCircle(0),
                      SizedBox(
                        height: 3.sp,
                        width: 92.sp,
                        child: DashLine(
                          color: Colors.white,
                          height: 1.sp,
                          dashWidth: 2.sp,
                        ),
                      ),
                      getStepCircle(1),
                      SizedBox(
                        height: 3.sp,
                        width: 92.sp,
                        child: DashLine(
                          color: Colors.white,
                          height: 1.sp,
                          dashWidth: 2.sp,
                        ),
                      ),
                      getStepCircle(2),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      getStepText(0, Lang.base_info),
                      getStepText(1, Lang.diagnose_info),
                      getStepText(2, Lang.make_report),
                    ],
                  ),
                ],
              ),
              Align(
                alignment: Alignment.bottomLeft,
                child: Container(
                  width: 1.sw,
                  height: 6.sp,
                  padding: EdgeInsets.only(left: 68.sp + step * 116.sp),
                  child: TriangleView(8.sp, 6.sp, colorBg),
                ),
              ),
            ],
          ),
        ),
        body: Stack(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                // 触摸收起键盘
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    // getCaseTypeView(),
                    Padding(
                      padding: EdgeInsets.fromLTRB(20.sp, 12.sp, 20.sp, 70.sp),
                      child: getBodyView(),
                    ),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: SizedBox(
                height: isKeyboardShow ? 0 : 90.sp,
                child: Container(
                  height: 90.sp,
                  padding: EdgeInsets.fromLTRB(20.sp, 14.sp, 20.sp, 34.sp),
                  decoration: BoxDecoration(
                    border: Border(top: BorderSide(color: color2B.withOpacity(0.04))),
                    color: Colors.white,
                  ),
                  child: getButtonView(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget getButtonView() {
    switch (step) {
      case 0:
        return HCustomButton(
          key: UniqueKey(),
          ms: 1000,
          initVisible: true,
          onPress: () async {
            if (statusInit) {
              switchStep(1);
            }
          },
          text: savedRecord.hasData() ? Lang.next_step : Lang.create,
        );
      case 1:
        return Row(
          children: [
            HCustomButton(
                key: UniqueKey(),
                ms: 1000,
                width: 0.5.sw - 26.sp,
                initVisible: true,
                bgColor: Colors.white,
                textColor: colorBrand,
                borderColor: colorBrand,
                borderWidth: 1.sp,
                onPress: () async {
                  switchStep(0);
                },
                text: Lang.previous_step),
            SizedBox(width: 12.sp),
            HCustomButton(
                key: UniqueKey(),
                ms: 1000,
                width: 0.5.sw - 26.sp,
                initVisible: true,
                onPress: () async {
                  hideKeyboard();
                  if (await isUltraIp()) {
                    showCloseUltraDialog(false);
                    return;
                  }
                  bool lost = await currentRecord.checkPhotos(removeLost: true);
                  if (lost) {
                    setState(() {});
                    Global.toast(Lang.record_image_lost);
                    return;
                  }
                  switchStep(2);
                },
                text: Lang.upload),
          ],
        );
      case 2:
        return Row(
          children: [
            HCustomButton(
                key: UniqueKey(),
                ms: 1000,
                width: 0.5.sw - 26.sp,
                initVisible: true,
                bgColor: Colors.white,
                textColor: colorBrand,
                borderColor: colorBrand,
                borderWidth: 1.sp,
                onPress: () async {
                  switchStep(1);
                },
                text: Lang.previous_step),
            SizedBox(width: 12.sp),
            HCustomButton(
                key: UniqueKey(),
                ms: currentUser.canMakeDiagnosePdf() ? 5000 : 1000,
                width: 0.5.sw - 26.sp,
                initVisible: true,
                onPress: () async {
                  hideKeyboard();
                  if (currentUser.canMakeDiagnosePdf()) {
                    makeReport();
                  } else {
                    Global.showCustomDialog(
                      SaveQrcodeView(),
                      isBringXBtn: false,
                    );
                  }
                },
                text: Lang.generate_report),
          ],
        );
    }
    return const SizedBox();
  }

  showCurrentRecord(DiagnoseInfo record) {
    logger("${record.toJson()}", key: "showCurrentRecord");
    setState(() {
      currentRecord = record;
      ageController.text = currentRecord.getAge().toString();
      nameController.text = currentRecord.name;
      appealController.text = currentRecord.chiefComplaint;
      remarkController.text = currentRecord.note;
      phoneController.text = currentRecord.phone;
      adviceController.text = currentRecord.advice;
      currentRecord.checkPhotos(removeLost: true);
      caseTypeIndex = currentRecord.isChild() ? 1 : 0;
    });
  }

  bool exitTip = false;
  bool submitTip = false;

  showCloseUltraDialog(bool isExit) {
    //强提示用户关闭ultra
    String content = isExit ? Lang.shut_off_ultra_exit : Lang.shut_off_ultra_submit;
    String subContent = (isExit && exitTip || !isExit && submitTip)
        ? "Q: ${Lang.device_dead_question}\nA: ${Lang.device_dead_answer}"
        : "";
    Global.showAlertDialog(
      Lang.friendly_tip,
      content,
      subContent: subContent,
      contentStyle: TextStyle(
        fontSize: 14.sp,
        color: const Color(0xffEF8666),
        fontWeight: FontWeight.w500,
      ),
      subContentStyle: TextStyle(
        fontSize: 14.sp,
        height: 1.8,
        color: color7C,
        fontWeight: FontWeight.w400,
      ),
      okText: Lang.ok,
      isHideCancelBtn: true,
    );
    delay(500, () {
      if (isExit) {
        exitTip = true;
      } else {
        submitTip = true;
      }
    });
  }

  getIsHaveErrorInfoScanImg() {
    bool isHave = false;
    for (int i = 0; i < swiperScanList.length; i++) {
      int errorCode = getErrorCode(swiperScanList[i]["img"]);
      if (isUltraLightError(errorCode, swiperScanList[i]["img"])) {
        errorCode = 0;
      }
      if (errorCode != 0) {
        isHave = true;
        break;
      }
    }
    return isHave;
  }

  AnimationController? controller;
  int submitTime = 0;

  setSubmitState(int status) {
    if (status == 0) {
      BotToast.cleanAll();
    } else if (status != submitStatus) {
      if (controller == null) {
        controller = AnimationController(duration: const Duration(seconds: 2), vsync: this);
        controller!.addStatusListener((status) {
          if (status == AnimationStatus.completed) {
            controller!.reset();
            controller!.forward();
          }
        });
        controller!.forward();
      }
      Global.showCustomDialog(
        SizedBox(
          width: 1.sw,
          height: 1.sh,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    RotationTransition(
                      alignment: Alignment.center,
                      turns: controller!,
                      child: Image.asset(
                        "res/scan/scan_submit_loading.png",
                        width: 88.sp,
                        height: 88.sp,
                      ),
                    ),
                    Image.asset(
                      status == 1 ? "res/scan/vector_wifi.png" : "res/scan/vector_file.png",
                      width: 40.sp,
                      height: 40.sp,
                    ),
                  ],
                ),
                SizedBox(height: 16.sp),
                Text(getSubmitText(status),
                    style: TextStyle(fontSize: 14.sp, color: Colors.white, fontWeight: FontWeight.w400)),
                SizedBox(height: 20.sp),
              ],
            ),
          ),
        ),
        backButtonBehavior: BackButtonBehavior.ignore,
        clickBgCallback: () {},
        isBringXBtn: false,
      );
      submitTime = DateTime.now().millisecondsSinceEpoch;
      delay(20000, () {
        if (submitStatus > 0) {
          int time = DateTime.now().millisecondsSinceEpoch;
          if (time - submitTime > 15000) {
            setSubmitState(0);
          }
        }
      });
    }
    submitStatus = status;
  }

  String getSubmitText(int status) {
    switch (status) {
      case 1:
        return Lang.connecting_network;
      default:
        return Lang.submitting_files;
    }
  }

  @override
  void onRoutePause(Route nextRoute) {
    super.onRoutePause(nextRoute);
    hideKeyboard();
  }

  @override
  void onRouteResume(Route nextRoute) {
    logger("DiagnoseInfoRecord onRouteResume");
    super.onRouteResume(nextRoute);
    hideKeyboard();
    AudioPlayerUtil.stopSound();
    setScreenVertiacl();

    delay(100, () {
      setScreenVertiacl();
      AudioPlayerUtil.stopSound();
      eventBus.fire(EventPopScan(fromRecord: true));
    });
    checkShowCloseUltra();
  }

  checkShowCloseUltra() async {
    // if (await isUltraIp()) {
    //   setState(() {
    //     showCloseUltraTip = true;
    //   });
    // }
  }

  getCaseTypeView() {
    if (step != 1) {
      return const SizedBox();
    }
    ScrollController scrollController = ScrollController();
    List<Widget> children = [];
    for (int i = 0; i < caseTypeList.length; i++) {
      Map caseData = caseTypeList[i];
      children.add(GestureDetector(
        onTap: () {
          if (i == 0) {
            scrollController.animateTo(0, duration: const Duration(milliseconds: 100), curve: Curves.linear);
          } else if (i == caseTypeList.length - 1) {
            scrollController.animateTo(3.sw, duration: const Duration(milliseconds: 300), curve: Curves.linear);
          }
          setState(() {
            caseTypeIndex = i;
          });
        },
        child: Container(
          alignment: Alignment.center,
          margin: EdgeInsets.only(left: i == 0 ? 20.sp : 0, right: 8.sp),
          padding: EdgeInsets.symmetric(horizontal: 12.sp),
          height: 36.sp,
          decoration: BoxDecoration(
            border: Border.all(
              color: caseTypeIndex == i ? colorBrand : colorE1,
            ),
            color: caseTypeIndex == i ? colorBrand : Colors.white,
            borderRadius: BorderRadius.circular(48.sp),
          ),
          child: Row(
            children: [
              MyText(caseData["title"], caseTypeIndex == i ? Colors.white : color2B, 14.sp),
              isNotEmpty(caseData["desc"])
                  ? MyText(caseData["desc"], caseTypeIndex == i ? Colors.white : color7C, 14.sp)
                  : const SizedBox(),
            ],
          ),
        ),
      ));
    }
    return SingleChildScrollView(
      controller: scrollController,
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: EdgeInsets.only(top: 16.sp),
        child: Row(children: children),
      ),
    );
  }
}

class ReportStateDialog extends BasePage {
  int reportId;
  dynamic makeReport;

  ReportStateDialog(this.reportId, this.makeReport);

  @override
  ReportStateDialogState createState() => ReportStateDialogState();
}

class ReportStateDialogState extends BasePageState<ReportStateDialog> {
  int state = 0; //0 - 正在生成, 1 - 成功， 2 - 失败

  @override
  void initState() {
    super.initState();
    addEventListener<EventReportStatus>((event) {
      if (event.reportId == widget.reportId) {
        setState(() {
          state = event.success ? 1 : 2;
        });
      }
    });
  }

  @override
  void dispose() {
    logger("ReportStateDialog dispose");
    eventBus.fire(EventReportDialogClose());
    super.dispose();
  }

  _getIcon() {
    switch (state) {
      case 0:
        return "res/icons/icon_loading.gif";
      case 1:
        return "res/icons/icon_success.png";
      case 2:
        return "res/icons/icon_fail.png";
      default:
        return "";
    }
  }

  _getTitle() {
    switch (state) {
      case 0:
        return Lang.report_generate_going;
      case 1:
        return Lang.report_generate_success;
      case 2:
        return Lang.report_generate_fail;
      default:
        return "";
    }
  }

  _getDesc() {
    switch (state) {
      case 0:
      case 1:
        return Lang.view_download_report;
      case 2:
        return Lang.try_again;
      default:
        return "";
    }
  }

  _getActionText() {
    switch (state) {
      case 0:
      case 1:
        return Lang.go_view;
      case 2:
        return Lang.remake;
      default:
        return "";
    }
  }

  _goAction() {
    BotToast.cleanAll();
    switch (state) {
      case 0:
      case 1:
        pushNamed("MyFilePage", {});
        break;
      case 2:
        widget.makeReport();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 276.sp,
      margin: EdgeInsets.all(32.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Align(
                alignment: Alignment.topCenter,
                child: Padding(
                  padding: EdgeInsets.all(20.sp),
                  child: Column(
                    children: [
                      Image.asset(_getIcon(), width: 32.sp),
                      SizedBox(height: 4.sp),
                      MyText(_getTitle(), color2B, 14.sp, FontWeight.w500),
                      SizedBox(height: 6.sp),
                      MyText(_getDesc(), color2B, 12.sp),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.topRight,
                child: GestureDetector(
                  onTap: () {
                    BotToast.cleanAll();
                  },
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 16.sp),
                    child: Image.asset("res/imgs/icon_close.png", width: 16.sp, color: colorB8),
                  ),
                ),
              ),
            ],
          ),
          Divider(height: 1.sp, color: const Color(0xffe5e5e5)),
          // Row(
          //   mainAxisSize: MainAxisSize.min,
          //   children: [
          //     Click(
          //       onTap: () {
          //         BotToast.cleanAll();
          //       },
          //       ms: 5000,
          //       child: Container(
          //         width: 137.sp,
          //         height: 40.sp,
          //         alignment: Alignment.center,
          //         child: MyText(Lang.close, color2B, 14.sp, FontWeight.w500),
          //       ),
          //     ),
          //     Container(
          //       width: 1.sp,
          //       height: 40.sp,
          //       color: const Color(0xffe5e5e5),
          //     ),
          Click(
            onTap: _goAction,
            ms: 5000,
            child: Container(
              width: 137.sp,
              height: 40.sp,
              alignment: Alignment.center,
              child: MyText(_getActionText(), color2B, 14.sp, FontWeight.w500),
            ),
          ),
          //     ],
          //   ),
        ],
      ),
    );
  }
}
