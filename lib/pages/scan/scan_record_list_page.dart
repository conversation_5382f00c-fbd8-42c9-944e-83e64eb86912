import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/manager/xianzong_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/pages/scan/record_search_page.dart';
import 'package:mooeli/pages/scan/scan_record_item_view.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_grid.dart';
import 'package:sprintf/sprintf.dart';

import '../../widget/custom_button.dart';

class ScanRecordListPage extends BasePage {
  ScanActivity scanActivity;

  ScanRecordListPage(this.scanActivity, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScanRecordListPageState();
  }
}

class ScanRecordListPageState extends BasePageState<ScanRecordListPage> {
  List<ScanInfo> dataList = [];
  List<ScanInfo> uploadList = [];
  List<ScanInfo> showList = [];
  int pageIndex = 0;
  int pageSize = 20;
  int totalCount = 0;
  bool isLoading = false;
  bool allLoad = false;
  bool addUpload = false;
  bool showReUpload = false;

  bool selectMode = false;
  List<ScanInfo> selectList = [];

  ///0-全部，1-待完善, 2-待上传，3-正在上传，4-已上传，5-上传失败
  int showMode = 0;

  GlobalKey<RefreshGridState> listKey = GlobalKey();

  final ScrollController _waitScrollController = ScrollController();

  Map<String, String> smileMap = {};

  @override
  initState() {
    super.initState();
    addEventListener<EventUploadRecord>((event) {
      addUpload = true;
    });
    addEventListener<EventUploadSingleRecord>((event) {
      delay(500, () {
        addSingleRecordUpload(event.record);
      });
    });
    addEventListener<EventDeleteRecord>((event) {
      Global.showAlertDialog(
        Lang.delete_record,
        Lang.delete_not_recoverd,
        okColor: colorRed,
        okText: Lang.delete,
        okCallBack: () {
          if (event.record.status == RecordStatus.completeUpload) {
            HHttp.request(
              "/v3/quickscan/scanRecord/delete",
              "POST",
              (data) {
                toast(Lang.delete_success);
                refreshDataList();
              },
              params: {
                "scanRecordId": event.record.recordId,
              },
            );
          } else {
            uploadList.remove(event.record);
            deleteRecords([event.record.recordId]);
            getUploadList();
          }
        },
      );
    });
    addEventListener<EventScanType>((event) {
      if (event.showType >= 0) {
        setState(() {
          showMode = event.showType;
        });
      }
      refreshDataList(600, true);
    });
    // addEventListener<EventRecordDialog>((event) {
    //   refreshDataList(50);
    //   delay(300, () {
    //     Global.showBottomModal(
    //       context,
    //       getWaitingUploadDialog(),
    //       maxHeight: 0.8.sh,
    //     );
    //   });
    // });
    addEventListener<EventUploadRecordResult>((event) {
      setState(() async {
        // uploadList = loadUploadRecordList(widget.scanActivity.caseId);
        await getUploadList();
        filterRecord(uploadList, dataList);
        for (int i = 0; i < uploadList.length; i++) {
          if (uploadList[i].recordId == event.record.recordId || uploadList[i].customTime == event.record.customTime) {
            uploadList[i] = event.record;
            smileMap[event.record.recordId] = event.record.smilePhoto;
          }
        }
        // saveUploadRecordList(widget.scanActivity.caseId, uploadList);
      });
      if (event.record.status == RecordStatus.completeUpload) {
        refreshDataList(500);
      }
    });
    addEventListener<EventRefreshRecordList>((event) {
      setState(() async {
        uploadList = await getUploadRecordList(widget.scanActivity.campaignId, widget.scanActivity.singleCampaign);
      });
    });
    initAsyncState();
  }

  @override
  dispose() {
    uploadingCaseIds.remove(widget.scanActivity.campaignId);
    super.dispose();
  }

  checkUploadStatus() async {
    int time = DateTime.now().millisecondsSinceEpoch;
    await getUploadList();
    logger("checkUploadStatus uploadTime: ${time - (uploadTimeMap[widget.scanActivity.campaignId] ?? 0)}");
    if (time - (uploadTimeMap[widget.scanActivity.campaignId] ?? 0) > 15000) {
      uploadingCaseIds.remove(widget.scanActivity.campaignId);
      List<ScanInfo> uploadingList = uploadList.where((element) => element.status == RecordStatus.uploading).toList();
      if (isNotEmpty(uploadingList)) {
        logger("startUploadScanRecords 000");
        if (await HHttp.checkInternetValid()) {
          startUploadScanRecords(widget.scanActivity);
        }
      }
    }
    logger("checkUploadStatus confirmTime: ${time - (confirmTimeMap[widget.scanActivity.campaignId] ?? 0)}");
    for (ScanInfo record in uploadList) {
      if (record.recordId == uploadRecordId && time - (confirmTimeMap[widget.scanActivity.campaignId] ?? 0) > 10000) {
        HHttp.request(
          "/v3/quickscan/scanRecord/confirm",
          "POST",
          (data) {
            logger("startUploadScanRecord timer ${record.recordName} success!");
            record.status = RecordStatus.completeUpload;
            eventBus.fire(EventUploadRecordResult(record));
            for (ScanInfo r in uploadList) {
              if (r.recordId == record.recordId) {
                r.status = RecordStatus.completeUpload;
              }
            }
            saveRecordToDb(widget.scanActivity.campaignId, record);
            getUploadList();
            addEventTrace({
              "domainName": "quickScan",
              "operationName": "confirmRecord",
              "value1": record.recordId,
            });
          },
          params: {
            "scanRecordId": record.recordId,
            "categoryList": record.getAllPhotos().map((path) {
              String fileName = Global.getFileNameByPath(path);
              return getTypeByFile(fileName);
            }).toList(),
          },
          isShowNetWorkLoading: false,
          isShowErrorToast: false,
        );
      }
    }
    try {
      for (String id in uploadIdMap.keys) {
        logger(
            "checkUploadStatus startUploadScanRecord uploadFileTime: ${uploadIdMap[id]!} ${time - uploadIdMap[id]!}");
        if (uploadIdMap[id]! % 10 > 0) {
          if (time - uploadIdMap[id]! > 15000) {
            uploadIdMap[id] = uploadIdMap[id]! - 1;
            HHttp.request(
              "/v3/file/confirmWithCrop",
              "POST",
              (data) {
                uploadIdMap.remove(id);
              },
              params: {
                "fileId": id,
                "sourceType": "RECORD_QUICKSCAN",
              },
              isShowNetWorkLoading: false,
              isShowErrorToast: false,
            );
          }
        }
      }
    } catch (ex) {
      //
    }
    delay(20000, checkUploadStatus);
  }

  initAsyncState() async {
    await refreshDataList();
    // delay(10000, checkUploadStatus);
    disconnectUltra(disconnectWifi: true);
    delay(1000, () async {
      if (isNotEmpty(uploadList)) {
        for (ScanInfo record in uploadList) {
          await record.checkPhotoLost(removeLost: false);
        }
      }
    });
  }

  refreshDataList([int ms = 600, bool checkPage = false]) async {
    delay(ms, () async {
      setState(() async {
        await getUploadList();
        showReUpload =
            isNotEmpty(uploadList) && uploadList.where((record) => record.status == RecordStatus.failUpload).isNotEmpty;
      });
      if (addUpload) {
        addUpload = false;
        addToUploadList();
      }
      List<ScanInfo> uploadingList = uploadList.where((element) => element.status == RecordStatus.uploading).toList();
      if (isNotEmpty(uploadingList)) {
        if (await HHttp.checkInternetValid()) {
          // for (ScanInfo record in uploadingList) {
          //   if (record.status == RecordStatus.waitingUpload || record.status == RecordStatus.recording) {
          //     record.status = record.hasAllRequest() ? RecordStatus.waitingUpload : RecordStatus.recording;
          //     if (record.status == RecordStatus.waitingUpload && !record.repeatSn) {
          //       record.status = RecordStatus.uploading;
          //     }
          //     saveRecordToDb(widget.scanActivity.campaignId, record);
          //   }
          // }
          logger("startUploadScanRecords 111");
          startUploadScanRecords(widget.scanActivity);
        }
      } else {
        for (ScanInfo record in uploadList) {
          setState(() {
            record.status = record.hasAllRequest() ? RecordStatus.waitingUpload : RecordStatus.recording;
          });
          saveRecordToDb(widget.scanActivity.campaignId, record);
        }
      }
    });
    if (checkPage && pageIndex > 0) {
      return;
    }
    return await getDataList(true);
  }

  Future<void> getUploadList() async {
    uploadList = await getUploadRecordList(widget.scanActivity.campaignId, widget.scanActivity.singleCampaign);
    filterRecord(uploadList, dataList);
    setState(() {});
  }

  onLoadMore() {
    logger("onLoadMore RecordList: ${dataList.length}");
    getDataList(false);
  }

  getDataList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    HHttp.request(
      "/v3/quickscan/scanRecord/page",
      "POST",
      (data) {
        List<ScanInfo> list = (data["data"] as List).map((i) => ScanInfo.fromJson(i, true)).toList();
        if (isNotEmpty(list)) {
          List<String> ids = [];
          for (var item in list) {
            if (isNotEmpty(item.smileFileId)) {
              ids.add(item.smileFileId);
            }
          }
          downloadThumbnail(
            ids,
            (id, path) {
              for (var item in list) {
                if (item.smileFileId == id) {
                  setState(() {
                    item.smilePhoto = path;
                  });
                }
              }
            },
            "RECORD_QUICKSCAN",
          );
        }
        setState(() {
          totalCount = getJsonInt(data, "totalCount");
          allLoad = true;
          if (refresh) {
            dataList = list;
            if (dataList.length >= pageSize) {
              allLoad = false;
            }
          } else {
            if (isNotEmpty(list)) {
              if (list.length >= pageSize) {
                allLoad = false;
              }
              dataList.addAll(list);
            }
          }
          getUploadList();
          logger("saveUploadRecordList filter ${uploadList.map((e) => e.recordName).toList()}");

          logger("record list uploadList: ${uploadList.map((e) => "[${e.recordId}, ${e.recordName}]").toList()}");
          logger("record list dataList: ${dataList.map((e) => "[${e.recordId}, ${e.recordName}]").toList()}");
          isLoading = false;
        });
      },
      errCallBack: (_) {
        setState(() {
          isLoading = false;
        });
      },
      params: {
        "pageCount": pageSize,
        "pageIndex": pageIndex,
        "filterParamList": widget.scanActivity.singleCampaign
            ? [
                {
                  "columnName": "singleCampaign",
                  "columnValue": widget.scanActivity.singleCampaign ? "true" : "false",
                  "filterEnum": "eq",
                }
              ]
            : [
                {
                  "columnName": "campaignId",
                  "columnValue": widget.scanActivity.campaignId,
                  "filterEnum": "eq",
                }
              ],
      },
      isShowErrorToast: false,
      isShowNetWorkLoading: false,
    );
  }

  getListView() {
    showList.clear();
    switch (showMode) {
      case 0:
        showList.addAll(uploadList);
        showList.addAll(dataList);
        break;
      case 1:
        showList.addAll(uploadList.where((element) => element.status == RecordStatus.recording).toList());
        break;
      case 2:
        showList.addAll(uploadList.where((element) => element.status == RecordStatus.waitingUpload).toList());
        break;
      case 3:
        showList.addAll(uploadList.where((element) => element.status == RecordStatus.uploading).toList());
        break;
      case 4:
        showList.addAll(dataList);
        break;
      case 5:
        showList.addAll(uploadList.where((element) => element.status == RecordStatus.failUpload).toList());
        break;
    }
    return showList.map((e) => _getRecordItem(e, false)).toList();
  }

  // 下拉刷新
  Future onRefresh() async {
    await refreshDataList();
  }

  String getCount(int mode) {
    String text = "0";
    switch (mode) {
      case 0:
        text = "${uploadList.length + totalCount}";
        break;
      case 1:
        text = "${uploadList.where((element) => element.status == RecordStatus.recording).length}";
        break;
      case 2:
        text = "${uploadList.where((element) => element.status == RecordStatus.waitingUpload).length}";
        break;
      case 3:
        text = "${uploadList.where((element) => element.status == RecordStatus.uploading).length}";
        break;
      case 4:
        text = "$totalCount";
        break;
      case 5:
        text = "${uploadList.where((element) => element.status == RecordStatus.failUpload).length}";
        break;
    }
    return text;
  }

  @override
  Widget build(BuildContext context) {
    List icons = [
      "icon_activity_all",
      "icon_upload_improved",
      "icon_upload_wait",
      "icon_upload_ing",
      "icon_upload_complete",
      "icon_upload_fail",
    ];
    List titles = [
      Lang.all,
      Lang.status_recording,
      Lang.status_waiting_upload,
      Lang.status_uploading,
      Lang.status_complete_upload,
      Lang.status_fail_upload,
    ];
    final paddingTop = MediaQuery.of(context).padding.top;
    final isSelectAll = selectList.length == uploadList.length && selectList.isNotEmpty;
    return Container(
        padding: EdgeInsets.only(top: paddingTop),
        decoration: BoxDecoration(
          color: colorPurpleLilac,
          image: const DecorationImage(image: AssetImage("res/imgs/my_page_content_bg.png"), fit: BoxFit.cover),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Column(
              children: [
                Expanded(
                  child: Container(
                    width: 248.sp,
                    height: 1.sh - 84.sp,
                    decoration:
                        BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
                    margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
                    padding: EdgeInsets.all(16.sp),
                    child: Column(
                      children: [
                        Click(
                            child: Container(
                              height: 56.sp,
                              margin: EdgeInsets.only(bottom: 16.sp),
                              padding: EdgeInsets.all(8.sp),
                              decoration:
                                  BoxDecoration(color: colorBlueDeep, borderRadius: BorderRadius.circular(8.sp)),
                              child: Row(
                                children: [
                                  Image.asset(
                                    "res/icons/icon_back_white.png",
                                    height: 32.sp,
                                  ),
                                  SizedBox(width: 4.sp),
                                  MyText(Lang.back, Colors.white, 24.sp)
                                ],
                              ),
                            ),
                            onTap: () {
                              pop();
                            }),
                        ...List.generate(
                          6,
                          (index) => Click(
                              child: Container(
                                height: 56.sp,
                                margin: EdgeInsets.only(bottom: 16.sp),
                                padding: EdgeInsets.all(8.sp),
                                decoration: BoxDecoration(
                                    color: showMode == index ? colorPurpleLavender : Colors.transparent,
                                    borderRadius: BorderRadius.circular(8.sp)),
                                child: Row(
                                  children: [
                                    Image.asset(
                                      "res/icons/${icons[index]}${showMode == index ? "_select" : ""}.png",
                                      height: 32.sp,
                                    ),
                                    SizedBox(width: 4.sp),
                                    MyText(titles[index], showMode == index ? colorBlueDeep : color2B, 24.sp,
                                        showMode == index ? FontWeight.w500 : FontWeight.w400),
                                    const Spacer(),
                                    MyText(getCount(index), showMode == index ? colorBlueDeep : color2B, 24.sp,
                                        showMode == index ? FontWeight.w500 : FontWeight.w400),
                                  ],
                                ),
                              ),
                              onTap: () {
                                setState(() {
                                  showMode = index;
                                });
                              }),
                        ),
                        // Image.asset("res/icons/icon_activity_line.png", height: 1.sp,),
                        // Click(
                        //     child: Container(
                        //       height: 56.sp,
                        //       margin: EdgeInsets.fromLTRB(0.sp,16.sp,0.sp, 16.sp),
                        //       padding: EdgeInsets.all(8.sp),
                        //       decoration: const BoxDecoration(
                        //         color: Colors.transparent,
                        //       ),
                        //       child: Row(
                        //         children: [
                        //           Image.asset("res/icons/icon_sort${sortDesc ? "_desc" : ""}.png", height: 32.sp,),
                        //           MyText(sortDesc ? Lang.time_sort : Lang.time_desc, color2B, 24.sp),
                        //         ],
                        //       ),
                        //     ), onTap: () {
                        //   setState(() {sortDesc = !sortDesc;});
                        // }),
                      ],
                    ),
                  ),
                ),
                // Container(
                //   width: 1.sw - 96.sp,
                //   height: 78.sp,
                //   margin: EdgeInsets.fromLTRB(0, 32.sp, 24.sp, 0),
                //   padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                //   decoration: BoxDecoration(
                //     color: Colors.white,
                //     borderRadius: BorderRadius.circular(16.sp),
                //   ),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     children: [
                //       Row(
                //         children: [0, 1, 2, 3, 4].map((e) => getNavigatorText(e)).toList(),
                //       ),
                //       showMode == 2 ? MyText(Lang.upload_record_time_tip, color7C, 24.sp) : const SizedBox(),
                //     ],
                //   ),
                // ),
                // Expanded(
                //   child: Container(
                //     padding: EdgeInsets.only(top: 32.sp),
                //     child: RefreshGrid(
                //       key: listKey,
                //       childList: getListView(),
                //       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                //         crossAxisCount: 4,
                //         childAspectRatio: 438.0 / 244,
                //         crossAxisSpacing: 24.sp,
                //         mainAxisSpacing: 24.sp,
                //       ),
                //       isLoading: isLoading,
                //       onRefresh: onRefresh,
                //       onLoadMore: onLoadMore,
                //       nullImgName: "res/icons/empty.png",
                //       nullText: Lang.no_record,
                //     ),
                //   ),
                // ),
              ],
            ),
            Expanded(
              flex: 1,
              child: Container(
                height: 1.sh - 84.sp,
                margin: EdgeInsets.fromLTRB(8.sp, 16.sp, 8.sp, 16.sp),
                child: Column(
                  children: [
                    Click(
                      onTap: () {
                        push(RecordSearchPage(widget.scanActivity));
                      },
                      child: Container(
                        height: 56.sp,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(32.sp),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 8.sp, horizontal: 12.sp),
                        child: Row(
                          children: [
                            Image.asset("res/icons/icon_activity_search.png", width: 30.sp),
                            SizedBox(width: 12.sp),
                            MyText(sprintf(Lang.search_record_hint, [widget.scanActivity.getCampaignName()]), colorA4,
                                24.sp),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 20.sp),
                    Expanded(
                      child: RefreshGrid(
                        childList: getListView(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          childAspectRatio: 274.0 / 374,
                          crossAxisSpacing: 16.sp,
                          mainAxisSpacing: 16.sp,
                        ),
                        isLoading: isLoading,
                        onRefresh: onRefresh,
                        onLoadMore: onLoadMore,
                        nullImgName: "res/icons/empty.png",
                        nullText: Lang.no_record,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
                width: 248.sp,
                height: 1.sh - 84.sp,
                decoration:
                    BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
                margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
                padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
                child: Column(
                  children: selectMode
                      ? [
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            radius: 8.sp,
                            onPress: () {
                              setState(() {
                                selectList.clear();
                                selectMode = false;
                              });
                            },
                            bgColor: colorPurpleLavender,
                            child: MyText(Lang.exit_batch_select, colorBlueDeep, 24.sp),
                          ),
                          if (showMode == 2 || showMode == 5)
                            HCustomButton(
                              key: UniqueKey(),
                              height: 56.sp,
                              fontSize: 24.sp,
                              radius: 8.sp,
                              onPress: () {
                                setState(() {
                                  if (isSelectAll) {
                                    selectList.clear();
                                  }else {
                                    selectList = [...uploadList];
                                  }
                                });
                              },
                              bgColor: colorPurpleLavender,
                              child: MyText(isSelectAll ? Lang.cancel_select_all : Lang.batch_select_all_record, colorBlueDeep, 24.sp),
                            ),
                          const Spacer(),
                          if (isNotEmpty(selectList)) ...[
                            MyText(sprintf(Lang.select_record_count, [selectList.length]), color2B, 20.sp),
                            SizedBox(height: 16.sp),
                            Image.asset("res/imgs/dotted_divider.png", width: 180.sp),
                            SizedBox(height: 16.sp),
                          ],
                          // HCustomButton(
                          //   key: UniqueKey(),
                          //   height: 56.sp,
                          //   fontSize: 24.sp,
                          //   fixedBottom: false,
                          //   radius: 8.sp,
                          //   onPress: () {},
                          //   bgColor: colorBlueDeep.withOpacity(isEmpty(selectList) ? 0.6 : 1),
                          //   child: MyText(Lang.export_u_card, Colors.white, 24.sp),
                          // ),
                          if (showMode == 2 || showMode == 5)
                            HCustomButton(
                              key: UniqueKey(),
                              height: 56.sp,
                              fontSize: 24.sp,
                              fixedBottom: false,
                              radius: 8.sp,
                              onPress: () async {
                                hideKeyboard();
                                bool isHaveNetWork = await HHttp.checkInternetValid();
                                if (!isHaveNetWork) {
                                  Global.toast(Lang.no_network_upload);
                                  return;
                                }
                                List<String> localRecordIds = selectList
                                    .where((element) => element.status != RecordStatus.completeUpload)
                                    .map((e) => e.recordId)
                                    .toList();
                                for (String recordId in localRecordIds) {
                                  ScanInfo? record = await getRecordById(recordId);
                                  if (record != null && record!.hasAllRequest()) {
                                    setState(() {
                                      record.status = RecordStatus.uploading;
                                    });
                                    saveRecordToDb(widget.scanActivity.campaignId, record);
                                    startUploadScanRecords(widget.scanActivity);
                                  }
                                }
                                setState(() {
                                  selectList.clear();
                                  selectMode = false;
                                });
                              },
                              bgColor: colorBlueDeep.withOpacity(isEmpty(selectList) ? 0.6 : 1),
                              child: MyText(Lang.upload, Colors.white, 24.sp),
                            ),
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            fixedBottom: false,
                            radius: 8.sp,
                            onPress: () {
                              hideKeyboard();
                              Global.showAlertDialog(
                                Lang.delete_record,
                                Lang.delete_not_recoverd,
                                okColor: colorRed,
                                okText: Lang.delete,
                                okCallBack: () async {
                                  List<ScanInfo> cloudRecords = selectList
                                      .where((element) => element.status == RecordStatus.completeUpload)
                                      .toList();
                                  List<String> localRecordIds = selectList
                                      .where((element) => element.status != RecordStatus.completeUpload)
                                      .map((e) => e.recordId)
                                      .toList();
                                  dataList.removeWhere((element) => cloudRecords.contains(element));
                                  uploadList.removeWhere((element) => localRecordIds.contains(element.recordId));
                                  setState(() {});

                                  for (ScanInfo record in cloudRecords) {
                                    HHttp.request(
                                      "/v3/quickscan/scanRecord/delete",
                                      "POST",
                                      (data) {
                                        if (record.recordId == cloudRecords.last.recordId) {
                                          toast(Lang.delete_success);
                                          refreshDataList();
                                          setState(() {
                                            selectList.clear();
                                            selectMode = false;
                                          });
                                        }
                                      },
                                      params: {
                                        "scanRecordId": record.recordId,
                                      },
                                    );
                                  }

                                  if (isNotEmpty(localRecordIds)) {
                                    deleteRecords(localRecordIds);
                                    await getUploadList();
                                    if (isEmpty(cloudRecords)) {
                                      toast(Lang.delete_success);
                                      setState(() {
                                        selectList.clear();
                                        selectMode = false;
                                      });
                                    }
                                  }
                                },
                              );
                            },
                            bgColor: colorRed.withOpacity(isEmpty(selectList) ? 0.6 : 1),
                            child: MyText(Lang.delete, Colors.white, 24.sp),
                          ),
                        ]
                      : [
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            radius: 8.sp,
                            onPress: () {
                              setState(() {
                                selectList.clear();
                                selectMode = true;
                              });
                            },
                            bgColor: colorPurpleLavender,
                            child: MyText(Lang.batch_select_record, colorBlueDeep, 24.sp),
                          ),
                          const Spacer(),
                          HCustomButton(
                            key: UniqueKey(),
                            height: 56.sp,
                            fontSize: 24.sp,
                            fixedBottom: false,
                            radius: 8.sp,
                            onPress: () {
                              hideKeyboard();
                              if (uploadList.length >= MAX_SCAN_SIZE) {
                                // Global.showBottomModal(
                                //   context,
                                //   getWaitingUploadDialog(),
                                //   maxHeight: 0.8.sh,
                                // );
                                // Global.toast(Lang.upload_records_first);
                              } else {
                                // User.instance.refreshToken(
                                //   okCallback: () {
                                // editMode = false;
                                // deleteList.clear();
                                pushNamed("ScanInfoRecord", {"caseId": widget.scanActivity.campaignId});
                                //   },
                                // );
                              }
                            },
                            bgColor: colorBlueDeep,
                            child: MyText(Lang.new_scan, Colors.white, 24.sp),
                          ),
                        ],
                )),
            // Align(
            //   alignment: Alignment.bottomCenter,
            //   child: getBottomButtons(),
            // ),
            // isNotEmpty(showList)
            //     ? Positioned(
            //         right: 0,
            //         bottom: 100.sp,
            //         child: GestureDetector(
            //           onTap: () {
            //             (listKey.currentState as RefreshGridState).scrollToPosition(0);
            //           },
            //           child: Image.asset(
            //             "res/icons/icon_to_top.png",
            //             width: 80.sp,
            //           ),
            //         ),
            //       )
            //     : const SizedBox(),
          ],
        ));
  }

  // getWaitingUploadDialog() {
  //   return Container(
  //     constraints: BoxConstraints(maxHeight: 0.8.sh),
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.only(topLeft: Radius.circular(16.sp), topRight: Radius.circular(16.sp)),
  //       color: colorBg,
  //     ),
  //     child: Stack(
  //       children: [
  //         Container(
  //           padding: EdgeInsets.all(20.sp),
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               Container(
  //                 color: Colors.transparent,
  //                 padding: EdgeInsets.only(bottom: 20.sp),
  //                 child: Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     SizedBox(width: 20.sp),
  //                     MyText(Lang.status_waiting_upload, color2B, 16.sp, FontWeight.w500),
  //                     GestureDetector(
  //                       onTap: () {
  //                         Navigator.pop(context);
  //                       },
  //                       child: Image.asset("res/imgs/modal_close.png", width: 20.sp),
  //                     )
  //                   ],
  //                 ),
  //               ),
  //               isEmpty(recordList)
  //                   ? Column(
  //                       mainAxisSize: MainAxisSize.min,
  //                       children: [
  //                         SizedBox(height: 120.sp),
  //                         Image.asset("res/icons/empty.png", width: 120.sp),
  //                         SizedBox(height: 24.sp),
  //                         MyText(Lang.no_record, colorAB, 14.sp),
  //                       ],
  //                     )
  //                   : Container(
  //                       constraints: BoxConstraints(maxHeight: 0.8.sh - 90.sp),
  //                       child: SingleChildScrollView(
  //                         controller: _waitScrollController,
  //                         child: Column(
  //                           children: [
  //                             ...recordList.reversed.map((record) => _getRecordItem(record, true)).toList(),
  //                             SizedBox(height: 90.sp),
  //                           ],
  //                         ),
  //                       ),
  //                     ),
  //             ],
  //           ),
  //         ),
  //         Align(
  //           alignment: Alignment.bottomCenter,
  //           child: Container(
  //             height: 90.sp,
  //             padding: EdgeInsets.fromLTRB(20.sp, 14.sp, 20.sp, 34.sp),
  //             decoration: BoxDecoration(
  //               color: colorBg,
  //               border: Border(top: BorderSide(color: color2B.withOpacity(0.05))),
  //             ),
  //             child: HCustomButton(
  //               ms: 1000,
  //               width: 335.sp,
  //               height: 42.sp,
  //               initVisible: isNotEmpty(recordList),
  //               onPress: () async {
  //                 int incompleteCount =
  //                     recordList.where((record) => (record.status == RecordStatus.recording || record.repeatSn)).length;
  //                 if (incompleteCount == recordList.length) {
  //                   Global.showAlertDialog(
  //                     Lang.attention,
  //                     sprintf(Lang.record_all_incomplete, [incompleteCount]),
  //                     okText: Lang.i_knew,
  //                     isHideCancelBtn: true,
  //                   );
  //                   return;
  //                 }
  //                 if (incompleteCount > 0) {
  //                   Global.showAlertDialog(
  //                     Lang.attention,
  //                     sprintf(Lang.record_part_incomplete, [incompleteCount]),
  //                     okText: Lang.upload_other_complete_data,
  //                     cancelText: Lang.view_error_data,
  //                     okRed: true,
  //                     cancelWidth: 0.35.sw,
  //                     okWidth: 0.45.sw,
  //                     okCallBack: () {
  //                       Navigator.pop(context);
  //                       addToUploadList();
  //                     },
  //                     cancelCallback: () {
  //                       logger("waitScrollController.animateTo ready");
  //                       for (int i = recordList.length - 1; i >= 0; i--) {
  //                         if (!recordList[i].hasAllRequest() || recordList[i].repeatSn) {
  //                           logger("waitScrollController.animateTo $i ${recordList[i].recordName}");
  //                           try {
  //                             _waitScrollController.animateTo(
  //                               128.sp * (recordList.length - 1 - i) - 8.sp,
  //                               duration: const Duration(milliseconds: 300),
  //                               curve: Curves.easeInOut,
  //                             );
  //                           } catch (ex) {
  //                             //
  //                           }
  //                           return;
  //                         }
  //                       }
  //                     },
  //                     isBringXBtn: true,
  //                   );
  //                   return;
  //                 }
  //                 addToUploadList();
  //                 Navigator.of(context).pop();
  //               },
  //               text: sprintf(Lang.upload_all, [recordList.length]),
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  addSingleRecordUpload(ScanInfo record) async {
    bool isHaveNetWork = await HHttp.checkInternetValid();
    if (!isHaveNetWork) {
      Global.toast(Lang.no_network_upload);
      return;
    }
    setState(() async {
      if (record.hasAllRequest()) {
        record.status = RecordStatus.uploading;
        saveRecordToDb(widget.scanActivity.campaignId, record);
        logger("startUploadScanRecords 222");
        startUploadScanRecords(widget.scanActivity);
      }
      // addAllRecordList(widget.scanActivity.caseId, uploadList);
    });
  }

  addToUploadList() async {
    bool isHaveNetWork = await HHttp.checkInternetValid();
    if (!isHaveNetWork) {
      Global.toast(Lang.no_network_upload);
      return;
    }
    // User.instance.refreshToken(
    //   okCallback: () {
    await getUploadList();
    for (ScanInfo record in uploadList) {
      if (record.status == RecordStatus.waitingUpload || record.status == RecordStatus.recording) {
        record.status = record.hasAllRequest() ? RecordStatus.waitingUpload : RecordStatus.recording;
        if (record.status == RecordStatus.waitingUpload && !record.repeatSn) {
          record.status = RecordStatus.uploading;
        }
        saveRecordToDb(widget.scanActivity.campaignId, record);
      }
    }
    logger("startUploadScanRecords 333");
    startUploadScanRecords(widget.scanActivity);
  }

  Widget _getRecordItem(ScanInfo record, bool editable) {
    if (isEmpty(record.smilePhoto) && isNotEmpty(smileMap[record.recordId])) {
      record.smilePhoto = smileMap[record.recordId]!;
    }
    Widget item = ScanRecordItem(
      widget.scanActivity,
      record,
      selectMode: selectMode,
      inSelect: selectList.contains(record),
      key: UniqueKey(),
    );
    if (selectMode) {
      return GestureDetector(
        onTap: () {
          setState(() {
            if (selectList.contains(record)) {
              selectList.remove(record);
            } else {
              selectList.add(record);
            }
          });
        },
        child: item,
      );
    } else {
      return item;
    }
  }

  @override
  void onRouteResume(Route nextRoute) {
    super.onRouteResume(nextRoute);
    refreshDataList(600, true);
    disconnectUltra(disconnectWifi: true);
  }

// getBottomButtons() {
//   bottomBtn(String icon, String text, onTap) => Click(
//         onTap: onTap,
//         child: Container(
//           height: 76.sp,
//           margin: EdgeInsets.only(bottom: 40.sp),
//           padding: EdgeInsets.fromLTRB(64.sp, 0.sp, 64.sp, 0.sp),
//           decoration: BoxDecoration(
//             color: colorBrand,
//             borderRadius: BorderRadius.circular(40.sp),
//             boxShadow: [
//               BoxShadow(
//                 color: colorBrand,
//                 blurRadius: 1.sp,
//                 spreadRadius: 1.sp,
//               ),
//             ],
//           ),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Image.asset(icon, width: 32.sp),
//               SizedBox(width: 8.sp),
//               MyText(text, Colors.white, 24.sp, FontWeight.w500),
//             ],
//           ),
//         ),
//       );
//   createBtn() => bottomBtn(
//         "res/icons/icon_add.png",
//         Lang.create_new_scan,
//         () {
//           if (uploadList.length >= MAX_SCAN_SIZE) {
//             // Global.showBottomModal(
//             //   context,
//             //   getWaitingUploadDialog(),
//             //   maxHeight: 0.8.sh,
//             // );
//             Global.toast(Lang.upload_records_first);
//           } else {
//             push(
//               ScanInfoRecord(
//                 activity: widget.scanActivity,
//                 recordData: const {},
//               ),
//             );
//           }
//         },
//       );
//
//   if (isNotEmpty(showList)) {
//     if (showMode == 1 && uploadList.where((element) => element.status == RecordStatus.waitingUpload).isNotEmpty) {
//       return Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           bottomBtn(
//             "res/icons/icon_upload.png",
//             Lang.upload_all,
//             () {
//               int incompleteCount =
//                   uploadList.where((record) => (record.status == RecordStatus.recording || record.repeatSn)).length;
//               if (incompleteCount == uploadList.length) {
//                 Global.showAlertDialog(
//                   Lang.attention,
//                   sprintf(Lang.record_all_incomplete, [incompleteCount]),
//                   okText: Lang.i_knew,
//                   isHideCancelBtn: true,
//                 );
//                 return;
//               }
//               if (incompleteCount > 0) {
//                 Global.showAlertDialog(
//                   Lang.attention,
//                   sprintf(Lang.record_part_incomplete, [incompleteCount]),
//                   okText: Lang.upload_other_complete_data,
//                   cancelText: Lang.cancel,
//                   okColor: colorRed,
//                   okCallBack: () {
//                     addToUploadList();
//                   },
//                   isBringXBtn: true,
//                 );
//                 return;
//               }
//               addToUploadList();
//             },
//           ),
//           SizedBox(width: 24.sp),
//           createBtn(),
//         ],
//       );
//     } else if (showMode == 4) {
//       return Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           bottomBtn(
//             "res/icons/icon_retry.png",
//             Lang.retry_upload_all,
//             () {
//               addToUploadList();
//             },
//           ),
//           SizedBox(width: 24.sp),
//           createBtn(),
//         ],
//       );
//     }
//   }
//
//   return createBtn();
// }
}
