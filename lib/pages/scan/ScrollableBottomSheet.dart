import 'package:flutter/material.dart';

class ScrollableBottomSheet {

  BuildContext context;
  Widget child;

  ScrollController controller = ScrollController();
  double lastPositionY = 0;
  double lastScroll = 0;
  double height;
  double borderRadius;

  ScrollableBottomSheet({
    required this.context,
    required this.child,
    this.height = 0,
    this.borderRadius = 0,
  });

  static final Finalizer<ScrollController> _finalizer = Finalizer((controller) => controller.dispose());

  void dispose() {
    controller.dispose();
  }

  void show() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(borderRadius),
        ),
      ),
      builder: (BuildContext context) {
        return SizedBox(
            height: height,
            child: Listener(
                onPointerDown: (PointerDownEvent event) {
                  lastPositionY = event.position.dy;
                  lastScroll = controller.offset;
                },
                onPointerMove: (PointerMoveEvent event) {
                  if(lastScroll == 0.0 && event.position.dy > lastPositionY) {
                    Navigator.pop(context);
                  }
                },
                child: SingleChildScrollView(
                  controller: controller,
                  child: child,
                ),
            ),
        );
      },
    );
  }

}