import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/mooeli/mooeli_record_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/scan_record_item_view.dart';
import 'package:mooeli/pages/web/web_report_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_grid.dart';

class RecordSearchPage extends BasePage {
  ScanActivity scanActivity;

  RecordSearchPage(this.scanActivity, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return RecordSearchPageState();
  }
}

class RecordSearchPageState extends BasePageState<RecordSearchPage> {
  List<ScanInfo> uploadList = [];
  List<ScanInfo> recordList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;

  Timer? timer;
  TextEditingController keywordController = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    delay(200, () {
      showKeyboard(focusNode);
    });
  }

  refreshLingyaList() async {
    return await getSearchList(true);
  }

  onLoadMore() {
    getSearchList(false);
  }

  getSearchList(bool refresh) async {
    focusNode.unfocus();
    if (isLoading || !refresh && allLoad) {
      return;
    }
    List filters = widget.scanActivity.singleCampaign
        ? [
            {
              "columnName": "singleCampaign",
              "columnValue": widget.scanActivity.singleCampaign ? "true" : "false",
              "filterEnum": "eq",
            }
          ]
        : [
            {
              "columnName": "campaignId",
              "columnValue": widget.scanActivity.campaignId,
              "filterEnum": "eq",
            }
          ];

    addFilterParam(name, operator, value) {
      filters.add({
        "columnName": name,
        "filterEnum": operator,
        "columnValue": value,
      });
    }

    if (isNotEmpty(keywordController.text)) {
      addFilterParam("index", "like", keywordController.text);
    } else {
      setState(() {
        pageIndex = 0;
        isLoading = false;
        recordList.clear();
      });
      return;
    }
    uploadList = await getSearchRecordList(
        widget.scanActivity.campaignId, keywordController.text, widget.scanActivity.singleCampaign);
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    Map<String, dynamic> params = {};
    params = {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "filterParamList": filters,
    };
    HHttp.request(
      "/v3/quickscan/scanRecord/page",
      "POST",
      (data) {
        List<ScanInfo> list = (data["data"] as List).map((i) => ScanInfo.fromJson(i, true)).toList();
        if (isNotEmpty(list)) {
          List<String> ids = [];
          for (var item in list) {
            if (isNotEmpty(item.smileFileId)) {
              ids.add(item.smileFileId);
            }
          }
          downloadThumbnail(
            ids,
            (id, path) {
              for (var item in list) {
                if (item.smileFileId == id) {
                  setState(() {
                    item.smilePhoto = path;
                  });
                }
              }
            },
            "RECORD_QUICKSCAN",
          );
        }

        setState(() {
          allLoad = true;
          if (refresh) {
            recordList = list;
            if (recordList.length >= pageSize) {
              allLoad = false;
            }
          } else {
            if (isNotEmpty(list)) {
              if (list.length >= pageSize) {
                allLoad = false;
              }
              recordList.addAll(list);
            }
          }
          isLoading = false;
        });
      },
      errCallBack: (_) {
        setState(() {
          isLoading = false;
        });
      },
      params: params,
    );
  }

  getItemView(ScanInfo record) {
    return GestureDetector(
      onTap: () {
        MooeliItem item = MooeliItem();
        item.caseId = record.campaignId;
        item.caseName = record.recordName;
        push(MooeliRecordPage(item, scanInfo: record, recordId: record.recordId, singleRecord: true));
      },
      child: Container(
        width: 335.sp,
        height: 158.sp,
        margin: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 16.sp),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 1.sp,
              spreadRadius: 1.sp,
            ),
          ],
        ),
        child: Column(
          children: [
            getScanInfoItem(record, false, true, keywordController.text),
            Container(width: 335.sp, height: 1.sp, color: colorE1),
            Row(
              children: [
                Container(
                  width: 167.sp,
                  height: 45.sp,
                  color: Colors.transparent,
                  alignment: Alignment.center,
                  child: MyText(Lang.view_detail, colorBrand, 14.sp),
                ),
                Container(width: 1.sp, height: 45.sp, color: colorE1),
                Click(
                  ms: 5000,
                  child: Container(
                    width: 167.sp,
                    height: 45.sp,
                    color: Colors.transparent,
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        MyText(Lang.make_report, colorBrand, 14.sp),
                        SizedBox(width: 2.sp),
                        Image.asset("res/icons/icon_beta.png", height: 12.sp),
                      ],
                    ),
                  ),
                  onTap: () async {
                    User.instance.refreshToken(okCallback: () {
                      push(WebReportPage(record));
                    });
                  },
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  getListView() {
    List<ScanInfo> list = [];
    if (isNotEmpty(uploadList)) {
      list.addAll(uploadList);
    }
    if (isNotEmpty(recordList)) {
      list.addAll(recordList);
    }
    return list.map((item) => ScanRecordItem(item.getScanActivity(), item, key: UniqueKey())).toList();
  }

// 下拉刷新
  Future onRefresh() async {
    await refreshLingyaList();
  }

  delaySearch() {
    if (timer != null) {
      timer!.cancel();
    }
    timer = Timer(const Duration(milliseconds: 800), () {
      refreshLingyaList();
    });
  }

  @override
  void dispose() {
    if (timer != null) {
      timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
        contentHeight: 72.sp,
        child: SafeArea(
          child: Container(
            margin: EdgeInsets.fromLTRB(20.sp, 16.sp, 20.sp, 0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Click(
                        child: Container(
                          width: 164.sp,
                          height: 56.sp,
                          padding: EdgeInsets.all(8.sp),
                          decoration: BoxDecoration(color: colorBlueDeep, borderRadius: BorderRadius.circular(8.sp)),
                          child: Row(
                            children: [
                              Image.asset(
                                "res/icons/icon_back_white.png",
                                height: 32.sp,
                              ),
                              SizedBox(width: 4.sp),
                              MyText(Lang.back, Colors.white, 24.sp)
                            ],
                          ),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                        }),
                    Container(
                      width: 920.sp,
                      height: 56.sp,
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: focusNode.hasFocus || isNotEmpty(keywordController.text) ? colorSearchBorder : colorBg,
                        ),
                        borderRadius: BorderRadius.circular(30.sp),
                      ),
                      child: Row(
                        children: [
                          Image.asset("res/icons/icon_activity_search.png", width: 30.sp),
                          SizedBox(width: 12.sp),
                          Expanded(
                            child: TextField(
                              controller: keywordController,
                              focusNode: focusNode,
                              textAlign: TextAlign.start,
                              textAlignVertical: TextAlignVertical.center,
                              maxLength: 20,
                              style: TextStyle(fontSize: 24.sp, color: color2B),
                              onChanged: (value) {
                                delaySearch();
                                setState(() {});
                              },
                              decoration: InputDecoration(
                                /*focusedBorder: OutlineInputBorder(
                                  borderSide: BorderSide(color: colorSearchBorder)
                                ),*/
                                prefix: SizedBox(
                                  width: 10.sp,
                                ),

                                hintText: Lang.input_record_name,
                                //sprintf(Lang.search_record_hint, [widget.scanActivity.campaignName]),
                                hintStyle: TextStyle(fontSize: 24.sp, color: colorB8),
                                counterText: '',
                                border: InputBorder.none,
                                isDense: true,
                              ),
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                keywordController.clear();
                              });
                            },
                            child: isNotEmpty(keywordController.text)
                                ? Image.asset("res/icons/icon_close_one.png", width: 30.sp)
                                : SizedBox(),
                          )
                        ],
                      ),
                    ),
                    SizedBox(width: 164.sp, height: 56.sp)
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: Container(
          width: 1.sw,
          height: 1.sh,
          color: Colors.transparent,
          child: Stack(
            children: [
              Click(
                  onTap: hideKeyboard,
                  child: SizedBox(
                    width: 1.sw,
                    height: 1.sh - 100.sp,
                  )),
              isEmpty(keywordController.text)
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.all(24.sp),
                      child: RefreshGrid(
                        childList: getListView(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 5,
                          childAspectRatio: 344.0 / 422,
                          crossAxisSpacing: 16.sp,
                          mainAxisSpacing: 16.sp,
                        ),
                        isLoading: isLoading,
                        onRefresh: onRefresh,
                        onLoadMore: onLoadMore,
                        nullImgName: "res/imgs/empty_pic.png",
                        nullWidget: getEmptyText(),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getEmptyText() {
    int index = Lang.cannot_find_record.indexOf("__");
    String str1 = Lang.cannot_find_record.substring(0, index);
    String str2 = Lang.cannot_find_record.substring(index + 2);
    return RichText(
      text: TextSpan(
        text: str1,
        style: TextStyle(
          fontSize: 24.sp,
          color: colorAB,
        ),
        children: [
          TextSpan(
            text: keywordController.text,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorRed,
            ),
          ),
          TextSpan(
            text: str2,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorAB,
            ),
          ),
        ],
      ),
    );
  }
}
