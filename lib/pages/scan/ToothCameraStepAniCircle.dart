import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ToothCameraStepAniCircle extends StatefulWidget {
  final double topSpace;
  final double leftSpace;

  const ToothCameraStepAniCircle({Key? key, required this.topSpace,required this.leftSpace})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _ToothCameraStepAniCircleState();
}

class _ToothCameraStepAniCircleState extends State<ToothCameraStepAniCircle>
    with TickerProviderStateMixin {
  late Animation<double> aniScale;
  late AnimationController aniScaleControll;

  late Animation<double> aniOpacity;
  late AnimationController aniOpacityControll;

  late Animation<double> aniSmall;
  late AnimationController aniSmallControll;

  @override
  initState() {
    int aniTimerMillSec = 1600;

    super.initState();
    aniScaleControll = AnimationController(
        duration: Duration(milliseconds: aniTimerMillSec), vsync: this);
    aniScale =
        CurvedAnimation(parent: aniScaleControll, curve: Curves.bounceInOut);
    aniScale = TweenSequence([
      //这是一个动画序列，weight表示权重
      TweenSequenceItem(
          tween: Tween(begin: 15.sp, end: 38.sp)
              .chain(CurveTween(curve: Curves.easeIn)),
          weight: 1),
    ]).animate(aniScaleControll)
      ..addListener(() {
        setState(() {});
      });


    aniOpacityControll = AnimationController(
        duration: Duration(milliseconds: aniTimerMillSec), vsync: this);
    aniOpacity =
        CurvedAnimation(parent: aniOpacityControll, curve: Curves.bounceInOut);
    aniOpacity = TweenSequence([
      //这是一个动画序列，weight表示权重
      TweenSequenceItem(
          tween: Tween(begin: 1.0, end: 0.85)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 1),
      TweenSequenceItem(
          tween: Tween(begin: 0.85, end: 0.85)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 1),
      TweenSequenceItem(
          tween: Tween(begin: 0.85, end: 0.0)
              .chain(CurveTween(curve: Curves.linear)),
          weight: 1),
    ]).animate(aniOpacityControll)
      ..addListener(() {
        setState(() {});
      });

    aniSmallControll = AnimationController(
        duration: Duration(milliseconds: aniTimerMillSec), vsync: this);
    aniSmall =
        CurvedAnimation(parent: aniSmallControll, curve: Curves.bounceInOut);
    aniSmall = TweenSequence([
      //这是一个动画序列，weight表示权重
      TweenSequenceItem(
          tween: Tween(begin: 12.sp, end: 15.sp)
              .chain(CurveTween(curve: Curves.easeIn)),
          weight: 2),
      TweenSequenceItem(
          tween: Tween(begin: 15.sp, end: 12.sp)
              .chain(CurveTween(curve: Curves.easeIn)),
          weight: 2),

    ]).animate(aniSmallControll)
      ..addListener(() {
        setState(() {});
      });

    playAnimate();
  }

  playAnimate() {
    aniScaleControll.reset();
    aniScaleControll.repeat();

    aniOpacityControll.reset();
    aniOpacityControll.repeat();

    aniSmallControll.reset();
    aniSmallControll.repeat();
  }

  @override
  dispose() {
    aniScaleControll.dispose();
    aniOpacityControll.dispose();
    aniSmallControll.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        playAnimate();
      },
      child: Container(
          margin: EdgeInsets.only(top: widget.topSpace,left:widget.leftSpace),
          width: 38.sp,
          height: 38.sp,
          child: Stack(children: [
            Align(
                alignment: Alignment.center,
                child: Container(
                    width: aniScale.value,
                    height: aniScale.value,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(aniOpacity.value),
                      borderRadius: BorderRadius.circular(aniScale.value),
                    ))),
            Align(
                alignment: Alignment.center,
                child: Container(
                  width: aniSmall.value,
                  height: aniSmall.value,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(aniSmall.value),
                  ),
                )),
          ])),
    );
  }
}
