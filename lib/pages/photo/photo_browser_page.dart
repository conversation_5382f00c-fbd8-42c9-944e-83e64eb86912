import 'dart:convert';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class FadeRoute extends PageRouteBuilder {
  final Widget page;

  FadeRoute({required this.page})
      : super(
          pageBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
          ) =>
              page,
          transitionsBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
            Widget child,
          ) =>
              FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
}

class PhotoViewSimpleScreen extends StatelessWidget {
  PhotoViewSimpleScreen({
    super.key,
    required this.image,
    required this.imageType,
    this.minScale, //最大缩放倍数
    this.maxScale, //最小缩放倍数
    this.heroTag, //hero动画tagid
  });

  String image;
  int imageType = 0; //0-url, 1-assets, 2-file, 3-base64
  late dynamic minScale;
  late dynamic maxScale;
  late String? heroTag;

  ImageProvider getImage() {
    switch (imageType) {
      case 0:
        return CachedNetworkImageProvider(image);
      case 1:
        return AssetImage(image);
      case 2:
        return FileImage(File(image));
      case 3:
        return MemoryImage(base64Decode(image));
    }
    return CachedNetworkImageProvider(image);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        constraints: BoxConstraints.expand(
          height: MediaQuery.of(context).size.height,
        ),
        child: Stack(
          children: <Widget>[
            Positioned(
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              child: InkWell(
                child: PhotoView(
                  imageProvider: getImage(),
                  minScale: minScale,
                  maxScale: maxScale,
                  heroAttributes: PhotoViewHeroAttributes(tag: heroTag ?? ""),
                  enableRotation: true,
                ),
                onTap: () => Navigator.of(context).pop(),
              ),
            ),
            Positioned(
              //右上角关闭按钮
              right: 10,
              top: MediaQuery.of(context).padding.top,
              child: IconButton(
                icon: Icon(
                  Icons.close,
                  size: 30.sp,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}

class PhotoViewGalleryScreen extends StatefulWidget {
  List images = [];
  List<String>? texts;

  int index = 0;
  String? heroTag;
  PageController? controller;
  int imageType = 0; //0-url, 1-assets, 2-file

  PhotoViewGalleryScreen({
    required this.images,
    this.index = 0,
    this.controller,
    required this.imageType,
    this.texts,
    this.heroTag = "",
    Key? key,
  }) : super(key: key) {
    controller = controller ?? PageController(initialPage: index);
  }

  @override
  _PhotoViewGalleryScreenState createState() => _PhotoViewGalleryScreenState();
}

class _PhotoViewGalleryScreenState extends State<PhotoViewGalleryScreen> {
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    currentIndex = widget.index;
  }

  @override
  void dispose() {
    super.dispose();
  }

  ImageProvider getImage(index) {
    switch (widget.imageType) {
      case 0:
        return CachedNetworkImageProvider(widget.images[index]);
      case 1:
        return AssetImage(widget.images[index]);
      case 2:
        return FileImage(File(widget.images[index]));
    }
    return CachedNetworkImageProvider(widget.images[index]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black38,
      body: Stack(
        children: <Widget>[
          Positioned(
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            child: InkWell(
              child: PhotoViewGallery.builder(
                scrollPhysics: const BouncingScrollPhysics(),
                builder: (BuildContext context, int index) {
                  return PhotoViewGalleryPageOptions(
                    imageProvider: getImage(index),
                    heroAttributes: isNotEmpty(widget.heroTag) ? PhotoViewHeroAttributes(tag: widget.heroTag!) : null,
                  );
                },
                itemCount: widget.images.length,
                backgroundDecoration: null,
                pageController: widget.controller,
                enableRotation: true,
                onPageChanged: (index) {
                  setState(() {
                    currentIndex = index;
                  });
                },
                loadingBuilder: (context, progress) => Center(
                  child: SizedBox(
                    width: 40.sp,
                    height: 40.sp,
                    child: CircularProgressIndicator(
                      strokeWidth: 4.sp,
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
              ),
              onTap: () => Navigator.of(context).pop(),
            ),
          ),
          widget.images.length > 1
              ? Positioned(
                  //图片index显示
                  top: MediaQuery.of(context).padding.top + 15.sp,
                  width: MediaQuery.of(context).size.width,
                  child: Center(child: MyText("${currentIndex + 1}/${widget.images.length}", Colors.white, 16.sp)),
                )
              : const SizedBox(),
          Positioned(
            //图片index显示
            top: MediaQuery.of(context).padding.top + 60.sp,
            width: MediaQuery.of(context).size.width,
            child: widget.texts != null && widget.texts!.length == widget.images.length
                ? Center(
                    child: MyText(widget.texts![currentIndex], Colors.white, 16.sp),
                  )
                : const SizedBox(),
          ),
          Positioned(
            //右上角关闭按钮
            right: 10,
            top: MediaQuery.of(context).padding.top,
            child: IconButton(
              icon: Icon(
                Icons.close,
                size: 30.sp,
                color: Colors.white,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      ),
    );
  }
}
