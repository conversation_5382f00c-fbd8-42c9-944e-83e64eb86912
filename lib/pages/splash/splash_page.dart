import 'dart:async';
import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/main_tabs.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class SplashScreen extends BasePage {
  @override
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends BasePageState<SplashScreen> {
  late Timer _timer;
  int counter = 4;
  String imageUrl = "";

  toMainTab() {
    Future.delayed(const Duration(milliseconds: 20), () {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => MainTabs(key: Global.mainTabGlobalKey)),
        (route) => false,
      );
    });
  }

  @override
  void initState() {
    super.initState();
    setScreenVertiacl();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (mounted) {
        setState(() {
          counter--;
        });
        if (counter <= 0) {
          toMainTab();
        }
      }
    });
    try {
      Map configJson = getStaticConfigJson();
      int splashIndex = Global.sharedPrefs.getInt("splash_index") ?? 0;
      dynamic images = configJson["splash"]["images"];
      if (images.length > 0) {
        if (splashIndex >= images.length) {
          splashIndex = 0;
        }
        setState(() {
          imageUrl = images[splashIndex];
        });
        Global.sharedPrefs.setInt("splash_index", splashIndex + 1);
      } else {
        toMainTab();
      }
    } catch (ex) {
      toMainTab();
    }
  }

  @override
  void dispose() {
    super.dispose();
    logger("splash dispose");
    _timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black12,
      body: Stack(
        children: [
          Image(
            fit: BoxFit.cover,
            width: 1.sw,
            height: 1.sh,
            image: CachedNetworkImageProvider(imageUrl),
          ),
          Align(
            alignment: Alignment.topRight,
            child: GestureDetector(
              onTap: toMainTab,
              child: Container(
                margin: EdgeInsets.fromLTRB(0, 60.sp, 24.sp, 0),
                padding: EdgeInsets.all(8.sp),
                decoration: BoxDecoration(
                  color: Colors.black38,
                  border: Border.all(color: Colors.white),
                  borderRadius: BorderRadius.circular(25.sp),
                ),
                child: MyText("${counter}s ${Lang.skip}", Colors.white, 14.sp),
              ),
            ),
          )
        ],
      ),
    );
  }
}
