import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:orientation/orientation.dart';
import 'package:route_life/route_life.dart';

import 'photo/photo_browser_page.dart';

abstract class BasePage extends StatefulWidget {
  const BasePage({Key? key}) : super(key: key);
}

abstract class BasePageState<T extends BasePage> extends State<T> with RouteLifeMixin {
  T get widget => super.widget;

  List listenerList = [];

  bool isDisposed = false;

  bool isPaused = false;

  @override
  void initState() {
    // logger("initState $this");
    super.initState();
    addEventListener<EventLanguage>((event) {
      onLanguageChanged();
      setState(() {});
    });
  }

  onLanguageChanged() {}

  setScreenHorizontal() {
    logger("setScreenHorizontal $this");
    if (Platform.isAndroid) {
      OrientationPlugin.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    } else {
      OrientationPlugin.setPreferredOrientations([DeviceOrientation.landscapeRight]).then((value) {
        OrientationPlugin.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
      });
    }
  }

  setScreenVertiacl() {
    logger("setScreenVertiacl $this");
    // OrientationPlugin.setPreferredOrientations([DeviceOrientation.portraitUp]);
    // OrientationPlugin.forceOrientation(DeviceOrientation.portraitUp);
    setScreenHorizontal();
  }

  pushNamed(String name, arguments) {
    Global.globalNavigator.currentState?.pushNamed(name, arguments: arguments);
  }

  push(BasePage page) {
    int time = DateTime.now().millisecondsSinceEpoch;
    if (time - Global.checkVersionTime > 300000) {
      Global.checkNewestVersion();
    }
    Global.globalNavigator.currentState?.push(MaterialPageRoute(builder: (context) => page));
  }

  replace(BasePage page) {
    Global.globalNavigator.currentState?.pushReplacement(MaterialPageRoute(builder: (context) => page));
  }

  pop() {
    Navigator.of(context).pop();
  }

  hideKeyboard() {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  showKeyboard(FocusNode focusNode) {
    delay(300, () => FocusScope.of(context).requestFocus(focusNode));
  }

  openFadePage(page) {
    Global.globalNavigator.currentState?.push(FadeRoute(page: page));
  }

  viewLingyaImage(LingyaFile file) {
    openFadePage(PhotoViewSimpleScreen(
      image: file.imagePath,
      heroTag: file.fileId,
      imageType: 2,
    ));
  }

  Widget getOriginPhoto(String displayImage, LingyaFile file) {
    if (displayImage.isNotEmpty) {
      return GestureDetector(
        onTap: () {
          openFadePage(PhotoViewSimpleScreen(
            image: displayImage,
            heroTag: file.fileId,
            imageType: 3,
          ));
        },
        child: Hero(
          tag: file.fileId,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.sp),
            child: Image.memory(
              base64Decode(displayImage),
              gaplessPlayback: true,
              width: 335.sp,
              height: 200.sp,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );
    } else {
      return getDetailImage(file, () => viewLingyaImage(file));
    }
  }

  viewLingyaImages({required List<LingyaFile> fileList, int index = 0}) {
    List images = [];
    List<String> texts = [];
    for (LingyaFile file in fileList) {
      images.add(file.imagePath);
      texts.add(categoryMap[file.category]!);
    }
    openFadePage(PhotoViewGalleryScreen(
      images: images,
      texts: texts,
      index: index,
      heroTag: fileList[index].fileId,
      imageType: 2,
    ));
  }

  viewMooeliImage(MooeliFile file) {
    openFadePage(PhotoViewSimpleScreen(
      image: file.imagePath,
      heroTag: file.id,
      imageType: 2,
    ));
  }

  viewMooeliImages({required List<MooeliFile> fileList, int index = 0}) {
    List images = [];
    List<String> texts = [];
    for (MooeliFile file in fileList) {
      images.add(file.imagePath);
      texts.add(categoryMap[file.attribute]!);
    }
    openFadePage(PhotoViewGalleryScreen(
      images: images,
      texts: texts,
      index: index,
      heroTag: fileList[index].id,
      imageType: 2,
    ));
  }

  addEventListener<K>(Function(K result) onEvent) {
    StreamSubscription subscription = eventBus.on<K>().listen(onEvent);
    listenerList.add(subscription);
  }

  @override
  setState(VoidCallback fn) {
    if (mounted && !isDisposed) {
      try {
        super.setState(fn);
      } catch (ex) {}
    }
  }

  delay(int ms, Function func) {
    Future.delayed(Duration(milliseconds: ms), () {
      if (mounted && !isDisposed) {
        func();
      }
    });
  }

  @override
  void dispose() {
    isDisposed = true;
    for (var listener in listenerList) {
      if (listener != null) {
        listener.cancel();
      }
    }
    super.dispose();
  }

  @override
  void onRoutePause(Route nextRoute) {
    isPaused = true;
  }

  @override
  void onRouteResume(Route nextRoute) {
    isPaused = false;
  }
}
