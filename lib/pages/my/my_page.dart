import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/model/user_info.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/my/language_setting.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/android_method.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';
import 'package:path_provider/path_provider.dart';

import '../web/webview_page.dart';

class MyPage extends BasePage {
  const MyPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MyPageState();
  }
}

class MyPageState extends BasePageState<MyPage> {
  List announceList = [];
  String cacheSize = "0M";
  bool showAbout = false;
  int clickTitle = 0;
  int type = 0;
  double brightness = 0;
  double volume = 0;

  LingyaTenant selectTenant = LingyaTenant();
  List<LingyaTenant> tenantList = [];

  String deviceSn = "";

  @override
  void initState() {
    logger("screen size: ${1.sw} ${1.sh}  ${1920 / 1.sw * 1.sh}");
    super.initState();
    addEventListener<EventRefreshUserInfo>((event) {
      setState(() {});
    });
    addEventListener<EventMainTab>((event) {
      if (event.tab == 1) {
        getCacheSize();
      }
    });
    addEventListener<EventSwitchTenant>((event) {
      getUserInfo();
    });
    callMcByFunName("getBrightness").then((value) {
      logger("brightness: $value");
      brightness = value;
    });
    callMcByFunName("getVolume").then((value) {
      logger("volume: $value");
      volume = value;
    });
    getUserInfo();
    getCacheSize();
    getTenantList();
  }

  getCacheSize() async {
    try {
      // 获取缓存路径
      final Directory tempDir = await getTemporaryDirectory();

      // 遍历缓存路径下的所有文件,计算总大小
      int totalSize = 0;
      await for (final FileSystemEntity entity in tempDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }

      setState(() {
        if (totalSize == 0) {
          cacheSize = "0M";
        } else if (totalSize < 1024 * 1000) {
          cacheSize = "${(totalSize / 1024.0).toStringAsFixed(2)} K";
        } else if (totalSize < 1024 * 1024 * 1000) {
          cacheSize = "${(totalSize / 1024.0 / 1024).toStringAsFixed(2)} M";
        } else {
          cacheSize = "${(totalSize / 1024.0 / 1024 / 1024).toStringAsFixed(2)} G";
        }
      });
    } catch (e) {
      //
    }

    deviceSn = await getDeviceSn();
    setState(() {});
  }

  Future<void> clearCache() async {
    try {
      if (isNotEmpty(cacheSize)) {
        final Directory tempDir = await getTemporaryDirectory();
        await for (final FileSystemEntity entity in tempDir.list(recursive: true)) {
          if (entity is File) {
            await entity.delete();
          }
        }

        setState(() {
          cacheSize = "0M";
        });
      }

      toast(Lang.clear_cache_finish);
    } catch (e) {
      //
    }
  }

  getUserInfo() {
    HHttp.request("/v3/provider/getProviderUserInfo", "POST", (data) {
      setState(() {
        String accessToken = currentUser.accessToken;
        String refreshToken = currentUser.refreshToken;
        currentUser = UserInfo.fromJson(data);
        currentUser.accessToken = accessToken;
        currentUser.refreshToken = refreshToken;
        logger("get UserInfo refreshToken: ${currentUser.refreshToken}");
        Global.sharedPrefs.setString("login_user", jsonEncode(currentUser.toJson()));
        HHttp.setToken(accessToken);
        eventBus.fire(EventRelogin());
      });
    });
  }

  void getTenantList([bool refreshByUser = false]) {
    //根据用户当前tenant刷新
    HHttp.request("/v3/tenant/list", "POST", (data) {
      setState(() {
        tenantList.clear();
        try {
          tenantList = (data["tenantList"] as List)
              .map((e) => LingyaTenant.fromJson(e, true))
              .where((tenant) => tenant.isNormal())
              .toList();
        } catch (ex) {
          //
        }
        // tenantList.add(LingyaTenant.fromJson(data["personInfo"], false));
        logger("TenantLog getTenantList: ${tenantList.map((e) => "${e.id}_${e.name}").toList()}");
        logger("TenantLog currentUser.tenantId: ${currentUser.providerId}");
        if (refreshByUser) {
          for (LingyaTenant t in tenantList) {
            if (t.id == currentUser.providerId) {
              selectTenant = t;
              logger("TenantLog select0: ${selectTenant.id} ${selectTenant.name}");
            }
          }
          if (isEmpty(selectTenant.id)) {
            LingyaTenant tenant = tenantList.first;
            HHttp.request(
              "/v3/tenant/switch",
              "POST",
              (data) {
                setState(() {
                  selectTenant = tenant;
                });
                saveCurrentToken(data);
                logger("TenantLog select: ${selectTenant.id} ${selectTenant.name}");
              },
              params: {
                "id": tenant.isTenant ? tenant.id : "0",
              },
            );
          }
          logger("TenantLog selectTenant: ${selectTenant.id} ${selectTenant.name}");
        }
      });
    });
  }

  _getDrawerView() {
    return Container(
      width: 662.sp,
      height: 1.sh,
      color: colorFB,
      padding: EdgeInsets.fromLTRB(48.sp, 80.sp, 48.sp, 48.sp),
      child: SingleChildScrollView(
        child: Column(
          children: tenantList.map((e) => _getTenantItem(e)).toList(),
        ),
      ),
    );
  }

  Widget _getTenantItem(LingyaTenant tenant) {
    bool isSelect = tenant.id == currentUser.providerId;
    return GestureDetector(
      onTap: () {
        if (!isSelect) {
          logger("TenantLog switch: ${tenant.id} ${tenant.name}");
          HHttp.request(
            "/v3/tenant/switch",
            "POST",
            (data) {
              setState(() {
                selectTenant = tenant;
              });
              toast(Lang.switch_success);
              saveCurrentToken(data);
              logger("TenantLog select: ${selectTenant.id} ${selectTenant.name}");
              eventBus.fire(EventSwitchTenant(selectTenant));
              _scaffoldKey.currentState?.closeDrawer();
            },
            params: {
              "id": tenant.isTenant ? tenant.id : "0",
            },
          );
        } else {
          _scaffoldKey.currentState?.closeDrawer();
        }
      },
      child: Container(
          width: 566.sp,
          height: 126.sp,
          padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
          margin: EdgeInsets.only(bottom: 32.sp),
          alignment: Alignment.centerLeft,
          decoration: isSelect
              ? BoxDecoration(
                  // color: colorBrand,
                  borderRadius: BorderRadius.circular(8.sp),
                  border: Border.all(color: colorBrand),
                )
              : const BoxDecoration(),
          child: Row(
            children: [
              Image.asset(tenant.getTenantIcon(), width: 88.sp),
              SizedBox(width: 16.sp),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 364.sp),
                    child: SingleText(tenant.name, color2B, 32.sp, FontWeight.w500),
                  ),
                  SizedBox(height: 10.sp),
                  tenant.getTenantStatus(),
                ],
              ),
              const Spacer(),
              isSelect ? const SizedBox() : Image.asset("res/icons/icon_right_normal.png", width: 48.sp),
            ],
          )),
    );
  }

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: Container(
        decoration: BoxDecoration(
          color: colorPurpleLilac,
          image: const DecorationImage(image: AssetImage("res/imgs/my_page_content_bg.png"), fit: BoxFit.cover),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Click(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  margin: EdgeInsets.fromLTRB(24.sp, 92.sp, 0.sp, 0.sp),
                  child: Image.asset("res/icons/icon_back_blue.png", height: 56.sp),
                )),
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 60.sp, left: 24.sp),
                    padding: EdgeInsets.all(24.sp),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(16.sp),
                    ),
                    child: Row(
                      children: [
                        User.instance.getUserAvatar(72.sp),
                        SizedBox(width: 16.sp),
                        Expanded(
                            child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SingleText(currentUser.personName, color2B, 24.sp, FontWeight.w600),
                            SizedBox(height: 1.sp),
                            SingleText(currentUser.getTenantName(), colorA4, 24.sp),
                          ],
                        )),
                        Click(
                          onTap: () {
                            getTenantList();
                            if (isNotEmpty(tenantList)) {
                              _scaffoldKey.currentState?.openDrawer();
                            }
                          },
                          child: Container(
                            height: 56.sp,
                            alignment: Alignment.center,
                            padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                            decoration: BoxDecoration(
                              color: colorPurpleLavender,
                              borderRadius: BorderRadius.circular(8.sp),
                            ),
                            child: MyText(Lang.switch_tenant, colorBlueDeep, 24.sp),
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    height: 1.sh - 244.sp,
                    margin: EdgeInsets.fromLTRB(24.sp, 24.sp, 0.sp, 40.sp),
                    decoration:
                        BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Click(
                            onTap: () {
                              setState(() {
                                type = 0;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 14.sp),
                              margin: EdgeInsets.fromLTRB(0.sp, 24.sp, 0.sp, 14.sp),
                              decoration: BoxDecoration(
                                color: type == 0 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Image.asset(
                                      type == 0
                                          ? "res/icons/icon_setting_secure_select.png"
                                          : "res/imgs/icon_setting_secure.png",
                                      height: 40.sp),
                                  SizedBox(width: 16.sp),
                                  MyText(Lang.account_setting, type == 0 ? colorBlueDeep : color2B, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 0
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_black.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 14.sp),
                            margin: EdgeInsets.fromLTRB(0.sp, 10.sp, 0.sp, 2.sp),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset("res/icons/icon_setting_system.png", height: 40.sp),
                                SizedBox(width: 16.sp),
                                MyText(Lang.setting_system, color2B, 24.sp),
                              ],
                            ),
                          ),
                          // Click(
                          //   onTap: () {
                          //     setState(() {
                          //       type = 1;
                          //     });
                          //   },
                          //   child: Container(
                          //     padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                          //     decoration: BoxDecoration(
                          //       color: type == 1 ? colorPurpleLavender : null,
                          //     ),
                          //     child: Row(
                          //       crossAxisAlignment: CrossAxisAlignment.center,
                          //       children: [
                          //         MyText(Lang.mobile_wlan, type == 1 ? colorBlueDeep : color7C, 24.sp),
                          //         const Spacer(),
                          //         Image.asset(
                          //             type == 1
                          //                 ? "res/icons/icon_arrow_right_blue.png"
                          //                 : "res/icons/icon_arrow_right_gray.png",
                          //             height: 40.sp),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                          // Click(
                          //   onTap: () {
                          //     setState(() {
                          //       type = 2;
                          //     });
                          //   },
                          //   child: Container(
                          //     padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                          //     decoration: BoxDecoration(
                          //       color: type == 2 ? colorPurpleLavender : null,
                          //     ),
                          //     child: Row(
                          //       crossAxisAlignment: CrossAxisAlignment.center,
                          //       children: [
                          //         MyText(Lang.wlan, type == 2 ? colorBlueDeep : color7C, 24.sp),
                          //         const Spacer(),
                          //         Image.asset(
                          //             type == 2
                          //                 ? "res/icons/icon_arrow_right_blue.png"
                          //                 : "res/icons/icon_arrow_right_gray.png",
                          //             height: 40.sp),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 3;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 3 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.screen_light, type == 3 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 3
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 11;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 11 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.auto_lock_screen, type == 11 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 3
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 4;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 4 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.system_audio, type == 4 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 4
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 14.sp),
                            margin: EdgeInsets.fromLTRB(0.sp, 10.sp, 0.sp, 2.sp),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset("res/icons/my_setup.png", height: 40.sp),
                                SizedBox(width: 16.sp),
                                MyText(Lang.general_setting, color2B, 24.sp),
                              ],
                            ),
                          ),
                          // Click(
                          //   onTap: () {
                          //     setState(() {
                          //       type = 5;
                          //     });
                          //   },
                          //   child: Container(
                          //     padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                          //     decoration: BoxDecoration(
                          //       color: type == 5 ? colorPurpleLavender : null,
                          //     ),
                          //     child: Row(
                          //       crossAxisAlignment: CrossAxisAlignment.center,
                          //       children: [
                          //         MyText(Lang.change_language, type == 5 ? colorBlueDeep : color7C, 24.sp),
                          //         const Spacer(),
                          //         Image.asset(
                          //             type == 5
                          //                 ? "res/icons/icon_arrow_right_blue.png"
                          //                 : "res/icons/icon_arrow_right_gray.png",
                          //             height: 40.sp),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 6;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 6 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.clear_cache, type == 6 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 6
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 14.sp),
                            margin: EdgeInsets.fromLTRB(0.sp, 10.sp, 0.sp, 2.sp),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset("res/imgs/icon_setting_about.png", height: 40.sp),
                                SizedBox(width: 16.sp),
                                MyText(Lang.setting_about, color2B, 24.sp),
                              ],
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 7;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 7 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.user_agreement, type == 7 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 7
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 8;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 8 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.privacy, type == 8 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 8
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 9;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(95.sp, 18.sp, 27.sp, 18.sp),
                              decoration: BoxDecoration(
                                color: type == 9 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  MyText(Lang.version_upgrade, type == 9 ? colorBlueDeep : color7C, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 9
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_gray.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                          Click(
                            onTap: () {
                              setState(() {
                                type = 10;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(24.sp, 18.sp, 24.sp, 14.sp),
                              margin: EdgeInsets.fromLTRB(0.sp, 24.sp, 0.sp, 14.sp),
                              decoration: BoxDecoration(
                                color: type == 10 ? colorPurpleLavender : null,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Image.asset(
                                      type == 10
                                          ? "res/icons/icon_setting_message_blue.png"
                                          : "res/imgs/icon_setting_message.png",
                                      height: 40.sp),
                                  SizedBox(width: 16.sp),
                                  MyText(Lang.setting_contact, type == 10 ? colorBlueDeep : color2B, 24.sp),
                                  const Spacer(),
                                  Image.asset(
                                      type == 10
                                          ? "res/icons/icon_arrow_right_blue.png"
                                          : "res/icons/icon_arrow_right_black.png",
                                      height: 40.sp),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: getContentView(),
            ),
          ],
        ),
      ),
      drawer: _getDrawerView(),
    );
  }

  getContentView() {
    switch (type) {
      case 0:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.account_setting, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              Row(
                children: [
                  MyText("${Lang.current_account} ${Global.sharedPrefs.getString("login_account")}", color7C, 24.sp),
                  const Spacer(),
                  Click(
                    onTap: () async {
                      if (await HHttp.checkInternetValid()) {
                        Global.showAlertDialog(
                          Lang.logout_account,
                          Lang.confirm_logout,
                          contentCenter: true,
                          okText: Lang.exit,
                          okCallBack: () {
                            User.instance.clearUserData();
                          },
                        );
                      } else {
                        Global.showAlertDialog(
                          Lang.logout_account,
                          Lang.confirm_logout_offline,
                          contentCenter: true,
                          okText: Lang.exit,
                          okCallBack: () {
                            User.instance.clearUserData();
                          },
                        );
                      }
                    },
                    child: Container(
                      height: 56.sp,
                      alignment: Alignment.center,
                      padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFAF0F1),
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      child: MyText(Lang.logout_account, colorRedMuted, 24.sp),
                    ),
                  )
                ],
              ),
              SizedBox(height: 40.sp),
              MyText("设备序列号: $deviceSn", color7C, 24.sp),
            ],
          ),
        );
      case 3:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.screen_light, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              Row(
                children: [
                  MyText(Lang.current_brightness, color7C, 24.sp),
                  SizedBox(
                    width: 8.sp,
                  ),
                  Expanded(
                    flex: 1,
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                          trackHeight: 16.sp,
                          // 轨道的高度
                          thumbShape: RoundSliderThumbShape(enabledThumbRadius: 18.sp),
                          // 滑块的大小
                          overlayShape: RoundSliderOverlayShape(overlayRadius: 24.sp),
                          // 滑块点击时的扩展大小
                          activeTrackColor: colorBlueDeep,
                          // 激活部分的颜色
                          inactiveTrackColor: colorPurpleLilacGray,
                          // 未激活部分的颜色
                          thumbColor: Colors.white),
                      child: Slider(
                          value: brightness,
                          min: 0,
                          max: 1,
                          divisions: 100,
                          // 分割数
                          // label: brightness.toStringAsFixed(1), // 滑块标签
                          onChanged: (double value) {
                            setState(() {
                              brightness = value;
                            });
                            callMcByFunName("setBrightness", params: {"brightness": value});
                          }),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      case 4:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.system_audio, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              Row(
                children: [
                  MyText(Lang.current_audio, color7C, 24.sp),
                  SizedBox(
                    width: 8.sp,
                  ),
                  Expanded(
                    flex: 1,
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                          trackHeight: 16.sp,
                          // 轨道的高度
                          thumbShape: RoundSliderThumbShape(enabledThumbRadius: 18.sp),
                          // 滑块的大小
                          overlayShape: RoundSliderOverlayShape(overlayRadius: 24.sp),
                          // 滑块点击时的扩展大小
                          activeTrackColor: colorBlueDeep,
                          // 激活部分的颜色
                          inactiveTrackColor: colorPurpleLilacGray,
                          // 未激活部分的颜色
                          thumbColor: Colors.white),
                      child: Slider(
                          value: volume,
                          min: 0,
                          max: 1,
                          divisions: 100,
                          // 分割数
                          // label: brightness.toStringAsFixed(1), // 滑块标签
                          onChanged: (double value) {
                            setState(() {
                              volume = value;
                            });
                            callMcByFunName("setVolume", params: {"volume": value});
                          }),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      case 5:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.change_language, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              const LanguageSetting()
            ],
          ),
        );
      case 6:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.clear_cache, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              Row(
                children: [
                  MyText("${Lang.current_cache} $cacheSize", color7C, 24.sp),
                  const Spacer(),
                  Click(
                    onTap: clearCache,
                    child: Container(
                      height: 56.sp,
                      alignment: Alignment.center,
                      padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                      decoration: BoxDecoration(
                        color: colorPurpleLilac,
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      child: MyText(Lang.clear_cache, colorBlueDeep, 24.sp),
                    ),
                  )
                ],
              ),
            ],
          ),
        );
      case 7:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.user_agreement, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              SizedBox(
                height: 1.sh - 280.sp,
                child: HWebView(
                  key: UniqueKey(),
                  title: "",
                  url: "${HHttp.getOfficialHost()}lechipai_scan/user_agreement.html",
                  hideAppbar: true,
                  hideClose: true,
                ),
              ),
            ],
          ),
        );
      case 8:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.privacy, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              SizedBox(
                height: 1.sh - 280.sp,
                child: HWebView(
                  key: UniqueKey(),
                  title: "",
                  url: "${HHttp.getOfficialHost()}lechipai_scan/privacy_agreement.html",
                  hideAppbar: true,
                  hideClose: true,
                ),
              ),
            ],
          ),
        );
      case 9:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.version_upgrade, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              Row(
                children: [
                  MyText("${Lang.version}${Global.appVersion}", color7C, 24.sp),
                  const Spacer(),
                  Click(
                    onTap: () {
                      Global.checkNewestVersion(userCheck: true);
                    },
                    child: Container(
                      height: 56.sp,
                      alignment: Alignment.center,
                      padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                      decoration: BoxDecoration(
                        color: colorPurpleLilac,
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      child: MyText(Lang.check_version, colorBlueDeep, 24.sp),
                    ),
                  )
                ],
              ),
            ],
          ),
        );
      case 10:
        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.setting_contact, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 40.sp),
              MyText(Lang.contact_way, color7C, 24.sp),
              SizedBox(height: 24.sp),
              Align(
                alignment: Alignment.bottomCenter,
                child: Image.asset("res/scan/mooeli_service.png", width: 240.sp),
              ),
            ],
          ),
        );
      case 11:
        final lockSettings = [
          {
            "title": Lang.auto_lock_screen_never,
            "time": -1
          },
          {
            "title": Lang.auto_lock_screen_15s,
            "time": 15
          },
          {
            "title": Lang.auto_lock_screen_30s,
            "time": 30
          },
          {
            "title": Lang.auto_lock_screen_1m,
            "time": 60
          },
          {
            "title": Lang.auto_lock_screen_2m,
            "time": 120
          },
          {
            "title": Lang.auto_lock_screen_5m,
            "time": 300
          },
          {
            "title": Lang.auto_lock_screen_10m,
            "time": 600
          },
          {
            "title": Lang.auto_lock_screen_30m,
            "time": 1800
          },
        ];
        final currentTime = Global.sharedPrefs.getInt('auto_lock_screen_time') ?? 120;

        return Container(
          height: 1.sh - 100.sp,
          margin: EdgeInsets.fromLTRB(24.sp, 60.sp, 116.sp, 40.sp),
          padding: EdgeInsets.all(34.sp),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.6),
            borderRadius: BorderRadius.all(Radius.circular(16.sp)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.auto_lock_screen, color2B, 24.sp, FontWeight.w500),
              SizedBox(height: 38.sp),
              ...lockSettings.map((e) => _buildRadio(e['time'] == currentTime, e['title'] as String, (){
                setState(() {
                  Global.sharedPrefs.setInt('auto_lock_screen_time', e['time'] as int);
                });
                callMcByFunName('setLockScreenTime', params: {'time':e['time']});
              })).toList()
            ],
          ),
        );
      default:
        return const SizedBox();
    }
  }

  Widget _buildRadio(bool selected, String title, VoidCallback onTap) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.only(bottom: 28.sp),
        width: double.infinity,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: 24.sp,
              width: 24.sp,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: selected ? colorBlueDeep : colorE1,
                  width: selected ? 7  .sp : 2.sp,
                ),
              ),
            ),
            SizedBox(width: 8.sp),
            MyText(title, selected ? colorBlueDeep : const Color(0xFF4A494F), 24.sp),
          ],
        ),
      ),
    );
  }
}
