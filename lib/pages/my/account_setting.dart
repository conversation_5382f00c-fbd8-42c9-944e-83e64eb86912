import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

class AccountSetting extends StatefulWidget {
  const AccountSetting({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _AccountSettingState();
  }
}

class _AccountSettingState extends State<AccountSetting> with WidgetsBindingObserver {
  //时差字符串
  String timeUTCOffsetStr = "";

  @override
  initState() {
    Duration duration = DateTime.now().timeZoneOffset;
    int offsetMinCount = duration.inMinutes;
    timeUTCOffsetStr =
        "${offsetMinCount >= 0 ? "+" : ""}${(offsetMinCount ~/ 60).toString().padLeft(2, "0")}${(offsetMinCount % 60).toString().padLeft(2, "0")}";

    super.initState();
    WidgetsBinding.instance.addObserver(this);

    initAsyncState();
  }

  void initAsyncState() async {}

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    switch (state) {
      case AppLifecycleState.resumed:
        initAsyncState();
        break;
      default:
    }
  }

  @override
  dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  getAllWidget() {
    List<Widget> widgets = [];
    // Map widgetInfos = {
    //   "${Lang.account_area}${isLoginChinaArea() ? Lang.login_china_inland : Lang.login_europe}": const SizedBox(),
    // };
    // if (disableAboardLogin) {
    //   widgetInfos = {};
    // }
    //
    // widgetInfos["登录账号"] = Row(
    //   mainAxisAlignment: MainAxisAlignment.end,
    //   crossAxisAlignment: CrossAxisAlignment.center,
    //   children: [
    //     MyText(Global.sharedPrefs.getString("login_account") ?? "", const Color(0xFF999999), 14.sp),
    //   ],
    // );

    widgets.add(Padding(
      padding: EdgeInsets.only(top: 25.sp),
      child: Row(
        children: [
          MyText(Lang.current_account, color2B, 14.sp, FontWeight.w500),
          SizedBox(width: 8.sp),
          MyText(Global.sharedPrefs.getString("login_account") ?? "", color2B, 14.sp),
        ],
      ),
    ));
    widgets.add(Padding(
        padding: EdgeInsets.fromLTRB(0, 25.sp, 0, 25.sp),
        child: GestureDetector(
          onTap: () {
            sendEventPoint("account_settings", "cancel_account", {"state": "click"});
            Navigator.pushNamed(context, "AccountLogoff");
          },
          child: Row(
            children: [
              Expanded(child: MyText(Lang.destroy_account, colorRed, 14.sp, FontWeight.w500)),
              Image.asset("res/icons/arrow_right.png", width: 16.sp, height: 14.sp, color: colorRed),
            ],
          ),
        )));

    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(Lang.account_setting),
          // actions: [
          //   GestureDetector(
          //     onTap: () {
          //       Global.showTipDialog(Lang.setting_logout_tip);
          //     },
          //     child: Padding(
          //       padding: EdgeInsets.only(right: 24.sp),
          //       child: Image.asset(
          //         "res/icons/light_question.png",
          //         width: 20.sp,
          //         height: 20.sp,
          //       ),
          //     ),
          //   )
          // ],
        ),
        body: SafeArea(
          child: Stack(children: [
            Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 70.sp),
                child: SingleChildScrollView(
                  child: Column(
                    children: getAllWidget(),
                  ),
                )),
            // HCustomButton(
            //   fontSize: 16.sp,
            //   fixedBottom: true,
            //   onPress: () {
            //     Global.showAlertDialog(Lang.logout_account, Lang.confirm_logout, contentCenter: true, okCallBack: () {
            //       User.instance.clearUserData();
            //     });
            //   },
            //   borderColor: Colors.white,
            //   borderWidth: 1.sp,
            //   textColor: colorRed,
            //   text: Lang.logout,
            //   width: 1.sw - 40.sp,
            //   height: 48.sp,
            //   ms: 1000,
            //   bgColor: Colors.white,
            // ),
            Padding(
              padding: EdgeInsets.only(bottom: 40.sp),
              child: HCustomButton(
                fixedBottom: true,
                text: Lang.logout,
                borderColor: colorFB,
                bgColor: Colors.white,
                borderWidth: 1.sp,
                textColor: colorRed,
                onPress: () async {
                  sendEventPoint("account_settings", "logout", {"state": "click"});
                  if (await HHttp.checkInternetValid()) {
                    Global.showAlertDialog(
                      Lang.logout_account,
                      Lang.confirm_logout,
                      contentCenter: true,
                      okText: Lang.exit,
                      okCallBack: () {
                        sendEventPoint("account_settings", "logout", {"state": "success"});
                        User.instance.clearUserData();
                      },
                    );
                  } else {
                    Global.showAlertDialog(
                      Lang.logout_account,
                      Lang.confirm_logout_offline,
                      contentCenter: true,
                      okText: Lang.exit,
                      okCallBack: () {
                        sendEventPoint("account_settings", "logout", {"state": "success"});
                        User.instance.clearUserData();
                      },
                    );
                  }
                },
              ),
            ),
          ]),
        ));
  }
}
