import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/scan/UltraManager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:sprintf/sprintf.dart';

class AccountLogoff extends StatefulWidget {
  const AccountLogoff({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _AccountLogoffState();
  }
}

class _AccountLogoffState extends State<AccountLogoff> {
  reqLogoff() async {
    sendEventPoint("account_settings", "cancel_account", {"state": "confirm"});
    HHttp.request(
      "/v3/provider/deleteProvider",
      "POST",
      (data) {
        sendEventPoint("account_settings", "cancel_account", {"state": "success"});
        Global.toast(Lang.destroy_account_success);
        User.instance.clearUserData(forceLogout: false, logoff: true);
      },
      params: {"providerId": currentUser.personId},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.apply_destroy_account),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 70.sp),
                child: Center(
                  child: Column(
                    children: [
                      SizedBox(height: 40.sp),
                      Image.asset("res/icons/acc_logoff.png", width: 60.sp, height: 60.sp),
                      SizedBox(height: 16.sp),
                      MyText(Lang.destroy_account_means, color2B, 18.sp, FontWeight.w500),
                      SizedBox(height: 16.sp),
                      HeightText(sprintf(Lang.destroy_account_tip1, [monitorName]), color2B, 16.sp, 1.5),
                    ],
                  ),
                ),
              ),
            ),
            HCustomButton(
              fixedBottom: true,
              onPress: () {
                sendEventPoint("account_settings", "cancel_account", {"state": "apply"});
                HHttp.request("/v3/tenant/list", "POST", (data) {
                  try {
                    List<LingyaTenant> tenantList =
                        (data["tenantList"] as List).map((e) => LingyaTenant.fromJson(e, true)).toList();
                    if (tenantList.isNotEmpty) {
                      Global.showAlertDialog(Lang.cannot_destroy_account, Lang.tenant_cannot_destroy_account,
                          okText: Lang.i_knew, isHideCancelBtn: true);
                    } else {
                      Global.showAlertDialog(Lang.destroy_account, Lang.destroy_account_alert,
                          cancelText: Lang.give_up, okText: Lang.confirm_destroy_account, okCallBack: reqLogoff);
                    }
                  } catch (ex) {
                    //
                  }
                }, params: {});
              },
              text: Lang.apply_destroy,
            ),
          ],
        ),
      ),
    );
  }
}
