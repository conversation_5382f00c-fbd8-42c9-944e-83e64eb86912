import 'dart:convert';
import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/my/storage_setting.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

class GeneralSetting extends BasePage {
  const GeneralSetting({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GeneralSettingState();
  }
}

class _GeneralSettingState extends BasePageState<GeneralSetting> {
  @override
  void initState() {
    super.initState();
    getScanActivities();
  }

  int pageIndex = 0;
  List<ScanActivity> caseList = [];

  getScanActivities() {
    int pageSize = 50;
    HHttp.request("/v3/quickscan/campaign/page", "POST", (data) {
      List<ScanActivity> list = (data["data"] as List).map((j) => ScanActivity.fromJson(j)).toList();
      setState(() {
        if (isNotEmpty(list)) {
          if (list.length >= pageSize) {
            pageIndex++;
            getScanActivities();
          }
          caseList.addAll(list);
          logger("getScanActivities: $caseList");
        }
      });
    }, params: {
      "pageCount": 50,
      "pageIndex": pageIndex,
    });
  }

  exportScanPhotos() async {
    if (isEmpty(caseList)) {
      return;
    }
    Global.showCustomDialog(Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: 0.8.sw),
        padding: EdgeInsets.fromLTRB(38.sp, 28.sp, 38.sp, 26.sp),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(6.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 42.sp,
              height: 42.sp,
              child: const CircularProgressIndicator(color: Colors.white),
            ),
            SizedBox(height: 20.sp),
            MyText(Lang.export_ing, Colors.white, 16.sp),
          ],
        ),
      ),
    ));
    delay(500, () async {
      try {
        Directory dir = await getTemporaryDirectory();
        String zipPath = "${dir.path}/${currentUser.username}.zip";
        if (File(zipPath).existsSync()) {
          File(zipPath).deleteSync();
        }
        var ziper = ZipFileEncoder();
        ziper.create(zipPath);

        List<String> recordIds = [];
        Directory externalDir = await getApplicationSupportDirectory();
        List<FileSystemEntity> dirs = externalDir.listSync();
        for (FileSystemEntity dir in dirs) {
          if (dir is Directory) {
            String name = dir.path.split("/").last;
            if (name.startsWith("scan_") && name.length > 8) {
              recordIds.add(name.substring(5));
              await ziper.addDirectory(dir);
            }
          }
        }

        for (ScanActivity scanActivity in caseList) {
          List<ScanInfo> recordList = await getAllRecordList(scanActivity.campaignId);
          if (isNotEmpty(recordList)) {
            File jsonFile = File('${dir.path}/record_list_${scanActivity.campaignId}.json');
            Map map = {
              "scan_case": scanActivity.toJson(),
              "record_list": recordList.map((record) => record.toJson()).toList(),
            };
            jsonFile.writeAsStringSync(jsonEncode(map));
            await ziper.addFile(File(getLocalPath(jsonFile.path)));

            for (ScanInfo record in recordList) {
              if (!recordIds.contains(record.recordId)) {
                for (String file in record.getAllPhotos()) {
                  await ziper.addFile(File(getLocalPath(file)));
                }
              }
            }
          }
        }
        ziper.close();
        BotToast.cleanAll();
        Share.shareXFiles([XFile(zipPath)]);
      } catch (ex) {
        BotToast.cleanAll();
      }
    });
  }

  getAllWidget() {
    return [
      Container(
        margin: EdgeInsets.fromLTRB(0.sp, 8.sp, 0.sp, 8.sp),
        padding: EdgeInsets.fromLTRB(16.sp, 20.sp, 16.sp, 20.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 0.2.sp,
              spreadRadius: 0.2.sp,
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            Navigator.pushNamed(context, "LanguageSetting");
          },
          child: Row(
            children: [
              Expanded(child: MyText(Lang.multiple_language, color2B, 16.sp, FontWeight.w500)),
              Image.asset("res/icons/arrow_right.png", width: 16.sp, height: 14.sp, color: color2B),
            ],
          ),
        ),
      ),
      Container(
        margin: EdgeInsets.fromLTRB(0.sp, 8.sp, 0.sp, 8.sp),
        padding: EdgeInsets.fromLTRB(16.sp, 20.sp, 16.sp, 20.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 0.2.sp,
              spreadRadius: 0.2.sp,
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            push(const StorageSetting());
          },
          child: Row(
            children: [
              Expanded(child: MyText(Lang.data_storage, color2B, 16.sp, FontWeight.w500)),
              Image.asset("res/icons/arrow_right.png", width: 16.sp, height: 14.sp, color: color2B),
            ],
          ),
        ),
      ),
      Container(
        margin: EdgeInsets.fromLTRB(0.sp, 8.sp, 0.sp, 8.sp),
        padding: EdgeInsets.fromLTRB(16.sp, 20.sp, 16.sp, 20.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 0.2.sp,
              spreadRadius: 0.2.sp,
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            Global.showAlertDialog(
              Lang.export_scan_data_file,
              Lang.export_scan_data_desc,
              isBringXBtn: true,
              okText: Lang.export,
              okColor: colorBrand,
              okCallBack: exportScanPhotos,
            );
          },
          child: Row(
            children: [
              Expanded(child: MyText(Lang.export_scan_data_file, color2B, 16.sp, FontWeight.w500)),
              Image.asset("res/icons/arrow_right.png", width: 16.sp, height: 14.sp, color: color2B),
            ],
          ),
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.general_setting),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 70.sp),
              child: SingleChildScrollView(
                child: Column(
                  children: getAllWidget(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
