import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class HttpLog extends BasePage {
  const HttpLog({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _HttpLogState();
  }
}

String logText = "";

class _HttpLogState extends BasePageState<HttpLog> {
  String firstLogin = "";

  @override
  void initState() {
    super.initState();
    Global.initGlobalAsync();
    delay(500, () {
      String account = Global.sharedPrefs.getString("login_account") ?? "";
      int? mode = Global.sharedPrefs.getInt("login_mode");
      if (mode != null) {
        switch (mode!) {
          case 0:
            account += "注册登录";
            break;
          case 1:
            account += "密码登录";
            break;
          case 2:
            account += "验证码登录";
            break;
        }
      }
      setState(() {
        firstLogin = "${Global.sharedPrefs.getString("first_login")}\n$account";
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: const Text("Http出错日志"),
        actions: [
          ElevatedButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: "最初版本: $firstLogin\n$logText"));
              Global.toast(Lang.copy_success);
            },
            child: MyText("复制", Colors.white, 12.sp),
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.all(16.sp),
          child: SingleChildScrollView(
            child: MyText("最初版本: $firstLogin\n$logText", color2B, 12.sp),
          ),
        ),
      ),
    );
  }
}
