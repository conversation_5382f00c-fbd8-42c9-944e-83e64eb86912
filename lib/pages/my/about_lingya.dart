import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class AboutLingya extends StatefulWidget {
  const AboutLingya({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AboutLingyaState();
}

class _AboutLingyaState extends State<AboutLingya> {
  String version = "";
  int clickLogo = 0;

  @override
  initState() {
    version = Global.appVersion;
    super.initState();
  }

  getMyItems() {
    List<Widget> allItemsWidget = [];
    List myItemsInfo = [
      {
        "title": Lang.user_agreement,
        "event": () {
          sendEventPoint("about_Mooeli", "user_agreement");
          Global.openUserAgreement(context);
        },
        "overriteRight": null,
      },
      {
        "title": Lang.privacy,
        "event": () {
          sendEventPoint("about_Mooeli", "privacy_policy");
          Global.openPrivacy(context);
        },
        "overriteRight": null,
      },
      {
        "title": Lang.version_upgrade,
        "event": () {
          Global.checkNewestVersion(userCheck: true);
        },
        "overriteRight": Global.isHaveNewestAppVerAlert
            ? Stack(
                alignment: Alignment.topRight,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 6.sp,
                        height: 6.sp,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(6.sp),
                        ),
                      ),
                      MyText(" ${Lang.new_version}", color7C, 14.sp),
                      Image.asset("res/icons/arrow_right.png", width: 18.sp, height: 16.sp, color: color2B),
                    ],
                  )
                ],
              )
            : Row(
                children: [
                  Image.asset("res/icons/arrow_right.png", width: 18.sp, height: 16.sp, color: color2B),
                ],
              )
      },
    ];

    allItemsWidget.add(Padding(
        padding: EdgeInsets.fromLTRB(24.sp, 20.sp, 27.sp, 21.sp),
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                clickLogo++;
                if (clickLogo == 10) {
                  scanShowIp = true;
                  toast("扫描显示ip：已开启\n再点3下关闭");
                } else if (clickLogo == 13) {
                  scanShowIp = false;
                  toast("扫描显示ip：已关闭");
                }
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(30.sp),
                child: Image.asset("res/icons/logo.png", height: 102.sp, width: 102.sp),
              ),
            ),
            SizedBox(height: 8.sp),
            MyText(
                "${Lang.version} $version${HHttp.getHttpFlag() == "" ? "" : "_${HHttp.getHttpFlag()}"}", color7C, 14.sp)
          ],
        )));

    for (var i = 0; i < myItemsInfo.length; i++) {
      allItemsWidget.add(
        Padding(
          padding: EdgeInsets.fromLTRB(24.sp, 21.sp, 27.sp, 21.sp),
          child: Column(
            children: [
              GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: myItemsInfo[i]["event"],
                  child: Row(
                    children: [
                      Expanded(child: Text(myItemsInfo[i]["title"], style: TextStyle(fontSize: 16.sp))),
                      myItemsInfo[i]["overriteRight"] ??
                          Image.asset("res/icons/arrow_right.png", width: 18.sp, height: 16.sp, color: color2B),
                    ],
                  )),
            ],
          ),
        ),
      );
    }
    return allItemsWidget;
  }

  int clickTitle = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: GestureDetector(
            onTap: () {
              clickTitle++;
              if (kDebugMode || clickTitle > 5 && clickTitle % 10 == 0) {
                Navigator.pushNamed(context, "HttpLog");
              }
            },
            child: Text(Lang.about_lyoral),
          ),
        ),
        body: Stack(
          children: [
            Column(
              children: getMyItems(),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(bottom: 60.sp),
                child: GestureDetector(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: Lang.icp_code_only_cn));
                    Global.toast(Lang.icp_code_copied);
                    Global.openUrlByWebView(context, Lang.icp_code_system, "https://beian.miit.gov.cn/");
                  },
                  child: Text(
                    Lang.icp_code_only_cn,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: color7C,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
