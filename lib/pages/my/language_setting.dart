import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';

class LanguageSetting extends BasePage {
  const LanguageSetting({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _LanguageSettingState();
  }
}

class _LanguageSettingState extends BasePageState<LanguageSetting> {
  int selectIndex = 0;

  Map languageMap = {};

  @override
  void initState() {
    super.initState();
    initLanguageMap();
    initIndex();
  }

  @override
  onLanguageChanged() {
    initLanguageMap();
  }

  initLanguageMap() {
    setState(() {
      languageMap = getLanguageMap();
    });
  }

  initIndex() {
    Locale? locale = getCurrentLocale();
    logger("setting initLocale: $locale");
    if (locale == null) {
      selectIndex = 0;
    } else {
      List list = languageMap.values.toList();
      for (int i = 1; i < languageMap.length; i++) {
        if (list[i] != null && list[i] is Locale && locale.languageCode == (list[i] as Locale).languageCode) {
          selectIndex = i;
          return;
        }
      }
    }
  }

  getAllWidget() {
    List<Widget> widgets = [];
    for (int i = 0; i < languageMap.keys.length; i++) {
      String key = languageMap.keys.toList()[i];
      widgets.add(GestureDetector(
          onTap: () {
            setState(() {
              selectIndex = i;
            });
            selectLanguage(languageMap[key]);
          },
          child: Padding(
            padding: EdgeInsets.fromLTRB(0, 0.sp, 0, 48.sp),
            child: Row(
              children: [
                SizedBox(
                  width: 24.sp,
                  height: 24.sp,
                  child: Material(
                    color: Colors.transparent,
                    child: Radio<int>(
                      activeColor: colorBlueDeep,
                      value: i,
                      onChanged: (value) {
                        setState(() {
                          selectIndex = i;
                        });
                        selectLanguage(languageMap[key]);
                      },
                      groupValue: selectIndex,
                    ),
                  ),
                ),
                SizedBox(width: 16.sp,),
                Expanded(
                    child: Text(key, style: TextStyle(fontSize: 24.sp, color: color7C))),
              ],
            ),
          )));
    }

    return widgets;
  }

  void selectLanguage(Locale? locale) async {
    sendEventPoint("general_settings", "language", {
      "state": "success",
      "content": selectIndex == 0
          ? "auto"
          : selectIndex == 1
              ? "Chinese"
              : "English"
    });
    if (locale == null) {
      resetLocale();
      await loadLocale(Localizations.localeOf(context));
    } else {
      setCurrentLocale(locale);
      await loadLocale(locale);
    }
    initLanguageMap();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
          child: Column(
            children: getAllWidget(),
          ),
        );
  }
}
