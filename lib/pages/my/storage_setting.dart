import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:path_provider/path_provider.dart';

class StorageSetting extends BasePage {
  const StorageSetting({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _StorageSettingState();
  }
}

class _StorageSettingState extends BasePageState<StorageSetting> {
  double cacheSize = -1;
  String cacheUnit = "KB";
  double fileSize = -1;
  String fileUnit = "KB";

  int pageIndex = 0;
  List<String> caseIdList = [];

  @override
  void initState() {
    super.initState();
    getCacheSize();
    getScanActivities();
  }

  getCacheSize() async {
    try {
      // 获取缓存路径
      final Directory tempDir = await getTemporaryDirectory();

      // 遍历缓存路径下的所有文件,计算总大小
      int totalSize = 0;
      await for (final FileSystemEntity entity in tempDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }

      setState(() {
        if (totalSize == 0) {
          cacheSize = 0;
          cacheUnit = "KB";
        } else if (totalSize < 1024 * 1000) {
          cacheSize = double.parse((totalSize / 1024.0).toStringAsFixed(2));
          cacheUnit = "KB";
        } else if (totalSize < 1024 * 1024 * 1000) {
          cacheSize = double.parse((totalSize / 1024.0 / 1024).toStringAsFixed(2));
          cacheUnit = "M";
        } else {
          cacheSize = double.parse((totalSize / 1024.0 / 1024 / 1024).toStringAsFixed(2));
          cacheUnit = "G";
        }
      });
    } catch (e) {
      //
    }

    try {
      // 获取缓存路径
      final Directory tempDir = await getApplicationSupportDirectory();

      // 遍历缓存路径下的所有文件,计算总大小
      int totalSize = 0;
      await for (final FileSystemEntity entity in tempDir.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }

      setState(() {
        if (totalSize == 0) {
          fileSize = 0;
          fileUnit = "KB";
        } else if (totalSize < 1024 * 1000) {
          fileSize = double.parse((totalSize / 1024.0).toStringAsFixed(2));
          fileUnit = "KB";
        } else if (totalSize < 1024 * 1024 * 1000) {
          fileSize = double.parse((totalSize / 1024.0 / 1024).toStringAsFixed(2));
          fileUnit = "M";
        } else {
          fileSize = double.parse((totalSize / 1024.0 / 1024 / 1024).toStringAsFixed(2));
          fileUnit = "G";
        }
      });
    } catch (e) {
      //
    }
  }

  getScanActivities() {
    int pageSize = 50;
    HHttp.request("/v3/quickscan/campaign/page", "POST", (data) {
      List<ScanActivity> list = (data["data"] as List).map((j) => ScanActivity.fromJson(j)).toList();
      setState(() {
        if (isNotEmpty(list)) {
          if (list.length >= pageSize) {
            pageIndex++;
            getScanActivities();
          }
          caseIdList.addAll(list.map((e) => e.campaignId).toList());
          logger("getScanActivities: $caseIdList");
        }
      });
    }, params: {
      "pageCount": 50,
      "pageIndex": pageIndex,
    });
  }

  Future<void> clearCache() async {
    try {
      if (cacheSize > 0) {
        final Directory tempDir = await getTemporaryDirectory();
        await for (final FileSystemEntity entity in tempDir.list(recursive: true)) {
          if (entity is File) {
            await entity.delete();
          }
        }

        setState(() {
          cacheSize = 0;
          cacheUnit = "KB";
        });
        toast(Lang.clear_cache_finish);
      }
    } catch (e) {
      //
    }
  }

  Future<void> clearFiles() async {
    try {
      if (fileSize > 0) {
        final Directory tempDir = await getTemporaryDirectory();
        await for (final FileSystemEntity entity in tempDir.list(recursive: true)) {
          if (entity is File) {
            await entity.delete();
          }
        }

        setState(() {
          fileSize = 0;
          fileUnit = "KB";
        });
        for (String caseId in caseIdList) {
          Global.sharedPrefs.remove("record_list_$caseId");
          Global.sharedPrefs.remove("upload_list_$caseId");
          Global.sharedPrefs.remove("all_list_$caseId");
        }
        deleteLocalCacheFile(true);
        deleteLocalCacheFile(false);

        toast(Lang.clear_scan_data_finish);
      }
    } catch (e) {
      logger("clearFiles error! $e");
    }
  }

  void deleteLocalCacheFile(bool isDocument) async {
    Directory? externalDir =
        isDocument ? await getApplicationDocumentsDirectory() : await getApplicationSupportDirectory();
    Directory saveDir = Directory('${externalDir!.path}/scan_data');
    await for (final FileSystemEntity entity in saveDir.list(recursive: true)) {
      if (entity is File) {
        await entity.delete();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.data_storage),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 70.sp),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.fromLTRB(0.sp, 8.sp, 0.sp, 8.sp),
                      padding: EdgeInsets.fromLTRB(16.sp, 20.sp, 16.sp, 20.sp),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.sp),
                        boxShadow: [
                          BoxShadow(
                            color: colorShader,
                            blurRadius: 0.2.sp,
                            spreadRadius: 0.2.sp,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(child: MyText(Lang.cache, color2B, 16.sp, FontWeight.w500)),
                              GestureDetector(
                                onTap: clearCache,
                                child: Container(
                                  padding: EdgeInsets.fromLTRB(20.sp, 4.sp, 20.sp, 4.sp),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(48.sp),
                                    border: Border.all(color: cacheSize > 0 ? colorBrand : colorE1),
                                  ),
                                  child: MyText(Lang.clear, cacheSize > 0 ? colorBrand : colorE1, 12.sp),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 14.sp),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MyText("${cacheSize < 0 ? '-' : (cacheSize == 0 ? '0' : cacheSize)}", Colors.black, 24.sp,
                                  FontWeight.w600),
                              SizedBox(width: 4.sp),
                              MyText(cacheUnit, Colors.black, 14.sp),
                            ],
                          ),
                          SizedBox(height: 12.sp),
                          HeightText(Lang.clear_cache_desc, color7C, 14.sp, 1.4),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.fromLTRB(0.sp, 8.sp, 0.sp, 8.sp),
                      padding: EdgeInsets.fromLTRB(16.sp, 20.sp, 16.sp, 20.sp),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.sp),
                        boxShadow: [
                          BoxShadow(
                            color: colorShader,
                            blurRadius: 0.2.sp,
                            spreadRadius: 0.2.sp,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(child: MyText(Lang.scan_data_file, color2B, 16.sp, FontWeight.w500)),
                              GestureDetector(
                                onTap: () {
                                  showCustomDialog(Center(
                                    child: SizedBox(
                                      width: 276.sp,
                                      child: Stack(
                                        children: [
                                          Container(
                                            width: 276.sp,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.circular(8.sp),
                                            ),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                    padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 0),
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius: BorderRadius.circular(16.sp),
                                                    ),
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Row(
                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                          children: [
                                                            Image.asset("res/icons/icon_alert3.png", width: 20.sp),
                                                            SizedBox(width: 4.sp),
                                                            MyText(Lang.clear_data, color2B, 16.sp, FontWeight.w500),
                                                          ],
                                                        ),
                                                        SizedBox(height: 20.sp),
                                                        HeightText(Lang.clear_data_desc1, colorRed, 14.sp, 1.6,
                                                            FontWeight.w500),
                                                        HeightText(Lang.clear_data_desc2, color2B, 14.sp, 1.6),
                                                        SizedBox(height: 24.sp),
                                                      ],
                                                    )),
                                                Divider(height: 1.sp, color: colorE1),
                                                Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Click(
                                                      onTap: () {
                                                        SmartDialog.dismiss();
                                                      },
                                                      ms: 5000,
                                                      child: Container(
                                                        width: 137.5.sp,
                                                        height: 46.sp,
                                                        alignment: Alignment.center,
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(8.sp),
                                                        ),
                                                        child: Text(Lang.cancel,
                                                            textAlign: TextAlign.center,
                                                            style: TextStyle(
                                                              fontSize: 14.sp,
                                                              fontWeight: FontWeight.w500,
                                                              color: color2B,
                                                            )),
                                                      ),
                                                    ),
                                                    Container(
                                                      width: 0.5.sp,
                                                      height: 46.sp,
                                                      color: colorE1,
                                                    ),
                                                    Click(
                                                      onTap: () {
                                                        SmartDialog.dismiss();
                                                        clearFiles();
                                                      },
                                                      ms: 5000,
                                                      child: Container(
                                                        width: 138.sp,
                                                        height: 46.sp,
                                                        alignment: Alignment.center,
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: BorderRadius.circular(8.sp),
                                                        ),
                                                        child: Text(Lang.clear,
                                                            textAlign: TextAlign.center,
                                                            style: TextStyle(
                                                              fontSize: 14.sp,
                                                              fontWeight: FontWeight.w500,
                                                              color: colorRed,
                                                            )),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          Positioned(
                                            top: 16.sp,
                                            right: 12.sp,
                                            child: GestureDetector(
                                              onTap: () {
                                                SmartDialog.dismiss();
                                              },
                                              child: Padding(
                                                padding: EdgeInsets.all(4.sp),
                                                child: Image.asset(
                                                  "res/imgs/icon_close.png",
                                                  width: 16.sp,
                                                  color: colorA4,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ));
                                },
                                child: Container(
                                  padding: EdgeInsets.fromLTRB(20.sp, 4.sp, 20.sp, 4.sp),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(48.sp),
                                    border: Border.all(color: fileSize > 0 ? colorRed : colorE1),
                                  ),
                                  child: MyText(Lang.clear, fileSize > 0 ? colorRed : colorE1, 12.sp),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 14.sp),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MyText("${fileSize < 0 ? '-' : (fileSize == 0 ? '0' : fileSize)}", Colors.black, 24.sp,
                                  FontWeight.w600),
                              SizedBox(width: 4.sp),
                              MyText(fileUnit, Colors.black, 14.sp),
                            ],
                          ),
                          SizedBox(height: 12.sp),
                          RichText(
                            text: TextSpan(
                              text: Lang.scan_data_file_desc1,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: colorAB,
                                height: 1.4,
                              ),
                              children: [
                                TextSpan(
                                  text: Lang.scan_data_file_desc2,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: colorRed,
                                    height: 1.4,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                TextSpan(
                                  text: Lang.scan_data_file_desc3,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: colorAB,
                                    height: 1.4,
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
