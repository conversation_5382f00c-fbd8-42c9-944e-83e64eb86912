import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/file_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/viewer/pdf_preview.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class MyFilePage extends BasePage {
  const MyFilePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _MyFilePageState();
  }
}

class _MyFilePageState extends BasePageState<MyFilePage> {
  List<FileInfo> dataList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;

  @override
  initState() {
    super.initState();
    initAsyncState();
  }

  initAsyncState() async {
    await refreshDataList();
  }

  refreshDataList() async {
    return await getDataList(true);
  }

  onLoadMore() {
    getDataList(false);
  }

  getDataList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    HHttp.request(
      "/v3/tempfile/page",
      "POST",
      (data) {
        List<FileInfo> list = (data["tempFileVoList"] as List).map((j) => FileInfo.fromJson(j)).toList();

        if (isNotEmpty(list)) {
          List<String> ids = [];
          for (var item in list) {
            if (isNotEmpty(item.avatarFileId)) {
              ids.add(item.avatarFileId);
            }
          }
          downloadThumbnail(
            ids,
            (id, path) {
              for (var item in list) {
                if (item.avatarFileId == id) {
                  setState(() {
                    item.avatarPhoto = path;
                  });
                }
              }
            },
            "RECORD",
          );
        }
        setState(() {
          allLoad = true;
          if (refresh) {
            dataList = list;
            if (dataList.length >= pageSize) {
              allLoad = false;
            }
          } else {
            if (isNotEmpty(list)) {
              if (list.length >= pageSize) {
                allLoad = false;
              }
              dataList.addAll(list);
            }
          }
          //筛选初诊报告(category == "813")，自动拉取下一页数据
          dataList = dataList.where((file) => file.category == "813").toList();
          isLoading = false;
          // if (!allLoad) {
          //   getDataList(false);
          // }
        });
      },
      errCallBack: (resp) {
        setState(() {
          isLoading = false;
        });
      },
      params: {
        "pageCount": pageSize,
        "pageIndex": pageIndex,
      },
    );
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < dataList.length; i++) {
      FileInfo file = dataList[i];
      allWidget.add(
        GestureDetector(
          onTap: () {
            if (file.isSuccess()) {
              push(PdfViewerPagePage(file: file));
            }
          },
          child: Container(
            margin: EdgeInsets.fromLTRB(20.sp, 6.sp, 20.sp, 6.sp),
            padding: EdgeInsets.all(16.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.sp),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                isNotEmpty(file.avatarPhoto)
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(40.sp),
                        child: Image.file(
                          File(getLocalPath(file.avatarPhoto)),
                          width: 48.sp,
                          height: 48.sp,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Image.asset(
                        "res/imgs/record_avatar.png",
                        width: 48.sp,
                        height: 48.sp,
                        fit: BoxFit.cover,
                      ),
                SizedBox(width: 12.sp),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: SingleText(
                                isNotEmpty(file.caseName) ? file.caseName : " - ", color2B, 16.sp, FontWeight.w500),
                          ),
                          file.getStatusView(),
                        ],
                      ),
                      SizedBox(height: 8.sp),
                      MyText("${file.getGender()}  ${sprintf(Lang.year_old, [file.getAge()])}", color2B, 14.sp),
                      // SizedBox(height: 8.sp),
                      // SingleText("${Lang.report_name}: ${file.recordName}", color2B, 14.sp),
                      file.isSuccess() || file.isOutdated()
                          ? Padding(
                              padding: EdgeInsets.only(top: 8.sp),
                              child: SingleText(
                                  "${Lang.generate_time}: ${Global.getShowTime(milliseconds: int.parse(file.createTime), ignoreSameDay: false)}",
                                  color2B,
                                  14.sp),
                            )
                          : const SizedBox(),
                      file.isSuccess()
                          ? Padding(
                              padding: EdgeInsets.only(top: 8.sp),
                              child: SingleText(sprintf(Lang.file_effect_time, [file.getEffectTime()]), color2B, 14.sp),
                            )
                          : const SizedBox(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    return allWidget;
  }

  // 下拉刷新
  Future onRefresh() async {
    await refreshDataList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(Lang.my_file)),
      body: Column(
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 10.sp),
            padding: EdgeInsets.all(8.sp),
            decoration: BoxDecoration(
              color: colorPink.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.sp),
            ),
            child: MyText(Lang.my_file_tip, colorPink, 12.sp),
          ),
          Expanded(
            child: RefreshList(
              childList: getListView(),
              isLoading: isLoading,
              onRefresh: onRefresh,
              onLoadMore: onLoadMore,
              nullImgName: "res/icons/empty.png",
              nullText: Lang.no_record,
            ),
          ),
        ],
      ),
    );
  }
}
