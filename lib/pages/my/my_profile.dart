import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/default_style.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_pickers/time_picker/model/date_mode.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:flutter_pickers/time_picker/model/suffix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/oss_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

class Profile extends StatefulWidget {
  const Profile({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ProfileState();
  }
}

class _ProfileState extends State<Profile> {
  final picker = ImagePicker();
  final GlobalKey saveButtonKey = GlobalKey<HCustomButtonState>();
  String? avatar = User.instance.getUserData("Avatar");
  String? avatarUrl = Global.getTempScanCacheData("Avatar");
  String nickName = User.instance.getUserData("DisplayName");
  String gender = User.instance.getUserData("Gender");
  String birthday = User.instance.getUserData("Birthday");
  RegExp nameReg = RegExp(r'^[a-zA-Z\d_\u4e00-\u9fa5]+$');

  TextEditingController nameController = TextEditingController();

  @override
  initState() {
    initParmas();
    super.initState();
  }

  initParmas() {
    setState(() {
      avatar = User.instance.getUserData("Avatar");
      avatarUrl = Global.getTempScanCacheData("Avatar");
      nickName = User.instance.getUserData("DisplayName");
      gender = User.instance.getUserData("Gender");
      birthday = User.instance.getUserData("Birthday");
    });
  }

  @override
  dispose() {
    SmartDialog.dismiss();
    super.dispose();
  }

  Future uploadByPath(bool isCamera) async {
    Navigator.pop(context);

  }

  void chooseByCamera() async {
    await uploadByPath(true);
  }

  void chooseByPhotos() async {
    await uploadByPath(false);
  }

  Future chooseImg() async {
    Global.showBottomModal(
      maxHeight: 210.sp,
      isTransparent: true,
      context,
      Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            HCustomButton(
              onPress: chooseByCamera,
              text: Lang.take_photo,
              height: 56.sp,
            ),
            SizedBox(height: 14.sp),
            HCustomButton(
              onPress: chooseByPhotos,
              text: Lang.choose_from_gallery,
              height: 56.sp,
            ),
          ],
        ),
      ),
    );
  }

  reqSave(dynamic param, {dynamic callback}) {
    // {
    //   "Avatar": avatar,
    // "DisplayName": nickName,
    // "Gender": gender,
    // "Birthday": birthday,
    // }
    User.instance.reqModifyUserDataByKvs(param, () {
      initParmas();
      if (callback != null) {
        callback();
      }
      // Global.toast(Lang.save_success);
    });
  }

  checkIsHaveChange() {
    if (avatar == User.instance.getUserData("Avatar") &&
        (nickName == User.instance.getUserData("DisplayName")) &&
        gender == User.instance.getUserData("Gender") &&
        birthday == User.instance.getUserData("Birthday")) {
      (saveButtonKey.currentState as HCustomButtonState).setVisible(false);
    } else {
      (saveButtonKey.currentState as HCustomButtonState).setVisible(true);
    }
  }

  showNicknameEditor() {
    sendEventPoint("personal_information", "nickname", {"state": "click"});
    nameController.text = "";
    showCustomDialog(
      Container(
        margin: EdgeInsets.all(32.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                padding: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: Column(
                  children: [
                    Text(Lang.nickname,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: color2B,
                        )),
                    SizedBox(height: 20.sp),
                    Align(
                      alignment: Alignment.topCenter,
                      child: Text(
                        Lang.nickname_rule,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: color2B,
                        ),
                      ),
                    ),
                    SizedBox(height: 12.sp),
                    Material(
                      color: Colors.white,
                      child: Container(
                        height: 36.sp,
                        padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.black12,
                          ),
                          borderRadius: BorderRadius.circular(8.sp),
                        ),
                        child: TextField(
                          controller: nameController,
                          textAlign: TextAlign.left,
                          maxLength: 11,
                          style: TextStyle(fontSize: 14.sp, color: color2B),
                          decoration: InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            hintText: Lang.input_nickname,
                            hintStyle: TextStyle(fontSize: 14.sp, color: const Color(0xFFCCCCCC)),
                            isDense: true,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.sp),
                  ],
                )),
            Divider(height: 1.sp, color: const Color(0xffe5e5e5)),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Container(
                      width: 0.4.sw,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      padding: EdgeInsets.fromLTRB(16.sp, 18.sp, 16.sp, 18.sp),
                      child: Text(Lang.cancel,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: color2B,
                          )),
                    )),
                Container(
                  width: 0.5.sp,
                  height: 54.sp,
                  color: const Color(0xffe5e5e5),
                ),
                GestureDetector(
                    onTap: () {
                      String nickname = nameController.text;
                      logger("HTTP-LOG nickname: $nickname");
                      if (isEmpty(nickname)) {
                        toast(Lang.empty_nickname);
                        return;
                      }
                      if (!nameReg.hasMatch(nickname)) {
                        toast(Lang.invalid_nickname);
                        return;
                      }
                      reqSave(
                        {"DisplayName": nickname},
                        callback: () {
                          sendEventPoint("personal_information", "nickname", {"state": "success"});
                          SmartDialog.dismiss();
                        },
                      );
                    },
                    child: Container(
                      width: 0.4.sw,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.sp),
                      ),
                      padding: EdgeInsets.fromLTRB(16.sp, 18.sp, 16.sp, 18.sp),
                      child: Text(Lang.confirm_ok,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: color2B,
                          )),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  showGenderPicker() {
    sendEventPoint("personal_information", "gender", {"state": "click"});
    PickerStyle pickerStyle = DefaultPickerStyle();
    pickerStyle.pickerHeight = 120.sp;
    pickerStyle.commitButton = Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 12, right: 22),
        child: MyText(Lang.confirm, colorBlueDeep, 16.sp));
    pickerStyle.cancelButton = Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 22, right: 12),
        child: MyText(Lang.cancel, Theme.of(context!).unselectedWidgetColor, 16.sp));

    Pickers.showSinglePicker(
      context,
      data: [Lang.gender_man, Lang.gender_woman],
      pickerStyle: pickerStyle,
      onConfirm: (data, index) {
        reqSave({"Gender": data}, callback: () {
          sendEventPoint("personal_information", "gender", {"state": "success"});
          setState(() {
            gender = data;
          });
        });
      },
    );
  }

  showDatePicker() {
    sendEventPoint("personal_information", "birthday", {"state": "click"});
    List strList = birthday.split('-');
    int month = strList.length < 3
        ? 0
        : strList[1].substring(0, 1) == "0"
            ? int.parse(strList[1].substring(1, 2))
            : int.parse(strList[1]);
    int day = strList.length < 3
        ? 0
        : strList[2].substring(0, 1) == "0"
            ? int.parse(strList[2].substring(1, 2))
            : int.parse(strList[2]);

    PickerStyle pickerStyle = DefaultPickerStyle();
    pickerStyle.commitButton = Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 12, right: 22),
        child: MyText(Lang.confirm, colorBlueDeep, 16.sp));
    pickerStyle.cancelButton = Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 22, right: 12),
        child: MyText(Lang.cancel, Theme.of(context!).unselectedWidgetColor, 16.sp));

    Pickers.showDatePicker(
      context,
      mode: DateMode.YMD,
      suffix: Suffix(years: Lang.picker_year, month: Lang.picker_month, days: Lang.picker_day),
      pickerStyle: pickerStyle,
      selectDate: birthday == "" ? PDuration.now() : PDuration(year: int.parse(strList[0]), month: month, day: day),
      minDate: PDuration(year: 1960, month: 1, day: 1),
      maxDate: PDuration(year: DateTime.now().year, month: DateTime.now().month, day: DateTime.now().day),
      onConfirm: (p) {
        String pickDay = "${p.year}-${p.month! < 10 ? "0${p.month}" : p.month}-${p.day! < 10 ? "0${p.day}" : p.day}";
        reqSave(
          {"Birthday": pickDay},
          callback: () {
            sendEventPoint("personal_information", "birthday", {"state": "success"});
            setState(() {
              birthday = pickDay;
            });
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (saveButtonKey.currentState != null) {
      checkIsHaveChange();
    }

    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(Lang.user_profile),
      ),
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            // 触摸收起键盘
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Padding(
                padding: EdgeInsets.fromLTRB(24.sp, 0, 16.sp, 70.sp),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: chooseImg,
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(0, 23.sp, 8.sp, 23.sp),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                  child: Text(Lang.avatar,
                                      style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: color2B))),
                              avatarUrl != null
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.circular(8.sp),
                                      child: Image.file(
                                        File(avatarUrl!),
                                        height: 52.sp,
                                        width: 52.sp,
                                        fit: BoxFit.cover,
                                      ))
                                  : User.instance.getUserAvatar(52.sp),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: showNicknameEditor,
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(0, 23.sp, 8.sp, 23.sp),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                  child: Text(Lang.nickname,
                                      style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: color2B))),
                              MyText(nickName.isEmpty ? Lang.click_to_set : nickName, color2B, 14.sp),
                              SizedBox(width: 8.sp),
                              Image.asset("res/icons/arrow_right.png", width: 14.sp, height: 14.sp, color: color2B),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: showGenderPicker,
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(0, 23.sp, 8.sp, 23.sp),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                  child: Text(Lang.gender,
                                      style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: color2B))),
                              MyText(gender, color2B, 14.sp),
                              SizedBox(width: 8.sp),
                              Image.asset("res/icons/arrow_right.png", width: 14.sp, height: 14.sp, color: color2B),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: showDatePicker,
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(0, 23.sp, 8.sp, 23.sp),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                  child: Text(Lang.birthday,
                                      style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, color: color2B))),
                              MyText(birthday == "" ? Lang.click_to_set : birthday, color2B, 14.sp),
                              SizedBox(width: 8.sp),
                              Image.asset("res/icons/arrow_right.png", width: 14.sp, height: 14.sp, color: color2B),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ),
        ),
      ),
    );
  }
}
