import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class ContactUs extends StatefulWidget {
  const ContactUs({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ContactUsState();
}

class _ContactUsState extends State<ContactUs> {
  getMyItems() {
    List<Widget> allItemsWidget = [];

    allItemsWidget.add(Padding(
        padding: EdgeInsets.fromLTRB(24.sp, 20.sp, 27.sp, 21.sp),
        child: Column(
          children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(30.sp),
                child: Image.asset("res/icons/logo.png", height: 102.sp, width: 102.sp)),
            Sized<PERSON><PERSON>(height: 28.sp),
            HeightText(Lang.contact_way, color7C, 14.sp, 2),
          ],
        )));

    return allItemsWidget;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.setting_contact),
      ),
      body: Column(
        children: getMyItems(),
      ),
    );
  }
}
