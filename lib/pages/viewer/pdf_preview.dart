import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/file_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:share_plus/share_plus.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class PdfViewerPagePage extends BasePage {
  FileInfo? file;
  String? path;

  PdfViewerPagePage({this.file, this.path, Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _PdfViewerPagePageState();
  }
}

class _PdfViewerPagePageState extends BasePageState<PdfViewerPagePage> {
  @override
  initState() {
    super.initState();
    initAsyncState();
  }

  String pdfPath = "";

  initAsyncState() async {
    if (isNotEmpty(widget.path)) {
      setState(() {
        pdfPath = widget.path!;
      });
    } else if (widget.file != null) {
      HHttp.request(
        "/v3/tempfile/getDowloadUrl",
        "POST",
        (data) {
          HHttp.downFileIfNotExist(
            data,
            (path) {
              setState(() {
                pdfPath = path;
              });
            },
            forceDownload: true,
          );
        },
        params: {
          "fileId": widget.file!.fileId,
          "name":
              "${widget.file!.caseName}_${Lang.diagnose_report}_${Global.getDateByTimestamp(milliseconds: int.parse(widget.file!.createTime), type: 2)}.pdf",
        },
      );
    }
  }

  @override
  void dispose() {
    eventBus.fire(EventClosePdf());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(Lang.report_preview),
        // actions: [
        //   IconButton(
        //     padding: EdgeInsets.fromLTRB(16.sp, 4.sp, 16.sp, 4.sp),
        //     icon: Icon(Icons.more_horiz_outlined, size: 20.sp, color: color2B),
        //     onPressed: () async {
        //       if (isNotEmpty(pdfPath)) {
        //         Uint8List bytes = await File(pdfPath).readAsBytes();
        //         int dirIndex = pdfPath.lastIndexOf("/");
        //         String dir = pdfPath.substring(0, dirIndex);
        //         String fileType = ".pdf";
        //         int typeIndex = pdfPath.substring(dirIndex).lastIndexOf(".");
        //         if (typeIndex > 0) {
        //           fileType = pdfPath.substring(dirIndex).substring(typeIndex);
        //         }
        //         String newPath = pdfPath;
        //
        //         if (isNotEmpty(widget.path)) {
        //           newPath = "$dir/${Global.getFileNameByPath(widget.path!)}";
        //         } else if (widget.file != null) {
        //           newPath =
        //               "$dir/${widget.file!.caseName}_${Lang.diagnose_report}_${Global.getDateByTimestamp(milliseconds: int.parse(widget.file!.createTime), type: 2)}$fileType";
        //         }
        //         await File(newPath).writeAsBytes(bytes);
        //
        //         Share.shareXFiles([XFile(newPath)]);
        //       }
        //     },
        //   )
        // ],
      ),
      body: isEmpty(pdfPath)
          ? Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset("res/icons/icon_loading.gif", width: 32.sp),
                  SizedBox(height: 8.sp),
                  MyText(Lang.loading, color7C, 12.sp),
                ],
              ),
            )
          : Platform.isAndroid
              ? PDFView(
                  filePath: pdfPath,
                  enableSwipe: true,
                  swipeHorizontal: false,
                  autoSpacing: false,
                  pageFling: false,
                  onRender: (pages) {
                    logger("PDFView onRender $pages");
                  },
                  onError: (error) {
                    logger("PDFView onError $error");
                  },
                  onPageError: (page, error) {
                    logger("PDFView onPageError $page $error");
                  },
                  onViewCreated: (PDFViewController pdfViewController) {
                    logger("PDFView onViewCreated");
                  },
                  onPageChanged: (int? page, int? total) {
                    logger("PDFView onPageChanged $page/$total");
                  },
                )
              : SfPdfViewer.file(
                  File(pdfPath),
                  canShowScrollHead: false,
                ),
    );
  }
}
