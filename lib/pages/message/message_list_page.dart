import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/message_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class MessageListPage extends BasePage {
  const MessageListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MessageListPageState();
  }
}

class MessageListPageState extends BasePageState<MessageListPage> {
  List<MessageItem> messageList = [];
  int pageIndex = 0;
  int pageSize = 50;
  int unreadCont = 0;
  bool isLoading = false;
  bool allLoad = false;

  @override
  initState() {
    super.initState();
    addEventListener<EventRefreshMessage>((event) {
      refreshMessageList();
    });
    addEventListener<EventSwitchTenant>((event) {
      refreshMessageList();
    });
    initAsyncState();
  }

  initAsyncState() async {
    await refreshMessageList();
  }

  refreshMessageList() async {
    return await getMessageList(true);
  }

  onLoadMore() {
    getMessageList(false);
  }

  sendUnrendEvent() {
    int count = 0;
    for (MessageItem item in messageList) {
      if (item.state == 0) {
        count++;
      }
    }
    setState(() {
      unreadCont = count;
    });
    eventBus.fire(EventMessageUnread(count));
  }

  getMessageList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });

    HHttp.request(
      "/v3/message/getMessagePage",
      "POST",
      (data) {
        onGetMessageList(json) {
          loggerHttp("getMessageList CallBack ${(data['messageVoList'] as List).length}");
          List<MessageItem> list = (data['messageVoList'] as List).map((i) => MessageItem.fromJson(i, json)).toList();

          setState(() {
            allLoad = true;
            if (refresh) {
              messageList = list;
              if (messageList.length >= pageSize) {
                allLoad = false;
              }
            } else {
              if (isNotEmpty(list)) {
                if (list.length >= pageSize) {
                  allLoad = false;
                }
                messageList.addAll(list);
              }
            }
            isLoading = false;
          });

          sendUnrendEvent();
        }

        getMessageConfigJson((json) {
          onGetMessageList(json);
        });
      },
      errCallBack: (err) {
        logger("getMessageList error");
        setState(() {
          isLoading = false;
        });
      },
      params: {
        "pageCount": pageSize,
        "pageIndex": pageIndex,
        "filters": [],
        "sortedBy": "modifyTime",
        "sortedOrder": "descending",
        // "lastTime": null,
        // "main": true,
      },
    );
  }

  setMeesageAllRead() {
    List ids = messageList.where((item) => item.state == 0).map((item) => item.id).toList();
    if (isEmpty(ids)) {
      return;
    }
    HHttp.request(
      "/v3/message/setMessageReaded",
      "POST",
      (data) {
        setState(() {
          for (MessageItem item in messageList.where((item) => item.state == 0)) {
            item.state = 1;
          }
        });
        sendUnrendEvent();
      },
      params: {
        "idList": ids,
      },
    );
  }

  setMessageRead(MessageItem item) {
    if (item.state == 1) {
      return;
    }
    HHttp.request(
      "/v3/message/setMessageReaded",
      "POST",
      (data) {
        setState(() {
          item.state = 1;
        });
        sendUnrendEvent();
      },
      params: {
        "idList": [item.id]
      },
      isShowNetWorkLoading: false,
    );
  }

  getItemView(MessageItem item) {
    return GestureDetector(
      onTap: () {
        setMessageRead(item);
        if (isNotEmpty(item.metadata) && "3d" == item.metadata!["result"]) {
          // HHttp.request(
          //   "/v2/access/build-subaccess",
          //   "POST",
          //   (data) {
          //     Global.openUrlByWebView(
          //       context,
          //       "3D分析-${Global.getDateByTimestamp(milliseconds: int.parse(item.createTime))}",
          //       "${HHttp.getH5Host()}/dental-3dstudio/#/?Access=${data["access"]}&language=zh",
          //       horizontal: true,
          //       hideAppbar: true,
          //     );
          //   },
          //   params: {"caseId": item.caseId, "recordId": item.recordId, "data": {}},
          // );
        } else if (item.message.contains("http")) {
          int start = item.message.indexOf("http");
          int end = item.message.indexOf("\"", start);
          Global.openUrlByWebView(context, "", item.message.substring(start, end), readTitle: true);
        }
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(0, 6.sp, 0, 6.sp),
        padding: EdgeInsets.fromLTRB(12.sp, 14.sp, 12.sp, 12.sp),
        constraints: BoxConstraints(minHeight: 72.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              item.state == 0 ? "res/imgs/icon_msg_unread.png" : "res/imgs/icon_msg_read.png",
              width: 40.sp,
            ),
            SizedBox(width: 10.sp),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 252.sp,
                  child: SingleText(item.title, color2B, 16.sp, FontWeight.w500),
                ),
                SizedBox(height: 2.sp),
                MyText(Global.getShowTime(milliseconds: int.parse(item.createTime)), colorAB, 12.sp),
                SizedBox(height: 4.sp),
                SizedBox(
                  width: 252.sp,
                  child: HeightText(item.getMessage(), color2B, 14.sp, 1.5),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < messageList.length; i++) {
      allWidget.add(getItemView(messageList[i]));
    }
    return allWidget;
  }

  // 下拉刷新
  Future onRefresh() async {
    await refreshMessageList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
          contentHeight: 108.sp,
          child: Container(
            margin: EdgeInsets.fromLTRB(20.sp, 52.sp, 20.sp, 8.sp),
            height: 48.sp,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MyText(Lang.message_page, color2B, 20.sp, FontWeight.w500),
                      SizedBox(height: 4.sp),
                      MyText(sprintf(Lang.unread_message_count, [unreadCont]), colorAB, 12.sp)
                    ],
                  ),
                ),
                GestureDetector(
                    onTap: setMeesageAllRead,
                    child: Column(
                      children: [
                        Image.asset(
                          "res/imgs/icon_clear.png",
                          width: 32.sp,
                        ),
                        MyText(Lang.set_all_read, color7C, 10.sp),
                      ],
                    )),
              ],
            ),
          )),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.sp),
        child: RefreshList(
          childList: getListView(),
          isLoading: isLoading,
          onRefresh: onRefresh,
          onLoadMore: onLoadMore,
          nullImgName: "res/icons/msg_empty.png",
          nullText: Lang.no_message,
        ),
      ),
    );
  }
}
