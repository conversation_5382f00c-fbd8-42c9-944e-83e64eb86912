import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_flutter/qr_flutter.dart';

class MooeliAddMonitorPage extends BasePage {
  MooeliItem mooeli;

  MooeliAddMonitorPage(this.mooeli, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MooeliAddMonitorPageState();
  }
}

class MonitorInput {
  int type = 0;
  String name = "";
  List<String> goals = [];
  TextEditingController brandController = TextEditingController();
  TextEditingController productController = TextEditingController();
  int startTime = 0;
  int cycle = 4;
  String cycleUnit = "WEEK";

  MonitorInput(this.type, this.name, this.cycle);

  String? get configKey => monitorKeyMap[name];
}

class MooeliAddMonitorPageState extends BasePageState<MooeliAddMonitorPage> {
  dynamic configJson = {};
  GlobalKey repaintKey = GlobalKey();

  int monitorType = 0; //0-口腔全科观察，1-隐形矫正监控，2-固定矫正监控
  List<MonitorInput> inputMonitors = [
    MonitorInput(0, "口腔全科观察", 4),
    MonitorInput(1, "隐形矫正监控", 2),
    MonitorInput(2, "固定矫正监控", 4),
  ];

  String bindCode = "";
  String bindUser = "";

  Map<String, String> cycleMap = {
    "DAY": "天",
    "WEEK": "周",
    "MONTH": "月",
  };

  @override
  initState() {
    super.initState();
    getMooeliConfigJson((json) {
      setState(() {
        configJson = json;
      });
    });
  }

  _buildBody() {
    if (isEmpty(configJson)) {
      return Center(
        child: MyText(Lang.loading, color7C, 14.sp),
      );
    }
    if (isNotEmpty(bindCode)) {
      return SingleChildScrollView(
        child: RepaintBoundary(
          key: repaintKey,
          child: Container(
            width: 1.sw,
            color: Colors.white,
            padding: EdgeInsets.all(24.sp),
            alignment: Alignment.center,
            child: Column(
              children: [
                SizedBox(height: 40.sp),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.check_circle, color: Colors.green[400], size: 20.sp),
                    SizedBox(width: 8.sp),
                    MyText("监控协议添加成功", color2B, 14.sp),
                  ],
                ),
                SizedBox(height: 24.sp),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset("res/tabs/lyoral_icon.png", width: 60.sp),
                    Padding(
                      padding: EdgeInsets.fromLTRB(24.sp, 0, 24.sp, 0),
                      child: Image.asset("res/imgs/icon_link.png", width: 40.sp),
                    ),
                    Image.asset("res/tabs/mooeli_icon.png", width: 60.sp),
                  ],
                ),
                SizedBox(height: 24.sp),
                MyText("将这条专属邀请码发送给$bindUser，用于注册Mooeli App后关联这条病例。", color2B, 14.sp),
                SizedBox(height: 24.sp),
                Container(
                  padding: EdgeInsets.all(12.sp),
                  decoration: BoxDecoration(
                    color: colorF3,
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      MyText(bindCode, color2B, 24.sp),
                      GestureDetector(
                        onTap: () {
                          Clipboard.setData(ClipboardData(text: bindCode));
                          Global.toast(Lang.copy_success);
                        },
                        child: Padding(
                            padding: EdgeInsets.all(12.sp),
                            child: MyText(
                              "复制",
                              color2B,
                              14.sp,
                              FontWeight.w500,
                            )),
                      )
                    ],
                  ),
                ),
                SizedBox(height: 24.sp),
                GestureDetector(
                  child: QrImageView(data: bindCode, size: 0.6.sw),
                  onTap: () async {
                    PermissionStatus storageStatus = await Permission.storage.status;
                    if (storageStatus != PermissionStatus.granted) {
                      storageStatus = await Permission.storage.request();
                      if (storageStatus != PermissionStatus.granted) {
                        openAppSettings();
                      }
                    } else {
                      _saveQrCode();
                    }
                  },
                ),
                SizedBox(height: 12.sp),
                MyText("点击二维码保存到手机", color2B, 14.sp),
                SizedBox(height: 12.sp),
              ],
            ),
          ),
        ),
      );
    }
    return GestureDetector(
      onTap: hideKeyboard,
      child: Padding(
        padding: EdgeInsets.all(20.sp),
        child: Column(
          children: [
            _buildTypeView(),
            SizedBox(height: 20.sp),
            Expanded(
              child: Container(
                padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.black12,
                  ),
                  borderRadius: BorderRadius.circular(8.sp),
                ),
                child: ListView(
                  children: _buildList(),
                ),
              ),
            ),
            HCustomButton(
              ms: 1000,
              height: 40.sp,
              onPress: _addMonitor,
              initVisible: true,
              text: Lang.add_mooeli_monitor,
              fixedBottom: true,
              bgColor: colorBrand,
            ),
          ],
        ),
      ),
    );
  }

  _saveQrCode() async {
    try {
      RenderRepaintBoundary boundary = repaintKey.currentContext!.findRenderObject()! as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      await ImageGallerySaver.saveImage(byteData!.buffer.asUint8List());
      Global.toast('保存成功');
    } catch (e) {
      //
    }
  }

  List<Widget> _buildList() {
    return [
      Padding(
        padding: EdgeInsets.fromLTRB(0, 20.sp, 0, 12.sp),
        child: MyText("监控协议名称", Colors.black, 16.sp, FontWeight.w500),
      ),
      MyText(inputMonitors[monitorType].name, color2B, 14.sp),
      _buildMultiCheckForm("${inputMonitors[monitorType].configKey}MonitorGoals"),
      _buildTypeExtraView(),
      Padding(
        padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 10.sp),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            MyText("*", Colors.red, 14.sp, FontWeight.w500),
            MyText("首次扫描日期", Colors.black, 16.sp, FontWeight.w500),
          ],
        ),
      ),
      GestureDetector(
        onTap: () async {
          hideKeyboard();
          final DateTime? date = await showDatePicker(
            context: context,
            initialDate: inputMonitors[monitorType].startTime > 0
                ? DateTime.fromMillisecondsSinceEpoch(inputMonitors[monitorType].startTime)
                : DateTime.now(),
            firstDate: DateTime.now(),
            lastDate: DateTime(2030),
            locale: currentLocale,
          );
          if (date != null) {
            setState(() {
              inputMonitors[monitorType].startTime = date!.millisecondsSinceEpoch;
            });
          }
        },
        child: Container(
          height: 36.sp,
          padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: Colors.black12,
            ),
            borderRadius: BorderRadius.circular(8.sp),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              inputMonitors[monitorType].startTime > 0
                  ? MyText(
                      Global.getDateByTimestamp(milliseconds: inputMonitors[monitorType].startTime),
                      color2B,
                      14.sp,
                    )
                  : MyText("请选择日期", Colors.black26, 14.sp),
              Icon(Icons.calendar_month, color: Colors.black26, size: 24.sp),
            ],
          ),
        ),
      ),
      Padding(
        padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 10.sp),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            MyText("*", Colors.red, 14.sp, FontWeight.w500),
            MyText("扫描周期 ", Colors.black, 16.sp, FontWeight.w500),
            MyText("系统将自动按周期提醒患者扫描", color2B, 12.sp),
          ],
        ),
      ),
      Container(
        height: 36.sp,
        padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
        child: Row(
          children: [
            MyText("每", color2B, 14.sp),
            Container(
              height: 32.sp,
              margin: EdgeInsets.fromLTRB(6.sp, 0, 6.sp, 0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black12,
                ),
                borderRadius: BorderRadius.circular(4.sp),
              ),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (inputMonitors[monitorType].cycle > 1) {
                        setState(() {
                          inputMonitors[monitorType].cycle--;
                        });
                      }
                    },
                    child: SizedBox(
                      width: 32.sp,
                      height: 32.sp,
                      child: Icon(
                        Icons.remove,
                        color: color2B,
                        size: 20.sp,
                      ),
                    ),
                  ),
                  Container(width: 1.sp, height: 32.sp, color: Colors.black12),
                  Container(
                      width: 32.sp,
                      alignment: Alignment.center,
                      child: MyText(inputMonitors[monitorType].cycle.toString(), color2B, 14.sp)),
                  Container(width: 1.sp, height: 32.sp, color: Colors.black12),
                  GestureDetector(
                    onTap: () {
                      if (inputMonitors[monitorType].cycle < 30) {
                        setState(() {
                          inputMonitors[monitorType].cycle++;
                        });
                      }
                    },
                    child: SizedBox(
                      width: 32.sp,
                      height: 32.sp,
                      child: Icon(
                        Icons.add,
                        color: color2B,
                        size: 20.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
                height: 32.sp,
                margin: EdgeInsets.fromLTRB(6.sp, 0, 6.sp, 0),
                padding: EdgeInsets.fromLTRB(12.sp, 0, 6.sp, 0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.black12,
                  ),
                  borderRadius: BorderRadius.circular(4.sp),
                ),
                child: DropdownButton<String>(
                  value: inputMonitors[monitorType].cycleUnit,
                  style: TextStyle(color: color2B, fontSize: 14.sp),
                  underline: const SizedBox(height: 0),
                  items: cycleMap.keys
                      .map((key) => DropdownMenuItem(value: key, child: MyText(cycleMap[key]!, color2B, 14.sp)))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      inputMonitors[monitorType].cycleUnit = value!;
                    });
                  },
                )),
            MyText("扫描一次", color2B, 14.sp),
          ],
        ),
      ),
      Padding(
        padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 12.sp),
        child: MyText("患者录入资料", Colors.black, 16.sp, FontWeight.w500),
      ),
      MyText("口内扫描、微笑照", color2B, 14.sp),
      SizedBox(height: 40.sp),
    ];
  }

  _buildTypeView() {
    return Row(
      children: inputMonitors.map((e) => _buildTypeButton(e.type)).toList(),
    );
  }

  Widget _buildTypeButton(int type) {
    return GestureDetector(
      onTap: () {
        setState(() {
          monitorType = type;
        });
      },
      child: Container(
        alignment: Alignment.center,
        width: (1.sw - 58.sp) / inputMonitors.length,
        margin: EdgeInsets.only(left: type == 0 ? 0 : 6.sp),
        constraints: BoxConstraints(minHeight: 36.sp),
        decoration: BoxDecoration(
          border: Border.all(
            color: monitorType == type ? colorBrand : color2B.withOpacity(0.6),
          ),
          color: monitorType == type ? colorBrand.withOpacity(0.4) : Colors.white,
          borderRadius: BorderRadius.circular(48.sp),
        ),
        child: MyText(inputMonitors[type].name, monitorType == type ? colorBrand : color2B, 14.sp),
      ),
    );
  }

  _buildTypeExtraView() {
    switch (monitorType) {
      case 1:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 10.sp),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  MyText("*", colorRed, 14.sp, FontWeight.w500),
                  MyText("矫治器品牌", color2B, 16.sp, FontWeight.w500),
                ],
              ),
            ),
            Container(
              height: 36.sp,
              padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black12,
                ),
                borderRadius: BorderRadius.circular(8.sp),
              ),
              child: TextField(
                controller: inputMonitors[monitorType].brandController,
                textAlign: TextAlign.left,
                maxLength: 100,
                maxLines: 1,
                style: TextStyle(fontSize: 14.sp, color: color2B),
                decoration: InputDecoration(
                  counterText: '',
                  border: InputBorder.none,
                  hintText: "请输入内容",
                  hintStyle: TextStyle(fontSize: 14.sp, color: const Color(0xFFCCCCCC)),
                  isDense: true,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 10.sp),
              child: MyText("矫治器产品", Colors.black, 16.sp, FontWeight.w500),
            ),
            Container(
              height: 36.sp,
              padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black12,
                ),
                borderRadius: BorderRadius.circular(8.sp),
              ),
              child: TextField(
                controller: inputMonitors[monitorType].productController,
                textAlign: TextAlign.left,
                maxLength: 100,
                maxLines: 1,
                style: TextStyle(fontSize: 14.sp, color: color2B),
                decoration: InputDecoration(
                  counterText: '',
                  border: InputBorder.none,
                  hintText: "请输入内容",
                  hintStyle: TextStyle(fontSize: 14.sp, color: const Color(0xFFCCCCCC)),
                  isDense: true,
                ),
              ),
            ),
          ],
        );
      case 2:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 10.sp),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  MyText("*", Colors.red, 14.sp, FontWeight.w500),
                  Expanded(
                    child: MyText("托槽品牌", Colors.black, 16.sp, FontWeight.w500),
                  ),
                ],
              ),
            ),
            Container(
              height: 36.sp,
              padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black12,
                ),
                borderRadius: BorderRadius.circular(8.sp),
              ),
              child: TextField(
                controller: inputMonitors[monitorType].brandController,
                textAlign: TextAlign.left,
                maxLength: 100,
                maxLines: 1,
                style: TextStyle(fontSize: 14.sp, color: color2B),
                decoration: InputDecoration(
                  counterText: '',
                  border: InputBorder.none,
                  hintText: "请输入内容",
                  hintStyle: TextStyle(fontSize: 14.sp, color: const Color(0xFFCCCCCC)),
                  isDense: true,
                ),
              ),
            ),
          ],
        );
        break;
      default:
        return const SizedBox();
    }
  }

  _buildMultiCheckForm(String? key) {
    if (isEmpty(key)) {
      return const SizedBox();
    }
    List<Widget> children = [];
    List texts = (configJson[key] as Map).values.map((e) => e["data"]).toList();
    children.add(Padding(
      padding: EdgeInsets.fromLTRB(0, 24.sp, 0, 10.sp),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          MyText("*", Colors.red, 14.sp, FontWeight.w500),
          Expanded(
            child: MyText(texts[0], Colors.black, 16.sp, FontWeight.w500),
          ),
        ],
      ),
    ));
    for (int i = 1; i < texts.length; i++) {
      String text = texts[i];
      children.add(
        GestureDetector(
          onTap: () {
            hideKeyboard();
            setState(() {
              if (!inputMonitors[monitorType].goals.contains(text)) {
                inputMonitors[monitorType].goals.add(text);
              } else {
                inputMonitors[monitorType].goals.remove(text);
              }
            });
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                fillColor: MaterialStateProperty.resolveWith((Set<MaterialState> states) {
                  return color2B.withOpacity(0.6);
                }),
                checkColor: color2B,
                value: inputMonitors[monitorType].goals.contains(text),
                onChanged: (value) {
                  hideKeyboard();
                  setState(() {
                    if (value!) {
                      if (!inputMonitors[monitorType].goals.contains(text)) {
                        inputMonitors[monitorType].goals.add(text);
                      }
                    } else {
                      inputMonitors[monitorType].goals.remove(text);
                    }
                  });
                },
              ),
              Expanded(
                child: MyText(
                  text,
                  color2B,
                  14.sp,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  _addMonitor() {
    if (isEmpty(inputMonitors[monitorType].goals)) {
      Global.toast("请选择监控目标！");
      return;
    }
    if (inputMonitors[monitorType].startTime <= 0) {
      Global.toast("请选择首次扫描日期！");
      return;
    }
    if (isEmpty(inputMonitors[monitorType].brandController.text)) {
      if (monitorType == 1) {
        Global.toast("请选择矫治器品牌！");
        return;
      } else if (monitorType == 2) {
        Global.toast("请选择托槽品牌！");
        return;
      }
    }

    Map extendMap = {};
    if (monitorType == 1) {
      extendMap = {
        "orthodonticBrand": inputMonitors[monitorType].brandController.text,
        "orthodonticProduct": inputMonitors[monitorType].productController.text,
      };
    } else if (monitorType == 2) {
      extendMap = {
        "bracketBrand": inputMonitors[monitorType].brandController.text,
      };
    }

    Map param = {
      "caseId": widget.mooeli.caseId,
      "name": inputMonitors[monitorType].name,
      "gaol": inputMonitors[monitorType].goals,
      "firstScanTime": inputMonitors[monitorType].startTime,
      "timeNumber": inputMonitors[monitorType].cycle,
      "timeUnit": inputMonitors[monitorType].cycleUnit,
      "essentialMaterialList": (configJson["PatientEntryData"] as Map)
          .values
          .where((element) => element["number"] != 0)
          .map((e) => {"code": e["code"], "showWord": e["data"]})
          .toList(),
      "tagList": (configJson["${inputMonitors[monitorType].configKey}HotTag"] as Map)
          .values
          .where((element) => element["number"] != 0)
          .map((e) => e["data"])
          .toList(),
      "extendedInfo": extendMap,
    };

    HHttp.request(
      "/v3/monitor/add",
      "POST",
      (data) {
        setState(() {
          bindCode = data["bindCode"];
          bindUser = data["caseName"];
        });
        eventBus.fire(EventAddMonitor(widget.mooeli.caseId));
      },
      params: param,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Lang.add_mooeli_monitor),
      ),
      body: _buildBody(),
    );
  }
}
