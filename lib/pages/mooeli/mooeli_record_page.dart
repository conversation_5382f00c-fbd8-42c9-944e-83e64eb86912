import 'dart:collection';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/lingya_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/model/scan_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/scan/ScanManager.dart';
import 'package:mooeli/pages/scan/ToothCameraView.dart';
import 'package:mooeli/pages/web/web_report_page.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:sprintf/sprintf.dart';

class MooeliRecordPage extends BasePage {
  MooeliItem mooeli;
  ScanInfo? scanInfo;
  String? recordId;
  bool singleRecord; //true-地推，false-监控

  MooeliRecordPage(
    this.mooeli, {
    Key? key,
    this.recordId,
    this.scanInfo,
    this.singleRecord = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MooeliRecordPageState();
  }
}

class MooeliRecordPageState extends BasePageState<MooeliRecordPage> {
  dynamic configJson = {};
  final GlobalKey _buttonKey = GlobalKey<HCustomButtonState>();

  final double headerHeight = 32.sp, itemHeight = 48.sp;

  bool loaded = false;

  List<MooeliPhase> phaseList = [];
  MooeliRecord selectedRecord = MooeliRecord();
  Map<String, List<MooeliRecord>> recordMap = {};

  List<MooeliFile> fileList = [];
  int status = 0, algorithmStatus = 0;
  String? diseaseYati, diseaseYazhou, diseaseYalie, patientFeedback;
  bool isProviderFeedback = false;
  bool hasAlgorithm = false, hasAiResult = false;
  Widget? fitWidget;
  List allTeeth = []; //String
  String lostToothText = "";
  String extractToothText = "";

  dynamic algorithmJson;
  List convertList = [];

  String stageText = "";

  ScanInfo scanInfo = ScanInfo();

  bool aiResultEdited = false;
  bool hideAiResult = false;
  bool teethSpace = false; //缺牙间隙

  @override
  initState() {
    super.initState();
    getMooeliConfigJson((json) {
      setState(() {
        configJson = json;
      });
    });
    if (widget.scanInfo != null) {
      scanInfo = widget.scanInfo!;
      addEventListener<EventUploadRecordResult>((event) {
        if (scanInfo.recordId == event.record.recordId) {
          setState(() {
            scanInfo = event.record;
          });
        }
      });
      getScanConfigJson((json) {
        if (isNotEmpty(json) && isNotEmpty(json["quickScanV3_conversionProject"])) {
          Map map = json["quickScanV3_conversionProject"] as Map;
          List list = map.values.toList();
          setState(() {
            convertList = list;
          });
        }
        if (isNotEmpty(json) && isNotEmpty(json["quickScanV3_progressStage"])) {
          Map stageMap = json["quickScanV3_progressStage"];
          for (Map stage in stageMap.values) {
            if (stage["code"] == scanInfo.progressStage) {
              setState(() {
                stageText = stage["data-$lang"];
              });
            }
          }
        }
      });

      for (String key in faceMap.keys) {
        addFaceFile(int.parse(key), scanInfo.getPhoto(key));
      }
      addMouthFile(108, scanInfo.upPhotos, 0);
      addMouthFile(105, scanInfo.closePhotos, 0);
      addMouthFile(103, scanInfo.closePhotos, 1);
      addMouthFile(107, scanInfo.closePhotos, 2);
      addMouthFile(152, scanInfo.openPhotos, 0);
      addMouthFile(151, scanInfo.openPhotos, 1);
      addMouthFile(153, scanInfo.openPhotos, 2);
      addMouthFile(155, scanInfo.maskPhotos, 0);
      addMouthFile(154, scanInfo.maskPhotos, 1);
      addMouthFile(156, scanInfo.maskPhotos, 2);
      addMouthFile(106, scanInfo.downPhotos, 0);
      logger("recordId: ${widget.recordId}");
      if (scanInfo.status != RecordStatus.completeUpload) {
        loaded = true;
      } else {
        selectedRecord.recordId = widget.recordId!;
        getFileList();
      }
    } else {
      addEventListener<EventFeedback>((event) {
        if (event.recordId == selectedRecord.recordId) {
          setState(() {
            isProviderFeedback = event.feedback;
          });
          if (_buttonKey.currentState != null) {
            (_buttonKey.currentState as HCustomButtonState)
                .setText(isProviderFeedback ? Lang.view_feedback : Lang.send_feedback);
          }
        }
      });
      getPhaseList();
    }
  }

  addFaceFile(int type, String path) {
    if (isNotEmpty(path)) {
      MooeliFile file = MooeliFile();
      file.id = "${scanInfo.recordId}_$type";
      file.attribute = type.toString();
      file.imagePath = path;
      fileList.add(file);
    }
  }

  addMouthFile(int type, List list, int index) {
    if (isNotEmpty(list) && list.length > index) {
      MooeliFile file = MooeliFile();
      file.id = "${scanInfo.recordId}_$type";
      file.attribute = type.toString();
      file.imagePath = list[index];
      fileList.add(file);
    }
  }

  getPhaseList() {
    HHttp.request(
      "/v3/monitor/page",
      "POST",
      (data) {
        setState(() {
          phaseList = (data["monitorList"] as List).map((i) => MooeliPhase.fromJson(i)).toList();
        });
        if (isNotEmpty(phaseList)) {
          getRecordList(phaseList.first, true);
        }
      },
      params: {
        "caseId": widget.mooeli.caseId,
        "pageCount": 50,
        "pageIndex": 0,
      },
    );
  }

  getRecordList(MooeliPhase phase, bool selected) {
    HHttp.request(
      "/v3/monitor/record/page",
      "POST",
      (data) {
        setState(() {
          List<MooeliRecord> recordList = (data['recordList'] as List).map((i) => MooeliRecord.fromJson(i)).toList();
          if (isNotEmpty(recordList)) {
            recordMap[phase.phaseId] = recordList;

            if (isNotEmpty(widget.recordId)) {
              for (MooeliRecord record in recordList) {
                if (record.recordId == widget.recordId) {
                  selectedRecord = record;
                  selectedRecord.phaseName = phase.phaseName;
                  getFileList();
                  break;
                }
              }
            }
          } else {
            setState(() {
              loaded = true;
            });
          }

          if (selected) {
            if (isEmpty(widget.recordId)) {
              if (isNotEmpty(recordList)) {
                selectedRecord = recordList.first;
              }
              selectedRecord.phaseName = phase.phaseName;
              getFileList();
            }
            for (int i = 1; i < phaseList.length; i++) {
              getRecordList(phaseList[i], false);
            }
          }
        });
      },
      params: {
        "monitorId": phase.phaseId,
        "pageCount": 50,
        "pageIndex": 0,
      },
    );
  }

  getFileList([bool onlyShowStatus = false]) {
    if (isEmpty(selectedRecord.recordId)) {
      setState(() {
        loaded = true;
      });
      return;
    }
    HHttp.request(
      widget.singleRecord ? "/v3/quickscan/scanRecord/get" : "/v3/monitor/record/get",
      "POST",
      (data) {
        setState(() {
          status = getJsonInt(data, "status");
          loaded = true;
          if (!onlyShowStatus) {
            fileList =
                (data["fileInfoList"] as Map).values.map((i) => MooeliFile.fromJson(i, widget.singleRecord)).toList();
            if (isNotEmpty(fileList)) {
              List<String> ids = [];
              for (var file in fileList) {
                if (isNotEmpty(file.id)) {
                  ids.add(file.id);
                }
              }
              downloadOrigin(
                ids,
                (id, path) {
                  for (var file in fileList) {
                    if (file.id == id) {
                      setState(() {
                        file.imagePath = path;
                      });
                    }
                  }
                },
                widget.singleRecord ? "RECORD_QUICKSCAN" : "RECORD_MONITOR",
              );
            }
          }
          if (isNotEmpty(data["algorithmInfoMap"]) && isNotEmpty((data["algorithmInfoMap"] as Map).values)) {
            String algorithmId = (data["algorithmInfoMap"] as Map).values.first;
            hasAlgorithm = true;
            getAlgorithmResult(
              algorithmId,
              (json) {
                algorithmJson = json;
                aiResultEdited = json["edited"] ?? false;
                List missList = json["miss_teeth_list"] ?? [];
                if (isNotEmpty(missList)) {
                  lostToothText = "${Lang.suspended}${Lang.lost_teeth}：${missList.join("、")}";
                }
                teethSpace = isNotEmpty(getJsonList(json, "miss_teeth_space"));
                List extractList = getJsonList(json, "teeth_extraction_list");
                if (isNotEmpty(extractList)) {
                  extractToothText = "${Lang.suspended}${Lang.extract_teeth}：${extractList.join("、")}";
                }
                getDiseaseAnalyticsResult(json);
                getFitAnalyticsResult(json);

                try {
                  List<String> positions = [
                    "front",
                    "left",
                    "lower",
                    "right",
                    "upper",
                    "open_front",
                    "open_left",
                    "open_right",
                  ];
                  Set teeth = {};
                  for (String pos in positions) {
                    try {
                      logger("getMissTeeth $pos -> : ${(json["image"][pos]["teeth"] as Map).keys.toList()}");
                      teeth.addAll((json["image"][pos]["teeth"] as Map).keys.toList());
                    } catch (ex) {
                      //
                    }
                  }
                  allTeeth.clear();
                  allTeeth.addAll(teeth);
                  getMissTeeth(allTeeth, double.parse(scanInfo.age));
                  HHttp.request(
                    "/v3/quickscan/scanRecord/getPdfInfo",
                    "POST",
                    (data) {
                      try {
                        double age = data["pdfInfo"]["baseInfo"]["age"];
                        getMissTeeth(allTeeth, age);
                      } catch (ex) {
                        logger("getPdfInfo $ex");
                      }
                    },
                    isShowErrorToast: false,
                    params: {
                      "onlyShowStatus": false,
                      "scanRecordId": scanInfo.recordId,
                    },
                  );
                } catch (ex) {
                  logger("getMissTeeth error:$ex");
                }
                setState(() {
                  hasAiResult = true;
                });
              },
              useJson: true,
            );
          }
          algorithmStatus = getJsonInt(data, "algorithmStatus");
          // if (algorithmStatus == 5 || algorithmStatus == 7) {
          //   HHttp.request(
          //     "/v2/analysis/forceReplayByRecordAndSource",
          //     "POST",
          //     (data) {
          //       delay(1000, () {
          //         getFileList(true);
          //       });
          //     },
          //     params: {
          //       "id": selectedRecord.recordId,
          //       "sourceType": "5006" // 字符串类型
          //     },
          //   );
          // }
          if (onlyShowStatus) {
            if (algorithmStatus == 6) {
              delay(1000, () {
                getFileList(true);
              });
            }
          } else {
            if (loaded) {
              if (algorithmStatus == 6) {
                delay(1000, () {
                  getFileList(true);
                });
              }
            } else {
              delay(1000, () {
                getFileList();
              });
            }
            scanInfo.tagList = data["tagList"] as List;
            scanInfo.note = getJsonString(data, "note");
            scanInfo.recordSn = getJsonString(data, "qrCodeId");
            patientFeedback = data["patientFeedback"];
            isProviderFeedback = data["isProviderFeedback"];
          }
          if (_buttonKey.currentState != null) {
            (_buttonKey.currentState as HCustomButtonState)
                .setText(isProviderFeedback ? Lang.view_feedback : Lang.send_feedback);
          }
        });
      },
      params: {
        widget.singleRecord ? "scanRecordId" : "id": selectedRecord.recordId,
        // "onlyShowStatus": onlyShowStatus,
      },
    );
  }

  void getDiseaseAnalyticsResult(json) {
    String result = "";
    Map<int, Set> map = {};
    try {
      for (dynamic direction in (json["image"] as Map).values) {
        for (dynamic descriptions in (direction["disease"] as Map).values) {
          if (isNotEmpty(descriptions["map"] as List)) {
            if (map[descriptions["label"]] == null) {
              map[descriptions["label"]] = <dynamic>{};
            }
            map[descriptions["label"]]!.addAll(descriptions["map"]);
            try {
              if (descriptions["label"] == 13 && descriptions["degree"] >= 0.8) {
                setState(() {
                  teethSpace = true;
                });
              }
            } catch (ex) {
              //
            }
          }
        }
      }
    } catch (ex) {
      //
    }
    if (isNotEmpty(map)) {
      setState(() {
        diseaseYati = getDiseaseText(map, [2, 8, 4, 0, 1, 5, 3, 6, 7, 9, 11]);
        // diseaseYazhou = getDiseaseText(map, [8, 7]);
        diseaseYalie = getDiseaseText(map, [10, 12, 13]);
      });
    }
  }

  String getDiseaseText(Map<int, Set> map, List<int> ids) {
    String result = "";
    for (int id in ids) {
      if (id == 10) {
        if (isNotEmpty(extractToothText)) {
          if (isNotEmpty(result)) {
            result = "$result\n";
          }
          result = "$result$extractToothText";
        }
        if (isNotEmpty(lostToothText)) {
          if (isNotEmpty(result)) {
            result = "$result\n";
          }
          result = "$result$lostToothText";
        }
        if (teethSpace) {
          String spaceText = "${Lang.suspended}${Lang.space_teeth}";
          if (!result.contains(spaceText)) {
            if (isNotEmpty(result)) {
              result = "$result\n";
            }
            result = "$result$spaceText";
          }
        }
      }
      if (diseaseNameMap.containsKey(id.toString()) && map.containsKey(id)) {
        if (id == 12 || id == 13) {
          if (isNotEmpty(result)) {
            result = "$result\n";
          }
          result = "$result${Lang.suspended}${diseaseNameMap[id.toString()]?.toLowerCase()}";
        } else {
          //牙髓病变只显示手动修改结果
          bool showResult = aiResultEdited || id != 9 && id != 10 && id != 11;
          // if (showResult) {
          //   showResult = widget.recordType != 2 || id != 6 && id != 7 && id != 8;
          // }
          if (showResult) {
            if (isNotEmpty(result)) {
              result = "$result\n";
            }
            result =
                "$result${Lang.suspended}${diseaseNameMap[id.toString()]?.toLowerCase()}：${SplayTreeSet.from(map[id] as Set, (int a, int b) => a - b).join("、")}";
          }
        }
      }
    }
    if (isEmpty(result)) {
      result = Lang.teeth_good;
    }
    return result;
  }

  Map teeth_id_map = {
    "upper": [17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27],
    "lower": [47, 46, 45, 44, 43, 42, 41, 31, 32, 33, 34, 35, 36, 37],
    "milk_upper": [55, 54, 53, 52, 51, 61, 62, 63, 64, 65],
    "milk_lower": [85, 84, 83, 82, 81, 71, 72, 73, 74, 75],
  };

//恒牙乳牙对照
  Map milk_permanent_relation = {
    55: 15,
    54: 14,
    53: 13,
    52: 12,
    51: 11,
    61: 21,
    62: 22,
    63: 23,
    64: 24,
    65: 25,
    85: 45,
    84: 44,
    83: 43,
    82: 42,
    81: 41,
    71: 31,
    72: 32,
    73: 33,
    74: 34,
    75: 35,
  };

  getMissTeeth(List teeth_list, double age) {
    if (isNotEmpty(lostToothText)) {
      return;
    }
    if (isEmpty(teeth_list)) {
      setState(() {
        lostToothText = Lang.lost_all_teeth;
      });
    }
    if (!checkContainTeethList(teeth_list, [...teeth_id_map["upper"], ...teeth_id_map["milk_upper"]])) {
      setState(() {
        lostToothText = Lang.lost_upper_teeth;
      });
    }
    if (!checkContainTeethList(teeth_list, [...teeth_id_map["lower"], ...teeth_id_map["milk_lower"]])) {
      setState(() {
        lostToothText = Lang.lost_lower_teeth;
      });
    }
    dynamic type = checkTeethListType(teeth_list);
    logger("getMissTeeth: type $type");
    if (type == 'else') {
      setState(() {
        lostToothText = Lang.lost_all_teeth;
      });
    } else {
      List miss_list = [];
      if (type == 'permanent') {
        miss_list = getMissTeethList(teeth_list, [...teeth_id_map["upper"], ...teeth_id_map["lower"]], age);
      } else if (type == 'milk') {
        miss_list = getMissTeethList(teeth_list, [...teeth_id_map["milk_upper"], ...teeth_id_map["milk_lower"]], age);
      } else {
        miss_list = handleMixTeethList(
            getMissTeethList(
                teeth_list,
                [
                  ...teeth_id_map["upper"],
                  ...teeth_id_map["lower"],
                  ...teeth_id_map["milk_upper"],
                  ...teeth_id_map["milk_lower"]
                ],
                age),
            teeth_list,
            age);
      }
      if (isNotEmpty(miss_list)) {
        setState(() {
          lostToothText = "${Lang.suspended}${Lang.lost_teeth}：${miss_list.join("、")}";
        });
      }
    }
    getDiseaseAnalyticsResult(algorithmJson);
    logger("getMissTeeth lostToothText $lostToothText");
  }

  bool checkContainTeethList(List allList, List list_2) {
    // logger("getMissTeeth: checkContainTeethList $list_1 $list_2");
    bool contain = false;
    for (int i in list_2) {
      if (allList.contains(i.toString())) contain = true;
    }
    logger("getMissTeeth: checkContainTeethList $contain");
    return contain;
  }

//判断是混合牙列还是其他
  checkTeethListType(list) {
    logger("getMissTeeth: checkTeethListType");
    dynamic permanent_list = [...teeth_id_map["upper"], ...teeth_id_map["lower"]];
    dynamic milk_list = [...teeth_id_map["milk_upper"], ...teeth_id_map["milk_lower"]];
    dynamic if_permanent = checkContainTeethList(list, permanent_list);
    dynamic if_milk = checkContainTeethList(list, milk_list);
    return if_permanent && !if_milk ? 'permanent' : (if_permanent && if_milk ? 'mix' : (if_milk ? 'milk' : 'else'));
  }

//获取缺失牙list
  getMissTeethList(List allList, List list_2, double age) {
    logger("getMissTeeth: getMissTeethList $allList $list_2");
    List miss_list = [];

    for (int teeth_id in list_2) {
      if (!allList.contains(teeth_id.toString())) {
        if (age > 12 || teeth_id % 10 != 7) {
          miss_list.add(teeth_id);
        }
      }
    }
    //小于等于12岁不考虑七号牙
    // if (age <= 12) {
    //   miss_list.map((teeth_id, index) {
    //     String str = teeth_id.toString();
    //     if (str[str.length - 1] == '7') miss_list.splice(index, 1);
    //   });
    // }
    logger("getMissTeeth miss_list: $miss_list");
    return miss_list;
  }

//混合牙列缺失牙特殊处理
  handleMixTeethList(List list, teeth_get, double age) {
    logger("getMissTeeth: handleMixTeethList");
    List miss_list = [];
    for (int teeth_id in list) {
      if (!milk_permanent_relation.containsKey(teeth_id) && !milk_permanent_relation.values.contains(teeth_id)) {
        if (age > 12 || teeth_id % 10 != 7) {
          miss_list.add(teeth_id);
        }
      } else if (milk_permanent_relation.containsKey(teeth_id) &&
          !teeth_get.contains(milk_permanent_relation[teeth_id].toString())) {
        if (age > 12 || teeth_id % 10 != 7) {
          miss_list.add(teeth_id);
        }
      }
    }
    // if (age <= 12) {
    //   miss_list.map((teeth_id, index) =>
    //   {
    //     String str = teeth_id.toString()
    //     if(str[str.length - 1] == '7') miss_list.splice(index, 1)
    //   });
    // }
    return miss_list;
  }

  void getFitAnalyticsResult(json) {
    List<Widget> children = [];
    fitText(String text) {
      return HeightText(text, color2B, 24.sp, 1.6);
    }

    try {
      if (json["image"]["left"]["info"]["molar"]["type"] != null ||
          json["image"]["right"]["info"]["molar"]["type"] != null) {
        String result = "";
        if (json["image"]["left"]["info"]["molar"]["type"] != null) {
          result = "${Lang.left_side} ${getMolarDesc(json["image"]["left"]["info"]["molar"]["type"])}";
        }
        if (json["image"]["right"]["info"]["molar"]["type"] != null) {
          result =
              "${isNotEmpty(result) ? "$result、" : ""}${Lang.right_side} ${getMolarDesc(json["image"]["right"]["info"]["molar"]["type"])}";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("${Lang.molar_relationship}："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["left"]["info"]["canine"]["type"] != null ||
          json["image"]["right"]["info"]["canine"]["type"] != null) {
        String result = "";
        if (json["image"]["left"]["info"]["canine"]["type"] != null) {
          result = "${Lang.left_side} ${getMolarDesc(json["image"]["left"]["info"]["canine"]["type"], true)}";
        }
        if (json["image"]["right"]["info"]["canine"]["type"] != null) {
          result =
              "${isNotEmpty(result) ? "$result、" : ""}${Lang.right_side} ${getMolarDesc(json["image"]["right"]["info"]["canine"]["type"], true)}";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("${Lang.canine_relationship}："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
      }
    } catch (ex) {
      //
    }
    try {
      children.add(
        Padding(
          padding: EdgeInsets.only(top: 8.sp),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              fitText("${Lang.midline_relationship}："),
              Expanded(
                child: fitText(getMidDesc(json["image"]["front"]["info"]["middle_line"]["type"])),
              ),
            ],
          ),
        ),
      );
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["front"]["info"]["bite"]["type"] > -5) {
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("${Lang.overbite_relationship}："),
                Expanded(
                  child: fitText(getBiteDesc(json["image"]["front"]["info"]["bite"]["type"])),
                ),
              ],
            ),
          ),
        );
      }
    } catch (ex) {
      //
    }
    try {
      if (json["image"]["left"]["info"]["overjet"]["type"] > -3) {
        String result = getOverjetDesc(json["image"]["left"]["info"]["overjet"]["type"]);
        if (json["image"]["left"]["info"]["overjet"]["ref"] != null) {
          double ref = json["image"]["left"]["info"]["overjet"]["ref"];
          // result = "$result、${Lang.tooth_overjet} ${ref.toStringAsFixed(2)} mm";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("${Lang.overjet_relationship}："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
      } else if (json["image"]["right"]["info"]["overjet"]["type"] > -3) {
        String result = getOverjetDesc(json["image"]["right"]["info"]["overjet"]["type"]);
        if (json["image"]["right"]["info"]["overjet"]["ref"] != null) {
          double ref = json["image"]["right"]["info"]["overjet"]["ref"];
          // result = "$result、${Lang.tooth_overjet} ${ref.toStringAsFixed(2)} mm";
        }
        children.add(
          Padding(
            padding: EdgeInsets.only(top: 8.sp),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                fitText("${Lang.overjet_relationship}："),
                Expanded(
                  child: fitText(result),
                ),
              ],
            ),
          ),
        );
      }
    } catch (ex) {
      //
    }
    if (isNotEmpty(children)) {
      setState(() {
        fitWidget = Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 16.sp, 8.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorPurpleLavender,
                ),
                child: MyText(Lang.bat_relation, color2B, 24.sp, FontWeight.w600),
              ),
              ...children,
            ],
          ),
        );
      });
    }
  }

  getNavigatorView() {
    List<Widget> children = [
      Click(
        onTap: pop,
        child: Container(
          height: 56.sp,
          margin: EdgeInsets.only(bottom: 16.sp),
          padding: EdgeInsets.all(8.sp),
          decoration: BoxDecoration(color: colorBlueDeep, borderRadius: BorderRadius.circular(8.sp)),
          child: Row(
            children: [
              Image.asset(
                "res/icons/icon_back_white.png",
                height: 32.sp,
              ),
              SizedBox(width: 4.sp),
              MyText(Lang.back, Colors.white, 24.sp)
            ],
          ),
        ),
      ),
    ];
    return Container(
      width: 248.sp,
      height: 1.sh,
      decoration: BoxDecoration(color: Colors.white.withOpacity(0.6), borderRadius: BorderRadius.circular(16.sp)),
      margin: EdgeInsets.fromLTRB(24.sp, 16.sp, 24.sp, 24.sp),
      padding: EdgeInsets.all(16.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            getNavigatorView(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _getListView(),
                ),
              ),
            ),
            Container(
                width: 248.sp,
                height: 1.sh,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(16.sp),
                ),
                margin: EdgeInsets.fromLTRB(24.sp, 24.sp, 24.sp, 24.sp),
                padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
                child: Column(
                  children: [
                    const Spacer(),
                    HCustomButton(
                      key: UniqueKey(),
                      height: 56.sp,
                      fontSize: 24.sp,
                      fixedBottom: false,
                      radius: 8.sp,
                      onPress: () {
                        makeReport();
                      },
                      bgColor: colorBlueDeep,
                      child: MyText(Lang.make_report, Colors.white, 24.sp),
                    ),
                  ],
                ))
          ],
        ),
      ),
      drawer: widget.singleRecord ? null : _getDrawerView(),
    );
  }

  @override
  void onRouteResume(Route nextRoute) {
    //这里是防止图片浏览魔法效果返回后白屏的现象
    setState(() {});
  }

  //是否包含types中任意一种
  bool containTypes(List<int> types) {
    if (isNotEmpty(fileList)) {
      for (MooeliFile file in fileList) {
        for (int type in types) {
          if (file.attribute == type.toString()) {
            return true;
          }
        }
      }
    }
    return false;
  }

  List<MooeliFile> getFilesByType(List<int> types) {
    List<MooeliFile> list = [];
    for (int type in types) {
      MooeliFile? file = getFileByType(type);
      if (file != null) {
        list.add(file);
      }
    }
    return list;
  }

  MooeliFile? getFileByType(int type) {
    if (isNotEmpty(fileList)) {
      for (MooeliFile file in fileList) {
        if (file.attribute == type.toString()) {
          return file;
        }
      }
    }
    return null;
  }

  getMouthPhotoView(int type, String defaultImage) {
    MooeliFile? file = getFileByType(type);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 12.sp),
        ClipRRect(
          borderRadius: BorderRadius.circular(16.sp),
          child: SizedBox(
            width: 94.sp,
            height: 65.sp,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  "res/imgs/$defaultImage",
                  width: 94.sp,
                  height: 65.sp,
                  fit: BoxFit.cover,
                ),
                file != null
                    ? GestureDetector(
                        onTap: () {
                          List<int> types = [108, 105, 103, 107, 152, 151, 153, 155, 154, 156, 106];
                          int index = 0;
                          List<MooeliFile> files = [];
                          for (int t in types) {
                            MooeliFile? f = getFileByType(t);
                            if (f != null) {
                              if (t == type) {
                                index = files.length;
                              }
                              files.add(f);
                            }
                          }
                          viewMooeliImages(fileList: files, index: index);
                        },
                        child: Image.file(
                          File(file.imagePath),
                          width: 94.sp,
                          height: 65.sp,
                          fit: BoxFit.cover,
                        ),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ),
        SizedBox(height: 4.sp),
        MyText(categoryMap[type.toString()]!, color2B, 12.sp),
      ],
    );
  }

  Widget getFaceItemView(MooeliFile file, [bool isLast = false]) {
    String title = faceMap[file.attribute] ?? "";
    return Container(
      width: 190.sp,
      margin: EdgeInsets.only(right: isLast ? 0 : 24.sp),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.sp),
          MyText(title, color7C, 24.sp),
          SizedBox(height: 8.sp),
          ClipRRect(
            borderRadius: BorderRadius.circular(9.3.sp),
            child: isNotEmpty(file.imagePath)
                ? GestureDetector(
                    onTap: () {
                      int index = 0;
                      List<String> imgList = [];
                      List<String> titleList = [];
                      for (String key in faceMap.keys) {
                        MooeliFile? f = getFileByType(int.parse(key));
                        if (f != null) {
                          if (key == file.attribute) {
                            index = titleList.length;
                          }
                          titleList.add(faceMap[key]);
                          imgList.add(f.imagePath);
                        }
                      }
                      Global.showPhotoBrowerModal(
                        context,
                        imgPathList: imgList,
                        titles: titleList,
                        defaultShowIndex: index,
                        initScale: 0.75,
                      );
                    },
                    child: Image.file(
                      File(getLocalPath(file.imagePath)),
                      width: 190.sp,
                      height: 190.sp,
                      fit: BoxFit.cover,
                    ),
                  )
                : const SizedBox(),
          ),
        ],
      ),
    );
  }

  getFacePhotoView() {
    List<int> types = faceMap.keys.map((e) => int.parse(e)).toList();
    List<MooeliFile> faceFiles = [];
    for (int type in types) {
      MooeliFile? file = getFileByType(type);
      if (file != null) {
        faceFiles.add(file);
      }
    }
    if (isEmpty(faceFiles)) {
      return const SizedBox();
    }

    List<Widget> children = [];
    for (int i = 0; i < faceFiles.length; i++) {
      children.add(getFaceItemView(faceFiles[i], i == faceFiles.length - 1));
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  retryAnalysis() {
    HHttp.request(
      "/v3/analysis/forceReplayByRecordAndSource",
      "POST",
      (data) {
        setState(() {
          algorithmStatus = 6;
        });
        delay(1000, () {
          getFileList(true);
        });
      },
      params: {
        "sourceId": selectedRecord.recordId,
        "sourceType": 4003,
        "taskType": 5006,
      },
    );
  }

  makeReport() {
    User.instance.refreshToken(okCallback: () {
      push(WebReportPage(scanInfo));
    });
  }

  getAiFailView(String text) {
    return Container(
      alignment: Alignment.topCenter,
      child: Column(
        children: [
          HeightText(text, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
          SizedBox(height: 24.sp),
          Click(
            ms: 5000,
            onTap: retryAnalysis,
            child: Container(
                padding: EdgeInsets.fromLTRB(20.sp, 4.sp, 20.sp, 4.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.sp),
                  border: Border.all(color: colorBrand),
                ),
                child: MyText(Lang.retry_analysis, colorBrand, 24.sp, FontWeight.w500)),
          ),
        ],
      ),
    );
  }

  List<Widget> _getListView() {
    List<Widget> children = [];
    List<Widget> aiList = [
      MyText(Lang.ai_analytics, color2B, 32.sp, FontWeight.w500),
      SizedBox(height: 16.sp),
    ];
    logger("aiList algorithmStatus: $algorithmStatus");
    if (algorithmStatus >= 5 && algorithmStatus <= 8) {
      switch (algorithmStatus) {
        case 5:
        case 6:
          aiList.add(
            Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.fromLTRB(0, 100.sp, 0, 100.sp),
              child: HeightText(Lang.ai_status_analyzing, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
            ),
          );
          break;
        case 7:
          aiList.add(
            Container(
              alignment: Alignment.topCenter,
              child: getAiFailView(Lang.ai_status_overtime),
            ),
          );
          break;
        case 8:
          if (!hasAiResult) {
            aiList.add(
              Container(
                alignment: Alignment.topCenter,
                child: HeightText(Lang.ai_status_analyzing, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
              ),
            );
            break;
          }
          if (!hasAlgorithm) {
            aiList.add(
              Container(
                alignment: Alignment.topCenter,
                child: getAiFailView(Lang.ai_status_fail),
              ),
            );
            break;
          }
          bool hasResult = false;
          if (isNotEmpty(diseaseYati)) {
            hasResult = true;
            aiList.add(
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 16.sp, 8.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorPurpleLavender,
                ),
                child: Row(
                  children: [
                    MyText(Lang.tooth_body, color2B, 24.sp, FontWeight.w600),
                    SizedBox(width: 8.sp),
                    Click(
                      onTap: () {
                        Global.showCustomDialog(
                          Center(
                            child: Container(
                              width: 616.sp,
                              padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 16.sp),
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: BorderRadius.all(Radius.circular(16.sp)),
                              ),
                              child: HeightText(
                                  Lang.tooth_body_ai_tip, Colors.white, 24.sp, 1.6, FontWeight.w400, TextAlign.center),
                            ),
                          ),
                        );
                      },
                      child: Image.asset("res/icons/brand_question.png", width: 32.sp),
                    ),
                  ],
                ),
              ),
            );
            logger("diseaseYati $diseaseYati");
            if (lostToothText == Lang.lost_all_teeth) {
              diseaseYati = Lang.cannot_judge;
            }
            if (isEmpty(diseaseYati)) {
              diseaseYati = Lang.teeth_good;
            }
            if (isNotEmpty(diseaseYati)) {
              hasResult = true;
              aiList.add(
                HeightText(diseaseYati!, color2B, 24.sp, 2.0),
              );
            }
          }
          // if (isNotEmpty(diseaseYazhou)) {
          //   hasResult = true;
          //   aiList.add(
          //     Container(
          //       width: double.infinity,
          //       margin: EdgeInsets.only(top: 16.sp),
          //       padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 16.sp, 8.sp),
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.all(Radius.circular(4.sp)),
          //         color: colorPurpleLavender,
          //       ),
          //       child: MyText(Lang.tooth_around, color2B, 24.sp, FontWeight.w600),
          //     ),
          //   );
          //   aiList.add(
          //     HeightText(diseaseYazhou!, color2B, 24.sp, 2.0),
          //   );
          // }
          if (isEmpty(diseaseYalie) && isNotEmpty(lostToothText)) {
            diseaseYalie = lostToothText;
          }
          if (teethSpace) {
            String spaceText = "${Lang.suspended}${Lang.space_teeth}";
            if (isEmpty(diseaseYalie) || diseaseYalie == Lang.teeth_good) {
              diseaseYalie = spaceText;
            } else if (!(diseaseYalie ?? "").contains(spaceText)) {
              diseaseYalie = "$spaceText\n${diseaseYalie ?? ""}";
            }
          }
          if (diseaseYalie == Lang.teeth_good) {
            diseaseYalie = Lang.teeth_list_good;
          }
          if (isNotEmpty(diseaseYalie)) {
            hasResult = true;
            aiList.add(
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 16.sp),
                padding: EdgeInsets.fromLTRB(16.sp, 8.sp, 16.sp, 8.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.sp)),
                  color: colorPurpleLavender,
                ),
                child: Row(
                  children: [
                    MyText(Lang.tooth_list, color2B, 24.sp, FontWeight.w600),
                    SizedBox(width: 8.sp),
                    Click(
                      onTap: () {
                        Global.showCustomDialog(
                          Center(
                            child: Container(
                              width: 616.sp,
                              padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 32.sp, 16.sp),
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: BorderRadius.all(Radius.circular(16.sp)),
                              ),
                              child: HeightText(
                                  Lang.tooth_list_ai_tip, Colors.white, 24.sp, 1.6, FontWeight.w400, TextAlign.center),
                            ),
                          ),
                        );
                      },
                      child: Image.asset("res/icons/brand_question.png", width: 32.sp),
                    ),
                  ],
                ),
              ),
            );
            aiList.add(
              HeightText(diseaseYalie!, color2B, 24.sp, 2.0),
            );
          }
          if (fitWidget != null) {
            hasResult = true;
            aiList.add(fitWidget!);
          }
          if (!hasResult && !hasAlgorithm) {
            aiList.add(
              Container(
                height: 750.sp,
                alignment: Alignment.center,
                child: HeightText(Lang.ai_status_no_result, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
              ),
            );
          }
          break;
      }
    } else {
      aiList.add(
        Container(
          alignment: Alignment.topCenter,
          padding: EdgeInsets.fromLTRB(0, 100.sp, 0, 100.sp),
          child: HeightText(Lang.loading, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
        ),
      );
    }

    if (widget.singleRecord) {
      children.add(
        Container(
          width: 1.sw - 296.sp,
          padding: EdgeInsets.all(24.sp),
          margin: EdgeInsets.only(top: 16.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.sp),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyText(Lang.doc_base_info, color2B, 32.sp, FontWeight.w500),
              SizedBox(height: 16.sp),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 190.sp,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MyText(Lang.name, color7C, 20.sp),
                        MyText(scanInfo.recordName, color2B, 24.sp),
                      ],
                    ),
                  ),
                  SizedBox(width: 24.sp),
                  SizedBox(
                    width: 190.sp,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MyText(Lang.gender, color7C, 20.sp),
                        MyText(scanInfo.getGender(), color2B, 24.sp),
                      ],
                    ),
                  ),
                  SizedBox(width: 24.sp),
                  SizedBox(
                    width: 190.sp,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MyText(Lang.age, color7C, 20.sp),
                        MyText(scanInfo.getAge(), color2B, 24.sp),
                      ],
                    ),
                  ),
                  SizedBox(width: 24.sp),
                  SizedBox(
                    width: 190.sp,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MyText(Lang.mobile, color7C, 20.sp),
                        MyText(isNotEmpty(scanInfo.patientPhone) ? scanInfo.patientPhone : "-", color2B, 24.sp),
                      ],
                    ),
                  ),
                ],
              ),
              getFacePhotoView(),
              // SizedBox(height: 32.sp),
              // MyText(Lang.record_sn, color2B, 28.sp, FontWeight.w500),
              // SizedBox(height: 16.sp),
              // MyText(isEmpty(scanInfo.recordSn) ? Lang.none : scanInfo.recordSn, color7C, 24.sp),
            ],
          ),
        ),
      );
      if (isNotEmpty(scanInfo.tagList)) {
        children.add(
          Container(
            width: 1.sw - 296.sp,
            padding: EdgeInsets.all(24.sp),
            margin: EdgeInsets.only(top: 24.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.sp),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MyText(Lang.convert_project, color2B, 32.sp, FontWeight.w500),
                Wrap(
                    children: scanInfo.tagList
                        .map(
                          (tag) => Container(
                            margin: EdgeInsets.fromLTRB(0, 16.sp, 16.sp, 0),
                            padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 12.sp),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.sp),
                              color: colorF3,
                            ),
                            child: MyText(tag, color2B, 24.sp),
                          ),
                        )
                        .toList()),
              ],
            ),
          ),
        );
      }
      // if (isNotEmpty(stageText) && scanInfo.progressStage != "NONE") {
      //   children.add(
      //     Container(
      //       width: 1.sw - 296.sp,
      //       padding: EdgeInsets.all(24.sp),
      //       margin: EdgeInsets.only(top: 24.sp),
      //       decoration: BoxDecoration(
      //         borderRadius: BorderRadius.circular(16.sp),
      //         color: Colors.white,
      //       ),
      //       child: Column(
      //         crossAxisAlignment: CrossAxisAlignment.start,
      //         children: [
      //           MyText(Lang.progress_stage, color2B, 32.sp, FontWeight.w500),
      //           SizedBox(height: 16.sp),
      //           Container(
      //             padding: EdgeInsets.fromLTRB(16.sp, 12.sp, 16.sp, 12.sp),
      //             decoration: BoxDecoration(
      //               borderRadius: BorderRadius.circular(16.sp),
      //               color: colorF3,
      //             ),
      //             child: MyText(stageText, color2B, 24.sp),
      //           ),
      //         ],
      //       ),
      //     ),
      //   );
      // }
      if (isNotEmpty(scanInfo.note)) {
        children.add(
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.sp),
            margin: EdgeInsets.only(top: 24.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.sp),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MyText(Lang.remark, color2B, 32.sp, FontWeight.w500),
                SizedBox(height: 16.sp),
                MyText(scanInfo.note, color7C, 24.sp),
              ],
            ),
          ),
        );
      }
    }
    children.add(
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 652.sp,
            padding: EdgeInsets.all(24.sp),
            margin: EdgeInsets.only(top: 24.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.sp),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MyText(Lang.intraoral_photo, color2B, 32.sp, FontWeight.w500),
                loaded
                    ? status == 7
                        ? Container(
                            height: 750.sp,
                            alignment: Alignment.center,
                            padding: EdgeInsets.only(bottom: 96.sp),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(height: 100.sp),
                                Image.asset("res/imgs/image_error.png", width: 200.sp),
                                SizedBox(height: 32.sp),
                                MyText(Lang.loading_image_error, color7C, 24.sp),
                                SizedBox(height: 32.sp),
                              ],
                            ),
                          )
                        : getScanStepView()
                    : Container(
                        height: 750.sp,
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(bottom: 96.sp),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(height: 100.sp),
                            Image.asset("res/icons/icon_loading.gif", width: 64.sp),
                            SizedBox(height: 32.sp),
                            HeightText(Lang.loading_images, color7C, 24.sp, 2.0, FontWeight.w400, TextAlign.center),
                          ],
                        ),
                      ),
              ],
            ),
          ),
          SizedBox(width: 24.sp),
          Container(
            width: 1.sw - 1268.sp,
            padding: EdgeInsets.all(24.sp),
            margin: EdgeInsets.only(top: 24.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.sp),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: aiList,
            ),
          ),
        ],
      ),
    );

    if (widget.singleRecord) {
      // if (isNotEmpty(scanInfo.recordSn)) {
      //   children.add(Stack(
      //     children: [
      //       Container(
      //         width: 1.sw - 40.sp,
      //         padding: EdgeInsets.all(20.sp),
      //         margin: EdgeInsets.only(top: 16.sp),
      //         decoration: BoxDecoration(
      //           color: Colors.white,
      //           borderRadius: BorderRadius.circular(16.sp),
      //         ),
      //         child: Row(
      //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //           children: [
      //             MyText("${Lang.scan_bind_code}:", color2B, 16.sp, FontWeight.w500),
      //             MyText(scanInfo.recordSn, color7C, 14.sp),
      //           ],
      //         ),
      //       ),
      //       Positioned(
      //         right: 0,
      //         top: 10.sp,
      //         child: Image.asset("res/icons/icon_beta.png", width: 48.sp),
      //       ),
      //     ],
      //   ));
      // }
      children.add(SizedBox(height: 120.sp));
    } else {
      if (isNotEmpty(scanInfo.tagList) || isNotEmpty(patientFeedback)) {
        List<Widget> list = [
          MyText(Lang.patient_remark, color2B, 16.sp, FontWeight.w500),
        ];
        if (isNotEmpty(scanInfo.tagList)) {
          List<Widget> tagViews = [];
          for (int i = 0; i < scanInfo.tagList.length; i++) {
            tagViews.add(Container(
                margin: EdgeInsets.only(top: 4.sp, right: 10.sp, bottom: 4.sp),
                padding: EdgeInsets.fromLTRB(10.sp, 4.sp, 10.sp, 4.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.sp),
                  border: Border.all(color: colorBrand, width: 1.0.sp),
                ),
                child: MyText(getMooeliTag(scanInfo.tagList[i].toString()), colorBrand, 12.sp)));
          }
          list.add(
            Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: 4.sp),
              child: Wrap(
                children: tagViews,
              ),
            ),
          );
        }
        if (isNotEmpty(patientFeedback)) {
          list.add(
            HeightText(patientFeedback!, color2B, 14.sp, 2.0),
          );
        }
        children.add(
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(20.sp),
            margin: EdgeInsets.only(top: 16.sp),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.sp),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: list,
            ),
          ),
        );
      }
      // children.add(
      //   Padding(
      //     padding: EdgeInsets.fromLTRB(0.2.sw, 32.sp, 0.2.sw, 20.sp),
      //     child: HCustomButton(
      //         key: _buttonKey,
      //         height: 40.sp,
      //         onPress: () {
      //           push(isProviderFeedback
      //               ? MooeliViewFeedbackPage(selectedRecord)
      //               : MooeliSendFeedbackPage(selectedRecord));
      //         },
      //         text: isProviderFeedback ? Lang.view_feedback : Lang.send_feedback),
      //   ),
      // );
      children.add(getSloganView());
    }
    return children;
  }

  String getTagsText() {
    List<String> result = [];
    if (isNotEmpty(convertList)) {
      for (String tag in scanInfo.tagList) {
        bool contain = false;
        for (dynamic mapList in convertList) {
          dynamic list = mapList["projectNames"] as List;
          for (dynamic map in list) {
            // logger("getTagsText $map");
            if (map.values.contains(tag) && isNotEmpty(map[lang])) {
              result.add(map[lang]);
              contain = true;
              break;
            }
          }
          if (contain) {
            break;
          }
        }
        if (!contain) {
          result.add(tag);
        }
      }
    }
    return result.join(lang == "zh" ? "   " : ", ");
  }

  String getMooeliTag(String key) {
    Map? tagMap = configJson["${monitorKeyMap[selectedRecord.phaseName]}HotTag"];
    logger("getMooeliTag: $key $tagMap");
    if (isNotEmpty(tagMap)) {
      for (Map map in tagMap!.values) {
        if (map["data"] == key) {
          return map["data${getLangStr()}"];
        }
      }
    }
    return key;
  }

  addChildScanImgByRowIndex(parentList, targetList, ToothCameraTypeEnum type, [bool isLight = false]) {
    for (int i = 0; i < targetList.length; i++) {
      if (File(getLocalPath(targetList[i])).existsSync()) {
        parentList.add({
          "img": targetList[i], //每组口扫图片
          "type": type, //口扫步骤 ToothCameraTypeEnum
          "index": i, //每组图片序号，0-左，1-中，2-右
          "light": isLight,
        });
      }
    }
  }

  List swiperScanList = [];
  List<List> scanPhotoList = [];

  getScanStepView() {
    double width = 186.7.sp, height = 116.7.sp;
    List<Widget> listWidgets = [];
    List defaultScanImgs = [
      ["default_up.png"],
      ["default_close1.png", "default_close2.png", "default_close3.png"],
      ["default_open1.png", "default_open2.png", "default_open3.png"],
      ["default_mask1.png", "default_mask2.png", "default_mask3.png"],
      ["default_down.png"],
    ];
    List<ToothCameraTypeEnum> scanSteps = [
      ToothCameraTypeEnum.maxillary, //上颌
      ToothCameraTypeEnum.occlusion, //咬合
      ToothCameraTypeEnum.openMaskOff, //微张(取下牙套)
      ToothCameraTypeEnum.openMaskOn, //微张(带上牙套)
      ToothCameraTypeEnum.mandibular, //下颌
    ];
    swiperScanList = [];
    scanPhotoList.clear();
    for (int i = 0; i < 5; i++) {
      List targetList = [];
      if (i == 0) {
        targetList = getFilesByType([108]).map((e) => e.imagePath).toList();
      } else if (i == 1) {
        targetList = getFilesByType([105, 103, 107]).map((e) => e.imagePath).toList();
      } else if (i == 2) {
        targetList = getFilesByType([152, 151, 153]).map((e) => e.imagePath).toList();
      } else if (i == 3) {
        targetList = getFilesByType([155, 154, 156]).map((e) => e.imagePath).toList();
      } else if (i == 4) {
        targetList = getFilesByType([106]).map((e) => e.imagePath).toList();
      }
      if (isEmpty(targetList)) {
        continue;
      }
      scanPhotoList.add(targetList);
      addChildScanImgByRowIndex(swiperScanList, targetList, scanSteps[i]);
      List<Widget> rowChilds = [];
      List<String> errorIndexReason = [];
      for (int j = 0; j < (i == 0 || i == 4 ? 1 : 3); j++) {
        bool isHaveErrorInfo = Global.isHaveErrorInfoByScanImgPath(j < targetList.length ? targetList[j] : "");
        rowChilds.add(Container(
          margin: j == 1 ? EdgeInsets.fromLTRB(21.35.sp, 21.35.sp, 21.35.sp, 0) : EdgeInsets.only(top: 21.35.sp),
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.sp),
            color: const Color(0xffe6e6e6),
          ),
          child: GestureDetector(
            onTap: () {
              if (j < targetList.length) {
                Global.showCustomDialog(PhotoViewDialog(targetList[j], scanPhotoList));
                // Navigator.push(context, MaterialPageRoute(builder: (context) {
                //   return CheckScanImgSwiper(
                //     isCanReScan: false,
                //     updateStateByType: () {},
                //     initChooseImgPath: targetList[j],
                //     scanImgInfoList: swiperScanList,
                //   );
                // }));
              }
            },
            child: Stack(alignment: Alignment.center, children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.sp),
                child: j < targetList.length && File(getLocalPath(targetList[j])).existsSync()
                    ? Image.file(
                        File(getLocalPath(targetList[j])),
                        width: width,
                        height: height,
                        fit: BoxFit.cover,
                      )
                    : Image.asset(
                        "res/scan/${defaultScanImgs[i][j]}",
                        width: width,
                        height: height,
                        fit: BoxFit.cover,
                      ),
              ),
              !isHaveErrorInfo
                  ? const SizedBox()
                  : Positioned(
                      top: 8.sp,
                      right: 8.sp,
                      child: Image.asset("res/scan/scan_img_error.png", width: 32.sp),
                    ),
            ]),
          ),
        ));
        if (isHaveErrorInfo) {
          errorIndexReason.add(
              "${j + 1} - ${Global.getAlertTextByScanErrorCode(getErrorCode(targetList[j]), targetList[j])["content"]}");
        }
      }
      listWidgets.add(Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: rowChilds,
          )
        ],
      ));
    }
    return Column(
      children: listWidgets,
    );
  }

  _getDrawerView() {
    int count = 0;
    for (List records in recordMap.values) {
      count += records.length;
    }

    return Container(
      width: 313.sp,
      height: 1.sh,
      color: colorFB,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(20.sp, 68.sp, 20.sp, 4.sp),
            child: MyText(Lang.select_record, color2B, 20.sp, FontWeight.w500),
          ),
          Padding(
            padding: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
            child: MyText(sprintf(Lang.total_record_count, [count]), colorAB, 12.sp),
          ),
          Container(width: 313.sp, height: 1.sp, color: colorFB),
          SizedBox(
            height: 1.sh - 144.sp,
            child: SingleChildScrollView(
              child: Column(
                children: phaseList.map((phase) => _getPhaseMenuView(phase)).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> collapsePhaseIds = [];

  Widget _getPhaseMenuView(MooeliPhase phase) {
    List<MooeliRecord>? records = recordMap[phase.phaseId];
    return Padding(
      padding: EdgeInsets.only(left: 20.sp),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16.sp),
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (collapsePhaseIds.contains(phase.phaseId)) {
                      collapsePhaseIds.remove(phase.phaseId);
                    } else {
                      collapsePhaseIds.add(phase.phaseId);
                    }
                  });
                },
                child: Image.asset(
                  collapsePhaseIds.contains(phase.phaseId) ? "res/imgs/icon_collapse.png" : "res/imgs/icon_expand.png",
                  width: 16.sp,
                  height: 16.sp,
                ),
              ),
              ..._getTreeLine(phase, records),
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (collapsePhaseIds.contains(phase.phaseId)) {
                      collapsePhaseIds.remove(phase.phaseId);
                    } else {
                      collapsePhaseIds.add(phase.phaseId);
                    }
                  });
                },
                child: Container(
                  width: 253.sp,
                  height: 40.sp,
                  padding: EdgeInsets.fromLTRB(8.sp, 0.sp, 8.sp, 0.sp),
                  margin: EdgeInsets.fromLTRB(0.sp, 4.sp, 0.sp, 4.sp),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: colorPink1A,
                    borderRadius: BorderRadius.circular(4.sp),
                  ),
                  child: SingleText(
                    monitorNameMap[phase.phaseName] ?? phase.phaseName,
                    color2B,
                    14.sp,
                    FontWeight.w500,
                  ),
                ),
              ),
              collapsePhaseIds.contains(phase.phaseId) || isEmpty(records)
                  ? SizedBox(height: 16.sp)
                  : Column(
                      children: records!
                          .map((record) => GestureDetector(
                                onTap: () {
                                  setState(() {
                                    selectedRecord = record;
                                    selectedRecord.phaseName = phase.phaseName;
                                    loaded = false;
                                    fileList = [];
                                    _scaffoldKey.currentState?.closeDrawer();
                                    getFileList();
                                  });
                                },
                                child: Container(
                                    width: 253.sp,
                                    height: 40.sp,
                                    padding: EdgeInsets.fromLTRB(8.sp, 0.sp, 8.sp, 0.sp),
                                    margin: EdgeInsets.fromLTRB(0.sp, 4.sp, 0.sp, 4.sp),
                                    alignment: Alignment.centerLeft,
                                    decoration: BoxDecoration(
                                      color:
                                          record.recordId == selectedRecord.recordId ? colorBlue1A : Colors.transparent,
                                      borderRadius: BorderRadius.circular(4.sp),
                                    ),
                                    child: Row(
                                      children: [
                                        Image.asset("res/imgs/icon_contract.png", width: 24.sp),
                                        SizedBox(width: 8.sp),
                                        SingleText(Global.getDateByTimestamp(milliseconds: record.scanTime), color2B,
                                            14.sp, FontWeight.w500),
                                      ],
                                    )),
                              ))
                          .toList(),
                    ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _getTreeLine(MooeliPhase phase, List<MooeliRecord>? records) {
    List<Widget> list = [];
    if (collapsePhaseIds.contains(phase.phaseId) || isEmpty(records)) {
      list.add(const SizedBox());
    } else {
      list.add(Image.asset("res/imgs/icon_line0.png", width: 19.sp, fit: BoxFit.fitWidth));
      for (int i = 0; i < records!.length - 1; i++) {
        list.add(Image.asset("res/imgs/icon_line1.png", width: 19.sp, fit: BoxFit.fitWidth));
      }
      list.add(Image.asset("res/imgs/icon_line2.png", width: 19.sp, fit: BoxFit.fitWidth));
    }
    return list;
  }
}

class PhotoViewDialog extends StatefulWidget {
  String currentPhoto;

  List<List> photos;

  PhotoViewDialog(this.currentPhoto, this.photos);

  @override
  State<StatefulWidget> createState() {
    return PhotoViewDialogState();
  }
}

class PhotoViewDialogState extends State<PhotoViewDialog> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
              mainAxisSize: MainAxisSize.min,
              children: widget.photos
                  .map(
                    (List list) => Row(
                      mainAxisSize: MainAxisSize.min,
                      children: list
                          .map(
                            (photo) => ClipRRect(
                              borderRadius: BorderRadius.circular(10.sp),
                              child: Click(
                                onTap: () {
                                  logger("select $photo");
                                  setState(() {
                                    widget.currentPhoto = photo;
                                  });
                                },
                                child: Container(
                                  width: 223.sp,
                                  height: 140.sp,
                                  margin: EdgeInsets.all(9.sp),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.sp),
                                    border: photo == widget.currentPhoto
                                        ? Border.all(width: 6.sp, color: Colors.white)
                                        : null,
                                    image: DecorationImage(
                                      image: FileImage(File(getLocalPath(photo))),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  )
                  .toList()),
          SizedBox(width: 64.sp),
          ClipRRect(
            borderRadius: BorderRadius.circular(16.sp),
            child: Image.file(
              File(getLocalPath(widget.currentPhoto)),
              width: 980.sp,
              height: 608.sp,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }
}
