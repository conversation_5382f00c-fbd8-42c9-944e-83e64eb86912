// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:mooeli/common/global.dart';
// import 'package:mooeli/common/http.dart';
// import 'package:mooeli/manager/languaga_manager.dart';
// import 'package:mooeli/model/mooeli_data.dart';
// import 'package:mooeli/pages/base_page.dart';
// import 'package:mooeli/utils/colors_utils.dart';
// import 'package:mooeli/utils/common_utils.dart';
// import 'package:mooeli/widget/my_widget.dart';
//
// class MooeliTimelinePage extends BasePage {
//   MooeliPhase phase;
//
//   MooeliTimelinePage(this.phase, {Key? key}) : super(key: key);
//
//   @override
//   State<StatefulWidget> createState() {
//     return MooeliTimelinePageState();
//   }
// }
//
// class MooeliTimelinePageState extends BasePageState<MooeliTimelinePage> {
//   bool loaded = false;
//
//   List<MooeliRecord> recordList = [];
//
//   @override
//   initState() {
//     super.initState();
//     getRecordList();
//   }
//
//   getRecordList() {
//     HHttp.request(
//       "/v3/monitor/record/page",
//       "POST",
//       (data) {
//         setState(() {
//           recordList = (data['recordList'] as List).map((i) => MooeliRecord.fromJson(i)).toList();
//         });
//       },
//       params: {
//         "phaseId": widget.phase.phaseId,
//         "pageCount": 50,
//         "pageIndex": 0,
//       },
//     );
//   }
//
//   _buildRecordItem(MooeliRecord record) {
//     return GestureDetector(
//       onTap: () {
//         record.phaseName = widget.phase.phaseName;
//         // push(MooeliRecordPage(record));
//       },
//       child: Container(
//         decoration: BoxDecoration(
//           color: Colors.grey[200],
//           borderRadius: BorderRadius.circular(8.sp),
//         ),
//         margin: EdgeInsets.fromLTRB(20.sp, 5.sp, 20.sp, 5.sp),
//         alignment: Alignment.centerLeft,
//         padding: EdgeInsets.all(12.sp),
//         child: MyText(
//             "${Lang.scan_record}： ${Global.getDateByTimestamp(milliseconds: record.scanTime)}", Colors.black, 14.sp),
//       ),
//     );
//   }
//
//   _buildList() {
//     if (isEmpty(recordList)) {
//       return Center(
//         child: MyText(loaded ? Lang.no_record : Lang.loading, color2B, 14.sp),
//       );
//     }
//     return ListView.builder(
//       itemCount: recordList.length,
//       itemBuilder: (context, index) {
//         return _buildRecordItem(recordList[index]);
//       },
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(widget.phase.phaseName),
//       ),
//       body: _buildList(),
//     );
//   }
// }
