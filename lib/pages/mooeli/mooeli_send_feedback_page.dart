import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/mooeli/mooeli_view_feedback_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';

class MooeliSendFeedbackPage extends BasePage {
  MooeliRecord mooeli;

  MooeliSendFeedbackPage(this.mooeli, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MooeliSendFeedbackPageState();
  }
}

class MooeliSendFeedbackPageState extends BasePageState<MooeliSendFeedbackPage> {
  dynamic configJson = {};
  Map feedbackMap = {};
  Map<String, List<String>> selectTemplateMap = {};
  TextEditingController descController = TextEditingController();

  String typeKey = "";

  @override
  initState() {
    super.initState();
    typeKey = monitorKeyMap[widget.mooeli.phaseName] ?? "";
    getMooeliConfigJson((json) {
      setState(() {
        configJson = json;
      });
    });
  }

  _buildBody() {
    if (isEmpty(configJson)) {
      return Center(
        child: MyText(Lang.loading, color2B, 14.sp),
      );
    }
    if (isEmpty(typeKey)) {
      return Center(
        child: MyText("暂未支持当前监控类型", color7C, 14.sp),
      );
    }
    return Padding(
      padding: EdgeInsets.all(12.sp),
      child: Stack(
        children: [
          ListView(
            children: _buildList(),
          ),
          HCustomButton(
            ms: 1000,
            height: 40.sp,
            onPress: _sendFeedback,
            initVisible: true,
            text: "发送",
            fixedBottom: true,
          ),
        ],
      ),
    );
  }

  _buildList() {
    List<Widget> children = [];
    children.add(_buildSingleCheckForm("OralHygiene"));
    children.add(_buildSingleCheckForm("${typeKey}NextStep", useCode: false));
    children.add(_buildInputForm());
    children.add(_buildTemplateForm());
    children.add(SizedBox(height: 100.sp));
    return children;
  }

  _buildSingleCheckForm(String key, {bool useCode = true}) {
    if (isNotEmpty(configJson) && isNotEmpty(configJson[key])) {
      List<Widget> children = [];
      for (dynamic subMap in (configJson[key] as Map).values) {
        if (subMap["number"] == 0) {
          children.insert(
              0,
              Padding(
                padding: EdgeInsets.fromLTRB(4.sp, 20.sp, 4.sp, 4.sp),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    MyText("*", colorRed, 14.sp, FontWeight.w500),
                    Expanded(
                      child: MyText(subMap["data"], color2B, 16.sp, FontWeight.w500),
                    ),
                  ],
                ),
              ));
        } else {
          children.add(GestureDetector(
            onTap: () {
              setState(() {
                feedbackMap[key] = useCode ? subMap["code"] : subMap["data"];
              });
            },
            child: Padding(
              padding: EdgeInsets.all(4.sp),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 24.sp,
                    height: 24.sp,
                    child: Material(
                      color: Colors.transparent,
                      child: Radio<String>(
                        activeColor: color2B,
                        value: useCode ? subMap["code"] : subMap["data"],
                        onChanged: (value) {
                          setState(() {
                            feedbackMap[key] = useCode ? subMap["code"] : subMap["data"];
                          });
                        },
                        groupValue: feedbackMap[key],
                      ),
                    ),
                  ),
                  Expanded(
                    child: MyText(
                      subMap["data"],
                      color2B,
                      14.sp,
                    ),
                  ),
                ],
              ),
            ),
          ));
        }
      }
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    }
    return const SizedBox();
  }

  _buildInputForm() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 20.sp),
        MyText("其他说明", color2B, 16.sp, FontWeight.w500),
        SizedBox(height: 4.sp),
        Container(
          height: 120.sp,
          padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
          decoration: BoxDecoration(
            border: Border.all(
              color: colorE1,
            ),
            borderRadius: BorderRadius.circular(8.sp),
          ),
          child: TextField(
            controller: descController,
            textAlign: TextAlign.left,
            maxLength: 3000,
            style: TextStyle(fontSize: 14.sp, color: color2B.withOpacity(0.5)),
            decoration: InputDecoration(
              counterText: '',
              border: InputBorder.none,
              hintText: "请输入诊疗建议",
              hintStyle: TextStyle(fontSize: 14.sp, color: const Color(0xFFCCCCCC)),
              isDense: true,
            ),
          ),
        ),
      ],
    );
  }

  _buildTemplateForm() {
    Map<String, List<String>> templateMap = {};
    for (dynamic value in (configJson["${typeKey}FeedbackTemplate"] as Map).values) {
      String key = value["title"];
      String data = value["data"];
      if (!templateMap.containsKey(key)) {
        templateMap[key] = [];
      }
      templateMap[key]!.add(data);
    }

    List<Widget> children = [];
    for (var key in templateMap.keys) {
      children.add(_buildMultiCheckForm(key, templateMap[key]!));
    }

    return Column(
      children: [
        SizedBox(height: 20.sp),
        MyText("为您推荐以下医生反馈模板，请选择（可多选）", color2B, 16.sp, FontWeight.w500),
        SizedBox(height: 4.sp),
        Container(
          decoration: BoxDecoration(
            color: colorF3,
            borderRadius: BorderRadius.circular(8.sp),
          ),
          padding: EdgeInsets.all(12.sp),
          child: Column(children: children),
        )
      ],
    );
  }

  _buildMultiCheckForm(String key, List<String> texts) {
    if (!selectTemplateMap.containsKey(key)) {
      selectTemplateMap[key] = [];
    }
    List<Widget> children = [];
    children.add(Padding(
      padding: EdgeInsets.fromLTRB(4.sp, 10.sp, 4.sp, 0.sp),
      child: MyText(key, color2B, 16.sp, FontWeight.w500),
    ));
    for (String text in texts) {
      children.add(GestureDetector(
        onTap: () {
          setState(() {
            if (!(selectTemplateMap[key] as List).contains(text)) {
              (selectTemplateMap[key] as List).add(text);
            } else {
              (selectTemplateMap[key] as List).remove(text);
            }
          });
        },
        child: Padding(
          padding: EdgeInsets.only(top: 8.sp),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  fillColor: MaterialStateProperty.resolveWith((Set<MaterialState> states) {
                    logger("_buildMultiCheckForm $states");
                    return color2B.withOpacity(0.6);
                  }),
                  checkColor: color2B,
                  value: (selectTemplateMap[key] as List).contains(text),
                  onChanged: (value) {
                    setState(() {
                      if (value!) {
                        if (!(selectTemplateMap[key] as List).contains(text)) {
                          (selectTemplateMap[key] as List).add(text);
                        }
                      } else {
                        (selectTemplateMap[key] as List).remove(text);
                      }
                    });
                  }),
              Expanded(
                child: MyText(
                  text,
                  color2B,
                  14.sp,
                ),
              ),
            ],
          ),
        ),
      ));
      children.add(SizedBox(height: 10.sp));
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  _sendFeedback() {
    if (isEmpty(feedbackMap["${typeKey}NextStep"]) || isEmpty(feedbackMap["OralHygiene"])) {
      Global.toast("请选择口腔卫生状态和建议");
      return;
    }
    HHttp.request(
      "/v3/monitor/record/sendFeedback",
      "POST",
      (data) {
        eventBus.fire(EventFeedback(widget.mooeli.recordId, feedback: true));
        replace(MooeliViewFeedbackPage(widget.mooeli));
      },
      params: {
        "recordId": widget.mooeli.recordId,
        "nextSuggestion": feedbackMap["${typeKey}NextStep"],
        "oralHygiene": feedbackMap["OralHygiene"],
        "otherNotes": descController.text,
        "providerFeedbackList": selectTemplateMap.keys
            .map((key) => {
                  "title": key,
                  "feedbackList": selectTemplateMap[key],
                })
            .toList(),
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("发送反馈"),
      ),
      body: _buildBody(),
    );
  }
}
