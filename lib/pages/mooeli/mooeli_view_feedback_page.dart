import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/analytics_manager.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

class MooeliViewFeedbackPage extends BasePage {
  MooeliRecord mooeli;

  MooeliViewFeedbackPage(this.mooeli, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MooeliViewFeedbackPageState();
  }
}

class MooeliViewFeedbackPageState extends BasePageState<MooeliViewFeedbackPage> {
  dynamic feedbackJson = {};

  @override
  void initState() {
    super.initState();
    HHttp.request(
      "/v3/monitor/record/getFeedback",
      "POST",
      (data) {
        setState(() {
          feedbackJson = data;
        });
      },
      params: {
        "id": widget.mooeli.recordId,
      },
    );
  }

  _buildBody() {
    if (isEmpty(feedbackJson)) {
      return Center(
        child: MyText(Lang.loading, color2B, 14.sp),
      );
    }
    return Padding(
        padding: EdgeInsets.all(12.sp),
        child: ListView(
          children: _buildList(),
        ));
  }

  _buildList() {
    List<Widget> children = [
      Padding(
        padding: EdgeInsets.only(top: 20.sp),
        child: MyText("口腔卫生", color2B, 16.sp, FontWeight.w500),
      ),
      Padding(
        padding: EdgeInsets.only(top: 8.sp),
        child: MyText(
          healthMap[feedbackJson["oralHygiene"]!]!,
          color7C,
          14.sp,
        ),
      ),
      Padding(
        padding: EdgeInsets.only(top: 20.sp),
        child: MyText("下一步建议", color2B, 16.sp, FontWeight.w500),
      ),
      Padding(
        padding: EdgeInsets.only(top: 8.sp),
        child: MyText(
          feedbackJson["nextSuggestion"]!,
          color7C,
          14.sp,
        ),
      ),
    ];
    if (isNotEmpty(feedbackJson["otherNotes"])) {
      children.add(Padding(
        padding: EdgeInsets.only(top: 20.sp),
        child: MyText("其他说明", color2B, 16.sp, FontWeight.w500),
      ));
      children.add(Padding(
        padding: EdgeInsets.only(top: 8.sp),
        child: MyText(
          feedbackJson["otherNotes"]!,
          color7C,
          14.sp,
        ),
      ));
    }
    if (isNotEmpty(feedbackJson["providerFeedbackList"])) {
      for (var map in feedbackJson["providerFeedbackList"] as List) {
        if (isNotEmpty(map["feedbackList"] as List)) {
          children.add(Padding(
            padding: EdgeInsets.only(top: 20.sp, bottom: 4.sp),
            child: MyText(map["title"], color2B, 16.sp, FontWeight.w500),
          ));
          children.addAll((map["feedbackList"] as List)
              .map((text) => Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 6.sp,
                        height: 6.sp,
                        margin: EdgeInsets.all(8.sp),
                        decoration: BoxDecoration(
                          color: color7C,
                          borderRadius: BorderRadius.circular(6.sp),
                        ),
                      ),
                      Expanded(
                        child: HeightText(text, color7C, 14.sp, 1.5),
                      ),
                    ],
                  ))
              .toList());
        }
      }
    }

    children.add(SizedBox(height: 100.sp));
    return children;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("查看反馈"),
      ),
      body: _buildBody(),
    );
  }
}
