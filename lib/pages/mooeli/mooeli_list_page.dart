import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/mooeli/mooeli_record_page.dart';
import 'package:mooeli/pages/mooeli/mooeli_search_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/custom_button.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class MooeliListPage extends BasePage {
  const MooeliListPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MooeliListPageState();
  }
}

class MooeliListPageState extends BasePageState<MooeliListPage> {
  List<MooeliItem> mooeliList = [];
  int pageIndex = 0;
  int pageSize = 20;
  bool isLoading = false;
  bool allLoad = false;

  bool searchMode = false;
  SearchCondition savedCondition = SearchCondition();
  SearchCondition tempCondition = SearchCondition();

  @override
  initState() {
    super.initState();
    initAsyncState();
  }

  initAsyncState() async {
    await refreshMooeliList();
  }

  //允许Global直接调用几个主页面的函数
  updatePage() {
    refreshMooeliList();
  }

  refreshMooeliList() async {
    return await getMooeliList(true);
  }

  onLoadMore() {
    getMooeliList(false);
  }

  getMooeliList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    List filters = [];
    if (isNotEmpty(savedCondition.keywordController.text)) {
      filters.add({
        "property": "index",
        "operator": "like",
        "value": savedCondition.keywordController.text,
      });
    }
    HHttp.request("/v3/monitor/case/page", "POST", (data) {
      if (!refresh && isEmpty(data['caseList'])) {
        setState(() {
          isLoading = false;
        });
        return;
      }
      logger("getMooeliList ${data['caseList']}");
      if (data['caseList'] == null) {
        data['caseList'] = [];
      }
      List<MooeliItem> list = (data['caseList'] as List).map((i) => MooeliItem.fromJson(i)).toList();
      if (isNotEmpty(list)) {
        List<String> ids = [];
        for (var item in list) {
          if (isNotEmpty(item.avatarFileId)) {
            ids.add(item.avatarFileId);
          }
        }
        downloadThumbnail(
          ids,
          (id, path) {
            logger("download for $id: $path, exist: ${File(path).lengthSync()}");
            for (var item in list) {
              if (item.avatarFileId == id) {
                setState(() {
                  item.avatarPath = path;
                });
              }
            }
          },
          "RECORD_MONITOR",
        );
      }

      setState(() {
        allLoad = true;
        if (refresh) {
          mooeliList = list;
          if (mooeliList.length >= pageSize) {
            allLoad = false;
          }
        } else {
          if (isNotEmpty(list)) {
            if (list.length >= pageSize) {
              allLoad = false;
            }
            mooeliList.addAll(list);
          }
        }
        isLoading = false;
      });
      logger("getMooeliList $mooeliList");
    }, errCallBack: (_) {
      setState(() {
        isLoading = false;
      });
    }, params: {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "filterParamList": filters,
    });
  }

  getItemView(MooeliItem item) {
    return GestureDetector(
      onTap: () {
        push(MooeliRecordPage(item));
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(0, 8.sp, 0, 8.sp),
        padding: EdgeInsets.only(left: 20.sp),
        width: 1.sw - 40.sp,
        height: 114.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 1.sp,
              spreadRadius: 1.sp,
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.sp),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(30.sp),
                        child: isEmpty(item.avatarPath)
                            ? Image.asset(
                                item.gender == "female" ? "res/imgs/avatar_female.png" : "res/imgs/avatar_male.png",
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )
                            : Image.file(
                                File(item.avatarPath),
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )),
                    SizedBox(width: 12.sp),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 250.sp,
                          height: 24.sp,
                          child: Container(
                            constraints: BoxConstraints(maxWidth: 250.sp),
                            child: SingleText(item.caseName, color2B, 16.sp, FontWeight.w500),
                          ),
                        ),
                        SizedBox(height: 2.sp),
                        MyText("${item.getGender()}  ${sprintf(Lang.year_old, [Global.getAge(item.birthday)])}",
                            color2B, 12.sp),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 12.sp),
                MyText(
                    "${Lang.last_scan}：${item.latestScanTime > 0 ? Global.getDateByTimestamp(milliseconds: item.latestScanTime) : Lang.none}",
                    color7C,
                    12.sp),
                SizedBox(height: 4.sp),
                MyText("${Lang.monitor_process}：${item.getStatusText()}", color7C, 12.sp),
              ],
            ),
          ],
        ),
      ),
    );
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < mooeliList.length; i++) {
      allWidget.add(getItemView(mooeliList[i]));
    }
    return allWidget;
  }

  // 下拉刷新
  Future onRefresh() async {
    await refreshMooeliList();
  }

  showFilterDialog() {
    showModalBottomSheet(
      isScrollControlled: true, // !important
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
            decoration: BoxDecoration(
              color: colorBg,
              borderRadius: BorderRadius.circular(8.sp),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.fromLTRB(20.sp, 20.sp, 20.sp, 0),
                  decoration: BoxDecoration(
                    color: colorBg,
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    Stack(
                      children: [
                        Align(
                          alignment: Alignment.center,
                          child: MyText(Lang.filter, color2B, 16.sp, FontWeight.w500),
                        ),
                        Align(
                          alignment: Alignment.topRight,
                          child: GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Image.asset("res/imgs/modal_close.png", width: 20.sp, height: 20.sp),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12.sp),
                    Row(
                      children: [
                        Container(
                          width: 1.sw - 100.sp,
                          height: 36.sp,
                          padding: EdgeInsets.fromLTRB(12.sp, 0, 12.sp, 0),
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xffe4e4e4)),
                            borderRadius: BorderRadius.circular(8.sp),
                          ),
                          child: TextField(
                            controller: tempCondition.keywordController,
                            textAlign: TextAlign.left,
                            autofocus: true,
                            style: TextStyle(fontSize: 14.sp, color: color2B),
                            decoration: InputDecoration(
                              counterText: '',
                              border: InputBorder.none,
                              hintText: Lang.input_name_or_id,
                              hintStyle: TextStyle(fontSize: 14.sp, color: Colors.black26),
                              isDense: true,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            hideKeyboard();
                            tempCondition.keywordController.clear();
                          },
                          icon: const Icon(Icons.cancel),
                          iconSize: 20.sp,
                          color: Colors.black26,
                        ),
                      ],
                    ),
                  ]),
                ),
                Container(width: 1.sw, height: 1.sp, color: const Color(0x66E0E4EE)),
                SizedBox(height: 16.sp),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    HCustomButton(
                      fontSize: 16.sp,
                      onPress: () {
                        hideKeyboard();
                        bool needSearch = savedCondition.hasCondition();
                        logger("needSearch  $needSearch");
                        setState(() {
                          savedCondition = SearchCondition();
                          tempCondition = SearchCondition();
                        });
                        if (needSearch) {
                          refreshMooeliList();
                        }
                        Navigator.pop(context);
                      },
                      borderColor: Colors.white24,
                      borderWidth: 1.sp,
                      textColor: color2B,
                      text: Lang.reset,
                      width: 160.sp,
                      height: 42.sp,
                      ms: 1000,
                      bgColor: Colors.white,
                    ),
                    SizedBox(width: 15.sp),
                    HCustomButton(
                        fontSize: 16.sp,
                        onPress: () {
                          hideKeyboard();
                          bool needSearch = !savedCondition.isEqual(tempCondition);
                          logger("needSearch  $needSearch");
                          setState(() {
                            savedCondition = SearchCondition.copy(tempCondition);
                          });
                          if (needSearch) {
                            refreshMooeliList();
                          }
                          Navigator.pop(context);
                        },
                        textColor: Colors.white,
                        text: Lang.confirm,
                        width: 160.sp,
                        height: 42.sp,
                        ms: 1000,
                        bgColor: colorBrand),
                  ],
                ),
                SizedBox(height: 30.sp),
              ],
            ),
          ),
        );
      },
      context: context,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: Text("MOOELI"),
            actions: [
              GestureDetector(
                child: Padding(
                  padding: EdgeInsets.only(right: 24.sp),
                  child: Image.asset(
                    "res/imgs/icon_search.png",
                    width: 38.sp,
                  ),
                ),
                onTap: () {
                  push(const MooeliSearchPage());
                  // setState(() {
                  //   tempCondition = SearchCondition.copy(savedCondition);
                  // });
                  // showFilterDialog();
                },
              ),
            ],
          ),
          body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.sp),
              child: RefreshList(
                childList: getListView(),
                onRefresh: onRefresh,
                onLoadMore: onLoadMore,
                isLoading: isLoading,
                nullImgName: "res/imgs/empty_pic.png",
                nullText: Lang.no_filter_case,
                nullWidget: savedCondition.hasCondition()
                    ? null
                    : isLoading
                        ? MyText(Lang.loading, color7C, 14.sp)
                        : RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: Lang.no_case_login,
                              style: TextStyle(
                                fontSize: 14.sp,
                                height: 1.5,
                                color: colorAB,
                              ),
                              children: [
                                TextSpan(
                                  text: Lang.lyoral_web,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: colorAB,
                                  ),
                                ),
                                TextSpan(
                                  text: Lang.create_case,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: colorAB,
                                  ),
                                ),
                              ],
                            ),
                          ),
              )),
        ),
      ],
    );
  }
}

class SearchCondition {
  TextEditingController keywordController = TextEditingController();

  SearchCondition();

  SearchCondition.copy(SearchCondition condition) {
    keywordController.text = condition.keywordController.text;
  }

  bool hasCondition() {
    return isNotEmpty(keywordController.text);
  }

  bool isEqual(SearchCondition condition) {
    return keywordController.text == condition.keywordController.text;
  }

  @override
  String toString() {
    return "SearchCondition ${keywordController.text}";
  }
}
