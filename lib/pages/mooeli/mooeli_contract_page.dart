// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:mooeli/common/http.dart';
// import 'package:mooeli/manager/languaga_manager.dart';
// import 'package:mooeli/model/mooeli_data.dart';
// import 'package:mooeli/pages/base_page.dart';
// import 'package:mooeli/pages/mooeli/mooeli_timeline_page.dart';
// import 'package:mooeli/utils/colors_utils.dart';
// import 'package:mooeli/utils/common_utils.dart';
// import 'package:mooeli/widget/my_widget.dart';
//
// class MooeliContractPage extends BasePage {
//   MooeliItem mooeli;
//
//   MooeliContractPage(this.mooeli, {Key? key}) : super(key: key);
//
//   @override
//   State<StatefulWidget> createState() {
//     return MooeliContractPageState();
//   }
// }
//
// class MooeliContractPageState extends BasePageState<MooeliContractPage> {
//   bool loaded = false;
//
//   List<MooeliPhase> phaseList = [];
//
//   @override
//   initState() {
//     super.initState();
//     getPhaseList();
//   }
//
//   getPhaseList() {
//     HHttp.request(
//       "/v3/monitor/page",
//       "POST",
//       (data) {
//         setState(() {
//           loaded = true;
//           phaseList = (data['phaseList'] as List).map((i) => MooeliPhase.fromJson(i)).toList();
//         });
//       },
//       params: {
//         "caseId": widget.mooeli.caseId,
//         "pageCount": 50,
//         "pageIndex": 0,
//       },
//     );
//   }
//
//   _buildRecordItem(MooeliPhase record) {
//     return GestureDetector(
//       onTap: () {
//         push(MooeliTimelinePage(record));
//       },
//       child: Container(
//           decoration: BoxDecoration(
//             color: Colors.grey[200],
//             borderRadius: BorderRadius.circular(8.sp),
//           ),
//           margin: EdgeInsets.fromLTRB(20.sp, 10.sp, 20.sp, 10.sp),
//           alignment: Alignment.centerLeft,
//           padding: EdgeInsets.all(12.sp),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               MyText(record.phaseName, Colors.black, 14.sp),
//               SizedBox(height: 8.sp),
//               MyText("${Lang.monitor_status}： ${record.getStatusText()}", Colors.black45, 14.sp),
//             ],
//           )),
//     );
//   }
//
//   _buildList() {
//     if (isEmpty(phaseList)) {
//       return Center(
//         child: MyText(loaded ? Lang.no_record : Lang.loading, color2B, 14.sp),
//       );
//     }
//     return ListView.builder(
//       itemCount: phaseList.length,
//       itemBuilder: (context, index) {
//         return _buildRecordItem(phaseList[index]);
//       },
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(widget.mooeli.caseName),
//       ),
//       body: _buildList(),
//     );
//   }
// }
