import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/manager/oss_manager.dart';
import 'package:mooeli/model/mooeli_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/mooeli/mooeli_record_page.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli/widget/refresh_list.dart';
import 'package:sprintf/sprintf.dart';

class MooeliSearchPage extends BasePage {
  const MooeliSearchPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MooeliSearchPageState();
  }
}

class MooeliSearchPageState extends BasePageState<MooeliSearchPage> {
  List<MooeliItem> mooeliList = [];
  int pageIndex = 0;
  int pageSize = 50;
  bool isLoading = false;
  bool allLoad = false;

  Timer? timer;
  TextEditingController keywordController = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    delay(200, () {
      showKeyboard(focusNode);
    });
  }

  refreshMooeliList() async {
    return await getMooeliList(true);
  }

  onLoadMore() {
    getMooeliList(false);
  }

  getMooeliList(bool refresh) async {
    if (isLoading || !refresh && allLoad) {
      return;
    }
    List filters = [];

    if (isNotEmpty(keywordController.text)) {
      filters.add({
        "property": "index",
        "operator": "like",
        "value": keywordController.text,
      });
    } else {
      setState(() {
        pageIndex = 0;
        isLoading = false;
        mooeliList.clear();
      });
      return;
    }
    if (refresh) {
      allLoad = false;
      pageIndex = 0;
    } else {
      pageIndex++;
    }
    setState(() {
      isLoading = true;
    });
    HHttp.request("/v3/monitor/case/page", "POST", (data) {
      List<MooeliItem> list = (data['caseList'] as List).map((i) => MooeliItem.fromJson(i)).toList();
      if (isNotEmpty(list)) {
        List<String> ids = [];
        for (var item in list) {
          if (isNotEmpty(item.avatarFileId)) {
            ids.add(item.avatarFileId);
          }
        }
        downloadThumbnail(
          ids,
          (id, path) {
            for (var item in list) {
              if (item.avatarFileId == id) {
                setState(() {
                  item.avatarPath = path;
                });
              }
            }
          },
          "RECORD_MONITOR",
        );
      }

      setState(() {
        allLoad = true;
        if (refresh) {
          mooeliList = list;
          if (mooeliList.length >= pageSize) {
            allLoad = false;
          }
        } else {
          if (isNotEmpty(list)) {
            if (list.length >= pageSize) {
              allLoad = false;
            }
            mooeliList.addAll(list);
          }
        }
        isLoading = false;
      });
    }, errCallBack: (_) {
      setState(() {
        isLoading = false;
      });
    }, params: {
      "pageCount": pageSize,
      "pageIndex": pageIndex,
      "filterParamList": filters,
    });
  }

  getItemView(MooeliItem item) {
    return GestureDetector(
      onTap: () {
        push(MooeliRecordPage(item));
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(20.sp, 8.sp, 20.sp, 8.sp),
        padding: EdgeInsets.only(left: 20.sp),
        width: 1.sw - 48.sp,
        height: 114.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
          boxShadow: [
            BoxShadow(
              color: colorShader,
              blurRadius: 1.sp,
              spreadRadius: 1.sp,
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.sp),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(30.sp),
                        child: isEmpty(item.avatarPath)
                            ? Image.asset(
                                item.gender == "female" ? "res/imgs/avatar_female.png" : "res/imgs/avatar_male.png",
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )
                            : Image.file(
                                File(item.avatarPath),
                                width: 37.sp,
                                height: 37.sp,
                                fit: BoxFit.cover,
                              )),
                    SizedBox(width: 12.sp),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 250.sp,
                          height: 24.sp,
                          child: Container(
                            constraints: BoxConstraints(maxWidth: 250.sp),
                            child: SingleText(item.caseName, color2B, 16.sp, FontWeight.w500),
                          ),
                        ),
                        SizedBox(height: 2.sp),
                        MyText("${item.getGender()}  ${sprintf(Lang.year_old, [Global.getAge(item.birthday)])}",
                            color2B, 12.sp),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 12.sp),
                MyText(
                    "${Lang.last_scan}：${item.latestScanTime > 0 ? Global.getDateByTimestamp(milliseconds: item.latestScanTime) : Lang.none}",
                    color7C,
                    12.sp),
                SizedBox(height: 4.sp),
                MyText("${Lang.monitor_process}：${item.getStatusText()}", color7C, 12.sp),
              ],
            ),
          ],
        ),
      ),
    );
  }

  getListView() {
    List<Widget> allWidget = [];
    for (int i = 0; i < mooeliList.length; i++) {
      allWidget.add(getItemView(mooeliList[i]));
    }
    return allWidget;
  }

// 下拉刷新
  Future onRefresh() async {
    await refreshMooeliList();
  }

  delaySearch() {
    if (timer != null) {
      timer!.cancel();
    }
    timer = Timer(const Duration(milliseconds: 800), () {
      refreshMooeliList();
    });
  }

  @override
  void dispose() {
    if (timer != null) {
      timer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LingyaAppBar(
        contentHeight: 90.sp,
        child: Container(
          margin: EdgeInsets.fromLTRB(20.sp, 44.sp, 20.sp, 0),
          child: Row(
            children: [
              Expanded(
                child: Container(
                    height: 38.sp,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.fromLTRB(10.sp, 0, 10.sp, 0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: colorBg),
                      borderRadius: BorderRadius.circular(20.sp),
                    ),
                    child: Stack(
                      children: [
                        TextField(
                          controller: keywordController,
                          focusNode: focusNode,
                          textAlign: TextAlign.start,
                          textAlignVertical: TextAlignVertical.center,
                          maxLength: 20,
                          style: TextStyle(fontSize: 14.sp, color: color2B),
                          onChanged: (value) {
                            delaySearch();
                          },
                          decoration: InputDecoration(
                            prefix: SizedBox(
                              width: 30.sp,
                            ),
                            hintText: Lang.input_name_or_id,
                            hintStyle: TextStyle(fontSize: 14.sp, color: colorB8),
                            counterText: '',
                            border: InputBorder.none,
                            isDense: true,
                          ),
                        ),
                        Positioned(
                          top: 6.sp,
                          child: Container(
                            padding: EdgeInsets.only(right: 12.sp),
                            child: Image.asset("res/imgs/icon_search2.png", height: 20.sp),
                          ),
                        ),
                      ],
                    )),
              ),
              SizedBox(width: 12.sp),
              Click(
                onTap: () {
                  hideKeyboard();
                  pop();
                },
                child: MyText(Lang.cancel, colorBrand, 14.sp),
              ),
            ],
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: Container(
          width: 1.sw,
          height: 1.sh,
          color: Colors.transparent,
          child: Stack(
            children: [
              Click(
                  onTap: hideKeyboard,
                  child: SizedBox(
                    width: 1.sw,
                    height: 1.sh - 100.sp,
                  )),
              isEmpty(keywordController.text)
                  ? const SizedBox()
                  : RefreshList(
                      childList: getListView(),
                      isLoading: isLoading,
                      onRefresh: onRefresh,
                      onLoadMore: onLoadMore,
                      nullImgName: "res/imgs/empty_pic.png",
                      nullWidget: getEmptyText(),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getEmptyText() {
    int index = Lang.cannot_find_case.indexOf("__");
    String str1 = Lang.cannot_find_case.substring(0, index);
    String str2 = Lang.cannot_find_case.substring(index + 2);
    return RichText(
      text: TextSpan(
        text: str1,
        style: TextStyle(
          fontSize: 24.sp,
          color: colorAB,
        ),
        children: [
          TextSpan(
            text: keywordController.text,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorBrand,
            ),
          ),
          TextSpan(
            text: str2,
            style: TextStyle(
              fontSize: 24.sp,
              color: colorAB,
            ),
          ),
        ],
      ),
    );
  }
}
