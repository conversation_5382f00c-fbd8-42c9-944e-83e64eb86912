import 'package:flutter/material.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/common/local_account.dart';
import 'package:mooeli/manager/config_manger.dart';
import 'package:mooeli/manager/xianzong_manager.dart';
import 'package:mooeli/model/lingya_data.dart';
import 'package:mooeli/pages/base_page.dart';
import 'package:mooeli/pages/home/<USER>';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/utils/event_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:mooeli_ffi/mooeli_ffi.dart';

class MainTabs extends BasePage {
  const MainTabs({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MainTabState();
  }
}

class MainTabState extends BasePageState<MainTabs> with WidgetsBindingObserver {
  int _tabIndex = 0;

  int unreadCount = 0;

  LingyaTenant selectTenant = LingyaTenant();
  List<LingyaTenant> tenantList = [];

  @override
  void initState() {
    super.initState();
    setScreenVertiacl();
    WidgetsBinding.instance.addObserver(this); //添加观察者
    FlutterBugly.init(androidAppId: "12749ce858", iOSAppId: "62c7935251");

    // addEventListener<EventOpenTenantDrawer>((event) {
    //   setState(() {
    //     selectTenant = event.selectTenant;
    //     tenantList = event.tenantList;
    //   });
    //   _scaffoldKey.currentState?.openDrawer();
    // });
    addEventListener<EventMessageUnread>((event) {
      setState(() {
        unreadCount = event.count;
      });
    });

    initAsyncState();
  }

  void initAsyncState() async {
    delay(2000, () {
      initRequestJson();
    });
    delay(1000, getConfigJson);

    callMcByFunName("getAppVersion").then((verStr) {
      Global.appVersion = verStr ?? "1.0.0";
      Global.checkNewestVersion(retry: 3);
    });

    if (HHttp.httpEnv != HttpEnv.prod || isNotEmpty(Global.sharedPrefs.getString("http_env"))) {
      copyLocalAssetZip();
    }
    initXianzong();
    delay(2000, () {
      try {
        if (isEmpty(Global.sharedPrefs.getString("first_login"))) {
          Global.sharedPrefs.setString("first_login",
              "${Global.getShowTime(milliseconds: DateTime.now().millisecondsSinceEpoch, ignoreSameDay: false)} v${Global.appVersion}");
        }
        logger("first_login: ${Global.sharedPrefs.getString("first_login")}");
      } catch (ex) {
        logger("first_login error: $ex");
      }
    });
  }

  getConfigJson() {
    // HHttp.request(
    //   "${HHttp.getOfficialHost()}probox/config_json.txt",
    //   "GET",
    //   isFullUrl: true,
    //   isShowNetWorkLoading: false,
    //   isShowErrorToast: false,
    //   cbFullResponse: true,
    //   (content) {
    //     try {
    //       String configStr = content.toString().replaceAll("\n", "").replaceAll("\t", "");
    //       Global.sharedPrefs.setString("config_json", configStr);
    //       logger("config save: $configStr");
    //       delay(200, () {
    //         eventBus.fire(EventConfigJson());
    //       });
    //     } catch (ex) {
    //       logger("config save err: $ex");
    //     }
    //   },
    // );
  }

  setMainTabShowPageIndex(int tabIndex) {
    setState(() {
      _tabIndex = tabIndex;
    });
  }

  updateStateTabPageByTabIndex(int index) {
    if (index == 2) {
      eventBus.fire(EventRefreshMessage());
    }
  }

  bool hasPaused = false;

  @override
  dispose() {
    WidgetsBinding.instance.removeObserver(this); //移除观察者
    super.dispose();
  }

  getTabTitle(int index, String iconNormal, String iconPressed, String name) {
    return Click(
      onTap: () {
        setState(() {
          _tabIndex = index;
        });
        eventBus.fire(EventMainTab(index));
      },
      child: Container(
        width: 240.sp,
        height: 72.sp,
        color: _tabIndex == index ? colorPurpleBg : Colors.transparent,
        padding: EdgeInsets.only(left: 40.sp),
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            Image.asset(_tabIndex == index ? iconPressed : iconNormal, width: 40.sp),
            SizedBox(width: 12.sp),
            MyText(name, _tabIndex == index ? colorBrand : color7C, 28.sp),
            const Spacer(),
            _tabIndex == index
                ? Container(
                    width: 8.sp,
                    height: 48.sp,
                    decoration: BoxDecoration(
                      color: colorBrand,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.sp),
                        bottomLeft: Radius.circular(8.sp),
                      ),
                    ),
                  )
                : const SizedBox(),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return const HomePage();
  }

  @override
  void onRouteResume(Route nextRoute) {
    super.onRouteResume(nextRoute);
    setScreenVertiacl();
  }
}
