import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/pages/login/login_page.dart';
import 'package:mooeli/pages/main_tabs.dart';
import 'package:mooeli/utils/Languages/l10n.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';

import 'pages/splash/splash_page.dart';

class FirstInToAppCheck extends StatefulWidget {
  const FirstInToAppCheck({Key? key, required this.initSetting}) : super(key: key);
  final Map initSetting;

  @override
  State<StatefulWidget> createState() {
    return _FirstInToAppCheckState();
  }
}

class _FirstInToAppCheckState extends State<FirstInToAppCheck> {
  bool isShowAgreenModal = false;

  @override
  void initState() {
    super.initState();
    Locale? locale = getCurrentLocale();
    if (locale != null) {
      logger("getCurrentLocale: $locale");
      S.load(locale);
    }
    // if (Platform.isAndroid) {
    //   bool? isAgreed = Global.sharedPrefs.getBool(Global.localStorageEnum["ANDROID_AGREEN_MODAL"]);
    //   if (isAgreed == null) {
    //     isShowAgreenModal = true;
    //   } else {
    //     //初始化个推
    //     Global.startInitGeTuiSDK();
    //     //
    //   }
    // } else {
    //   //初始化个推
    Global.startInitGeTuiSDK();
    //   //
    // }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isShowAgreenModal) {
      return Scaffold(
        body: Stack(
          children: [
            Container(
              width: 1.sw,
              height: 1.sh,
              color: Colors.white,
            ),
            Container(
              width: 1.sw,
              height: 1.sh,
              color: Colors.black.withOpacity(0.5),
            ),
            Align(
              alignment: Alignment.center,
              child: Container(
                padding: EdgeInsets.fromLTRB(20.sp, 10.sp, 10.sp, 10.sp),
                width: 0.8.sw,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.sp),
                  color: Colors.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: 8.sp),
                    Text(Lang.protocal_and_privacy,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w600,
                          color: color2B,
                        )),
                    SizedBox(height: 18.sp),
                    RichText(
                        text: TextSpan(
                            text: '    ${Lang.please_read_privacy1}',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: color2B,
                            ))),
                    SizedBox(height: 18.sp),
                    RichText(
                        text: TextSpan(
                            text: '    ${Lang.you_can_read}',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: color2B,
                            ),
                            children: [
                          TextSpan(
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Global.openUserAgreement(context);
                              },
                            text: Lang.user_agreement,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue,
                            ),
                          ),
                          TextSpan(
                            text: Lang.and,
                            style: const TextStyle(fontWeight: FontWeight.w400),
                          ),
                          TextSpan(
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Global.openPrivacy(context);
                              },
                            text: Lang.privacy,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue,
                            ),
                          ),
                          TextSpan(
                            text: Lang.please_read_privacy2,
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ])),
                    SizedBox(height: 24.sp),
                    Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
                      TextButton(
                          onPressed: () {
                            exit(0);
                          },
                          child: Text((Lang.not_agree),
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w600,
                                color: color2B.withOpacity(0.5),
                              ))),
                      TextButton(
                          onPressed: () {
                            Global.sharedPrefs.setBool(Global.localStorageEnum["ANDROID_AGREEN_MODAL"], true);
                            setState(() {
                              isShowAgreenModal = false;
                              //初始化个推
                              Global.startInitGeTuiSDK();
                              //
                            });
                          },
                          child: Text((Lang.agree_and_accept),
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w600,
                                color: color2B,
                              ))),
                    ]),
                  ],
                ),
              ),
            )
          ],
        ),
      );
    } else {
      bool showSplash = false;
      try {
        Map configJson = getStaticConfigJson();
        if (configJson["splash"]["rule"] == "day" &&
            Global.sharedPrefs.getString("show_splash_date") == Global.getDateByTimestamp()) {
          //配置每天展示一次，且今天已经展示过，则不再展示
          showSplash = false;
        } else if (configJson["splash"]["images"].length > 0) {
          showSplash = true;
          Global.sharedPrefs.setString("show_splash_date", Global.getDateByTimestamp());
        }
      } catch (ex) {
        //
      }

      return MainTabs(key: Global.mainTabGlobalKey);
    }
  }
}
