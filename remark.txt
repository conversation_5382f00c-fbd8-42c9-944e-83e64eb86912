camera for flutter :
ios version : ios10+*
android minSdkVersion: 21

----------------


bazel build -c opt --cxxopt=--std=c++11 --config=android_arm --cpu=armeabi-v7a //tensorflow/lite/delegates/hexagon/hexagon_nn:libhexagon_interface.so
bazel build -c opt --cxxopt=--std=c++11 --config=android_arm64 --cpu=arm64-v8a //tensorflow/lite/delegates/hexagon/hexagon_nn:libhexagon_interface.so
bazel build -c opt --cxxopt=--std=c++11 --config=android_x86 //tensorflow/lite/delegates/hexagon/hexagon_nn:libhexagon_interface.so
bazel build -c opt --cxxopt=--std=c++11 --config=android_x86_64 //tensorflow/lite/delegates/hexagon/hexagon_nn:libhexagon_interface.so


bazel build -c opt --cxxopt=--std=c++11 --config=android_arm64 --cpu=arm64-v8a //tensorflow/lite/delegates/hexagon:hexagon_delegate