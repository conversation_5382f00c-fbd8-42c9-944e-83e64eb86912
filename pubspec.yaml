name: mooeli
description: <PERSON><PERSON><PERSON>

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

#修改版本后要记得 pub get 一下
#测试时规则最后一位:
#奇数-> dev 开发环境
#偶数-> release 线上环境
version: 1.0.2+1002

environment:
  sdk: ">=2.17.5 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_local_notifications: ^16.3.2
  cupertino_icons: ^1.0.2
  #  camera: ^0.10.5+5
  shared_preferences: ^2.0.15
  video_player: ^2.4.5
  image_picker: ^0.8.5+3
  webview_flutter:
    path: ./custom_libs/webview_flutter/webview_flutter
  local_auth: ^2.1.2
  url_launcher: ^6.1.5
  path_provider: ^2.0.11
  sprintf: ^7.0.0
  flutter_screenutil:
    path: ./custom_libs/flutter_screenutil
  mooeli_ffi:
    path: ./custom_libs/mooeli_ffi
  #  mooeli_ultra:
  #    path: ./custom_libs/mooeli_ultra
  wifi_iot:
    path: ./custom_libs/wifi_iot-0.3.18
  dio: ^4.0.6
  orientation: ^1.3.0
  bot_toast: ^4.0.3
  flutter_pickers: ^2.1.8
  photo_view: ^0.14.0
  audioplayers: ^4.1.0
  archive: ^3.3.1
  #  getuiflut: 0.2.14
  crypto: ^3.0.2
  intl_utils: ^2.8.1
  route_life: ^1.0.0
  permission_handler: ^11.0.0
  external_path: ^1.0.3
  event_bus: ^2.0.0
  flutter_gif: ^0.0.4
  image_gallery_saver: ^2.0.3
  wakelock: ^0.6.2
  logger: ^1.3.0
  rf_expand_collapse_text: ^0.0.2
  flutter_smart_dialog: ^4.9.2
  cached_network_image: ^3.2.3
  launch_review: ^3.0.1
  iconfont_css_to_class:
    path: ./custom_libs/iconfont_css_to_class
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1
  encrypt: ^5.0.1
  flustars: ^2.0.1
  flutter_bugly: ^0.4.4
  uuid: ^3.0.7
  synchronized: ^3.0.1
  umeng_common_sdk: ^1.2.7
  share_plus: ^6.0.0
  open_file: ^3.3.2
  pdf: ^3.8.0
  flutter_pdfview: ^1.4.1
  syncfusion_flutter_pdfviewer: ^21.0.0
  #  flutter_inappwebview: ^6.0.0
  marquee: ^2.2.3
  flutter_uvc_camera:
    path: ./custom_libs/flutter_uvc_camera
  webcontent_converter: ^0.0.8+2
  device_info_plus: ^8.0.0
  flutter_staggered_grid_view: ^0.7.0
  install_plugin: ^2.1.0
  wifi_scan: ^0.4.0+1
  wifi_flutter:
    path: ./custom_libs/wifi_flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  image: ^3.2.0

flutter:
  uses-material-design: true
  assets:
    - assets/
    - res/icons/
    - res/imgs/
    - res/scan/
    - res/tabs/
    - res/videos/
    - res/local/
    - assets/webPdf/
    - assets/webPdf/appPdf/
    - assets/webPdf/assets/
    - assets/quickPdf/
    - assets/quickPdf/css/
    - assets/quickPdf/js/

  fonts:
    - family: DINExp
      fonts:
        - asset: assets/fonts/DINExp.otf
          weight: 700
    - family: IconFont
      fonts:
        - asset: assets/fonts/iconfont.ttf

flutter_intl:
  enabled: true
  arb_dir: resources/l10n
  output_dir: lib/Utils/languages
  main_locale: zh