group 'io.flutter.plugins.webviewflutter'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 32

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lintOptions {
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency'
    }

    dependencies {
        implementation 'androidx.annotation:annotation:1.5.0'
        implementation 'androidx.webkit:webkit:1.5.0'
        testImplementation 'junit:junit:4.13.2'
        testImplementation 'org.mockito:mockito-inline:5.1.0'
        testImplementation 'androidx.test:core:1.3.0'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
