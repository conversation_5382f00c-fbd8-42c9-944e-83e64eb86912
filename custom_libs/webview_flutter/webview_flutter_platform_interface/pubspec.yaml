name: webview_flutter_platform_interface
description: A common platform interface for the webview_flutter plugin.
#repository: https://github.com/flutter/plugins/tree/main/packages/webview_flutter/webview_flutter_platform_interface
issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+webview_flutter%22
# NOTE: We strongly prefer non-breaking changes, even at the expense of a
# less-clean API. See https://flutter.dev/go/platform-interface-breaking-changes
version: 2.0.1

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  meta: ^1.7.0
  plugin_platform_interface: ^2.1.0

dev_dependencies:
  build_runner: ^2.1.8
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0
