name: webview_flutter_wkwebview
description: A Flutter plugin that provides a WebView widget based on Apple's WKWebView control.
repository: https://github.com/flutter/plugins/tree/main/packages/webview_flutter/webview_flutter_wkwebview
issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+webview%22
version: 3.1.0

environment:
  sdk: ">=2.17.0 <3.0.0"
  flutter: ">=3.0.0"

flutter:
  plugin:
    implements: webview_flutter
    platforms:
      ios:
        pluginClass: FLTWebViewFlutterPlugin
        dartPluginClass: WebKitWebViewPlatform

dependencies:
  flutter:
    sdk: flutter
  path: ^1.8.0
  webview_flutter_platform_interface:
    path: ./../webview_flutter_platform_interface

dev_dependencies:
  build_runner: ^2.1.5
  flutter_driver:
    sdk: flutter
  flutter_test:
    sdk: flutter
  mockito: ^5.3.2
  pigeon: ^4.2.13
