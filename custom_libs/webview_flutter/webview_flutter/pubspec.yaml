name: webview_flutter
description: A Flutter plugin that provides a WebView widget on Android and iOS.
#repository: https://github.com/flutter/plugins/tree/main/packages/webview_flutter/webview_flutter
#issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+webview%22
version: 4.0.4

environment:
  sdk: ">=2.17.0 <3.0.0"
  flutter: ">=3.0.0"

flutter:
  plugin:
    platforms:
      android:
        default_package: webview_flutter_android
      ios:
        default_package: webview_flutter_wkwebview

dependencies:
  flutter:
    sdk: flutter
  webview_flutter_android:
    path: ./../webview_flutter_android
  webview_flutter_platform_interface:
    path: ./../webview_flutter_platform_interface
  webview_flutter_wkwebview:
    path: ./../webview_flutter_wkwebview

dev_dependencies:
  build_runner: ^2.1.5
  flutter_driver:
    sdk: flutter
  flutter_test:
    sdk: flutter
  mockito: ^5.3.2
  plugin_platform_interface: ^2.1.3
