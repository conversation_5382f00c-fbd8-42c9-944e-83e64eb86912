name: webview_flutter_web
description: A Flutter plugin that provides a WebView widget on web.
repository: https://github.com/flutter/plugins/tree/main/packages/webview_flutter/webview_flutter_web
issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+webview%22
version: 0.2.2

environment:
  sdk: ">=2.14.0 <3.0.0"
  flutter: ">=3.0.0"

flutter:
  plugin:
    implements: webview_flutter
    platforms:
      web:
        pluginClass: WebWebViewPlatform
        fileName: webview_flutter_web.dart

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  webview_flutter_platform_interface:
    path: ./../webview_flutter_platform_interface

dev_dependencies:
  build_runner: ^2.1.5
  flutter_driver:
    sdk: flutter
  flutter_test:
    sdk: flutter
  mockito: ^5.3.2
