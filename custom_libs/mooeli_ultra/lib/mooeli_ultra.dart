
import 'package:flutter/services.dart';

const methodChannel = MethodChannel('mooeli_ultra');

Future<dynamic> callMcByFunNameByUltra(String name, {dynamic params}) async {
  return await methodChannel.invokeMethod(name, params);
}

//监听native->flutter 事件
void addListenNative(String name, dynamic callBack) async {
  methodChannel.setMethodCallHandler((call) {
    dynamic result;
    switch (name) {
    //Future<dynamic> listenVolume(value)async{}
    //callMcByFunName("addListenVolume");
    //addListenNative("ListenVolume", listenVolume);
    //callMcByFunName("removeListenVolume");
      case "ListenVolume":
        if (callBack != null) {
          result = callBack(call.arguments);
        }
        break;
      default:
        result = null;
        break;
    }
    return result;
  });
}