import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

class UltraCameraView extends StatefulWidget {

  const UltraCameraView({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _UltraCameraViewState();
}

class _UltraCameraViewState extends State<UltraCameraView> {


  @override
  initState(){super.initState();initAsyncState();}

  initAsyncState() async {}

  @override
  dispose(){super.dispose();}


  @override
  Widget build(BuildContext context) {
    const String viewType = '<mooeli-ultra-view>';
    final Map<String, dynamic> creationParams = <String, dynamic>{};

    if(Platform.isAndroid){
      return AndroidView(
        viewType: viewType,
        layoutDirection: TextDirection.ltr,
        creationParams: creationParams,
        creationParamsCodec: const StandardMessageCodec(),
      );
    }else{
      return UiKitView(
        viewType: viewType,
        layoutDirection: TextDirection.ltr,
        creationParams: creationParams,
        creationParamsCodec: const StandardMessageCodec(),
      );
    }
  }
}