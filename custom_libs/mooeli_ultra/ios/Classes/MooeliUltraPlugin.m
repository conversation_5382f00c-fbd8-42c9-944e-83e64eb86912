#import "MooeliUltraPlugin.h"
#import "UltraCameraView.h"

#import "GXDeviceManager.h"

@implementation MooeliUltraPlugin{
    FlutterMethodChannel *methodChannel;
    UltraCameraViewFactory* ultraCameraViewFactory;
}

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  FlutterMethodChannel* channel = [FlutterMethodChannel
      methodChannelWithName:@"mooeli_ultra"
            binaryMessenger:[registrar messenger]];
  
  //
  UltraCameraViewFactory* factory =
      [[UltraCameraViewFactory alloc] initWithMessenger:registrar.messenger];
  [registrar registerViewFactory:factory withId:@"<mooeli-ultra-view>"];
    
  //    [registrar addMethodCallDelegate:[[MooeliUltraPlugin alloc] init] channel:channel];
  MooeliUltraPlugin *instance = [[MooeliUltraPlugin alloc] initWithRegistry:[registrar textures] messenger:[registrar messenger] channel:channel ultraCameraViewFactory:factory];
  [registrar addMethodCallDelegate:instance channel:channel];
}

//初始化
- (instancetype)initWithRegistry:(NSObject<FlutterTextureRegistry> *)registry messenger:(NSObject<FlutterBinaryMessenger> *)messenger channel:(FlutterMethodChannel *)channel ultraCameraViewFactory:(UltraCameraViewFactory*)ultraFactory
{
    self = [super init];
    NSAssert(self, @"super init cannot be nil");
    methodChannel = channel;
    ultraCameraViewFactory = ultraFactory;
    //返回self
    return self;
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
  if ([@"getPlatformVersion" isEqualToString:call.method]) {
    result([@"iOS " stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
  }else if ([@"saveNewestCameraImg" isEqualToString:call.method]) {
      NSString *path = call.arguments[@"path"];
      [self saveUIImageToPath:path];
      result(nil);
  }
  else if ([@"connectCamera" isEqualToString:call.method]) {
      [[GXDeviceManager shareInstence] IOSConnectDevice];
      result(nil);
  }else if ([@"isConnectedCamera" isEqualToString:call.method]) {
      UIImage * image = [ultraCameraViewFactory getNewestCameraImg];
      if(image != NULL){
          result([NSNumber numberWithBool:TRUE]);
      }else{
          result([NSNumber numberWithBool:FALSE]);
      }
  }else if ([@"disconnectCamera" isEqualToString:call.method]) {
      [ultraCameraViewFactory clearNewestCameraImg];
      [[GXDeviceManager shareInstence] IOSDisconnectDevice];
      result(nil);
  }else if ([@"getBatteryPercentValue" isEqualToString:call.method]) {
      int value = [ultraCameraViewFactory getBatteryPercentValue];
     result([NSNumber numberWithInt:value]);
  }else if ([@"setOverturnCamera" isEqualToString:call.method]) {
      [[GXDeviceManager shareInstence] DeviceRevVideo];
      result(nil);
  }else {
    result(FlutterMethodNotImplemented);
  }
}

-(void)saveUIImageToPath:(NSString*)path{
//    ultraCameraViewFactory
//测试图
//    image = [UIImage imageNamed:@"<EMAIL>"];
    UIImage * image0 = [ultraCameraViewFactory getNewestCameraImg];
    
    UIImage * image1 = [UIImage imageWithCGImage:image0.CGImage scale:image0.scale orientation:UIImageOrientationLeftMirrored];
    
    UIImage * image = [UIImage imageWithCGImage:image1.CGImage scale:image1.scale orientation:UIImageOrientationUp];

    if([path containsString:@".png"]){
        [UIImagePNGRepresentation(image) writeToFile:path atomically:YES];
    }else{
        CGFloat quality = 1.0;
        [UIImageJPEGRepresentation(image, quality) writeToFile:path atomically:YES];
    }
}


-(const void*)getUIImageBytes:(UIImage*)image{
    CGFloat quality = 1.0;
    NSData *imageData = UIImageJPEGRepresentation(image,quality);
    return imageData.bytes;
}

@end
