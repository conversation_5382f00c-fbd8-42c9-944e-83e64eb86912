//
//  UltraCameraView.m
//  mooeli_ultra
//
//  Created by <PERSON><PERSON> on 2022/12/12.
//

#import "UltraCameraView.h"
#import "GXDeviceManager.h"

@implementation UltraCameraViewFactory {
    NSObject<FlutterBinaryMessenger>* _messenger;
    UltraCameraView*cameraView;
}

- (instancetype)initWithMessenger:(NSObject<FlutterBinaryMessenger>*)messenger {
  self = [super init];
  if (self) {
    _messenger = messenger;
  }
  return self;
}

-(UIImage*)getNewestCameraImg{
    return [cameraView getNewestCameraImg];
}

-(void)clearNewestCameraImg{
    [cameraView clearNewestCameraImg];
}

-(int)getBatteryPercentValue{
    return [cameraView getBatteryPercentValue];
}

- (NSObject<FlutterPlatformView>*)createWithFrame:(CGRect)frame
                                   viewIdentifier:(int64_t)viewId
                                        arguments:(id _Nullable)args {
    cameraView = [[UltraCameraView alloc] initWithFrame:frame
                                         viewIdentifier:viewId
                                              arguments:args
                                        binaryMessenger:_messenger];
    return cameraView;
}

@end

@implementation UltraCameraView {
    UIImageView *_view;
    UIImage * newestImg;
    NSString * batteryPercentValue;
}

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger {
  if (self = [super init]) {
      _view =  [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height)];
      _view.backgroundColor = [UIColor darkGrayColor];

      [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(GetImage:) name:@"GETTHEIMAGE" object:nil];
      //用于获取设备电量数据，每秒回传一次
      [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(GetBattery:) name:@"GETTHEBATTERY" object:nil];
      [[GXDeviceManager shareInstence] CeckLocalNetwork];
  }
  return self;
}


-(int)getBatteryPercentValue{
    return [batteryPercentValue intValue];
}


- (void)GetBattery:(NSNotification *)notification
{
    NSString *BatteryStr = (NSString *)notification.object;
    NSArray *BatArr = [BatteryStr componentsSeparatedByString:@"*"];
    batteryPercentValue =  BatArr[0];
}


-(UIImage*)getNewestCameraImg{
    return newestImg;
}

-(void)clearNewestCameraImg{
    newestImg = nil;
}

- (void)GetImage:(NSNotification *)notification
{
    newestImg = (UIImage *)notification.object;
    
    //NSLog(@"ultra image: %f",newestImg==NULL?0.0:newestImg.size.width);
    _view.image = newestImg;
}

- (UIView*)view {
  return _view;
}
- (void)dealloc
{
    [[GXDeviceManager shareInstence] IOSDisconnectDevice];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
