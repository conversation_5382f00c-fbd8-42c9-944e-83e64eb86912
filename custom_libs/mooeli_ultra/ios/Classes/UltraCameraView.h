//
//  UltraCameraView.h
//  Pods
//
//  Created by <PERSON><PERSON> on 2022/12/12.
//

#import <Flutter/Flutter.h>
#import "UltraCameraView.h"

@interface UltraCameraView : NSObject <FlutterPlatformView>

- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger;

- (UIImage*)getNewestCameraImg;
- (int)getBatteryPercentValue;
- (void)clearNewestCameraImg;
- (UIView*)view;
@end


@interface UltraCameraViewFactory : NSObject <FlutterPlatformViewFactory>
- (UIImage*)getNewestCameraImg;
- (int)getBatteryPercentValue;
- (void)clearNewestCameraImg;
- (instancetype)initWithMessenger:(NSObject<FlutterBinaryMessenger>*)messenger;
@end
