//
//  GXDeviceManager.h
//  JRSDK
//
//  Created by 洪晓白 on 2019/6/11.
//  Copyright © 2019年 洪晓白. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol GXDeviceManagerDelegate <NSObject>

- (void)LocalRecordingTime:(NSString *)time;
- (void)LocalDidFinishRecordFilePath:(NSString *)file;
- (void)LocalDidFinishTakePhotoFilePath:(NSString *)file;

@end

@interface GXDeviceManager : NSObject
@property (nonatomic,weak)id<GXDeviceManagerDelegate> delegate;
@property (nonatomic,assign) BOOL Debug;
@property (nonatomic,assign) double VideoDeltaTime;
@property (nonatomic,strong) NSDate *LastVideoDate;
+ (instancetype)shareInstence;
- (void)CeckLocalNetwork;
- (void)IOSConnectDevice;
- (void)IOSDisconnectDevice;
- (void)DeviceRevVideo;
- (void)DeviceGetSSID;
- (void)LocalStartRecordVideo;
- (void)LocalStopRecordVideo;
- (void)LocalTakePhoto;
- (void)DeviceSetMirroredDisplay:(BOOL)mirrored;

@end

NS_ASSUME_NONNULL_END
