#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint mooeli_ultra.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'mooeli_ultra'
  s.version          = '0.0.1'
  s.summary          = 'UltraView for <PERSON><PERSON><PERSON>.'
  s.description      = <<-DESC
A new Flutter project.
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'xiaominghimi' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*','GXSDK/*'
  s.public_header_files = 'Classes/**/*.h'
  s.dependency 'Flutter'
  s.platform = :ios, '13.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386'}
  s.xcconfig = {
#       'HEADER_SEARCH_PATHS'=>'${PODS_TARGET_SRCROOT}/ThirdParty/FFMEPGDecode/universal/include',
#       'LIBRARY_SEARCH_PATHS'=>'${PODS_TARGET_SRCROOT}/ThirdParty/FFMEPGDecode/universal/lib',
      'Enable_Bitcode'=> 'NO',
      'OTHER_LDFLAGS'  =>  '-lObjC $(PROJECT_DIR)/../custom_libs/mooeli_ultra/ios/GXSDK/GXSDK.a'
  }
  s.libraries = 'iconv.2','c++','z'
end
