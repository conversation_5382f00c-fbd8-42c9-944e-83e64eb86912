package com.example.mooeli_ultra;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/** MooeliUltraPlugin */
public class MooeliUltraPlugin implements FlutterPlugin, MethodCallHandler {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private MethodChannel channel;
  private UltraCameraViewFactory ultraCameraViewFactory;

  @Override
  public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
    channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "mooeli_ultra");
    channel.setMethodCallHandler(this);
    //
    ultraCameraViewFactory = new UltraCameraViewFactory();
    flutterPluginBinding.getPlatformViewRegistry().registerViewFactory("<mooeli-ultra-view>", ultraCameraViewFactory);
  }

  @Override
  public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
    if (call.method.equals("getPlatformVersion")) {
      result.success("Android " + android.os.Build.VERSION.RELEASE);
    }else if (call.method.equals("saveNewestCameraImg")) {
      String path = call.argument("path");
      ultraCameraViewFactory.ultraCameraView.saveUIImageToPath(path);
      result.success(null);
    }else if (call.method.equals("connectCamera")) {
      if(ultraCameraViewFactory!=null && ultraCameraViewFactory.ultraCameraView !=null){
        ultraCameraViewFactory.ultraCameraView.connectCamera();
      }
      result.success(null);
    }else if (call.method.equals("isConnectedCamera")) {
      if(ultraCameraViewFactory!=null && ultraCameraViewFactory.ultraCameraView !=null){
        boolean isConnected = ultraCameraViewFactory.ultraCameraView.isConnectedCamera();
        result.success(isConnected);
      }else{
        result.success(false);
      }
    }else if (call.method.equals("disconnectCamera")) {
      if(ultraCameraViewFactory!=null && ultraCameraViewFactory.ultraCameraView !=null){
        ultraCameraViewFactory.ultraCameraView.disconnectCamera();
      }
      result.success(null);
    }else if (call.method.equals("getBatteryPercentValue")) {
      if(ultraCameraViewFactory!=null && ultraCameraViewFactory.ultraCameraView !=null){
        int value = ultraCameraViewFactory.ultraCameraView.getBatteryValue();
        result.success(value);
      }
    } else if (call.method.equals("setOverturnCamera")) {
      if(ultraCameraViewFactory!=null && ultraCameraViewFactory.ultraCameraView !=null){
        ultraCameraViewFactory.ultraCameraView.setOverturnCamera();
      }
      result.success(null);
    } else {
      result.notImplemented();
    }
  }

  @Override
  public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
    channel.setMethodCallHandler(null);
  }
}
