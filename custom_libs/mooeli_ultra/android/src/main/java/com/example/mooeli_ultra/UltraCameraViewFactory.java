package com.example.mooeli_ultra;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.annotation.NonNull;
import io.flutter.plugin.common.StandardMessageCodec;
import io.flutter.plugin.platform.PlatformView;
import io.flutter.plugin.platform.PlatformViewFactory;
import java.util.Map;

class UltraCameraViewFactory extends PlatformViewFactory {
    public UltraCameraView ultraCameraView;

    UltraCameraViewFactory() {
        super(StandardMessageCodec.INSTANCE);
    }

    @NonNull
    public UltraCameraView create(@NonNull Context context, int id, @Nullable Object args) {
        final Map<String, Object> creationParams = (Map<String, Object>) args;
        ultraCameraView = new UltraCameraView(context, id, creationParams);
        return ultraCameraView;
    }
}
