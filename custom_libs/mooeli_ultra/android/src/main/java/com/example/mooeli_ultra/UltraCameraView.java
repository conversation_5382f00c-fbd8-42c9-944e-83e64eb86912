package com.example.mooeli_ultra;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Matrix;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import io.flutter.plugin.platform.PlatformView;

import java.util.Map;

import androidx.appcompat.app.AppCompatActivity;

import com.netopsun.deviceshub.base.CMDCommunicator;
import com.netopsun.deviceshub.base.Devices;
import com.netopsun.dtsdk.DevicesHub;
import com.netopsun.dtsdk.player.GXVideoView;

import org.w3c.dom.Text;
import java.io.IOException;
import java.io.FileOutputStream;
import java.io.File;
import java.io.ByteArrayOutputStream;
import android.graphics.Bitmap;
import android.util.Log;

class UltraCameraView implements PlatformView {
    @NonNull
    private final GXVideoView gxVideoView;
    private Devices devices;
    private FrameLayout rootLayoutView;
    private TextView tv;
    private int battlePercentValue;
    private boolean isOverturn = false;

    private Context context;

    UltraCameraView(@NonNull Context context, int id, @Nullable Map<String, Object> creationParams) {
        this.context = context;
        rootLayoutView = (FrameLayout) LayoutInflater.from(context).inflate(R.layout.ultra_camera,null);
        gxVideoView = (GXVideoView) rootLayoutView.findViewById(R.id.ultra_camera);
        devices = DevicesHub.open("w70://");
        Log.e("UltraView", "UltraView init");
    }

    @NonNull
    @Override
    public View getView() {
        return rootLayoutView;
    }

    @Override
    public void dispose() {
        gxVideoView.stopPlayback();
        devices.getCMDCommunicator().disconnect();
        devices.release();
    }

    public int getBatteryValue(){
        return battlePercentValue;
    }

    public void setOverturnCamera(){
        /**
         * @param waitRespond 忽略，暂未用到
         * @param timeOutSecond 超时时间，单位为秒
         * @param isReversal 是否翻转
         * @param onExecuteCMDResult 忽略，暂未用到
         * @return
         */
        isOverturn = !isOverturn;
        devices.getCMDCommunicator().rotateVideo(false,3,isOverturn,null);
    }

    public void saveUIImageToPath(String path){

        Bitmap bitmap0 = Bitmap.createBitmap(gxVideoView.getmVideoWidth(), gxVideoView.getmVideoHeight(), Bitmap.Config.ARGB_8888);
        gxVideoView.getBitmap(bitmap0);

        Matrix matrix = new Matrix();
        matrix.postScale(-1, 1);   //镜像水平翻转
        Bitmap bitmap = Bitmap.createBitmap(bitmap0, 0, 0, gxVideoView.getmVideoWidth(), gxVideoView.getmVideoHeight(), matrix, true);

        File saveFile = new File(path);
        try {
            FileOutputStream saveImgOut = new FileOutputStream(saveFile,false);
            int quality = 100;
            if(path.indexOf(".png")!=-1){
                bitmap.compress(Bitmap.CompressFormat.PNG, quality, saveImgOut);
            }else{
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, saveImgOut);
            }
            saveImgOut.flush();
            saveImgOut.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    public byte[] getBitmapBytes(Bitmap bitmap){
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
        try {
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }

    public void disconnectCamera(){
        gxVideoView.stopPlayback();
    }

    public boolean isConnectedCamera(){
        return gxVideoView.isPlaying();
    }

    public void connectCamera() {
        //没有断开连接时，多次调用连接函数，会导致无法看图，可以在每次连接前调用一下断开连接的函数
        devices.getVideoCommunicator().disconnect();
        devices.getCMDCommunicator().disconnect();

        String localIpAddress = getLocalIpAddress(context);
        devices.bindInetAddress(localIpAddress);//通过IP地址绑定网卡

        //设置超时时间，当3000ms没有收到视频数据时，尝试重新连接
        devices.getVideoCommunicator().setReadFrameTimeOut(3000);
        //设置重连次数，当小于0时为无限次
        devices.getVideoCommunicator().setShouldReconnectTimes(-1);
        gxVideoView.setRender(0);//由于内部使用textureview渲染视频，每次进入activity或者调用setVideoCommunicator前需调用此句，否则部分手机会出现偶尔无法刷图
        gxVideoView.setMagnificationXCoefficient(-1);
        gxVideoView.postDelayed(new Runnable() {
            @Override
            public void run() {
                gxVideoView.setVideoCommunicator(devices.getVideoCommunicator());
                gxVideoView.start();//此处延迟300ms再开启视频，避免部分机型surfaceTexture未建立完成导致刷图黑屏
                Log.e("UltraView", "UltraView start");
            }
        }, 300);
        //连接命令端口
        devices.getCMDCommunicator().setBatteryMsgListener(new CMDCommunicator.OnBatteryMsgListener() {
            /**
             * @param level 电量百分比
             * @param value 实际回传值
             */
            @Override
            public void onBattery(int level, int value) {
                battlePercentValue  = level;
            }
        });
        devices.getCMDCommunicator().connect();
        Log.e("UltraView", "UltraView connect");
    }

    /**
     * 获取当前WiFi的ip地址
     *
     * @param context
     * @return
     */
    public static String getLocalIpAddress(Context context) {
        try {

            WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                    .getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            int i = wifiInfo.getIpAddress();
            return int2ip(i);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 将ip的整数形式转换成ip形式
     *
     * @param ipInt
     * @return
     */
    public static String int2ip(int ipInt) {
        StringBuilder sb = new StringBuilder();
        sb.append(ipInt & 0xFF).append(".");
        sb.append((ipInt >> 8) & 0xFF).append(".");
        sb.append((ipInt >> 16) & 0xFF).append(".");
        sb.append((ipInt >> 24) & 0xFF);
        return sb.toString();
    }
}