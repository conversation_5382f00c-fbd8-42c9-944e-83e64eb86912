#import "MooeliFfiPlugin.h"
#import <MediaPlayer/MediaPlayer.h>
#import <AVKit/AVKit.h>
///获取设备类型必须的库
#import <sys/utsname.h>
#if __has_include(<mooeli_ffi/mooeli_ffi-Swift.h>)
#import <mooeli_ffi/mooeli_ffi-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "mooeli_ffi-Swift.h"
#endif


@implementation MooeliFfiPlugin{
    MPVolumeView *_volumeView;
    UISlider *_volumeViewSlider;
    BOOL _volumeInWindow;
    FlutterMethodChannel* _channel;
    bool isAddListenVolume;
    bool isAddListenOrientation;
}
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    //swift plugin type
//    [SwiftMooeliFfiPlugin registerWithRegistrar:registrar];

    //object-c type
    FlutterMethodChannel* methodChannel = [FlutterMethodChannel methodChannelWithName:@"mooeli_ffi" binaryMessenger:registrar.messenger];
    //初始化
//    [registrar addMethodCallDelegate:[[MooeliFfiPlugin alloc] init] channel:methodChannel];
    MooeliFfiPlugin *instance = [[MooeliFfiPlugin alloc] initWithRegistry:[registrar textures] messenger:[registrar messenger] channel:methodChannel];
    [registrar addMethodCallDelegate:instance channel:methodChannel];
}

//初始化
- (instancetype)initWithRegistry:(NSObject<FlutterTextureRegistry> *)registry messenger:(NSObject<FlutterBinaryMessenger> *)messenger channel:(FlutterMethodChannel *)channel
{
    self = [super init];
    NSAssert(self, @"super init cannot be nil");
    _channel = channel;
    //初始化音量相关
    [self initVolumeView];
    //返回self
    return self;
}


- (void)initVolumeView {
  if (_volumeView == nil) {
    _volumeView =
        [[MPVolumeView alloc] initWithFrame:CGRectMake(-100, -100, 10, 10)];
    _volumeView.hidden = YES;
  }
  if (_volumeViewSlider == nil) {
    for (UIView *view in [_volumeView subviews]) {
      if ([view.class.description isEqualToString:@"MPVolumeSlider"]) {
        _volumeViewSlider = (UISlider *)view;
        break;
      }
    }
  }
  if (!_volumeInWindow) {
    UIWindow *window = UIApplication.sharedApplication.keyWindow;
    if (window != nil) {
      [window addSubview:_volumeView];
      _volumeInWindow = YES;
    }
  }
}
- (float)getVolume {
  if (_volumeViewSlider == nil) {
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    CGFloat currentVol = audioSession.outputVolume;
    return currentVol;
  } else {
    return _volumeViewSlider.value;
  }
}

- (float)setVolume:(float)vol {
  if (vol > 1.0) {
    vol = 1.0;
  } else if (vol < 0) {
    vol = 0.0;
  }
  [_volumeViewSlider setValue:vol animated:FALSE];
  vol = _volumeViewSlider.value;
  return vol;
}


- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
//  NSDictionary *arguments = [call arguments];
    if ([@"testByMethodChannel" isEqualToString:call.method]) {
        result([@"iOS " stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
    }
    else if ([@"getAppVersion" isEqualToString:call.method]) {
        result([[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"]
           ?: [NSNull null]);
    }
    else if ([@"getBrightness" isEqualToString:call.method]) {
      result([NSNumber numberWithFloat:[UIScreen mainScreen].brightness]);
    }
    else if ([@"setBrightness" isEqualToString:call.method]) {
      NSNumber *brightness = call.arguments[@"brightness"];
      [[UIScreen mainScreen] setBrightness:brightness.floatValue];
      result(nil);
    }
    else if ([@"isBrightnessKeptOn" isEqualToString:call.method]) {
      bool isIdleTimerDisabled =  [[UIApplication sharedApplication] isIdleTimerDisabled];
      result([NSNumber numberWithBool:isIdleTimerDisabled]);
    }
    else if ([@"setBrightnessKeptOn" isEqualToString:call.method]) {
      NSNumber *b = call.arguments[@"on"];
      [[UIApplication sharedApplication] setIdleTimerDisabled:b.boolValue];
      result(nil);
    }
    else if ([@"getVolume" isEqualToString:call.method]) {
        float vol = [self getVolume];
        result([NSNumber numberWithFloat:vol]);
    }
    else if ([@"setVolume" isEqualToString:call.method]) {
      NSNumber *volume = call.arguments[@"volume"];
      [self setVolume:[volume floatValue]];
      result(nil);
    }
    else if ([@"addListenVolume" isEqualToString:call.method]) {
        [self addListenVolume];
        result(nil);
    }
    else if ([@"removeListenVolume" isEqualToString:call.method]) {
        [self removeListenVolume];
        result(nil);
    }
    else if ([@"getDeviceInfo" isEqualToString:call.method]) {
        NSString *deveceName = [self getDeviceModelName];
        NSString *deveceVersion = [[UIDevice currentDevice] systemVersion];
        result(@{
            @"name":deveceName,
            @"version":deveceVersion,
            @"language":[[NSLocale currentLocale] localeIdentifier],
        });
    }else if ([@"ptraceDebugger" isEqualToString:call.method]) {
        //stawrt ptraceDebugger
        [SwiftMooeliFfiPlugin ptraceDebugger];
        result(nil);
    }else if ([@"checkProxy" isEqualToString:call.method]) {
        //stawrt ptraceDebugger
        bool isSetProxy = [self checkProxySetting];
        result([NSNumber numberWithBool:isSetProxy]);
    }else if ([@"checkJailbrokenRoot" isEqualToString:call.method]) {
        bool isCheckJR = [self checkJailbroken];
        result([NSNumber numberWithBool:isCheckJR]);
    }
    else {
      result(FlutterMethodNotImplemented);
    }
}

//是否设置了代理
- (BOOL) checkProxySetting {
 NSDictionary *proxySettings = (__bridge NSDictionary *)(CFNetworkCopySystemProxySettings());
 NSArray *proxies = (__bridge NSArray *)(CFNetworkCopyProxiesForURL((__bridge CFURLRef _Nonnull)([NSURL URLWithString:@"https://www.baidu.com"]), (__bridge CFDictionaryRef _Nonnull)(proxySettings)));
// NSLog(@"\n%@",proxies);
 NSDictionary *settings = proxies[0];
// NSLog(@"%@",[settings objectForKey:(NSString *)kCFProxyHostNameKey]);
// NSLog(@"%@",[settings objectForKey:(NSString *)kCFProxyPortNumberKey]);
// NSLog(@"%@",[settings objectForKey:(NSString *)kCFProxyTypeKey]);
 if ([[settings objectForKey:(NSString *)kCFProxyTypeKey] isEqualToString:@"kCFProxyTypeNone"])
 {
  return NO;
 }
 else
 {
  return YES;
 }
}

//是否处于越狱环境，反编译风险检测
- (BOOL)checkJailbroken {
    // 检查是否存在越狱常用文件
    NSArray *jailFilePaths = @[@"/Applications/Cydia.app",
                               @"/Library/MobileSubstrate/MobileSubstrate.dylib",
                               @"/bin/bash",
                               @"/usr/sbin/sshd",
                               @"/etc/apt"];
    for (NSString *filePath in jailFilePaths) {
        if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
            return YES;
        }
    }

    // 检查是否安装了越狱工具Cydia
    if([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"cydia://package/com.example.package"]]){
        return YES;
    }

    // 检查是否有权限读取系统应用列表
    if ([[NSFileManager defaultManager] fileExistsAtPath:@"/User/Applications/"]){
        NSArray *applist = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:@"/User/Applications/"
                                                                               error:nil];
        NSLog(@"applist = %@",applist);
        return YES;
    }

    //  检测当前程序运行的环境变量
    char *env = getenv("DYLD_INSERT_LIBRARIES");
    if (env != NULL) {
        return YES;
    }

    return NO;
}


//监测音量变化通知
- (void)addListenVolume
{
    if(isAddListenVolume){
        return;
    }
    isAddListenVolume = true;
    NSError *error;
    [[AVAudioSession sharedInstance] setActive:YES error:&error];
    [UIApplication.sharedApplication beginReceivingRemoteControlEvents];
    [[AVAudioSession sharedInstance] addObserver:self forKeyPath:@"outputVolume" options:NSKeyValueObservingOptionNew context:nil];
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    if ([keyPath isEqual:@"outputVolume"]) {
        [_channel invokeMethod:@"ListenVolume" arguments:@([[change objectForKey:@"new"] floatValue])];
    }
}
- (void)removeListenVolume {
    if(!isAddListenVolume){
        return;
    }
    isAddListenVolume = false;
    [UIApplication.sharedApplication endReceivingRemoteControlEvents];
    [AVAudioSession.sharedInstance removeObserver:self forKeyPath:@"outputVolume" context:nil];
}

//监听音量
- (void)volumeChanged:(NSNotification* )notification
{
    // 当前手机音量
    double volume = [[notification.userInfo valueForKey:@"AVSystemController_AudioVolumeNotificationParameter"] doubleValue];
    //发送数据到flutter
    [_channel invokeMethod:@"ListenVolume" arguments:@(volume)];
}

// 设备Models 信息 https://www.theiphonewiki.com/wiki/Models#p-search
- (NSString *)getDeviceModelName {
    struct utsname systemInfo;
    uname(&systemInfo);
          
    NSString * deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}
@end
