// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_JACOBI_MODULE_H
#define EIGEN_JACOBI_MODULE_H

#include "Core"

#include "src/Core/util/DisableStupidWarnings.h"

/** \defgroup Jacobi_Module Jacobi module
  * This module provides Jacobi and Givens rotations.
  *
  * \code
  * #include <Eigen/Jacobi>
  * \endcode
  *
  * In addition to listed classes, it defines the two following MatrixBase methods to apply a Jacobi or Givens rotation:
  *  - MatrixBase::applyOnTheLeft()
  *  - MatrixBase::applyOnTheRight().
  */

#include "src/Jacobi/Jacobi.h"

#include "src/Core/util/ReenableStupidWarnings.h"

#endif // EIGEN_JACOBI_MODULE_H

