//
// Created by syq on 2023/4/19.
//
#pragma once
#import <opencv2/opencv.hpp>
#include "../utils/defs.hpp"

class ChohoKalmanStabilizer {
private:
    cv::Ka<PERSON>Filter KF;
    Uint64 vacantCount;  //未检测到的点的数量
    float old_x, old_y;

    void initKalmanFilter(const Point2f *init_pos);

public:
    ChohoKalmanStabilizer()
            : vacantCount(1000), old_x(-99999.0f), old_y(-99999.0f) {}
    ChohoKalmanStabilizer(const Point2f &init_pos)
            : vacantCount(0), old_x(init_pos.x), old_y(init_pos.y) {
        initKalmanFilter(&init_pos);
    }
    ~ChohoKalmanStabilizer() {}
    void resetKalman();
    Point2f getEstPt(const Point2f *pt);
};

void ChohoKalmanStabilizer::resetKalman() {
    vacantCount = 1000;
    old_x = -99999.0f;
    old_y = -99999.0f;
}

void ChohoKalmanStabilizer::initKalmanFilter(const Point2f *init_pos) {
    KF.init(4, 4, 0, CV_32F);
    KF.transitionMatrix = (Mat1f(4, 4)
            << 1, 0, 0.3, 0, 0, 1, 0, 0.3, 0, 0, 1, 0, 0, 0, 0, 1);
    KF.statePre.at<float>(0) = init_pos->x;
    KF.statePre.at<float>(1) = init_pos->y;
    KF.statePre.at<float>(2) = 0;
    KF.statePre.at<float>(3) = 0;
    setIdentity(KF.measurementMatrix);
    setIdentity(KF.processNoiseCov, Scalar(0.01f, 0.01f, 0.0001f, 0.0001f));
    setIdentity(KF.measurementNoiseCov, Scalar(0.3f, 0.3f, 1e-1, 1e-1));
    setIdentity(KF.errorCovPost, Scalar(0.01f, 0.01f, 0.1f, 0.1f));
    KF.statePost.at<float>(0) = init_pos->x;
    KF.statePost.at<float>(1) = init_pos->y;
    KF.statePost.at<float>(2) = 0;
    KF.statePost.at<float>(3) = 0;
}

Point2f ChohoKalmanStabilizer::getEstPt(const Point2f *pt) {
    if (pt == nullptr) { //没有检测到点
        vacantCount += 1;
        if (vacantCount > 2) {
            old_x = -99999.0f;
            old_y = -99999.0f;
            return Point2f(-99999.0, -99999.0);
        }
        return Point2f(KF.statePost.at<float>(0), KF.statePost.at<float>(1));
    }
    if (vacantCount > 2) { //连续两次以上未检测到点就重置
        initKalmanFilter(pt);
    } else {
        Mat1f measurement(4, 1);
        measurement(0) = pt->x;
        measurement(1) = pt->y;
        measurement(2) = ((old_x > -1.0) ? pt->x - old_x : 0.0) / (1.0 + vacantCount);
        measurement(3) = ((old_y > -1.0) ? pt->y - old_y : 0.0) / (1.0 + vacantCount);
        KF.predict();
        KF.correct(measurement);
    }
    old_x = pt->x;
    old_y = pt->y;
    vacantCount = 0;
    return Point2f(KF.statePost.at<float>(0), KF.statePost.at<float>(1));
}