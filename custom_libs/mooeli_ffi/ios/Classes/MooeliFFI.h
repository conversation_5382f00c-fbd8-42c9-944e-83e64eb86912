//
// Created by <PERSON><PERSON> on 2022/8/3.
//
#pragma once
#include <stdint.h>
#include "exports/structs.h"
#include "exports/enums.h"

Struct2AIFace getAiFaceImgByPath(const char* imgPath,int isYMirror);
Struct2AIFace getAiFaceImgByDatas(uint8_t* imgDatas,int width,int height,int isYMirror);

void createTFLite(uint8_t* modalData,int modalDataLen,int isEncrypt, enum ModelType modelType);
void deleteTFLite();

Struct2PredictResult predictByPaths(const char* imgPath,const char* imgPath2,const char* imgPath3);
Struct2PredictResult predictByData(uint8_t * imgData,int width,int height,enum ImageType type,uint8_t* plane1,uint8_t* plane2, int bytesPerRow, int bytesPerPixel, int yRowStride);
Struct2ClarityResult clarityIncrease(const char* imgPath);

//quality: [0,100]
void compressImg(const char* imgPath,const char* imgOutPath,int tWidth,int tHeight,int quality);

Struct2CheckLightResult checkImgHaveLight(const char* imgPath);
Struct2CheckLightResult checkImgHaveLightByData(uint8_t* imgDatas,int width,int height);

int checkToothHaveLight(const char* imgPath,int centerX,int centerY);
int checkToothHaveLightByData(uint8_t* imgDatas,int width,int height,int centerX,int centerY);

//针对偏黄色调的图进行处理
void colorTransform(const char* imgPath);

int flipImage(const char *imgPath, const char *outPath);

int is_device_connect();
void connect_device();
void disconnect_device();
void enable_image_transit();
void disable_image_transit();
void save_image(const char* path);
Struct2DeviceImage retrieve_image();
float get_battery_percent();
enum DeviceType get_device_type();
void enable_light_white();
void enable_light_ultra_violet();
enum LightStatus retrieve_light_status();
UltraModel get_device_info();
int has_recent_image();
CString get_mac_address();

Struct2Line buildNurbsLine(const double *value,const int length, const int close);
