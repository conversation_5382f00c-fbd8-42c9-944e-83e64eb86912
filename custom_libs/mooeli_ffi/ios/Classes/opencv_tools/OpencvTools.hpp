//
// Created by syq on 2023/4/19.
//
#pragma once
#import <opencv2/opencv.hpp>
#include "../utils/defs.hpp"
#include "../utils/common.hpp"

class OpencvTools {
private:
    OpencvTools() {}

public:
    static Mat loadRGB(Uint8Ptr data, int width, int height);
    static Mat loadRGBFromPath(const char* path);
    static Mat loadRGBFromYUV420(Uint8Ptr plane0, Uint8Ptr plane1, Uint8Ptr plane2, int width, int height, int bytesPerRow, int bytesPerPixel, int yRowStride, Uint8Ptr &rgbData);
    static Mat loadRGBFromRGBA8888(Uint8Ptr data, int width, int height, Uint8Ptr &rgbData);
    static Mat gaussianBlur(cv::Mat image);
    static Mat mirror(cv::Mat image, MirrorType type);
    static float LightDetection(Mat image);
};

Mat OpencvTools::loadRGBFromPath(const char* path) {
    Mat image = cv::imread(path);
    if (image.empty()) return Mat();
    cvtColor(image, image, cv::COLOR_BGR2RGB);
    return image;
}

Mat OpencvTools::loadRGB(Uint8Ptr data, int width, int height) {
    return Mat(Size(width, height), CV_8UC3, data);
}

Mat OpencvTools::loadRGBFromYUV420(Uint8Ptr plane0, Uint8Ptr plane1, Uint8Ptr plane2, int width, int height, int bytesPerRow, int bytesPerPixel, int yRowStride, Uint8Ptr &rgbData) {
    rgbData = new uint8_t[width * height * 3];
    for (int i = 0; i < width; ++i) {
        for (int j = 0; j < height; ++j) {
            int uvIndex = bytesPerPixel * (i / 2) + bytesPerRow * (j / 2);
            int yIndex = j * yRowStride + i;
            int index = j * width + i;
            rgbData[index * 3] = plane0[yIndex];
            rgbData[index * 3 + 1] = plane1[uvIndex];
            rgbData[index * 3 + 2] = plane2[uvIndex];
        }
    }
    Mat image = Mat(height, width, CV_8UC3, rgbData);
    cvtColor(image, image, cv::COLOR_YUV2RGB);
    return image;
}

Mat OpencvTools::loadRGBFromRGBA8888(Uint8Ptr data, int width, int height, Uint8Ptr &rgbData) {
    Mat image = Mat(Size(width, height), CV_8UC4, data);
    cvtColor(image, image, cv::COLOR_BGRA2RGB);

    rgbData = new uint8_t[height * width * 3];
    for (int i = 0; i < height * width * 3; i++) {
        rgbData[i] = (uint8_t) image.at<Vec3b>(i / (width * 3),(i % (width * 3)) / 3)[i % 3];
    }
    delete[] data;
    return image;
}

Mat OpencvTools::gaussianBlur(Mat image) {
    int value1 = 2, value2 = 1; //磨皮程度与细节程度的确定
    int dx = value1 * 3;    //双边滤波参数之一
    double fc = value1 * 10.5; //双边滤波参数之一
    int p = 50; //透明度
    Mat temp1, temp2, temp3, temp4;
    //双边滤波
    bilateralFilter(image, temp1, dx, fc, fc);
    temp2 = (temp1 - image + 128);
    //高斯模糊
    GaussianBlur(temp2, temp3, Size(2 * value2 - 1, 2 * value2 - 1), 0, 0);
    temp4 = image + 2 * temp3 - 255;
    Mat dst = (image * (100 - p) + temp4 * p) / 100;
    //对双边滤波后的图像执行锐化操作，提升图片的棱角以及清晰度
    Mat resultImage;
    Mat kernel = (Mat1i(3, 3) << 0, -1, 0, -1, 5, -1, 0, -1, 0);
    filter2D(dst, resultImage, -1, kernel, Point(-1, -1), 0);
    return dst;
}

Mat OpencvTools::mirror(Mat image, MirrorType type) {
    Mat map_x, map_y, result;
    map_x.create(image.size(), CV_32FC1);
    map_y.create(image.size(), CV_32FC1);
    for (int col = 0; col < image.cols; col++) {
        for (int row = 0; row < image.rows; row++) {
            switch (type) {
                case center://中心对称
                    map_x.at<float>(row, col) = image.cols - col;
                    map_y.at<float>(row, col) = image.rows - row;
                    break;
                case vertical://垂直镜像
                    map_x.at<float>(row, col) = col;
                    map_y.at<float>(row, col) = image.rows - row;
                    break;
                case horizontal://水平镜像
                    map_x.at<float>(row, col) = image.cols - col;
                    map_y.at<float>(row, col) = row;
                    break;
            }
        }
    }
    remap(image, result, map_x, map_y, cv::INTER_LINEAR, cv::BORDER_CONSTANT, Scalar(0, 0, 0));
    map_x.release();
    map_y.release();
    return result;
}

float OpencvTools::LightDetection(Mat image) {
    Mat gray, binary;
    cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    threshold(gray, binary, 127, 255, cv::THRESH_BINARY);

    vector<vector<Point>> contours = {};
    findContours(binary, contours, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);

    float brightness = 0.0f;

    if (contours.size() > 0) {
        Mat mask = Mat::ones(image.size(), CV_8UC1);
        auto contour = *std::max_element(contours.begin(), contours.end(), [](vector<Point> a, vector<Point> b) {
            return a.size() < b.size();
        });
        fillPoly(mask, contour, Scalar(0, 0, 0));

        Scalar resultScalar = mean(image, mask == 1);
        brightness = (resultScalar[0] + resultScalar[1] + resultScalar[2]) / 3;
    }

    common_utils::clearVector(contours);
    return brightness;
}
