//
// Created by syq on 2023/4/20.
//
#pragma once

#include <iostream>
#include <cstdint>
#include <random>
#include <algorithm>
#include <chrono>
#include "../utils/defs.hpp"

class TFLiteCrypto {
private:
    TFLiteCrypto() {}
    static Uint8Vec salt; //秘钥
    static void encrypt_one_pass(Uint8Vec &plain);  //对称加密算法

public:
    static Uint8Vec encrypt(Uint8Vec &plain); //加密
    static Uint8Vec decrypt(Uint8Vec &plain); //解密
};


void TFLiteCrypto::encrypt_one_pass(Uint8Vec &plain) {
    const size_t array_len = plain.size() - 8;
    for (unsigned int i = 8; i < array_len + 8; i++) {
        unsigned int xor_loc = ((unsigned int)salt[plain[i % 8]] + array_len + i) % 256;
        uint8_t xor_with = salt[xor_loc];
        plain[i] = plain[i] ^ xor_with;
    }
}


Uint8Vec TFLiteCrypto::encrypt(Uint8Vec &plain) {
    static std::random_device rd;
    static std::mt19937 mt(rd());
    static std::uniform_int_distribution<uint8_t> u(0, 255);

    Uint8Vec encrypt = Uint8Vec();
    for(int i = 0; i < 8; ++i) {
        encrypt.push_back(u(mt));
    }
    encrypt.insert(encrypt.end(),plain.begin(),plain.end());

    encrypt_one_pass(encrypt);
    reverse(encrypt.begin(),encrypt.end());
    encrypt_one_pass(encrypt);
    return encrypt;
}


Uint8Vec TFLiteCrypto::decrypt(Uint8Vec &plain) {
    encrypt_one_pass(plain);
    reverse(plain.begin(), plain.end());
    encrypt_one_pass(plain);
    return Uint8Vec(plain.begin() + 8, plain.end());
}


Uint8Vec TFLiteCrypto::salt = {
        88,  1,   109, 253, 126, 113, 241, 98,  204, 16,  220, 27,  235, 122, 74, 229, 165, 219, 117,
        34,  185, 4,   12,  71,  150, 245, 183, 11,  21,  219, 47,  93,  209, 48, 10,  147, 195, 112,
        105, 57,  230, 170, 132, 197, 199, 189, 127, 164, 85,  64,  78,  65,  201, 167, 32,  147, 96,
        254, 178, 100, 225, 37,  109, 185, 185, 44,  42,  248, 117, 255, 180, 94,  230, 167, 148, 192,
        196, 131, 71,  247, 13,  208, 155, 76,  216, 3,   197, 222, 146, 169, 202, 91,  249, 69,  187,
        250, 117, 86,  183, 124, 223, 65,  8,   128, 181, 92,  209, 186, 73,  219, 236, 235, 130, 175,
        20,  143, 80,  200, 157, 124, 12,  70,  194, 73,  140, 22,  215, 184, 37,  129, 22,  12,  170,
        39,  0,   32,  78,  247, 19,  52,  100, 234, 89,  44,  158, 237, 82,  112, 24,  154, 62,  203,
        217, 153, 1,   11,  239, 183, 245, 11,  190, 52,  210, 208, 111, 109, 48,  79,  107, 220, 201,
        86,  133, 18,  174, 4,   121, 214, 8,   3,   228, 230, 62,  33,  170, 201, 153, 78,  157, 47,
        150, 69,  113, 154, 116, 198, 238, 198, 113, 134, 223, 122, 218, 33,  171, 230, 65,  25,  208,
        248, 199, 169, 89,  117, 138, 5,   239, 120, 188, 127, 174, 57,  87,  19,  160, 127, 38,  184,
        12,  23,  239, 23,  230, 150, 42,  175, 10,  55,  165, 46,  134, 107, 130, 44,  59,  31,  151,
        30,  80,  153, 70,  85,  38,  114, 205, 51
};

