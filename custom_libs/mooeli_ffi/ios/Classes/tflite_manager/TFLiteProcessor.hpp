//
// Created by syq on 2023/4/21.
//
#pragma once
#include "../utils/defs.hpp"

class TFLiteProcessor { //模型输出结果后处理
private:
    TFLiteProcessor() {}

    static vector<const char *> newLabels;
    static float cpAverage[10];
    static int cpAveragePointer;

    static Mat backOriImagePosition(PreprocessInfo preObjDic, Mat xyxy);
    static Struct2LastTFLiteBlurInfo checkIsBlur(PreprocessInfo preObjDic, Point lt, Point rb);
public:
    static Struct2LastTFLite processCenterPoint(
            vector<Mat> outputs, PreprocessInfo preprocessInfo, ChohoKalmanStabilizer& kalman);  //中心点检测
    static Struct2LastTFLite processQuality(
            vector<Mat> outputs, PreprocessInfo preprocessInfo);   //质检
};

float TFLiteProcessor::cpAverage[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
int TFLiteProcessor::cpAveragePointer = 0;

vector<const char *> TFLiteProcessor::newLabels = {
        //  "upper", "lower", "ul","kps","moli",
        "0", "1", "2", "3", "4",
        "11", "12", "13", "14", "15", "16", "17", "18",
        "21", "22", "23", "24", "25", "26", "27", "28",
        "31", "32", "33", "34", "35", "36", "37", "38",
        "41", "42", "43", "44", "45", "46", "47", "48",
};

Struct2LastTFLite TFLiteProcessor::processCenterPoint(vector<Mat> outputs, PreprocessInfo preprocessInfo, ChohoKalmanStabilizer& kalman) {
    Mat mat1 = outputs[0];  //coordinates
    Mat mat2 = outputs[1];  //score
    float *rectsArray;
    //赋予最新帧的分数
    cpAverage[cpAveragePointer % 10] = mat2.at<float>(0, 0);
    cpAveragePointer = (cpAveragePointer + 1) % 10;
    //取出最近的均值来计算
    float averageValue = (float)std::accumulate(cpAverage, cpAverage + 10, 0.0) / 10.0;
    if (averageValue >= 0.95) {
        mat1 = backOriImagePosition(preprocessInfo, mat1);
        if (mat1.at<float>(1, 1) >= 0) {
            float center_x = (mat1.at<float>(1, 0) + mat1.at<float>(0, 0)) / 2;
            float center_y = (mat1.at<float>(1, 1) + mat1.at<float>(0, 1)) / 2;

            Point2f measPt(center_x, center_y);
            Point2f statePt = (center_x >= 0 && center_y >= 0) ? kalman.getEstPt(&measPt)
                                                                  : kalman.getEstPt(nullptr);
            rectsArray = new float[18]{
                    mat1.at<float>(0, 0),
                    mat1.at<float>(0, 1),
                    mat1.at<float>(0, 0),
                    mat1.at<float>(0, 1),
                    -1,
                    100,
                    mat1.at<float>(1, 0),
                    mat1.at<float>(1, 1),
                    mat1.at<float>(1, 0),
                    mat1.at<float>(1, 1),
                    1,
                    100,
                    statePt.x,
                    statePt.y,
                    statePt.x,
                    statePt.y,
                    0,
                    100,
            };
        } else {
            rectsArray = new float[18]{0};
        }
    } else {
        rectsArray = new float[18]{0};
    }

    Struct2LastTFLite result;
    result.rects = rectsArray;
    result.rectEleCount = 6;
    result.rectsLength = 18;
    result.importNetOriImgW = preprocessInfo.ori_size_w;
    result.importNetOriImgH = preprocessInfo.ori_size_h;
    return result;
}

Struct2LastTFLite TFLiteProcessor::processQuality(vector<Mat> outputs, PreprocessInfo preprocessInfo) {
    Mat grid = outputs[0];

    //获取每个框体对应22个牙齿的得分 = 置信度*22个牙齿
    //参与点乘的两个Mat矩阵的数据类型只能是CV_32F、CV_64FC1、CV_32FC2、CV_64FC2这4种类型中的一种。
    //保证维度相同再相乘
    int class_num = (int) newLabels.size();
    int reg_num = 6;
    int conf_index = 6;

    // 角度信息，这里是 cos sin
    Mat direction = grid(Range::all(), Range(4, 6));

    // 该框是否包含物体的置信度
    Mat conf = repeat(grid.col(conf_index), 1, class_num);

    // 各个类别的置信度
    int cls_index = conf_index + 1;
    Mat cls = grid(Range::all(), Range(cls_index, cls_index + class_num));

    // 各个框的回归值 参数
    int reg_index = cls_index + class_num;
    Mat reg = grid(Range::all(), Range(reg_index, reg_index + reg_num));

    // conf × cls 为最终的置信度
    Mat probs = conf.mul(cls);

    //获取每个框对应最高分类别的下标
    Mat labelIndexs = Mat(grid.rows, 1, CV_32F);
    //获取每个框对应最高分牙齿的下标的分值
    Mat scores = Mat(grid.rows, 1, CV_32F);
    double max = 0;
    int maxIndex[2] = {0, 0};
    for (int i = 0; i < probs.rows; i++) {
        minMaxIdx(probs.row(i), 0, &max, 0, maxIndex);
        labelIndexs.at<float>(i, 0) = maxIndex[1];
        scores.at<float>(i, 0) = max;
    }

    //xywh，由于int8的问题，这里的数值已经被归一化
    Mat yolo_xywh = grid(Range::all(), Range(0, 4));
    //image back
    //变化坐标点用于后续画Rect统一左上角和右下角
    Mat xyxy = Mat(yolo_xywh.rows, yolo_xywh.cols, CV_32F);
    xyxy.col(0) = yolo_xywh.col(0) - yolo_xywh.col(2) / 2;
    xyxy.col(1) = yolo_xywh.col(1) - yolo_xywh.col(3) / 2;
    xyxy.col(2) = yolo_xywh.col(0) + yolo_xywh.col(2) / 2;
    xyxy.col(3) = yolo_xywh.col(1) + yolo_xywh.col(3) / 2;

    backOriImagePosition(preprocessInfo, xyxy);

    Mat xywh = xyxy.clone();
    xywh.col(2) = xywh.col(2) - xywh.col(0);
    xywh.col(3) = xywh.col(3) - xywh.col(1);

    //预测
    int bbox_num = xywh.rows;
    vector<Rect2d> boxes(bbox_num);
    for (int i = 0; i < xywh.rows; i++) {
        boxes[i] = (Rect2d(xywh.at<float>(i, 0), xywh.at<float>(i, 1), xywh.at<float>(i, 2),
                           xywh.at<float>(i, 3)));
    }

    vector<int> indices;
    vector<int> labels;
    bool class_wise = 1;
    if (class_wise) {
        vector<int> class_indices;
        for (int i = 0; i < class_num; i++) {
            Mat tempScore = probs(Range::all(), Range(i, i + 1));
            cv::dnn::NMSBoxes(boxes, tempScore, 0.2, 0.4, class_indices);
            for (auto v: class_indices) {
                indices.push_back(v);
                labels.push_back(i);
            }
            class_indices.clear();
        }
    } else {
        vector<int> class_indices;
        cv::dnn::NMSBoxes(boxes, scores, 0.2, 0.4, class_indices);
        for (auto v: class_indices) {
            indices.push_back(v);
            labels.push_back((int) labelIndexs.at<float>(v, 0));
        }
    }
    //

    common_utils::clearVector(boxes);

    //整理识别出来的目标矩形等信息
    // x, y, w, h, theta, prob, label, [..., reg_num]
    int num_features = 6 + 1 + reg_num;
    float *rectsArray = new float[indices.size() * num_features];

    //取出最大的框体左上右下
    Point lt, rb;

    for (int i = 0; i < indices.size(); i++) {
        int _index = labels[i];

        int _k = indices[i];
        //第几颗牙
        //评分(可信度)
        float _score = scores.at<float>(_k, 0);
        //牙齿矩形框位置
        float r_l = xyxy.at<float>(_k, 0);
        float r_t = xyxy.at<float>(_k, 1);
        float r_r = xyxy.at<float>(_k, 2);
        float r_b = xyxy.at<float>(_k, 3);
        float _theta = 0; // TODO

        //拿到最大的框体
        if (r_r - r_l > rb.x - lt.x) {
            lt = Point(r_l, r_t);
            rb = Point(r_r, r_b);
        }

        rectsArray[i * num_features] = r_l;
        rectsArray[i * num_features + 1] = r_t;
        rectsArray[i * num_features + 2] = r_r;
        rectsArray[i * num_features + 3] = r_b;
        rectsArray[i * num_features + 4] = _theta;
        rectsArray[i * num_features + 5] = _score;
        rectsArray[i * num_features + 6] = atoi(newLabels[_index]);

        for (int j = 0; j < reg_num; j++) {
            rectsArray[i * num_features + 7 + j] = reg.at<float>(_k, j);
        }
    }

    return {
        .rects = rectsArray,
        .rectEleCount = num_features,
        .rectsLength = (int) indices.size() * num_features,
        .blurInfo = checkIsBlur(preprocessInfo, lt, rb),
        .importNetOriImgW = preprocessInfo.ori_size_w,
        .importNetOriImgH = preprocessInfo.ori_size_h
    };
}

Mat TFLiteProcessor::backOriImagePosition(PreprocessInfo preObjDic, Mat xyxy) {
    int design_width = preObjDic.design_width;
    int design_height = preObjDic.design_height;
    //预处理图片的预处理数据,
    int offsets[4] = {preObjDic.offsets_l, preObjDic.offsets_t, preObjDic.offsets_r,
                      preObjDic.offsets_b};
    int rect[4] = {preObjDic.rect_x, preObjDic.rect_y, preObjDic.rect_w, preObjDic.rect_h};
    float scale = preObjDic.scale;
    //tflite 都是0~1的值，先还原坐标点
    xyxy.col(0) *= design_width;
    xyxy.col(1) *= design_height;
    if (xyxy.cols > 2) {
        xyxy.col(2) *= design_width;
        xyxy.col(3) *= design_height;
    }
    //image back
    xyxy.col(0) -= offsets[0];
    xyxy.col(1) -= offsets[1];
    if (xyxy.cols > 2) {
        xyxy.col(2) -= offsets[0];
        xyxy.col(3) -= offsets[1];
    }
    xyxy /= scale;
    xyxy.col(0) += rect[0];
    xyxy.col(1) += rect[1];
    if (xyxy.cols > 2) {
        xyxy.col(2) += rect[0];
        xyxy.col(3) += rect[1];
    }
    return xyxy;
}

Struct2LastTFLiteBlurInfo TFLiteProcessor::checkIsBlur(PreprocessInfo preObjDic, Point lt, Point rb) {

    int ori_size[2] = {preObjDic.ori_size_w, preObjDic.ori_size_h};
    //检查是否模糊(没有resize过的原图,且是灰色单通道)
    Mat oldImgMat;
    if (strcmp(preObjDic.imgPath, "") == 0) {
        oldImgMat = Mat(Size(ori_size[0], ori_size[1]), CV_8UC3, preObjDic.imgDataList);
        cvtColor(oldImgMat, oldImgMat, cv::COLOR_BGR2GRAY);//灰色单通道
    } else {
        oldImgMat = imread(preObjDic.imgPath, cv::IMREAD_GRAYSCALE);//灰色单通道
    }

    double laplacian_check = -1;
    double canny_check = -1;
    bool imgIsBlur = true;
    int checkLapMax = 200;
    int checkLapMin = 40;
    double checkCannyCount = 2.0;
    if (lt.x != 0 && lt.y != 0 && rb.x != 0 && rb.y != 0) {
        Mat tempLapMat;
        Laplacian(oldImgMat, tempLapMat, CV_16S);
        Mat tempScaMat;
        convertScaleAbs(tempLapMat, tempScaMat);
        minMaxIdx(tempScaMat, 0, &laplacian_check);
        if (laplacian_check > checkLapMax) {
            imgIsBlur = false;
        } else if (laplacian_check < checkLapMin) {
            imgIsBlur = true;
        } else {
            if (lt.x <= 0) { lt.x = 0; }
            if (lt.y <= 0) { lt.y = 0; }
            if (rb.x <= 0) { rb.x = 0; }
            if (rb.y <= 0) { rb.y = 0; }
            if (lt.x >= oldImgMat.cols) { lt.x = oldImgMat.cols; }
            if (rb.x >= oldImgMat.cols) { rb.x = oldImgMat.cols; }
            if (lt.y >= oldImgMat.rows) { lt.y = oldImgMat.rows; }
            if (rb.y >= oldImgMat.rows) { rb.y = oldImgMat.rows; }

            Mat rectMaxMat = oldImgMat(Range(lt.y, rb.y), Range(lt.x, rb.x));
            Mat edges;
            Canny(rectMaxMat, edges, 100, 200);

            canny_check = countNonZero(edges) * 1000.0 / (edges.rows * edges.cols);
            if (canny_check > checkCannyCount) {
                imgIsBlur = false;
            } else {
                imgIsBlur = true;
            }
        }
    }

    Struct2LastTFLiteBlurInfo blurInfo;
    blurInfo.isBlur = imgIsBlur;
    blurInfo.checkLapMax = checkLapMax;
    blurInfo.checkLapMin = checkLapMin;
    blurInfo.imgLaplacianCount = laplacian_check;
    blurInfo.checkCannyCount = checkCannyCount;
    blurInfo.imgCannyCount = canny_check;

    return blurInfo;
}

