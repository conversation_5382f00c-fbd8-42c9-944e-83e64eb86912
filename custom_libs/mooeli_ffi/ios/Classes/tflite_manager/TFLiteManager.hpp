//
// Created by syq on 2023/4/20.
//

#pragma once
#ifdef __ANDROID__
#import <tensorflow/lite/builtin_ops.h>
#import <tensorflow/lite/c/c_api.h>
#import <tensorflow/lite/c/c_api_experimental.h>
#import <tensorflow/lite/c/common.h>
#import <tensorflow/lite/delegates/xnnpack/xnnpack_delegate.h>
#import <tensorflow/lite/delegates/nnapi/nnapi_delegate.h>
#import <tensorflow/lite/delegates/coreml/coreml_delegate.h>
#import <tensorflow/lite/c/c_api_types.h>
#import <tensorflow/lite/kernels/register.h>
#import <tensorflow/lite/model.h>
#import <tensorflow/lite/optional_debug_tools.h>
#import <tensorflow/lite/string_util.h>
#else
#import <TensorFlowLiteC/TensorFlowLiteC.h>
#import <TensorFlowLiteCCoreML/TensorFlowLiteCCoreML.h>
#endif
#include <cstdint>
#include <cmath>
#include "TFLiteCrypto.hpp"
#include "../utils/common.hpp"
#include "../utils/defs.hpp"

class TFLiteManager {
private:
    TfLiteModel *model;
    uint8_t *modelOriData;
    TfLiteInterpreter *interpreter;
    TfLiteInterpreterOptions *modelOptions;
    vector<TfLiteDelegate*> delegates;
    ModelParams model_params;
    ModelType model_type;
    ModelState model_state = ModelState::empty;

    TfLiteInterpreterOptions* getModelOptions(bool enable_xnnpack, bool enable_nnapi, bool enable_coreml);
    ModelParams getModelParams();
    Size getOutputSize(const TfLiteTensor* outputTensor);

public:
    bool isInferable();
    MAKE_GETTER(model_type)
    MAKE_GETTER(model_state)
    void loadModel(uint8_t *modelData, int modelDataLen, int isEncrypt, ModelType type);  //加载模型
    void unloadModel();  //释放模型
    PreprocessInfo preprocess(Mat image); //输入图片预处理
    int inference(vector<void*> &inputs);  //进行推理
    vector<vector<Mat>> getOutputs(int batch_size);  //获取网络输出

};

bool TFLiteManager::isInferable() {
    return (model_state != empty && model_state != suspend) && interpreter != nullptr;
}

void TFLiteManager::loadModel(uint8_t *modelData, int modelDataLen, int isEncrypt, ModelType type) {
    if (interpreter != nullptr) { //释放现有模型
        unloadModel();
    }
    if (isEncrypt) { //处理加密模型
        Uint8Vec vBytes(modelData, modelData + modelDataLen);
        Uint8Vec decodeList = TFLiteCrypto::decrypt(vBytes);

        modelDataLen = (int)decodeList.size();
        modelOriData = new uint8_t[modelDataLen];  //ffi 用完就清理了，这里保留一份 modalOriData ，保证interpreter源不会丢失出现EXC_BAD_ACCESS
        memcpy(modelOriData, decodeList.data(), modelDataLen);

        common_utils::clearVector(vBytes);
        common_utils::clearVector(decodeList);
    } else {
        modelOriData = new uint8_t[modelDataLen];  //ffi 用完就清理了，这里保留一份 modalOriData ，保证interpreter源不会丢失出现EXC_BAD_ACCESS
        memcpy(modelOriData, modelData, modelDataLen);
    }
    model = TfLiteModelCreate(modelOriData, modelDataLen);
#ifdef __ANDROID__
    modelOptions = getModelOptions(true, false, false);
    interpreter = TfLiteInterpreterCreate(model, modelOptions);
    if(interpreter == nullptr) {
        TfLiteInterpreterOptionsDelete(modelOptions);
        modelOptions = getModelOptions(true, false, false);
        interpreter = TfLiteInterpreterCreate(model, modelOptions);
    }
#else
    modelOptions = getModelOptions(true, false, false);
    interpreter = TfLiteInterpreterCreate(model, modelOptions);
    if(interpreter == nullptr) {
        TfLiteInterpreterOptionsDelete(modelOptions);
        modelOptions = getModelOptions(true, false, false);
        interpreter = TfLiteInterpreterCreate(model, modelOptions);
    }
#endif
    TfLiteInterpreterAllocateTensors(interpreter);
    model_params = getModelParams();
    model_type = type;
    model_state = ModelState::ready;
}

void TFLiteManager::unloadModel() {
    if (interpreter == nullptr)  return;
    switch (model_state) {
        case empty:
        case suspend: return;
        case ready: {
            model_state = ModelState::empty;
            TfLiteInterpreterDelete(interpreter);
            TfLiteInterpreterOptionsDelete(modelOptions);
            TfLiteModelDelete(model);
            delete[] modelOriData;
            break;
        }
        case running: {
            model_state = ModelState::suspend;  //正在执行推断任务则挂起，等待执行完毕后释放
            break;
        }
    }
}

TfLiteInterpreterOptions *TFLiteManager::getModelOptions(bool enable_xnnpack, bool enable_nnapi, bool enable_coreml) {
    auto options = TfLiteInterpreterOptionsCreate();
#ifdef __ANDROID__
    if(enable_xnnpack) {
        auto delegate = TfLiteXNNPackDelegateCreate(nullptr);
        TfLiteInterpreterOptionsAddDelegate(options, delegate);
    }
    if (enable_nnapi) {
        TfLiteInterpreterOptionsSetUseNNAPI(options, enable_nnapi);
    }
#else
    if (enable_coreml) {
        TfLiteCoreMlDelegateOptions coremldelegate_opts;
        coremldelegate_opts.enabled_devices =  TfLiteCoreMlDelegateAllDevices;
        coremldelegate_opts.coreml_version = 3;
        coremldelegate_opts.max_delegated_partitions = 200;
        auto delegate = TfLiteCoreMlDelegateCreate(&coremldelegate_opts);
        if (delegate == nullptr && enable_xnnpack) {
            delegate = TfLiteXNNPackDelegateCreate(nullptr);
        }
        if (delegate != nullptr) {
            TfLiteInterpreterOptionsAddDelegate(options, delegate);
        }
    } else if(enable_xnnpack) {
        auto delegate = TfLiteXNNPackDelegateCreate(nullptr);
        TfLiteInterpreterOptionsAddDelegate(options, delegate);
    }
#endif
    TfLiteInterpreterOptionsSetEnableDelegateFallback(options, true);
    TfLiteInterpreterOptionsSetNumThreads(options, 2);
    return options;
}

ModelParams TFLiteManager::getModelParams() {
    auto *inputTensor = TfLiteInterpreterGetInputTensor(interpreter, 0);
    auto inputUint8Params = TfLiteTensorQuantizationParams(inputTensor);
    auto *outputTensor = TfLiteInterpreterGetOutputTensor(interpreter, 0);
    auto outputUint8Params = TfLiteTensorQuantizationParams(outputTensor);

    auto inputType = TfLiteTensorType(inputTensor);

    return {
        .isUint8 = inputType == kTfLiteUInt8,
        .inputInt8TFLiteScale = inputUint8Params.scale,
        .inputInt8TFLiteZeroPoint = inputUint8Params.zero_point,
        .outputInt8TFLiteScale = outputUint8Params.scale,
        .outputInt8TFLiteZeroPoint = outputUint8Params.zero_point,
        .design_width = TfLiteTensorDim(inputTensor, 2),
        .design_height = TfLiteTensorDim(inputTensor, 1),
        .outputRows = TfLiteTensorDim(outputTensor, 2),
        .outputCols = TfLiteTensorDim(outputTensor, 1)
    };
}

/***
 * @param image: 网络的输入图片mat
 * @param rgbData: 网络的输入图片bytes
 * @return: 预处理信息
 */
PreprocessInfo TFLiteManager::preprocess(Mat image) {
    PreprocessInfo preprocessInfo;

    Size oldImgSize = Size(image.cols, image.rows);
    Size netImgSize = Size(model_params.design_width, model_params.design_height);

    float scaleRatio = std::min(
            float(netImgSize.width) / float(oldImgSize.width),
            float(netImgSize.height) / float(oldImgSize.height)
    );
    Size newImgSize = Size(int(oldImgSize.width * scaleRatio), int(oldImgSize.height * scaleRatio));

    Mat newImgMat;
    resize(image, newImgMat, Size(newImgSize.width, newImgSize.height));

    int delta_w = netImgSize.width - newImgSize.width;
    int delta_h = netImgSize.height - newImgSize.height;
    int top = delta_h / 2;
    int left = delta_w / 2;
    int bottom = delta_h - (delta_h / 2);
    int right = delta_w - (delta_w / 2);


    copyMakeBorder(newImgMat, newImgMat, top, bottom, left, right, cv::BORDER_CONSTANT); //补充空白像素
    newImgMat.convertTo(newImgMat, CV_32F, 1.0 / 255, 0);  //像素归一化

    //int8的tflite
    if (model_params.isUint8) {
        newImgMat /= model_params.inputInt8TFLiteScale;
        newImgMat += model_params.inputInt8TFLiteZeroPoint;
        newImgMat.convertTo(newImgMat, CV_8U);
    }

    preprocessInfo.offsets_l = left;
    preprocessInfo.offsets_t = top;
    preprocessInfo.offsets_r = newImgSize.width + left;
    preprocessInfo.offsets_b = newImgSize.height + top;
    preprocessInfo.rect_x = 0;
    preprocessInfo.rect_y = 0;
    preprocessInfo.rect_w = oldImgSize.width;
    preprocessInfo.rect_h = oldImgSize.height;
    preprocessInfo.ori_size_w = oldImgSize.width;
    preprocessInfo.ori_size_h = oldImgSize.height;
    preprocessInfo.scale = scaleRatio;

    int imgByteSize = newImgMat.rows * newImgMat.cols * 3 * (model_params.isUint8 ? sizeof(uint8_t) : sizeof(float));
    preprocessInfo.imgByteSize = imgByteSize;
    preprocessInfo.imgBytes = ::operator new(imgByteSize);
    memcpy(preprocessInfo.imgBytes, newImgMat.data, imgByteSize);

    preprocessInfo.imgPath = "";
    preprocessInfo.design_width = model_params.design_width;
    preprocessInfo.design_height = model_params.design_height;
    preprocessInfo.rows = model_params.outputRows;
    preprocessInfo.cols = model_params.outputCols;
    return preprocessInfo;
}

int TFLiteManager::inference(vector<void*> &inputs) {
    model_state = ModelState::running;
    auto inputTensor = TfLiteInterpreterGetInputTensor(interpreter, 0);
    int totalByteSize = (int) TfLiteTensorByteSize(inputTensor);
    int batchByteSize = totalByteSize / inputs.size();

    auto inputBuffer = ::operator new(totalByteSize);
    for (int i = 0; i < inputs.size(); i++) {  //TFLite支持固定大小的batch
        memcpy((char*)inputBuffer + (i * batchByteSize), inputs[i], batchByteSize);
        //::operator delete[](inputs[i]);
    }
    TfLiteTensorCopyFromBuffer(inputTensor, inputBuffer, totalByteSize);
    ::operator delete[](inputBuffer);

    long startMs = common_utils::getTimestamp();
    if (TfLiteInterpreterInvoke(interpreter) != TfLiteStatus::kTfLiteOk) { //推理失败
        std::cout << "tflite invoke failure" << std::endl;
    }
    int runMs = common_utils::getTimestamp() - startMs;
    return runMs;
}

Size TFLiteManager::getOutputSize(const TfLiteTensor* outputTensor) {
    int rows = 0, cols = 0;
    switch (model_type) {
        case center_detection:
        case quality_inspection: {
            int dims = TfLiteTensorNumDims(outputTensor);
            rows = TfLiteTensorDim(outputTensor, dims - 1);
            cols = TfLiteTensorDim(outputTensor, dims - 2);
            break;
        }
        case clarity_enhance: {
            rows = TfLiteTensorDim(outputTensor, 2);
            cols = TfLiteTensorDim(outputTensor, 1);
            break;
        }
    }
    return Size(rows, cols);
}


/***
 * @param batch_size
 * @return (batch_size * num_outputs) Mats
 */
vector<vector<Mat>> TFLiteManager::getOutputs(int batch_size) {
    int num_outputs = TfLiteInterpreterGetOutputTensorCount(interpreter);

    vector<vector<Mat>> outputs(batch_size, vector<Mat>(num_outputs));
    for(int i = 0; i < num_outputs; ++i) {
        auto outputTensor = TfLiteInterpreterGetOutputTensor(interpreter, i);
        int totalByteSize = (int) TfLiteTensorByteSize(outputTensor);
        int batchByteSize = totalByteSize / batch_size;

        auto outputBuffer = ::operator new(totalByteSize);
        TfLiteTensorCopyToBuffer(outputTensor, outputBuffer, totalByteSize);

        for(int k = 0; k < batch_size; ++k) {
            auto batchBuffer = ::operator new(batchByteSize);
            memcpy(batchBuffer, (char *)outputBuffer + (k * batchByteSize), batchByteSize);

            Mat& grid = outputs[k][i];
            Size outputSize = getOutputSize(outputTensor);
            if (model_params.isUint8) {
                grid = Mat(outputSize, CV_8U, batchBuffer);
                grid.convertTo(grid, CV_32F);
                grid -= model_params.outputInt8TFLiteZeroPoint;
                grid *= model_params.outputInt8TFLiteScale;
            } else {
                grid = Mat(outputSize, CV_32F, batchBuffer);
            }
        }
        ::operator delete[](outputBuffer);
    }
    if (model_state == ModelState::suspend)  {
        model_state = ModelState::ready;
        unloadModel();
    } else {
        model_state = ModelState::ready;
    }
    return outputs;
}
