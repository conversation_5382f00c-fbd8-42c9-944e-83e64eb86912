//
// Created by syq on 2023/4/24.
//
#pragma once
#include "enums.h"

typedef struct {
    char* ptr;
    int length;
} CString;

typedef struct {
    uint8_t *imgData;
    int width;
    int height;
    enum ImageType type;
    //yuv参数
    uint8_t *plane0;
    uint8_t *plane1;
    uint8_t *plane2;
    int bytesPerRow;
    int bytesPerPixel;
    int yRowStride;
} Struct2Image;

typedef struct {
    int leftIndex;
    int rightIndex;
    int upIndex;
    int downIndex;
    int frontIndex;
} Struct2TFLiteMCO;

typedef struct {
    int isBlur;
    int checkLapMax;
    int checkLapMin;
    float imgLaplacianCount;
    float checkCannyCount;
    float imgCannyCount;
} Struct2LastTFLiteBlurInfo;

typedef struct {
    float* rects;
    int rectEleCount;
    int rectsLength;
    int num_classes;
    Struct2LastTFLiteBlurInfo blurInfo;
    int importNetOriImgW;
    int importNetOriImgH;
} Struct2LastTFLite;

typedef struct{
    Struct2LastTFLite last1;
    Struct2LastTFLite last2;
    Struct2LastTFLite last3;
    Struct2TFLiteMCO mco;
    int runMs;
    int totalMs;
} Struct2PredictResult;

typedef struct{
    int runMs;
    int totalMs;
} Struct2ClarityResult;

typedef struct {
    uint8_t *buffer;
    int bufferLen;
} Struct2AIFace;

typedef struct {
    int firstValue;
    int secondValue;
} Struct2CheckLightResult;

typedef struct {
    uint8_t *buffer;
    int buffer_size;
    int image_seq_id;
    int width;
    int height;
} Struct2DeviceImage;

typedef struct {
//    uint8_t **buffers;
//    uint32_t *buffer_sizes;
    char **paths;
//    int total_size;
    int length;
    int code;
} Struct2AnimateImage;

typedef struct {
    double *data;
    int length;
} Struct2Line;

typedef struct {
    int manufacturer;       //供应商代码
    int hardware_version;   //硬件版本
    int software_version;   //软件版本
} UltraModel; //设备型号
