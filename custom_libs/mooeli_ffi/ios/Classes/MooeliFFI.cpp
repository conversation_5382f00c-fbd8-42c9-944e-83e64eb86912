//
// Created by <PERSON><PERSON> on 2022/8/3.
//
#import <opencv2/opencv.hpp>

#include <cstdint>
#include <chrono>
#include "kalman/ChohoKalmanStabilizer.hpp"
#include "opencv_tools/OpencvTools.hpp"
#include "tflite_manager/TFLiteManager.hpp"
#include "tflite_manager/TFLiteProcessor.hpp"
#include "tasks/MooeliTasks.hpp"
#include "devices/DeviceManager.hpp"
#include "utils/socket.hpp"
#include "nurbs/Nurbs.hpp"

#define DART_API extern "C" __attribute__((visibility("default"))) __attribute__((used))

static TFLiteManager tfLiteManager;
static DeviceManager deviceManager;
static ChohoKalmanStabilizer kalman;

DART_API void createTFLite(uint8_t *modalData, int modalDataLen, int isEncrypt, ModelType type) {
    if (type == ModelType::center_detection) kalman.resetK<PERSON>man();
    tfLiteManager.loadModel(modalData, modalDataLen, isEncrypt, type);
}

DART_API void deleteTFLite() {
    tfLiteManager.unloadModel();
}

DART_API Struct2PredictResult predictByData(uint8_t *imgData, int width, int height, ImageType type,
                                            uint8_t *plane1, uint8_t *plane2, int bytesPerRow,
                                            int bytesPerPixel, int yRowStride) {
    if (!tfLiteManager.isInferable()) return {};

    Mat image;
    uint8_t *rgbData = imgData;
    switch (type) {
        case rgb: {
            image = OpencvTools::loadRGB(imgData, width, height);
            break;
        }
        case rbga8888: {
            image = OpencvTools::loadRGBFromRGBA8888(imgData, width, height, rgbData);
            break;
        }
        case yuv420: {
            image = OpencvTools::loadRGBFromYUV420(imgData, plane1, plane2, width, height,
                                                   bytesPerRow, bytesPerPixel, yRowStride, rgbData);
            delete[] imgData;
            break;
        }
    }

    long startPreMS = common_utils::getTimestamp();

    PreprocessInfo preprocessInfo = tfLiteManager.preprocess(image);
    preprocessInfo.imgDataList = rgbData;
    auto inputs = vector<void *>{preprocessInfo.imgBytes};
    int runMs = tfLiteManager.inference(inputs);
    auto outputs = tfLiteManager.getOutputs((int) inputs.size());

    Struct2LastTFLite result;
    if (tfLiteManager.get_model_type() == ModelType::center_detection) {
        result = TFLiteProcessor::processCenterPoint(outputs[0], preprocessInfo, kalman);
    } else {
        result = TFLiteProcessor::processQuality(outputs[0], preprocessInfo);
    }
    ::operator delete[](preprocessInfo.imgBytes);
    common_utils::clearVector(inputs);
    for (auto &output: outputs) {
        for (auto &grid: output) {
            ::operator delete[](grid.data);
        }
    }
    return {
            .last1 = result,
            .runMs = runMs,
            .totalMs = (int) (common_utils::getTimestamp() - startPreMS)
    };
}

DART_API Struct2PredictResult
predictByPaths(const char *imgPath, const char *imgPath2, const char *imgPath3) {
    if (tfLiteManager.get_model_type() != ModelType::quality_inspection)
        return {
                .runMs = 1,
                .totalMs = 1
        };
    if (!tfLiteManager.isInferable())
        return {
                .runMs = 2,
                .totalMs = 2
        };

    long startPreMS = common_utils::getTimestamp();
    vector<void *> inputs = {};
    vector<PreprocessInfo> preprocessInfos = {};

    if (imgPath != nullptr && strlen(imgPath) > 0) {
        Mat image = OpencvTools::loadRGBFromPath(imgPath);
        if (!image.empty()) {
            PreprocessInfo preprocessInfo = tfLiteManager.preprocess(image);
            preprocessInfo.imgPath = imgPath;
            inputs.push_back(preprocessInfo.imgBytes);
            preprocessInfos.push_back(preprocessInfo);
        }
    }
    if (imgPath2 != nullptr && strlen(imgPath2) > 0) {
        Mat image = OpencvTools::loadRGBFromPath(imgPath2);
        if (!image.empty()) {
            PreprocessInfo preprocessInfo = tfLiteManager.preprocess(image);
            preprocessInfo.imgPath = imgPath2;
            inputs.push_back(preprocessInfo.imgBytes);
            preprocessInfos.push_back(preprocessInfo);
        }
    }
    if (imgPath3 != nullptr && strlen(imgPath3) > 0) {
        Mat image = OpencvTools::loadRGBFromPath(imgPath3);
        if (!image.empty()) {
            PreprocessInfo preprocessInfo = tfLiteManager.preprocess(image);
            preprocessInfo.imgPath = imgPath3;
            inputs.push_back(preprocessInfo.imgBytes);
            preprocessInfos.push_back(preprocessInfo);
        }
    }
    if (inputs.empty())
        return {
                .runMs = 3,
                .totalMs = 3
        };
    if (!tfLiteManager.isInferable())
        return {
                .runMs = 4,
                .totalMs = 4
        };

    int runMs = tfLiteManager.inference(inputs);
    auto outputs = tfLiteManager.getOutputs((int) inputs.size());
    Struct2LastTFLite result;
    if (tfLiteManager.get_model_type() == ModelType::center_detection) {
        result = TFLiteProcessor::processCenterPoint(outputs[0], preprocessInfos[0], kalman);
    } else {
        result = TFLiteProcessor::processQuality(outputs[0], preprocessInfos[0]);
    }
    for (auto &input: inputs) {
        operator delete[](input);
        input = nullptr;
    }
    for (auto &output: outputs) {
        for (auto &grid: output) {
            ::operator delete[](grid.data);
        }
    }
    return {
            .last1 = result,
            .runMs = runMs,
            .totalMs = (int) (common_utils::getTimestamp() - startPreMS)
    };
}

DART_API Struct2ClarityResult clarityIncrease(const char *imgPath) {
    if (!tfLiteManager.isInferable()) return {};
    Mat image = OpencvTools::loadRGBFromPath(imgPath);
    if (image.empty()) return {};

    PreprocessInfo preprocessInfo = tfLiteManager.preprocess(image);
    preprocessInfo.imgPath = imgPath;

    Mat imageMat = Mat(Size(preprocessInfo.design_width, preprocessInfo.design_height), CV_32FC3,
                       preprocessInfo.imgBytes);
    cvtColor(imageMat, imageMat, cv::COLOR_RGB2YCrCb);
    Mat channel[3];
    split(imageMat, channel);

    long startPreMS = common_utils::getTimestamp();

    Mat input = channel[0].clone();
    auto inputs = vector<void *>{input.data};
    int runMs = tfLiteManager.inference(inputs);
    auto outputs = tfLiteManager.getOutputs((int) inputs.size());

    Struct2ClarityResult struct2ClarityResult;

    vector<Mat> channels = {outputs[0][0], channel[1], channel[2]};

    Mat result;
    merge(channels, result);
    cvtColor(result, result, cv::COLOR_YCrCb2BGR);

    common_utils::float2uint8(result);
    imwrite(imgPath, result);

    ::operator delete[](preprocessInfo.imgBytes);
    common_utils::clearVector(channels);
    common_utils::clearVector(inputs);
    for (auto &output: outputs) {
        for (auto &grid: output) {
            ::operator delete[](grid.data);
        }
    }

    struct2ClarityResult.runMs = runMs;
    struct2ClarityResult.totalMs = (int) (common_utils::getTimestamp() - startPreMS);
    return struct2ClarityResult;
}

DART_API Struct2AIFace getAiFaceImgByPath(const char *imgPath, int isYMirror) {
    Mat image = cv::imread(imgPath);
    return MooeliTasks::processAIFaceImage(image, isYMirror);
}

DART_API Struct2AIFace
getAiFaceImgByDatas(uint8_t *imgDatas, int width, int height, int isYMirror) {
    Mat image = Mat(Size(width, height), CV_8UC3, imgDatas);
    return MooeliTasks::processAIFaceImage(image, isYMirror);
}

DART_API void
compressImg(const char *imgPath, const char *imgOutPath, int tWidth, int tHeight, int quality) {
    Mat oldImgMat = cv::imread(imgPath);
    Mat newImgMat;
    if (tWidth != 0 || tHeight != 0) {
        if (tWidth != 0 && tHeight != 0) {
            resize(oldImgMat, newImgMat, Size(tWidth, tHeight));
        } else {
            Size newSize;
            if (tWidth == 0) {
                newSize = Size(tHeight / oldImgMat.rows * oldImgMat.cols, tHeight);
            } else {
                newSize = Size(tWidth, float(tWidth) / oldImgMat.cols * oldImgMat.rows);
            }
            resize(oldImgMat, newImgMat, newSize);
        }
    }
    vector<int> compression_params;
    compression_params.push_back(cv::IMWRITE_JPEG_QUALITY);
    compression_params.push_back(std::min(quality, 100));   //quality: [0,100]
    imwrite(imgOutPath, (tWidth != 0 || tHeight != 0) ? newImgMat : oldImgMat, compression_params);
    common_utils::clearVector(compression_params);
}

DART_API Struct2CheckLightResult checkImgHaveLightByData(uint8_t *imgDatas, int width, int height) {
    Mat img = Mat(Size(width, height), CV_8UC3, imgDatas);
    return MooeliTasks::processLightDetection(img);
}

DART_API Struct2CheckLightResult checkImgHaveLight(const char *imgPath) {
    Mat img = cv::imread(imgPath);
    return MooeliTasks::processLightDetection(img);
}

DART_API int checkToothHaveLight(const char *imgPath, int centerX, int centerY) {
    Mat img = cv::imread(imgPath);
    return MooeliTasks::processToothLightDetection(img, centerX, centerY);
}

DART_API int
checkToothHaveLightByData(uint8_t *imgDatas, int width, int height, int centerX, int centerY) {
    Mat img = Mat(Size(width, height), CV_8UC3, imgDatas);
    return MooeliTasks::processToothLightDetection(img, centerX, centerY);
}

DART_API void colorTransform(const char *imgPath) {
    Mat img = cv::imread(imgPath);
    Mat result;
    cvtColor(img, result, cv::COLOR_BGR2Lab);

    Scalar meanScalar = mean(result);
    float avg_a = meanScalar.val[1];
    float avg_b = meanScalar.val[2];
    float tempMaxA = (avg_a - 144) > 0 ? (avg_a - 144) / 16 : 0;
    float tempMaxB = (avg_b - 144) > 0 ? (avg_b - 144) / 16 : 0;
    float coefficient1 = tempMaxA >= 0.8 ? 0.8 : tempMaxA;
    float coefficient2 = tempMaxB >= 1.1 ? 1.1 : tempMaxB;

    for (int nrow = 0; nrow < result.rows; nrow++) {
        for (int ncol = 0; ncol < result.cols; ncol++) {
            auto &pixel = result.at<Vec3b>(nrow, ncol);
            pixel[1] = pixel[1] - ((avg_a - 128) * (pixel[0] / 255.0) * coefficient1);
            pixel[2] = pixel[2] - ((avg_b - 128) * (pixel[0] / 255.0) * coefficient2);
        }
    }

    Mat processDoneMat;
    cvtColor(result, processDoneMat, cv::COLOR_Lab2BGR);

    vector<int> compression_params;
    compression_params.push_back(cv::IMWRITE_JPEG_QUALITY);
    compression_params.push_back(100);
    imwrite(imgPath, processDoneMat, compression_params);
    common_utils::clearVector(compression_params);
}

DART_API int flipImage(const char *imgPath, const char *outPath) {
    Mat image = cv::imread(imgPath);
    if (!image.empty()) {
        cv::flip(image, image, 1);
        cv::imwrite(outPath, image);
        return 1;
    }
    return -1;
}

DART_API int is_device_connect() {
    return deviceManager.is_device_connect();
}

DART_API void connect_device() {
    deviceManager.connect_device();
}

DART_API void disconnect_device() {
    deviceManager.disconnect_device();
}

DART_API void enable_image_transit() {
    deviceManager.enable_image_transit();
}

DART_API void disable_image_transit() {
    deviceManager.disable_image_transit();
}

DART_API void save_image(const char *path) {
    std::string path_str(path);
    deviceManager.save_image(path_str);
}

DART_API Struct2DeviceImage retrieve_image() {
    return deviceManager.retrieve_image();
}

DART_API float get_battery_percent() {
    return deviceManager.get_battery_percent();
}


DART_API DeviceType get_device_type() {
    return deviceManager.get_device_type();
}

DART_API void enable_light_white() {  //开启白光灯
    deviceManager.enable_light_white();
}

DART_API void enable_light_ultra_violet() {  //开启紫外灯
    deviceManager.enable_light_ultra_violet();
}

DART_API LightStatus retrieve_light_status() {
    return deviceManager.retrieve_light_status();
}

DART_API UltraModel get_device_info() {
    return deviceManager.get_device_info();
}

DART_API int has_recent_image() {
    return deviceManager.has_recent_image() ? 1 : 0;
}

DART_API CString get_mac_address() {
    auto mac_address = deviceManager.get_mac_address();
    return common_utils::cstr(mac_address);
}

DART_API Struct2Line buildNurbsLine(const double *value, const int length, const int close) {
    std::vector<double> vec(value, value + length);
    std::vector<double> nurbs = Nurbs::buildNurbsLine(vec, close == 1);
    int size = (int) nurbs.size();
    auto result = new double[size];
    memcpy(result, nurbs.data(), size * sizeof(double));
    common_utils::clearVector(vec);
    return {
            .data = result,
            .length = size,
    };
}