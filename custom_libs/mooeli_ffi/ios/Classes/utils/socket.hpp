//
// Created by syq on 2023/5/30.
//
#pragma once
#include <sys/socket.h>

namespace SocketTools {
    std::string ip2string(sockaddr* addr) {
        static char ip[32];
        sprintf(ip, "%d.%d.%d.%d", (unsigned char)addr->sa_data[2], (unsigned char)addr->sa_data[3], (unsigned char)addr->sa_data[4], (unsigned char)addr->sa_data[5]);
        return std::string(ip);
    }

    ifaddrs* get_interface_addresses() {
        ifaddrs* addresses = 0;
        getifaddrs(&addresses);
        return addresses;
    }

    sockaddr* get_interface_address(std::string prefix) {
        auto addresses = SocketTools::get_interface_addresses();
        while(addresses) {
            std::string ip = SocketTools::ip2string(addresses->ifa_addr);
            if(ip.substr(0, prefix.size()) == prefix) {
                return addresses->ifa_addr;
            }
            addresses = addresses->ifa_next;
        }
        return nullptr;
    }
}
