//
// Created by syq on 2023/4/20.
//

#pragma once

#include <vector>
#include <chrono>

namespace common_utils {
    template<class T>
    void clearVector(std::vector<T> &vt) {
        std::vector<T> vtTemp;
        vtTemp.swap(vt);
    }

    template<class T, class K>
    void clearMap(std::map<T, K> &vt) {
        std::map<T, K> vtTemp;
        vtTemp.swap(vt);
    }

    long getTimestamp() {
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch());
        return ms.count();
    }

    vector<float> mat2vector(Mat mat) {
        vector<float> res = vector<float>();
        for (int i = 0; i < mat.rows; ++i) {
            for (int j = 0; j < mat.cols; ++j) {
                float num = mat.at<float>(i, j);
                res.push_back(num);
            }
        }
        return res;
    }

    int mat2bytes(Mat mat, Uint8Ptr &buffer) {
        Uint8Vec bytes;
        imencode(".jpg", mat, bytes);

        int buffer_size = (int) bytes.size();
        buffer = new Uint8[buffer_size];
        memcpy(buffer, bytes.data(), buffer_size);

        common_utils::clearVector(bytes);
        return buffer_size;
    }

    void float2uint8(Mat &mat) {
        for (int i = 0; i < mat.rows; ++i) {
            for (int j = 0; j < mat.cols; ++j) {
                auto &color = mat.at<Vec3f>(i, j);
                color[0] *= UCHAR_MAX;
                color[1] *= UCHAR_MAX;
                color[2] *= UCHAR_MAX;
            }
        }
    }

    std::string uint8vec_to_hex_string(Uint8Vec v) {
        std::stringstream ss;
        ss << std::hex << std::setfill('0');
        for (int i = 0; i < v.size(); i++) {
            ss << std::hex << std::setw(2) << static_cast<int>(v[i]);
        }
        return ss.str();
    }

    CString cstr(std::string str) {
        CString cstr;
        cstr.length = str.length();
        cstr.ptr = new char[cstr.length + 1];
        strcpy(cstr.ptr, str.c_str());
        return cstr;
    }

    cv::Scalar hexToScalar(const std::string &hexColor) {
        unsigned long hexValue;
        if (hexColor.front() == '#') {
            hexValue = std::stoul(hexColor.substr(1), nullptr, 16);
        } else {
            hexValue = std::stoul(hexColor, nullptr, 16);
        }

        int r = (hexValue >> 16) & 0xFF;
        int g = (hexValue >> 8) & 0xFF;
        int b = hexValue & 0xFF;

        return cv::Scalar(b, g, r);
    }
}

