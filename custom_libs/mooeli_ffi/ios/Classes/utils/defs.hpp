//
// Created by syq on 2023/4/20.
//
#pragma once
#include <stdint.h>
#include <netinet/in.h>
#include "../exports/enums.h"
#include "../exports/structs.h"

#define MAKE_GETTER(x) \
    auto get_##x() { return x; }
#define ASSERT_EQUAL(a, b) \
    if (a != b) { return false; }
#define ASSERT_NO_LESS(a, b) \
    if (a < b) { return false; }

using std::vector;
using std::cin;
using std::cout;
using std::chrono::high_resolution_clock;

using cv::Point;
using cv::Point2f;
using cv::Mat1f;
using cv::Mat1i;
using cv::Vec3b;
using cv::Vec3f;
using cv::Vec3i;
using cv::Scalar;
using cv::Mat;
using cv::Size;
using cv::Range;
using cv::Rect2d;

using Uint8 = uint8_t;
using Uint64 = uint64_t;
using Uint8Vec = std::vector<uint8_t>;
using Uint8Ptr = uint8_t*;

using SockAddr = sockaddr_in;

enum MirrorType {
    center,         //中心对称
    horizontal,     //水平镜像
    vertical,       //垂直镜像
};

typedef struct {
    bool isUint8;
    float inputInt8TFLiteScale;
    int inputInt8TFLiteZeroPoint;
    float outputInt8TFLiteScale;
    int outputInt8TFLiteZeroPoint;
    int design_width;
    int design_height;
    int outputRows;
    int outputCols;
    int shapeTotalCount;
} ModelParams; //模型参数

typedef struct {
    //pre
    int offsets_l;
    int offsets_t;
    int offsets_r;
    int offsets_b;
    int rect_x;
    int rect_y;
    int rect_w;
    int rect_h;
    int ori_size_w;
    int ori_size_h;
    float scale;
    void *imgBytes;
    int imgByteSize;
    int design_width;
    int design_height;
    //last
    uint8_t *imgDataList;
    const char *imgPath;
    int rows;
    int cols;
} PreprocessInfo; //模型输入预处理信息
