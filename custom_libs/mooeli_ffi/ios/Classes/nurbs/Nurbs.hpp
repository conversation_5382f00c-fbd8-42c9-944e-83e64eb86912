#pragma once

#include "../utils/defs.hpp"
#include <iostream>
#include <vector>
#include <cmath>
#include <string>
#include "../Eigen/Dense"

using namespace std;
using namespace Eigen;

namespace Nurbs {
    typedef struct tagPOINT3 {
        double x;
        double y;
        double z;
    } POINT3;

    double d(const Eigen::MatrixXd u, const int i, const int k) {
        return u(0, i + k) - u(0, i);
    }

    MatrixXd calCuM(const MatrixXd u, const int i) {
        Matrix<double, 4, 4> m;
        m.setZero();
        m(0, 0) = pow(d(u, i + 3, 1), 2) / (d(u, i + 2, 2) * d(u, i + 1, 3));
        m(0, 2) = pow(d(u, i + 2, 1), 2) / (d(u, i + 2, 2) * d(u, i + 2, 3));
        m(1, 2) = 3 * d(u, i + 3, 1) * d(u, i + 2, 1) / (d(u, i + 2, 2) * d(u, i + 2, 3));
        m(2, 2) = 3 * pow(d(u, i + 3, 1), 2) / (d(u, i + 2, 2) * d(u, i + 2, 3));
        m(3, 3) = pow(d(u, i + 3, 1), 2) / (d(u, i + 3, 2) * d(u, i + 3, 3));
        m(0, 1) = 1 - m(0, 0) - m(0, 2);
        m(1, 0) = -3.0 * m(0, 0);
        m(1, 1) = 3.0 * m(0, 0) - m(1, 2);
        m(2, 0) = 3.0 * m(0, 0);
        m(2, 1) = -3.0 * m(0, 0) - m(2, 2);
        m(3, 0) = -m(0, 0);
        m(3, 2) = -(m(2, 2) / 3.0 + m(3, 3) +
                    pow(d(u, i + 3, 1), 2) / (d(u, i + 3, 2) * d(u, i + 2, 3)));
        m(3, 1) = m(0, 0) - m(3, 2) - m(3, 3);
        return m;
    }


    bool calcuNURBS(const MatrixXd &mat, MatrixXd &vs, MatrixXd &ve, MatrixXd &outMat,
                    bool isClosed) {
        MatrixXd U, temp, tempDiff, dU, A, E, tempPnt, ctrlPnt, tempU, d, M, D, T;
        int row = int(mat.rows()), col = int(mat.cols()), stepSize = 3, outIdx = 0;
        double tempSum = 0.0, dt = 0.0;

        U.setZero(1, row + stepSize + 3);
        dU.setZero(1, row + stepSize + 3);
        A.setZero(row, row);
        E.setZero(row, col);
        temp.setZero(1, row - 1);

        for (int i = 1; i <= row - 1; i++) {
            tempDiff.setZero(1, col);
            tempDiff = mat.block(i, 0, 1, col) - mat.block(i - 1, 0, 1, col);
            temp(0, i - 1) = tempDiff.norm();
        }

        tempSum = temp.sum();

        U.block(0, row + stepSize - 1, 1, stepSize + 1).setOnes();
        for (int i = stepSize + 1; i <= row + stepSize - 2; i++) {
            U(0, i) = U(0, i - 1) + temp(0, i - stepSize - 1) / tempSum;
        }

        if (vs.norm() == 0) {
            vs = mat.block(1, 0, 1, col) - mat.block(0, 0, 1, col);
        }
        vs.normalize();

        if (ve.norm() == 0) {
            ve = mat.block(row - 1, 0, 1, col) - mat.block(row - 2, 0, 1, col);
        }
        ve.normalize();

        for (int i = stepSize + 1; i <= row + stepSize - 1; i++) {
            dU(0, i - 1) = U(0, i) - U(0, i - 1);
        }

        A(0, 0) = 1;
        A(row - 1, row - 1) = 1;
        E.block(0, 0, 1, col) = mat.block(0, 0, 1, col) + dU(0, 3) / 3 * vs;
        E.block(row - 1, 0, 1, col) = mat.block(row - 1, 0, 1, col) - dU(0, row + 1) / 3 * ve;

        for (int i = 2; i <= row - 1; i++) {
            A(i - 1, i - 2) = dU(0, i + 2) * dU(0, i + 2) / dU.block(0, i, 1, 3).sum();
            A(i - 1, i - 1) =
                    dU(0, i + 2) * (dU(0, i) + dU(0, i + 1)) / dU.block(0, i, 1, 3).sum() + \
            dU(0, i + 1) * (dU(0, i + 2) + dU(0, i + 3)) / dU.block(0, i + 1, 1, 3).sum();
            A(i - 1, i) = dU(0, i + 1) * dU(0, i + 1) / dU.block(0, i + 1, 1, 3).sum();
            E.block(i - 1, 0, 1, col) = (dU(0, i + 1) + dU(0, i + 2)) * mat.block(i - 1, 0, 1, col);
        }

        tempPnt.setZero();
        tempPnt = A.inverse() * E;
        ctrlPnt.setZero(tempPnt.rows() + 2, tempPnt.cols());
        int ctrlPntRow = int(ctrlPnt.rows());
        ctrlPnt.block(0, 0, 1, col) = mat.block(0, 0, 1, col);
        ctrlPnt.block(1, 0, ctrlPntRow - 2, col) = tempPnt;
        ctrlPnt.block(ctrlPntRow - 1, 0, 1, col) = mat.block(row - 1, 0, 1, col);

        T.resize(1, 4);

        int start = 0;
        int lastctrlPntIndex = ctrlPntRow - 3;

        if (isClosed) {
            start = 2;
            lastctrlPntIndex -= 2;
        }

        double each = 1.0;

        int count = 1;
        for (int i = start; i < lastctrlPntIndex; i++) {
            M = calCuM(U, i);
            D = ctrlPnt.block(i, 0, 4, col);

            for (int k = 0; k <= 3; k++) {
                T(0, k) = pow(0, k);
            }
            MatrixXd first = T * M * D;
            for (int k = 0; k <= 3; k++) {
                T(0, k) = pow(1, k);
            }
            MatrixXd last = T * M * D;

            double length = pow((first(0, 0) - last(0, 0)) * (first(0, 0) - last(0, 0)) +
                                (first(0, 1) - last(0, 1)) * (first(0, 1) - last(0, 1)), 0.5);

            if (length > 0) {
                dt = each / length;
                if (i == lastctrlPntIndex - 1 && dt > 0.2) dt = 0.2;
                for (double t = 0.0; t < 1.0; t = t + dt) {
                    count++;
                }
            }

        }

        outMat.resize(count, col);
        outMat.setZero();

        for (int i = start; i < lastctrlPntIndex; i++) {
            M = calCuM(U, i);
            D = ctrlPnt.block(i, 0, 4, col);

            for (int k = 0; k <= 3; k++) {
                T(0, k) = pow(0, k);
            }
            MatrixXd first = T * M * D;
            for (int k = 0; k <= 3; k++) {
                T(0, k) = pow(1, k);
            }
            MatrixXd last = T * M * D;

            double length = pow((first(0, 0) - last(0, 0)) * (first(0, 0) - last(0, 0)) +
                                (first(0, 1) - last(0, 1)) * (first(0, 1) - last(0, 1)), 0.5);

            if (length > 0) {
                dt = each / length;
                if (i == lastctrlPntIndex - 1 && dt > 0.2) dt = 0.2;

                for (double t = 0.0; t < 1.0; t = t + dt) {
                    for (int k = 0; k <= 3; k++) {
                        T(0, k) = pow(t, k);
                    }
                    outMat.block(outIdx, 0, 1, col) = T * M * D;

                    if (outIdx >= count - 1) {
                        break;
                    }
                    outIdx++;
                }
            }
        }

        if (!isClosed)
            outMat.block(outIdx, 0, 1, col) = ctrlPnt.block(ctrlPntRow - 1, 0, 1, col);  // 最后一点
        return true;
    }


    void caculateNurbs(std::vector<POINT3> &_mousePoint, std::vector<double> &axisLinePoint,
                       bool isClosed) {

        std::vector<POINT3> mousePoint;
        //mousePoint.emplace_back(_mousePoint[0]);

        for (int i = 0; i < _mousePoint.size(); ++i) {
            int before = i - 1;
            if (before < 0)before += _mousePoint.size();
            {
                double dxd = _mousePoint[before].x - _mousePoint[i].x;
                double dyd = _mousePoint[before].y - _mousePoint[i].y;
                double length0 = pow(dxd * dxd + dyd * dyd, 0.5);

                if (length0 <= 0.1) {
                    before--;
                }
            }

            int after = (i + 1) % _mousePoint.size();

            {
                double dx1 = _mousePoint[i].x - _mousePoint[after].x;
                double dy1 = _mousePoint[i].y - _mousePoint[after].y;
                double length1 = pow(dx1 * dx1 + dy1 * dy1, 0.5);

                if (length1 <= 0.1) {
                    after++;
                }
            }


            double dxd = _mousePoint[before].x - _mousePoint[after].x;
            double dyd = _mousePoint[before].y - _mousePoint[after].y;
            double lengthd = pow(dxd * dxd + dyd * dyd, 0.5);

            double dx0 = _mousePoint[before].x - _mousePoint[i].x;
            double dy0 = _mousePoint[before].y - _mousePoint[i].y;
            double length0 = pow(dx0 * dx0 + dy0 * dy0, 0.5);

            double dx1 = _mousePoint[i].x - _mousePoint[after].x;
            double dy1 = _mousePoint[i].y - _mousePoint[after].y;
            double length1 = pow(dx1 * dx1 + dy1 * dy1, 0.5);

            if (lengthd > 0 && length0 > lengthd * 2 && i > 0) {
                double dt = lengthd / length0;
                for (double t = dt; t < 1.0; t = t + dt) {
                    POINT3 pt;
                    pt.x = _mousePoint[before].x + t * (_mousePoint[i].x - _mousePoint[before].x);
                    pt.y = _mousePoint[before].y + t * (_mousePoint[i].y - _mousePoint[before].y);
                    pt.z = 0;
                    mousePoint.emplace_back(pt);
                }
            }

            mousePoint.emplace_back(_mousePoint[i]);

            if (lengthd > 0 && length1 > lengthd * 2 && i < _mousePoint.size() - 1) {
                double dt = lengthd / length1;
                for (double t = dt; t < 1.0; t = t + dt) {
                    POINT3 pt;
                    pt.x = _mousePoint[i].x + t * (_mousePoint[after].x - _mousePoint[i].x);
                    pt.y = _mousePoint[i].y + t * (_mousePoint[after].y - _mousePoint[i].y);
                    pt.z = 0;
                    mousePoint.emplace_back(pt);
                }

                //i++;
            }
        }

        if (isClosed == true) {
            int length = mousePoint.size();
            double dx = mousePoint[0].x - mousePoint[length - 1].x;
            double dy = mousePoint[0].y - mousePoint[length - 1].y;

            if (dx * dx + dy * dy >= 0.1) {
                POINT3 pt;
                pt.x = mousePoint[0].x;
                pt.y = mousePoint[0].y;
                pt.z = 0;
                mousePoint.emplace_back(pt);
            }

            {
                POINT3 pt;
                pt.x = mousePoint[mousePoint.size() - 2].x;
                pt.y = mousePoint[mousePoint.size() - 2].y;
                pt.z = 0;
                mousePoint.insert(mousePoint.begin(), pt);
            }

            {
                POINT3 pt;
                pt.x = mousePoint[mousePoint.size() - 3].x;
                pt.y = mousePoint[mousePoint.size() - 3].y;
                pt.z = 0;
                mousePoint.insert(mousePoint.begin(), pt);
            }


            {
                POINT3 pt;
                pt.x = mousePoint[3].x;
                pt.y = mousePoint[3].y;
                pt.z = 0;
                mousePoint.emplace_back(pt);
            }

            {
                POINT3 pt;
                pt.x = mousePoint[4].x;
                pt.y = mousePoint[4].y;
                pt.z = 0;
                mousePoint.emplace_back(pt);
            }


        }


        Matrix<double, Eigen::Dynamic, Eigen::Dynamic> srcMat;
        srcMat.setZero(mousePoint.size(), 3);

        MatrixXd outMat, vs, ve;
        vs.setZero();
        ve.setZero();
        for (int i = 0; i < mousePoint.size(); ++i) {
            srcMat(i, 0) = mousePoint[i].x;
            srcMat(i, 1) = mousePoint[i].y;
            srcMat(i, 2) = mousePoint[i].z;
        }


        calcuNURBS(srcMat, vs, ve, outMat, isClosed);

        int r = outMat.rows();
        int c = outMat.cols();

        std::vector<double> _axisLinePoint;
        for (int i = 0; i < r; ++i) {
            for (int j = 0; j < c; ++j) {
                _axisLinePoint.push_back(outMat(i, j));
            }
        }


        int endIndex = (int) _axisLinePoint.size() / 3 - 1;
        for (; endIndex >= 0; --endIndex) {
            if (_axisLinePoint[endIndex * 3] != 0 || _axisLinePoint[endIndex * 3 + 1] != 0) {
                break;
            }
        }


        for (int i = 0; i < endIndex + 1; ++i) {
            axisLinePoint.push_back(_axisLinePoint[i * 3]);
            axisLinePoint.push_back(_axisLinePoint[i * 3 + 1]);
        }
    }

    std::vector<double> buildNurbsLine(std::vector<double> axisPointArray, bool isClosed) {
        std::vector<double> axisLinePoint;
        if (axisPointArray.size() % 2 == 0) {
            std::vector<POINT3> mousePoint;
            for (int i = 0; i < axisPointArray.size() / 2; i++) {
                POINT3 pt;
                pt.x = axisPointArray[i * 2];
                pt.y = axisPointArray[i * 2 + 1];
                pt.z = 0;
                mousePoint.emplace_back(pt);
            }
            caculateNurbs(mousePoint, axisLinePoint, isClosed);
        }

        return axisLinePoint;
    }
}