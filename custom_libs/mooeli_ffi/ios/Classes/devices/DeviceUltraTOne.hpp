//
// Created by syq on 2023/5/25.
//
#pragma once
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <thread>
#include <opencv2/opencv.hpp>
#include "../utils/defs.hpp"

class DeviceUltraTOne {
private:
    std::string device_ip;
    int sockfd_ctrl; //控制指令
    int sockfd_data; //接受数据
    SockAddr address_ctrl;
    SockAddr address_data;

    static constexpr uint8_t IDENTIFIER_0 = 0xcc;
    static constexpr uint8_t IDENTIFIER_1 = 0x5a;
    static const Uint8Vec MAC_DATA_COMMAND;
    static const Uint8Vec REV_DATA_COMMAND;
    static const Uint8Vec SSID_DATA_COMMAND;
    static constexpr int MAC_DATA_SIZE = 12;
    static constexpr int REV_DATA_SIZE = 7;
    static constexpr int SSID_DATA_SIZE = 7;

    std::thread heartbeat_thread;
    std::thread image_thread;

    bool isTransmissionActive;
    std::queue<Mat> imageQueue;
    std::mutex queueMutex;

    SockAddr make_sock_addr(std::string ip, int port);
    Uint8Vec send_command(Uint8Vec command, int response_size);
    void receive_heartbeat();
    void close();

public:
    DeviceUltraTOne(const std::string &device_ip);
    ~DeviceUltraTOne();
    std::string connect();      //启动连接
//    void start_rstp();          //开启图像传输
//    void close_rstp();          //关闭图像传输
//    auto retrieve_image();      //获取最新的图像帧
//    auto get_device_info();
//    auto get_battery_info();
};

const Uint8Vec DeviceUltraTOne::MAC_DATA_COMMAND = {0x01, 0x05, 0x02, 0x01, 0x07};
const Uint8Vec DeviceUltraTOne::REV_DATA_COMMAND = {0x01, 0xa2, 0x02, 0x01, 0xa0};
const Uint8Vec DeviceUltraTOne::SSID_DATA_COMMAND = {0x01, 0x01, 0x02, 0x00, 0x02};

DeviceUltraTOne::DeviceUltraTOne(const std::string &device_ip) : device_ip(device_ip) {
    sockfd_ctrl = socket(AF_INET, SOCK_DGRAM, 0);
    sockfd_data = socket(AF_INET, SOCK_DGRAM, 0);
    address_ctrl = make_sock_addr(device_ip, 7777);
    address_data = make_sock_addr(device_ip, 7070);

    //bind local port
//    SockAddr address_local = {AF_INET, 0, {htonl(INADDR_ANY)}};
//    int success_ctrl = bind(sockfd_ctrl, (sockaddr*)&address_local, sizeof(address_local));
//    int success_data = bind(sockfd_data, (sockaddr*)&address_local, sizeof(address_local));
//    if (success_ctrl < 0 || success_data < 0)  {
//        this->close();
//        return;
//    }
}

DeviceUltraTOne::~DeviceUltraTOne() {
    this->close();
}

SockAddr DeviceUltraTOne::make_sock_addr(std::string ip, int port) {
    SockAddr sockAddr;
    memset(&sockAddr, 0, sizeof(sockAddr));
    sockAddr.sin_family = AF_INET;
    sockAddr.sin_port = htons(port);
    sockAddr.sin_addr.s_addr = inet_addr(ip.c_str());
    return sockAddr;
}

void DeviceUltraTOne::close() {
    ::close(sockfd_ctrl);
    ::close(sockfd_data);
}

Uint8Vec DeviceUltraTOne::send_command(Uint8Vec command, int response_size) {
    Uint8Vec packet = {IDENTIFIER_0, IDENTIFIER_1};
    std::copy(command.begin(), command.end(), std::back_inserter(packet));
    sendto(this->sockfd_ctrl,packet.data(),packet.size(),0,(sockaddr*)&address_ctrl,sizeof(address_ctrl));

    Uint8Vec packet_resp(1024, 0x0);
    int num_bytes = recv(sockfd_ctrl, packet_resp.data(), packet_resp.size(), MSG_DONTWAIT);
    if (num_bytes != response_size) { //失败
    }

    return packet_resp;
}



std::string DeviceUltraTOne::connect() {
    Uint8Vec response = this->send_command(MAC_DATA_COMMAND, MAC_DATA_SIZE);
    Uint8Vec mac = Uint8Vec(response.begin() + 5, response.begin() + 11);

    heartbeat_thread = std::thread(&DeviceUltraTOne::receive_heartbeat, this);

    return std::string(mac.begin(), mac.end());
}

void DeviceUltraTOne::receive_heartbeat() {
    while(1) {
        Uint8Vec packet(1024, 0x0);
        int num_bytes = recv(sockfd_ctrl, packet.data(), packet.size(), 0);

        int battery = packet[13];
        int battery_low = packet[14];
    }
}



//void DeviceUltraTOne::get_battery_info() {
//    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
//    if (sockfd == -1) {
//        std::cerr << "Failed to create socket." << std::endl;
//        return;
//    }
//
//    sockaddr_in serverAddr{};
//    serverAddr.sin_family = AF_INET;
//    serverAddr.sin_port = htons(7777);  // 服务器端口
//    serverAddr.sin_addr.s_addr = inet_addr("************");  // 服务器 IP
//
//    // 连接服务器
//    if (connect(sockfd, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == -1) {
//        std::cerr << "Failed to connect to server." << std::endl;
//        close(sockfd);
//        return;
//    }
//
//    std::string message = "\xcc\x5a\x01\x05\x02\x01\x07";
//    if(send(sockfd, message.c_str(), message.length(), 0) == -1) {
//        std::cerr << "Failed to send data to server." << std::endl;
//        close(sockfd);
//        return;
//    }
//
//    char buffer[1024];
//    memset(buffer, 0, sizeof(buffer));
//    if (recv(sockfd, buffer, sizeof(buffer), 0) == -1) {
//        std::cerr << "Failed to receive data from server." << std::endl;
//        close(sockfd);
//        return;
//    }
//    std::cout << "Received from server: " << buffer << std::endl;
//    close(sockfd);
//}
//
//

