//
// Created by syq on 2023/5/30.
//
#pragma once

#include "DeviceUltraTOne.hpp"
#include "DeviceUltraTTwo.hpp"
#include "../exports/enums.h"

const std::string device_ultra1_ip = "************";
const std::string device_ultra2_ip = "************";

class DeviceManager {
private:
    std::unique_ptr <DeviceUltraTOne> ultra_t1;
    std::unique_ptr <DeviceUltraTTwo> ultra_t2;
    DeviceType type;

public:
    DeviceManager();

    ~DeviceManager();

    DeviceType get_device_type();

    DeviceType set_device_type(DeviceType type);

    bool is_device_connect();

    bool is_image_valid();

    void connect_device();

    void disconnect_device();

    void save_image(std::string path);

    void enable_image_transit();

    void disable_image_transit();

    Struct2DeviceImage retrieve_image();

    Struct2DeviceImage get_image_ms();

    void enable_light_white();

    void enable_light_ultra_violet();

    LightStatus retrieve_light_status();

    UltraModel get_device_info();

    bool has_recent_image();

    std::string get_mac_address();

    float get_battery_percent();
};

DeviceManager::DeviceManager() {
    this->type = DeviceType::none;
}

DeviceManager::~DeviceManager() {}

DeviceType DeviceManager::get_device_type() {
    auto addresses = SocketTools::get_interface_addresses();
    std::string prefix_t1 = "192.168.99";
    std::string prefix_t2 = "192.168.97";
    for (; addresses; addresses = addresses->ifa_next) {
        if (addresses->ifa_addr == nullptr) continue;
        std::string ip = SocketTools::ip2string(addresses->ifa_addr);
        if (ip.substr(0, prefix_t1.size()) == prefix_t1) {
            return set_device_type(DeviceType::ultra_t1);
        } else if (ip.substr(0, prefix_t2.size()) == prefix_t2) {
            return set_device_type(DeviceType::ultra_t2);
        }
    }

    return set_device_type(DeviceType::none);
}

DeviceType DeviceManager::set_device_type(DeviceType device_type) {
    if (this->type == device_type) return device_type;
    switch (device_type) {
        case DeviceType::ultra_t1:
        case DeviceType::ultra_t2: {
            this->ultra_t2 = nullptr;    //需要保证先destruct后construct，否则另一个socket会关闭图传
            break;
        }
        case DeviceType::none: {
            if (this->ultra_t2) {  //断连时不析构，保留最后一帧
//                this->ultra_t2->close_data_stream();
//                this->ultra_t2->close_heartbeat();
                this->ultra_t2->saved_image_seq_id = -1;
            }
            break;
        }
    }
    this->type = device_type;
    return device_type;
}

bool DeviceManager::is_device_connect() {
    if (this->type != DeviceType::ultra_t2) return false;
    if (!this->ultra_t2) return false;
    return this->ultra_t2->retrieve_is_connect();
}

bool DeviceManager::is_image_valid() {
    if (this->type != DeviceType::ultra_t2) return false;
    if (!this->ultra_t2) return false;
    return this->ultra_t2->is_image_valid();
}


void DeviceManager::connect_device() {
    if (this->type != DeviceType::ultra_t2) return;
    this->ultra_t2 = nullptr;   //先析构
    this->ultra_t2 = std::make_unique<DeviceUltraTTwo>(device_ultra2_ip);
}

void DeviceManager::disconnect_device() {
    if (this->type != DeviceType::ultra_t2) return;
    if (!this->ultra_t2) return;
    this->ultra_t2->saved_image_seq_id = -1;
    this->ultra_t2->close_data_stream();
    this->ultra_t2->close_heartbeat();
}


void DeviceManager::enable_image_transit() {
    if (this->type != DeviceType::ultra_t2) return;
    if (!this->ultra_t2) return;
    this->ultra_t2->start_data_stream();
}

void DeviceManager::disable_image_transit() {
    if (this->type != DeviceType::ultra_t2) return;
    if (!this->ultra_t2) return;
    this->ultra_t2->close_data_stream();
    this->ultra_t2->saved_image_seq_id = -1;
}

void DeviceManager::save_image(std::string path) {
    if (this->type != DeviceType::ultra_t2) return;
    if (!this->ultra_t2) return;
    this->ultra_t2->save_image(path);
}

Struct2DeviceImage DeviceManager::retrieve_image() {
    //if(this->type != DeviceType::ultra_t2) return {nullptr, 0, -1,0, 0 };
    if (!this->ultra_t2) return {nullptr, 0, -1, 0, 0};
    Uint8Vec image = this->ultra_t2->retrieve_image();
    int image_seq_id = this->ultra_t2->get_saved_image_seq_id();
    if (image_seq_id < 0 || image.empty()) {
        return {nullptr, 0, -1, 0, 0};
    }

//    Mat rgba = cv::imdecode(image, cv::IMREAD_COLOR);
//    if(rgba.data == nullptr) {
//        return {nullptr, 0, -1,0, 0 };
//    }
//    cv::cvtColor(rgba, rgba, cv::COLOR_BGR2BGRA);
//    if(rgba.data == nullptr) {
//        return {nullptr, 0, -1,0, 0 };
//    }

    //cv::cvtColor(image, image, cv::COLOR_BGR2BGRA);
    // bool success = imwrite("/data/user/0/com.chohotech.moili/cache/ultra.jpg", image);
//    Uint8* buffer = nullptr;
//    int buffer_size = common_utils::mat2bytes(image, buffer);

    int buffer_size = (int) image.size();
    auto buffer = new Uint8[buffer_size];
    memcpy(buffer, image.data(), buffer_size * sizeof(Uint8));

//    int buffer_size = rgba.rows * rgba.cols * 4;
//    auto buffer = new Uint8[buffer_size];
//    memcpy(buffer, rgba.data, buffer_size * sizeof(Uint8));

    return {
            .buffer = buffer,
            .buffer_size = buffer_size,
            .image_seq_id = image_seq_id,
            .width= 0,
            .height = 0
    };
}

Struct2DeviceImage DeviceManager::get_image_ms() {
    if (!this->ultra_t2) return {nullptr, 0, -1, 0, 0};
    Uint8Vec image = this->ultra_t2->image_ms;

    int buffer_size = (int) image.size();
    auto buffer = new Uint8[buffer_size];
    memcpy(buffer, image.data(), buffer_size * sizeof(Uint8));

    common_utils::clearVector(this->ultra_t2->image_ms);

    return {
            .buffer = buffer,
            .buffer_size = buffer_size,
            .image_seq_id = 0,
            .width= 0,
            .height = 0
    };
}

float DeviceManager::get_battery_percent() {
    if (!this->ultra_t2) return 0;
    if (this->type != DeviceType::ultra_t2) return 0;
    return this->ultra_t2->retrieve_battery();
}

void DeviceManager::enable_light_white() {
    if (!this->ultra_t2) return;
    if (this->type != DeviceType::ultra_t2) return;
    this->ultra_t2->enable_light_white();
}

void DeviceManager::enable_light_ultra_violet() {
    if (!this->ultra_t2) return;
    if (this->type != DeviceType::ultra_t2) return;
    this->ultra_t2->enable_light_ultra_violet();
}

LightStatus DeviceManager::retrieve_light_status() {
    if (!this->ultra_t2 || this->type != DeviceType::ultra_t2) return LightStatus::white;
    return this->ultra_t2->get_light_status();
}

UltraModel DeviceManager::get_device_info() {
    if (!this->ultra_t2 || this->type != DeviceType::ultra_t2) return {0, 0, 0};
    return this->ultra_t2->retrieve_device_info();
}

bool DeviceManager::has_recent_image() {
    if (!this->ultra_t2) return false;
    return this->ultra_t2->is_image_valid();
}

std::string DeviceManager::get_mac_address() {
    if (!this->ultra_t2 || this->type != DeviceType::ultra_t2) return std::string();
    return this->ultra_t2->get_mac_address();
}

