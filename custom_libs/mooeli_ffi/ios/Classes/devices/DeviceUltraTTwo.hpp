//
// Created by syq on 2023/5/26.
//
#pragma once

#include <iostream>
#include <vector>
#include <thread>
#include <mutex>
#include <numeric>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <ifaddrs.h>
#include <stdio.h>
#include <cerrno>
#include <opencv2/opencv.hpp>
#include "../utils/socket.hpp"
#include "../utils/defs.hpp"

class DeviceUltraTTwo {
private:
    std::string device_ip;
    std::chrono::time_point<std::chrono::high_resolution_clock> last_packet_timestamp;
    int sockfd_ctrl; //控制指令
    int sockfd_data; //接受数据
    int sockfd_heartbeat; //心跳控制指令
    std::string device_mac;
    SockAddr address_ctrl;
    SockAddr address_data;
    
    std::atomic<bool> is_connect = false;  //是否连接
    std::atomic<bool> is_transit = false;  //是否正在传输
    std::atomic<bool> is_heartbeat = false;  //是否请求心跳包
    
    std::shared_ptr<UltraModel> device_model;  //设备型号
    
    static const Uint8Vec MAC_ADDRESS_COMMAND;
    static const Uint8Vec DEVICE_INFO_COMMAND;
    static const Uint8Vec BATTERY_INFO_COMMAND;
    static const Uint8Vec ENABLE_IMAGE_COMMAND;
    static const Uint8Vec DISABLE_IMAGE_COMMAND;
    static const Uint8Vec ENABLE_LIGHT_WHITE_COMMAND;
    static const Uint8Vec ENABLE_LIGHT_ULTRA_VIOLET_COMMAND;
    static const Uint8Vec LIGHT_STATUS_COMMAND;
    
    struct DataSlot{
        vector<std::pair<int, Uint8Vec>> data;
        Uint8 size = 255;
    };
    
    Uint8Vec saved_image;
    std::thread data_thread;
    std::mutex data_mutex;
    
    std::thread heartbeat_thread;
    std::mutex heartbeat_mutex;
    std::mutex ctrl_cmd_mutex;
    std::queue<float> battery_percents;
    
    SockAddr make_sock_addr(std::string ip, int port);
    Uint8Vec send_command(Uint8Vec command, int response_size);
    Uint8Vec send_command_heartbeat(Uint8Vec command, int response_size);
    
    void bind_interface();
    void set_connect_state(bool state);
    void close_socket();
    
    void isolate_update_image();
    void isolate_update_heartbeat();
    
    bool verify_ctrl_response(const Uint8Vec& response, Uint8 command_bit, int num_bytes, int response_size);
    bool verify_data_response(const Uint8Vec& response, int num_bytes);
    bool verify_jpeg(const Uint8Vec& bytes);
    
    float get_battery_percent_heartbeat();
    
public:
    Uint8Vec image_ms;
    int saved_image_seq_id = -1;
    std::shared_ptr<high_resolution_clock::time_point> saved_timestamp;
    DeviceUltraTTwo(const std::string &device_ip);
    ~DeviceUltraTTwo();
    MAKE_GETTER(saved_image)
    MAKE_GETTER(saved_image_seq_id)
    MAKE_GETTER(image_ms)
    bool is_image_valid();
    void start_data_stream();         //开启图像传输
    void close_data_stream();         //关闭图像传输
    void enable_light_white();          //开启白光灯
    void enable_light_ultra_violet();   //开启紫外灯
    void close_heartbeat();
    std::string get_mac_address();
    UltraModel get_device_info();
    LightStatus get_light_status();
    
    void save_image(std::string path);  //保存最新帧的图像
    auto retrieve_image();      //获取最新的图像帧
    auto retrieve_battery();
    auto retrieve_is_connect();
    UltraModel retrieve_device_info();
};

const Uint8Vec DeviceUltraTTwo::MAC_ADDRESS_COMMAND = {0x66, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99};
const Uint8Vec DeviceUltraTTwo::DEVICE_INFO_COMMAND = {0x66, 0x18, 0x01, 0x00, 0x00, 0x00, 0x19, 0x99};
const Uint8Vec DeviceUltraTTwo::BATTERY_INFO_COMMAND = {0x66, 0x21, 0x01, 0x00, 0x00, 0x00, 0x22, 0x99};
const Uint8Vec DeviceUltraTTwo::ENABLE_IMAGE_COMMAND = {0x23, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99};
const Uint8Vec DeviceUltraTTwo::DISABLE_IMAGE_COMMAND = {0x23, 0x37, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99};
const Uint8Vec DeviceUltraTTwo::ENABLE_LIGHT_WHITE_COMMAND = {0x66, 0x50, 0x01, 0x00, 0x00, 0x00, 0x51, 0x99};
const Uint8Vec DeviceUltraTTwo::ENABLE_LIGHT_ULTRA_VIOLET_COMMAND = {0x66, 0x55, 0x01, 0x00, 0x00, 0x00, 0x56, 0x99};
const Uint8Vec DeviceUltraTTwo::LIGHT_STATUS_COMMAND = {0x66, 0x58, 0x01, 0x00, 0x00, 0x00, 0x59, 0x99};

DeviceUltraTTwo::DeviceUltraTTwo(const std::string &device_ip) : device_ip(device_ip) {
    //std::this_thread::sleep_for(std::chrono::seconds(3));
    sockfd_ctrl = socket(AF_INET, SOCK_DGRAM, 0);
    sockfd_data = socket(AF_INET, SOCK_DGRAM, 0);
    sockfd_heartbeat = socket(AF_INET, SOCK_DGRAM, 0);
    
    std::string err = strerror(errno);
    
    address_ctrl = make_sock_addr(device_ip, 6090);
    address_data = make_sock_addr(device_ip, 6080);
    
    timeval rcv_timeout = {0, 300000}; // 300ms
    timeval send_timeout = {0, 100000}; // 100ms
    setsockopt(sockfd_ctrl, SOL_SOCKET, SO_SNDTIMEO, (char*)&send_timeout, sizeof(send_timeout));
    setsockopt(sockfd_ctrl, SOL_SOCKET, SO_RCVTIMEO, (char*)&rcv_timeout, sizeof(rcv_timeout));
    setsockopt(sockfd_data, SOL_SOCKET, SO_SNDTIMEO, (char*)&send_timeout, sizeof(send_timeout));
    setsockopt(sockfd_data, SOL_SOCKET, SO_RCVTIMEO, (char*)&rcv_timeout, sizeof(rcv_timeout));
    setsockopt(sockfd_heartbeat, SOL_SOCKET, SO_SNDTIMEO, (char*)&send_timeout, sizeof(send_timeout));
    setsockopt(sockfd_heartbeat, SOL_SOCKET, SO_RCVTIMEO, (char*)&rcv_timeout, sizeof(rcv_timeout));
    
    this->bind_interface();
    
    this->is_heartbeat.store(true);
    heartbeat_thread = std::thread(&DeviceUltraTTwo::isolate_update_heartbeat, this);
}

DeviceUltraTTwo::~DeviceUltraTTwo() {
    this->close_data_stream();
    this->close_heartbeat();
    this->close_socket();
}

SockAddr DeviceUltraTTwo::make_sock_addr(std::string ip, int port) {
    SockAddr sockAddr;
    memset(&sockAddr, 0, sizeof(sockAddr));
    sockAddr.sin_family = AF_INET;
    sockAddr.sin_port = htons(port);
    sockAddr.sin_addr.s_addr = inet_addr(ip.c_str());
    return sockAddr;
}

void DeviceUltraTTwo::bind_interface() {
    auto address = SocketTools::get_interface_address("192.168.97");
    if(address == nullptr) return;
    bind(sockfd_ctrl, (sockaddr*)address, sizeof(address));
    bind(sockfd_data, (sockaddr*)address, sizeof(address));
    bind(sockfd_heartbeat, (sockaddr*)address, sizeof(address));
}

void DeviceUltraTTwo::set_connect_state(bool state) {
    if (this->is_connect.load() == state) return;
    this->is_connect.store(state);
}

bool DeviceUltraTTwo::is_image_valid() {
    if (!this->saved_timestamp)  return false;
    auto current = high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(current - *this->saved_timestamp.get());
    return duration.count() <= 5;
}

bool DeviceUltraTTwo::verify_ctrl_response(const Uint8Vec& response, Uint8 command_bit, int num_bytes, int response_size) {
    ASSERT_EQUAL(num_bytes, response_size)
    ASSERT_EQUAL(response[0], 0x66)
    ASSERT_EQUAL(response[1], command_bit)
    Uint8 checksum = std::accumulate(response.begin() + 1, response.begin() + num_bytes - 2, 0);
    ASSERT_EQUAL(response[num_bytes - 2], checksum)
    ASSERT_EQUAL(response[num_bytes - 1], 0x99)
    return true;
}

bool DeviceUltraTTwo::verify_data_response(const Uint8Vec& response, int num_bytes) {
    bool is_tail = response[1];
    ASSERT_EQUAL(response[2], 0x2e)
    if (!is_tail) {
        ASSERT_EQUAL(num_bytes, 1472)
    } else {
        ASSERT_NO_LESS(num_bytes, 9)
    }
    Uint8 checksum = response[8];
    for(int i = 1; i < ((num_bytes - 8) / 64); ++i) {
        checksum ^= response[8 + i * 64];
    }
    ASSERT_EQUAL(response[7], checksum)
    return true;
}

bool DeviceUltraTTwo::verify_jpeg(const Uint8Vec& bytes) {
    int bytes_size = bytes.size();
    ASSERT_EQUAL(bytes[0], 0xFF)
    ASSERT_EQUAL(bytes[1], 0xD8)
    
    bool has_sof = false;
    for (int i = 2, segment_size; i < bytes_size - 1; i += segment_size + 2) {
        ASSERT_EQUAL(bytes[i], 0xFF)
        if (bytes[i + 1] == 0xC0)  has_sof = true;
        if (bytes[i + 1] == 0xDA)  break;   //数据段
        if (i + 3 >= bytes_size)  return false;
        segment_size = (bytes[i + 2] << 8) | bytes[i + 3];
    }
    ASSERT_EQUAL(has_sof, true)
    ASSERT_EQUAL(bytes[bytes_size - 1], 0xD9)
    ASSERT_EQUAL(bytes[bytes_size - 2], 0xFF)
    return true;
}

Uint8Vec DeviceUltraTTwo::send_command(Uint8Vec command, int response_size) {
    std::unique_lock<std::mutex> lck(ctrl_cmd_mutex);
    Uint8Vec packet(1024, 0x0);
    for (int i = 0; i < 3; ++i) {
        sendto(sockfd_ctrl, command.data(), command.size(),0,(sockaddr*)&address_ctrl,sizeof(address_ctrl));
        int num_bytes = recv(sockfd_ctrl, packet.data(), packet.size(), 0);
        if (verify_ctrl_response(packet, command[1] + 0x1, num_bytes, response_size)) {
            set_connect_state(true);
            lck.unlock();
            return packet;
        }
    }
    if (!this->is_image_valid()) {
        set_connect_state(false);
    }
    lck.unlock();
    return {0, 0, 0, 0, 0, 0, 0, 0};
}

// !!!Caller must lock heartbeat mutex!!!
Uint8Vec DeviceUltraTTwo::send_command_heartbeat(Uint8Vec command, int response_size) {
    Uint8Vec packet(1024, 0x0);
    for (int i = 0; i < 3; ++i) {
        sendto(sockfd_heartbeat, command.data(), command.size(),0,(sockaddr*)&address_ctrl,sizeof(address_ctrl));
        int num_bytes = recv(sockfd_heartbeat, packet.data(), packet.size(), 0);
        if(num_bytes < 3) continue;
        if (verify_ctrl_response(packet, command[1] + 0x1, num_bytes, response_size)) {
            set_connect_state(true);
            return packet;
        }
    }
    if (!this->is_image_valid()) {
        set_connect_state(false);
    }
    return {0, 0, 0, 0, 0, 0, 0, 0};
}

void DeviceUltraTTwo::close_socket() {
    ::close(sockfd_ctrl);
    ::close(sockfd_data);
    ::close(sockfd_heartbeat);
}

void DeviceUltraTTwo::isolate_update_image() {
    vector<DataSlot> buffer(256);
    this->last_packet_timestamp = high_resolution_clock::now();
    while(this->is_transit.load()) {
        if(
            std::chrono::duration_cast<std::chrono::milliseconds>(
                high_resolution_clock::now() - this->last_packet_timestamp
            ).count() > 800
        ){
            // 800ms no packet, resend start command
            Uint8Vec command = ENABLE_IMAGE_COMMAND;
            sendto(this->sockfd_data, command.data(), command.size(),0,(sockaddr*)&address_data,sizeof(address_data));
        }
        Uint8Vec packet(2048, 0x0);
        int num_bytes = recv(sockfd_data, packet.data(), packet.size(), 0);
        if (num_bytes < 5) continue;
        if (!verify_data_response(packet, num_bytes)) continue;
        this->last_packet_timestamp = high_resolution_clock::now();
        set_connect_state(true);
        
        int frame = packet[0];
        int index = packet[3];
        
        DataSlot &slot = buffer[frame];
        slot.size = packet[4];
        slot.data.push_back(std::make_pair(index, vector(packet.begin() + 8, packet.begin() + num_bytes)));
        if (slot.data.size() == slot.size) {
            Uint8Vec bytes_total;
            std::sort(slot.data.begin(), slot.data.end(), [](std::pair<int, Uint8Vec> x, std::pair<int, Uint8Vec> y) {
                return x.first < y.first;
            });
            for(int i = 0; i < slot.size; ++i) {
                Uint8Vec &bytes = slot.data[i].second;
                std::copy(bytes.begin(), bytes.end(), std::back_inserter(bytes_total));
            }
            if(this->verify_jpeg(bytes_total)) {
                std::lock_guard<std::mutex> lock(data_mutex);
                this->saved_image = bytes_total;
                this->saved_image_seq_id = frame;
                
                if (this->saved_timestamp){
                    auto current = high_resolution_clock::now();
                    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current - *this->saved_timestamp.get());
                    this->image_ms.push_back(static_cast<uint8_t>(duration.count()));
                }
                this->saved_timestamp = std::make_shared<high_resolution_clock::time_point>(high_resolution_clock::now());
            }
            for(int i = 0, j = frame + 256; i < 128; ++i, --j) { //clear old buffer
                buffer[j % 256].data.clear();
                buffer[j % 256].size = 255;
            }
        }
        buffer[(frame + 128) % 256].data.clear();
        buffer[(frame + 128) % 256].size = 255;
    }
}

void DeviceUltraTTwo::isolate_update_heartbeat() {
    this->device_mac = "";
    while(this->is_heartbeat.load()) {
        std::unique_lock<std::mutex> lck(heartbeat_mutex);
        float battery = get_battery_percent_heartbeat();
        this->battery_percents.push(battery);
        if (this->battery_percents.size() > 10) {
            this->battery_percents.pop();
        }
        if (this->device_model == nullptr) {
            UltraModel model = get_device_info();
            if (this->is_connect.load() && model.manufacturer!=0) this->device_model = std::make_shared<UltraModel>(model);
        }
        if ((this->device_mac).length() < 12) {
            Uint8Vec response = send_command(MAC_ADDRESS_COMMAND, 10);
            Uint8Vec mac_hex = Uint8Vec(response.begin() + 2, response.begin() + 8);
            std::string mac_address = common_utils::uint8vec_to_hex_string(mac_hex);
            if(mac_address != "000000000000") this->device_mac = mac_address;
        }
        lck.unlock();
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }
}

void DeviceUltraTTwo::start_data_stream() {
    if(this->is_transit.load()) return;
    Uint8Vec command = ENABLE_IMAGE_COMMAND;
    this->is_transit.store(true);
    for (int i = 0; i < 3; ++i) {
        sendto(this->sockfd_data, command.data(), command.size(),0,(sockaddr*)&address_data,sizeof(address_data));
    }
    data_thread = std::thread(&DeviceUltraTTwo::isolate_update_image, this);
}

void DeviceUltraTTwo::close_data_stream() {
    if(!this->is_transit.load()) return;
    Uint8Vec command = DISABLE_IMAGE_COMMAND;
    this->is_transit.store(false);
    if (data_thread.joinable()) {
        data_thread.join();
    }
    for (int i = 0; i < 3; ++i) {
        sendto(this->sockfd_data, command.data(), command.size(),0,(sockaddr*)&address_data,sizeof(address_data));
    }
    (this->saved_timestamp).reset(); //delete saved_timestamp
}

void DeviceUltraTTwo::enable_light_white() {
    if (this->device_model != nullptr && this->device_model->hardware_version < 0x80) return;
    Uint8Vec command = ENABLE_LIGHT_WHITE_COMMAND;
    for (int i = 0; i < 3; ++i) {
        sendto(sockfd_ctrl, command.data(), command.size(),0,(sockaddr*)&address_ctrl,sizeof(address_ctrl));
    }
}

void DeviceUltraTTwo::enable_light_ultra_violet() {
    if (this->device_model != nullptr && this->device_model->hardware_version < 0x80) return;
    Uint8Vec command = ENABLE_LIGHT_ULTRA_VIOLET_COMMAND;
    for (int i = 0; i < 3; ++i) {
        sendto(sockfd_ctrl, command.data(), command.size(),0,(sockaddr*)&address_ctrl,sizeof(address_ctrl));
    }
}

void DeviceUltraTTwo::close_heartbeat() {
    this->is_heartbeat.store(false);
    if (heartbeat_thread.joinable()) {
        heartbeat_thread.join();
    }
}

void DeviceUltraTTwo::save_image(std::string path) {
    std::lock_guard<std::mutex> lock(data_mutex);
    if (this->saved_image_seq_id < 0) return;
    Mat image = cv::imdecode(this->saved_image, cv::IMREAD_COLOR);
    if (!image.empty()) {
        cv::flip(image, image, 1);
        cv::imwrite(path, image);
    }
}


auto DeviceUltraTTwo::retrieve_image() {
    std::lock_guard<std::mutex> lock(data_mutex);
    return this->saved_image;
}


auto DeviceUltraTTwo::retrieve_battery() {
    std::unique_lock<std::mutex> lck(heartbeat_mutex);
    auto numbers = this->battery_percents;
    lck.unlock();
    
    float sum = 0, total = numbers.size();
    while(!numbers.empty()) {
        sum += numbers.front();
        numbers.pop();
    }
    return sum / total;
}

auto DeviceUltraTTwo::retrieve_is_connect() {
    return this->is_connect.load();
}

UltraModel DeviceUltraTTwo::retrieve_device_info() {
    std::unique_lock<std::mutex> lck(heartbeat_mutex);
    if (this->device_model == nullptr)  return {-1, -1, -1};
    return *this->device_model;
}



UltraModel DeviceUltraTTwo::get_device_info() {
    Uint8Vec response = send_command(DEVICE_INFO_COMMAND, 8);
    return {
        .manufacturer = response[2],
        .hardware_version = response[3],
        .software_version = (response[4] << 8) + response[5]
    };
}

std::string DeviceUltraTTwo::get_mac_address() {
    if((this->device_mac).length()<12) return "000000000000";
    return this->device_mac;
}

float DeviceUltraTTwo::get_battery_percent_heartbeat() {
    Uint8Vec response = send_command_heartbeat(BATTERY_INFO_COMMAND, 8);
    int battery = (response[2] & ((1 << 7) - 1));
    int charging = (response[2] >> 7);
    return battery;
}

LightStatus DeviceUltraTTwo::get_light_status() {
    if (this->device_model != nullptr && this->device_model->hardware_version < 0x80) return LightStatus::white;
    Uint8Vec response = send_command(LIGHT_STATUS_COMMAND, 8);
    int status = response[2];
    if(status)  return LightStatus::white;
    else        return LightStatus::ultra_violet;
}
