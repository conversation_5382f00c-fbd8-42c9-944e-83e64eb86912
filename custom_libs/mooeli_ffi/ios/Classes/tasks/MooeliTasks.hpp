//
// Created by syq on 2023/4/24.
//
#pragma once
#include "../utils/defs.hpp"


class MooeliTasks {
private:
    MooeliTasks() {}

public:
    static auto processAIFaceImage(Mat oldImg, int isYMirror);
    static auto processLightDetection(Mat img);
    static auto processToothLightDetection(Mat img, int centerX, int centerY);
};

auto MooeliTasks::processAIFaceImage(Mat oldImg, int isYMirror) {
    Mat reslutImgMat = OpencvTools::gaussianBlur(oldImg);

    //图片是否需要Y轴镜像反转
    if (isYMirror == 1) {
        reslutImgMat = OpencvTools::mirror(reslutImgMat, MirrorType::horizontal);
    }

    //转二进制编码
    vector<uint8_t> retv;
    imencode(".jpg", reslutImgMat, retv);

    int matTotal = (int) retv.size();
    auto uDatas = new uint8_t[matTotal];
    memcpy(uDatas, retv.data(), matTotal);

    common_utils::clearVector(retv);

    Struct2AIFace struct2AIFace;
    struct2AIFace.buffer = uDatas;
    struct2AIFace.bufferLen = matTotal;
    return struct2AIFace;
}

auto MooeliTasks::processLightDetection(Mat img) {
    Mat mask = Mat::ones(img.size(), CV_8UC1);
    Mat gray;
    cvtColor(img, gray, cv::COLOR_BGR2GRAY);
    Mat binary;
    threshold(gray, binary, 127, 255, cv::THRESH_BINARY);

    vector<vector<Point>> contours = {};
    findContours(binary, contours, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);
    float maxLenValue = 0;
    float maxLenIndex = 0;
    for (int i = 0; i < contours.size(); i++) {
        if (contours[i].size() >= maxLenValue) {
            maxLenValue = contours[i].size();
            maxLenIndex = i;
        }
    }

    Struct2CheckLightResult struct2CheckLightResult;
    struct2CheckLightResult.firstValue = 0;
    struct2CheckLightResult.secondValue = 0;

    if (contours.size() > 0) {
        //第一个值
        fillPoly(mask, contours[maxLenIndex], Scalar(0, 0, 0));

        Scalar resultScalar = mean(img, mask == 1);
        float result = (resultScalar[0] + resultScalar[1] + resultScalar[2]) / 3;
//        cout<<resultScalar<<endl;
//        cout<<result<<endl;
        common_utils::clearVector(contours);
        struct2CheckLightResult.firstValue = (int) result;
        //第二个值(方案不稳定，受到开口器位置影响较大)
//        Mat t1Mat = img(Range::all(),Range(img.cols - img.cols/10,img.cols));
//        Mat t1MatSort =  Mat::zeros(t1Mat.rows, t1Mat.cols, t1Mat.type());
//        //col 倒序
//        for( int nrow = 0; nrow < t1Mat.rows; nrow++)
//        {
//            for(int ncol = 0; ncol < t1Mat.cols; ncol++)
//            {
//                t1MatSort.at<Vec3b>(nrow,ncol) = t1Mat.at<Vec3b>(nrow,t1Mat.cols - 1 - ncol);
//            }
//        }
//        Mat t2Mat = img(Range::all(),Range(0,img.cols/10));
//        //防止减出来的数值是负数直接变成0,转换类型为 CV_32FC3
//        Mat t1ConverMat;
//        Mat t2ConverMat;
//        t1MatSort.convertTo(t1ConverMat, CV_32FC3);
//        t2Mat.convertTo(t2ConverMat, CV_32FC3);
//        Mat t3Mat = t1ConverMat - t2ConverMat;
//        Scalar result2Scalar = mean(t3Mat.mul(t3Mat));
//        float result2 = (result2Scalar[0]+result2Scalar[1]+result2Scalar[2])/3;
//        struct2CheckLightResult.secondValue = (int)result2;
    }

    return struct2CheckLightResult;
}

auto MooeliTasks::processToothLightDetection(Mat img, int centerX, int centerY) {
//    Mat mask = Mat::zeros(img.size(), CV_8UC1);
//    Mat gray;
//    cvtColor(img, gray, COLOR_BGR2GRAY);
//    Mat binary;
//    threshold(gray, binary, 127, 255, THRESH_BINARY);
//
//    vector<vector<Point>> contours = {};
//    findContours(binary, contours,RETR_LIST, CHAIN_APPROX_SIMPLE);
//    float maxLenValue = 0;
//    float maxLenIndex = 0;
//    for (int i = 0;i<contours.size();i++ )
//    {
//        if(contours[i].size() >= maxLenValue){
//            maxLenValue = contours[i].size();
//            maxLenIndex = i;
//        }
//    }
//
//    if(contours.size()>0){
//        fillPoly(mask, contours[maxLenIndex], Scalar(1,1,1));
//
//        //之前的方案
////        Scalar resultScalar = mean(img,mask==1);
////        float result = (resultScalar[0]+resultScalar[1]+resultScalar[2])/3;
//        //新方案
//        float result = 0;
//        for( int nrow = 0; nrow < mask.rows; nrow++)
//        {
//           for(int ncol = 0; ncol < mask.cols; ncol++)
//           {
//               if(mask.at<Vec3b>(nrow,ncol)[0] == 1 || mask.at<Vec3b>(nrow,ncol)[1] == 1 || mask.at<Vec3b>(nrow,ncol)[2] == 1){
//                   if(img.at<Vec3b>(nrow,ncol)[0] >= 200 || img.at<Vec3b>(nrow,ncol)[1] >= 200 || img.at<Vec3b>(nrow,ncol)[2] >= 200){
//                       result++;
//                   }
//               }
//           }
//        }
//
//        clearVector(contours);
//        return (int)result;
//    }else{
//        return 0;
//    }

    //最新方案
    int rectWH = img.rows / 3;
    int startRowIndex = centerY - rectWH / 2 >= 0 ? centerY - rectWH / 2 : 0;
    int endRowIndex = centerY + rectWH / 2;
    int startColIndex = centerX - rectWH / 2 >= 0 ? centerX - rectWH / 2 : 0;
    int endColIndex = centerX + rectWH / 2;

//    Mat rectMat = img(Range(startRowIndex,endRowIndex),Range(startColIndex,endColIndex));
//    Scalar scalarMean =  mean(rectMat);
//    return (scalarMean[0]+scalarMean[1]+scalarMean[2])/3;

    float allSumValue = 0;
    int allSumCounter = 0;
    for (int nrow = startRowIndex; nrow < endRowIndex; nrow++) {
        for (int ncol = startColIndex; ncol < endColIndex; ncol++) {
            allSumCounter++;
            Vec3i channels = img.at<Vec3b>(nrow, ncol)[1];
            if (channels[0] >= channels[1] && channels[0] >= channels[2]) {
                allSumValue += channels[0];
            } else if (channels[1] >= channels[0] && channels[1] >= channels[2]) {
                allSumValue += channels[1];
            } else if (channels[2] >= channels[0] && channels[2] >= channels[1]) {
                allSumValue += channels[2];
            }
        }
    }
    return (int) (allSumValue / allSumCounter);
}