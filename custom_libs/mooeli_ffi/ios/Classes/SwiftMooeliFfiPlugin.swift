import Flutter
import UIKit

public class SwiftMooeliFfiPlugin: NSObject, FlutterPlugin {
    
    //应用启动则调用ptrace防护：
    @objc public static func ptraceDebugger() {
        // bind ptrace()
        let pointerToPtrace = UnsafeMutableRawPointer(bitPattern: -2)
        let ptracePtr = dlsym(pointerToPtrace, "ptrace")
        typealias PtraceType = @convention(c) (CInt, pid_t, CInt, CInt) -> CInt
        let ptrace = unsafeBitCast(ptracePtr, to: PtraceType.self)

        // PT_DENY_ATTACH == 31
        let ptraceRet = ptrace(31, 0, 0, 0)

        if ptraceRet != 0 {
            print("Error occured when calling ptrace(). Denying debugger may not be reliable")
        }
    }
    public static func register(with registrar: FlutterPluginRegistrar) {
    }
    
//  public static func register(with registrar: FlutterPluginRegistrar) {
//    let channel = FlutterMethodChannel(name: "mooeli_ffi", binaryMessenger: registrar.messenger())
//    let instance = SwiftMooeliFfiPlugin()
//    registrar.addMethodCallDelegate(instance, channel: channel)
//  }
//
//  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
//    result("iOS " + UIDevice.current.systemVersion)
//  }
}
