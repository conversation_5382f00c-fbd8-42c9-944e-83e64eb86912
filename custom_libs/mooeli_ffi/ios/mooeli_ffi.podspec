#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint mooeli_ffi.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'mooeli_ffi'
  s.version          = '0.0.1'
  s.summary          = 'ffi'
  s.description      = <<-DESC
FFI For Mooeli: include c/c++ librarys
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Himi' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/*'
  s.dependency 'Flutter'
  s.platform = :ios, '13.0'

  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386 arm64',
      'HEADER_SEARCH_PATHS' => '$(PODS_TARGET_SRCROOT)/Classes'
  }
  
  s.swift_version = '5.0'
  s.preserve_paths = 'Frameworks/opencv2.framework',  'Frameworks/TensorFlowLiteC.framework',  'Frameworks/TensorFlowLiteCCoreML.framework'
  s.xcconfig = {
    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++17',
    'CLANG_CXX_LIBRARY' => 'libc++',
    'OTHER_LDFLAGS' => '-framework opencv2 -framework TensorFlowLiteC -framework TensorFlowLiteCCoreML -all_load'
  }
  s.vendored_frameworks = 'Frameworks/opencv2.framework', 'Frameworks/TensorFlowLiteC.framework', 'Frameworks/TensorFlowLiteCCoreML.framework'
  s.frameworks = 'AVFoundation', 'CoreML'
  s.library = 'c++'

end
