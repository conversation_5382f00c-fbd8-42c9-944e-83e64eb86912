// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
import 'dart:ffi' as ffi;

/// auto create by ffigen
class D2c {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  D2c(ffi.DynamicLibrary dynamicLibrary) : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  D2c.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  Struct2AIFace getAiFaceImgByPath(
    ffi.Pointer<ffi.Char> imgPath,
    int isYMirror,
  ) {
    return _getAiFaceImgByPath(
      imgPath,
      isYMirror,
    );
  }

  late final _getAiFaceImgByPathPtr = _lookup<
      ffi.NativeFunction<
          Struct2AIFace Function(
              ffi.Pointer<ffi.Char>, ffi.Int)>>('getAiFaceImgByPath');
  late final _getAiFaceImgByPath = _getAiFaceImgByPathPtr
      .asFunction<Struct2AIFace Function(ffi.Pointer<ffi.Char>, int)>();

  Struct2AIFace getAiFaceImgByDatas(
    ffi.Pointer<ffi.Uint8> imgDatas,
    int width,
    int height,
    int isYMirror,
  ) {
    return _getAiFaceImgByDatas(
      imgDatas,
      width,
      height,
      isYMirror,
    );
  }

  late final _getAiFaceImgByDatasPtr = _lookup<
      ffi.NativeFunction<
          Struct2AIFace Function(ffi.Pointer<ffi.Uint8>, ffi.Int, ffi.Int,
              ffi.Int)>>('getAiFaceImgByDatas');
  late final _getAiFaceImgByDatas = _getAiFaceImgByDatasPtr.asFunction<
      Struct2AIFace Function(ffi.Pointer<ffi.Uint8>, int, int, int)>();

  void createTFLite(
    ffi.Pointer<ffi.Uint8> modalData,
    int modalDataLen,
    int isEncrypt,
    int modelType,
  ) {
    return _createTFLite(
      modalData,
      modalDataLen,
      isEncrypt,
      modelType,
    );
  }

  late final _createTFLitePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Uint8>, ffi.Int, ffi.Int,
              ffi.Int32)>>('createTFLite');
  late final _createTFLite = _createTFLitePtr
      .asFunction<void Function(ffi.Pointer<ffi.Uint8>, int, int, int)>();

  void deleteTFLite() {
    return _deleteTFLite();
  }

  late final _deleteTFLitePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('deleteTFLite');
  late final _deleteTFLite = _deleteTFLitePtr.asFunction<void Function()>();

  Struct2PredictResult predictByPaths(
    ffi.Pointer<ffi.Char> imgPath,
    ffi.Pointer<ffi.Char> imgPath2,
    ffi.Pointer<ffi.Char> imgPath3,
  ) {
    return _predictByPaths(
      imgPath,
      imgPath2,
      imgPath3,
    );
  }

  late final _predictByPathsPtr = _lookup<
      ffi.NativeFunction<
          Struct2PredictResult Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>('predictByPaths');
  late final _predictByPaths = _predictByPathsPtr.asFunction<
      Struct2PredictResult Function(ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  Struct2PredictResult predictByData(
    ffi.Pointer<ffi.Uint8> imgData,
    int width,
    int height,
    int type,
    ffi.Pointer<ffi.Uint8> plane1,
    ffi.Pointer<ffi.Uint8> plane2,
    int bytesPerRow,
    int bytesPerPixel,
    int yRowStride,
  ) {
    return _predictByData(
      imgData,
      width,
      height,
      type,
      plane1,
      plane2,
      bytesPerRow,
      bytesPerPixel,
      yRowStride,
    );
  }

  late final _predictByDataPtr = _lookup<
      ffi.NativeFunction<
          Struct2PredictResult Function(
              ffi.Pointer<ffi.Uint8>,
              ffi.Int,
              ffi.Int,
              ffi.Int32,
              ffi.Pointer<ffi.Uint8>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Int,
              ffi.Int,
              ffi.Int)>>('predictByData');
  late final _predictByData = _predictByDataPtr.asFunction<
      Struct2PredictResult Function(ffi.Pointer<ffi.Uint8>, int, int, int,
          ffi.Pointer<ffi.Uint8>, ffi.Pointer<ffi.Uint8>, int, int, int)>();

  Struct2ClarityResult clarityIncrease(
    ffi.Pointer<ffi.Char> imgPath,
  ) {
    return _clarityIncrease(
      imgPath,
    );
  }

  late final _clarityIncreasePtr = _lookup<
      ffi.NativeFunction<
          Struct2ClarityResult Function(
              ffi.Pointer<ffi.Char>)>>('clarityIncrease');
  late final _clarityIncrease = _clarityIncreasePtr
      .asFunction<Struct2ClarityResult Function(ffi.Pointer<ffi.Char>)>();

  void compressImg(
    ffi.Pointer<ffi.Char> imgPath,
    ffi.Pointer<ffi.Char> imgOutPath,
    int tWidth,
    int tHeight,
    int quality,
  ) {
    return _compressImg(
      imgPath,
      imgOutPath,
      tWidth,
      tHeight,
      quality,
    );
  }

  late final _compressImgPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Int, ffi.Int, ffi.Int)>>('compressImg');
  late final _compressImg = _compressImgPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>, int, int, int)>();

  Struct2CheckLightResult checkImgHaveLight(
    ffi.Pointer<ffi.Char> imgPath,
  ) {
    return _checkImgHaveLight(
      imgPath,
    );
  }

  late final _checkImgHaveLightPtr = _lookup<
      ffi.NativeFunction<
          Struct2CheckLightResult Function(
              ffi.Pointer<ffi.Char>)>>('checkImgHaveLight');
  late final _checkImgHaveLight = _checkImgHaveLightPtr
      .asFunction<Struct2CheckLightResult Function(ffi.Pointer<ffi.Char>)>();

  Struct2CheckLightResult checkImgHaveLightByData(
    ffi.Pointer<ffi.Uint8> imgDatas,
    int width,
    int height,
  ) {
    return _checkImgHaveLightByData(
      imgDatas,
      width,
      height,
    );
  }

  late final _checkImgHaveLightByDataPtr = _lookup<
      ffi.NativeFunction<
          Struct2CheckLightResult Function(ffi.Pointer<ffi.Uint8>, ffi.Int,
              ffi.Int)>>('checkImgHaveLightByData');
  late final _checkImgHaveLightByData = _checkImgHaveLightByDataPtr.asFunction<
      Struct2CheckLightResult Function(ffi.Pointer<ffi.Uint8>, int, int)>();

  int checkToothHaveLight(
    ffi.Pointer<ffi.Char> imgPath,
    int centerX,
    int centerY,
  ) {
    return _checkToothHaveLight(
      imgPath,
      centerX,
      centerY,
    );
  }

  late final _checkToothHaveLightPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Char>, ffi.Int, ffi.Int)>>('checkToothHaveLight');
  late final _checkToothHaveLight = _checkToothHaveLightPtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>, int, int)>();

  int checkToothHaveLightByData(
    ffi.Pointer<ffi.Uint8> imgDatas,
    int width,
    int height,
    int centerX,
    int centerY,
  ) {
    return _checkToothHaveLightByData(
      imgDatas,
      width,
      height,
      centerX,
      centerY,
    );
  }

  late final _checkToothHaveLightByDataPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Uint8>, ffi.Int, ffi.Int, ffi.Int,
              ffi.Int)>>('checkToothHaveLightByData');
  late final _checkToothHaveLightByData = _checkToothHaveLightByDataPtr
      .asFunction<int Function(ffi.Pointer<ffi.Uint8>, int, int, int, int)>();

  void colorTransform(
    ffi.Pointer<ffi.Char> imgPath,
  ) {
    return _colorTransform(
      imgPath,
    );
  }

  late final _colorTransformPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'colorTransform');
  late final _colorTransform =
      _colorTransformPtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  int flipImage(
    ffi.Pointer<ffi.Char> imgPath,
    ffi.Pointer<ffi.Char> outPath,
  ) {
    return _flipImage(
      imgPath,
      outPath,
    );
  }

  late final _flipImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>('flipImage');
  late final _flipImage = _flipImagePtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  int is_device_connect() {
    return _is_device_connect();
  }

  late final _is_device_connectPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function()>>('is_device_connect');
  late final _is_device_connect =
      _is_device_connectPtr.asFunction<int Function()>();

  void connect_device() {
    return _connect_device();
  }

  late final _connect_devicePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('connect_device');
  late final _connect_device = _connect_devicePtr.asFunction<void Function()>();

  void disconnect_device() {
    return _disconnect_device();
  }

  late final _disconnect_devicePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('disconnect_device');
  late final _disconnect_device =
      _disconnect_devicePtr.asFunction<void Function()>();

  void enable_image_transit() {
    return _enable_image_transit();
  }

  late final _enable_image_transitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('enable_image_transit');
  late final _enable_image_transit =
      _enable_image_transitPtr.asFunction<void Function()>();

  void disable_image_transit() {
    return _disable_image_transit();
  }

  late final _disable_image_transitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('disable_image_transit');
  late final _disable_image_transit =
      _disable_image_transitPtr.asFunction<void Function()>();

  void save_image(
    ffi.Pointer<ffi.Char> path,
  ) {
    return _save_image(
      path,
    );
  }

  late final _save_imagePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'save_image');
  late final _save_image =
      _save_imagePtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  Struct2DeviceImage retrieve_image() {
    return _retrieve_image();
  }

  late final _retrieve_imagePtr =
      _lookup<ffi.NativeFunction<Struct2DeviceImage Function()>>(
          'retrieve_image');
  late final _retrieve_image =
      _retrieve_imagePtr.asFunction<Struct2DeviceImage Function()>();

  double get_battery_percent() {
    return _get_battery_percent();
  }

  late final _get_battery_percentPtr =
      _lookup<ffi.NativeFunction<ffi.Float Function()>>('get_battery_percent');
  late final _get_battery_percent =
      _get_battery_percentPtr.asFunction<double Function()>();

  int get_device_type() {
    return _get_device_type();
  }

  late final _get_device_typePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('get_device_type');
  late final _get_device_type =
      _get_device_typePtr.asFunction<int Function()>();

  void enable_light_white() {
    return _enable_light_white();
  }

  late final _enable_light_whitePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('enable_light_white');
  late final _enable_light_white =
      _enable_light_whitePtr.asFunction<void Function()>();

  void enable_light_ultra_violet() {
    return _enable_light_ultra_violet();
  }

  late final _enable_light_ultra_violetPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'enable_light_ultra_violet');
  late final _enable_light_ultra_violet =
      _enable_light_ultra_violetPtr.asFunction<void Function()>();

  int retrieve_light_status() {
    return _retrieve_light_status();
  }

  late final _retrieve_light_statusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'retrieve_light_status');
  late final _retrieve_light_status =
      _retrieve_light_statusPtr.asFunction<int Function()>();

  UltraModel get_device_info() {
    return _get_device_info();
  }

  late final _get_device_infoPtr =
      _lookup<ffi.NativeFunction<UltraModel Function()>>('get_device_info');
  late final _get_device_info =
      _get_device_infoPtr.asFunction<UltraModel Function()>();

  int has_recent_image() {
    return _has_recent_image();
  }

  late final _has_recent_imagePtr =
      _lookup<ffi.NativeFunction<ffi.Int Function()>>('has_recent_image');
  late final _has_recent_image =
      _has_recent_imagePtr.asFunction<int Function()>();

  CString get_mac_address() {
    return _get_mac_address();
  }

  late final _get_mac_addressPtr =
      _lookup<ffi.NativeFunction<CString Function()>>('get_mac_address');
  late final _get_mac_address =
      _get_mac_addressPtr.asFunction<CString Function()>();

  Struct2Line buildNurbsLine(
    ffi.Pointer<ffi.Double> value,
    int length,
    int close,
  ) {
    return _buildNurbsLine(
      value,
      length,
      close,
    );
  }

  late final _buildNurbsLinePtr = _lookup<
      ffi.NativeFunction<
          Struct2Line Function(
              ffi.Pointer<ffi.Double>, ffi.Int, ffi.Int)>>('buildNurbsLine');
  late final _buildNurbsLine = _buildNurbsLinePtr
      .asFunction<Struct2Line Function(ffi.Pointer<ffi.Double>, int, int)>();
}

class Struct2AIFace extends ffi.Struct {
  external ffi.Pointer<ffi.Uint8> buffer;

  @ffi.Int()
  external int bufferLen;
}

abstract class ModelType {
  static const int center_detection = 0;
  static const int quality_inspection = 1;
  static const int clarity_enhance = 2;
}

class Struct2PredictResult extends ffi.Struct {
  external Struct2LastTFLite last1;

  external Struct2LastTFLite last2;

  external Struct2LastTFLite last3;

  external Struct2TFLiteMCO mco;

  @ffi.Int()
  external int runMs;

  @ffi.Int()
  external int totalMs;
}

class Struct2LastTFLite extends ffi.Struct {
  external ffi.Pointer<ffi.Float> rects;

  @ffi.Int()
  external int rectEleCount;

  @ffi.Int()
  external int rectsLength;

  @ffi.Int()
  external int num_classes;

  external Struct2LastTFLiteBlurInfo blurInfo;

  @ffi.Int()
  external int importNetOriImgW;

  @ffi.Int()
  external int importNetOriImgH;
}

class Struct2LastTFLiteBlurInfo extends ffi.Struct {
  @ffi.Int()
  external int isBlur;

  @ffi.Int()
  external int checkLapMax;

  @ffi.Int()
  external int checkLapMin;

  @ffi.Float()
  external double imgLaplacianCount;

  @ffi.Float()
  external double checkCannyCount;

  @ffi.Float()
  external double imgCannyCount;
}

class Struct2TFLiteMCO extends ffi.Struct {
  @ffi.Int()
  external int leftIndex;

  @ffi.Int()
  external int rightIndex;

  @ffi.Int()
  external int upIndex;

  @ffi.Int()
  external int downIndex;

  @ffi.Int()
  external int frontIndex;
}

abstract class ImageType {
  static const int rgb = 0;
  static const int rbga8888 = 1;
  static const int yuv420 = 2;
}

class Struct2ClarityResult extends ffi.Struct {
  @ffi.Int()
  external int runMs;

  @ffi.Int()
  external int totalMs;
}

class Struct2CheckLightResult extends ffi.Struct {
  @ffi.Int()
  external int firstValue;

  @ffi.Int()
  external int secondValue;
}

class Struct2DeviceImage extends ffi.Struct {
  external ffi.Pointer<ffi.Uint8> buffer;

  @ffi.Int()
  external int buffer_size;

  @ffi.Int()
  external int image_seq_id;

  @ffi.Int()
  external int width;

  @ffi.Int()
  external int height;
}

abstract class DeviceType {
  static const int none = 0;
  static const int ultra_t1 = 1;
  static const int ultra_t2 = 2;
}

abstract class LightStatus {
  static const int white = 0;
  static const int ultra_violet = 1;
}

class UltraModel extends ffi.Struct {
  @ffi.Int()
  external int manufacturer;

  @ffi.Int()
  external int hardware_version;

  @ffi.Int()
  external int software_version;
}

class CString extends ffi.Struct {
  external ffi.Pointer<ffi.Char> ptr;

  @ffi.Int()
  external int length;
}

class Struct2Line extends ffi.Struct {
  external ffi.Pointer<ffi.Double> data;

  @ffi.Int()
  external int length;
}
