import 'dart:ffi';
import 'dart:ffi' as ffi;
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'bindings_ffi.dart';

const String dynamicLibName = "libmooeli_ffi.so";
final DynamicLibrary mooeliFfiLib = Platform.isAndroid
    ? DynamicLibrary.open(dynamicLibName) //格式为 lib + cmake定义的库名称 + .so
    : DynamicLibrary.process();

final int Function(int x, int y) testByFfi =
    mooeliFfiLib.lookup<NativeFunction<Int32 Function(Int32, Int32)>>('testByFfi').asFunction();

final D2c d2c = D2c(mooeliFfiLib);
//*** 尽量不要使用结构体指针数组，可以嵌套结构体，但是不要嵌套结构体指针数组，容易异常
D2c getD2cInstance() => d2c;

int ffiFlipImage(String imgPath, String outPath) {
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  Pointer<Char> pointerOut = string2Pointer(outPath);
  return d2c.flipImage(pointerPath, pointerOut);
}

bool ffiIsDeviceConnect() {
  int isConnect = d2c.is_device_connect();
  return isConnect > 0;
}

void ffiConnectDevice() {
  d2c.connect_device();
}

void ffiDisconnectDevice() {
  d2c.disconnect_device();
}

void ffiEnableImageTransit() {
  d2c.enable_image_transit();
}

void ffiDisableImageTransit() {
  d2c.disable_image_transit();
}

void ffiSaveImage(String imgPath) {
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  d2c.save_image(pointerPath);
  ffiFree(pointerPath);
}

int ffiGetDeviceType() {
  return d2c.get_device_type();
}

bool hasImage = false;

bool hasRecentImage() {
  return d2c.has_recent_image() > 0;
}

dynamic ffiRetrieveImage() {
  Struct2DeviceImage result = d2c.retrieve_image();
  hasImage = result.image_seq_id >= 0;
  if (!hasImage) {
    return null;
  }
  Uint8List imgDatas = result.buffer.asTypedList(result.buffer_size);
  //ffiFree(result.buffer);

  return {
    "imgDatas": imgDatas,
    "pointer": result.buffer,
    "image_seq_id": result.image_seq_id,
    "width": result.width,
    "height": result.height
  };
}

Uint8List pointerToUint8List(ffi.Pointer<ffi.Uint8> pointer, int length) {
  // 将指针转换为 Uint8List
  final list = pointer.asTypedList(length);
  return Uint8List.fromList(list);
}

List<double> ffiBuildNurbsLine(List<double> param, bool isClosed) {
  Pointer<Double> pointer = doubleList2Pointer(param);
  Struct2Line result = d2c.buildNurbsLine(pointer, param.length, isClosed ? 1 : 0);
  Pointer<Double> data = result.data;
  List<double> list = List<double>.from(data.asTypedList(result.length));
  ffiFree(data);
  ffiFree(pointer);
  return list;
}

double ffiGetBatteryPercent() {
  return d2c.get_battery_percent();
}

//对图片做色差处理(例如偏黄的图等)
void ffiColorTransform(String imgPath) {
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  d2c.colorTransform(pointerPath);
  ffiFree(pointerPath);
}

//检测是否牙齿有足够光亮(还是不太稳定)
bool ffiCheckToothHaveLight(String imgPath, double centerX, double centerY) {
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  int result = d2c.checkToothHaveLight(pointerPath, centerX.toInt(), centerY.toInt());
  ffiFree(pointerPath);
  if (kDebugMode) {
    print("检测牙齿光亮:$result");
  }
  return result >= 80;
}

bool ffiCheckToothHaveLightByData(dynamic obj, double centerX, double centerY) {
  Pointer<Uint8> pointer = uint8List2Pointer(obj["Uint8List"]);
  int result = d2c.checkToothHaveLightByData(pointer, obj["width"], obj["height"], centerX.toInt(), centerY.toInt());
  ffiFree(pointer);
  if (kDebugMode) {
    print("检测牙齿光亮:$result");
  }
  return result >= 70;
}

//检测是否开口器中有手电筒光
bool ffiCheckImgHaveLightProcess(String imgPath) {
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  Struct2CheckLightResult result = d2c.checkImgHaveLight(pointerPath);
  ffiFree(pointerPath);
  if (kDebugMode) {
    print("检测开口器内部光亮:${result.firstValue} | ${result.secondValue}");
  }
  return result.firstValue >= 95;
  // return result.firstValue >= 100 && result.secondValue<=70;
}

bool ffiCheckImgHaveLightProcessByData(dynamic obj) {
  Pointer<Uint8> pointer = uint8List2Pointer(obj["Uint8List"]);
  Struct2CheckLightResult result = d2c.checkImgHaveLightByData(pointer, obj["width"], obj["height"]);
  ffiFree(pointer);
  if (kDebugMode) {
    print("检测开口器内部光亮:${result.firstValue} | ${result.secondValue}");
  }
  return result.firstValue >= 95;
  // return result.firstValue >= 100 && result.secondValue<=70;
}

bool ffiCheckImgHaveLightSync(String imgPath) {
  return ffiCheckImgHaveLightProcess(imgPath);
}

bool ffiCheckImgHaveLightByDataSync(dynamic obj) {
  return ffiCheckImgHaveLightProcessByData(obj);
}

Future<bool> ffiCheckImgHaveLight(String imgPath) async {
  return ffiCheckImgHaveLightProcess(imgPath);
}

Future<bool> ffiCheckImgHaveLightByData(dynamic obj) async {
  return ffiCheckImgHaveLightProcessByData(obj);
}

//图片压缩
//width 不传 默认以 width 等比算高
//height 不传 默认以 height 等比算宽
void ffiCompressImg(String imgPath, {int width = 0, int height = 0, int quality = 88, String outPath = ""}) {
  if (outPath == "") {
    outPath = imgPath;
  }
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  Pointer<Char> pointerOutPath = string2Pointer(outPath);
  d2c.compressImg(pointerPath, pointerOutPath, width, height, quality);
  ffiFree(pointerPath);
  ffiFree(pointerOutPath);
}

//创建Interpreter
void ffiCreateTFLite(Uint8List modalData, int isEncrypt, int type) {
  Pointer<Uint8> pointer = uint8List2Pointer(modalData);
  d2c.createTFLite(pointer, modalData.length, isEncrypt, type);
  ffiFree(pointer);
}

//删除Interpreter
void ffiDeleteTFLite() {
  d2c.deleteTFLite();
}

dynamic ffiPredictByPaths(List paths, {bool isUseNewModal = false}) {
  Pointer<Char> pointer = string2Pointer(paths[0]);
  Pointer<Char> pointer2 = string2Pointer(paths.length > 1 ? paths[1] : "");
  Pointer<Char> pointer3 = string2Pointer(paths.length > 2 ? paths[2] : "");
  Struct2PredictResult struct2predictResult = d2c.predictByPaths(pointer, pointer2, pointer3);
  ffiFree(pointer);
  ffiFree(pointer2);
  ffiFree(pointer3);
  return ffiPredictResult(struct2predictResult, paths.length);
}

dynamic ffiClarityIncrease(String path) {
  Pointer<Char> pointer = string2Pointer(path);
  Struct2ClarityResult result = d2c.clarityIncrease(pointer);
  return result;
}

// imgDatasType
// 0 = rgb 格式的数据
// 1 = rbga8888 格式的数据
// 2 = yuv420 格式的数据
//以下这种利用 imgLib.Image 来转bytes的release后效率巨慢，这里我们该传入原始数据用直接opencv处理的方式来转换
// dynamic obj = ffiPredictByImgData(HImageUtils.cameraImg2Uint8ListBringWH(msgObj["obj"]));
dynamic ffiPredictByImgData(dynamic obj, {bool isUseNewModal = false}) {
  Pointer<Uint8> pointer = uint8List2Pointer(obj["Uint8List"]);
  Pointer<Uint8> pointerPlane1 = uint8List2Pointer(obj["plane1"] ?? []);
  Pointer<Uint8> pointerPlane2 = uint8List2Pointer(obj["plane2"] ?? []);
  Struct2PredictResult struct2predictResult = d2c.predictByData(
    pointer,
    obj["width"],
    obj["height"],
    obj["imgDatasType"] ?? 0,
    pointerPlane1,
    pointerPlane2,
    obj["bytesPerRow"] ?? 0,
    obj["bytesPerPixel"] ?? 0,
    obj["yRowStride"] ?? 0,
  );

  //c++ 端后处理自动会释放，无需dart再次释放-> ffiFree(pointer);
  //但是不经过后处理的需要用完手动释放

  ffiFree(pointerPlane1);
  ffiFree(pointerPlane2);
  return ffiPredictResult(struct2predictResult, 1);
}

dynamic ffiPredictResult(Struct2PredictResult struct2predictResult, int processNum) {
  List lastObjects = [];
  lastObjects.add(struct2predictResult.last1);
  if (processNum > 1) {
    lastObjects.add(struct2predictResult.last2);
  }
  if (processNum > 2) {
    lastObjects.add(struct2predictResult.last3);
  }

  return {
    //推断时间
    "runMs": struct2predictResult.runMs,
    //总推测时间
    "totalMs": struct2predictResult.totalMs,
    //后处理返回的数据
    "lastObjects": lastObjects,
  };
}

dynamic ffiGetAiFaceImgByPath(String imgPath, {bool isYMirror = false}) {
  Pointer<Char> pointerPath = string2Pointer(imgPath);
  Struct2AIFace result = d2c.getAiFaceImgByPath(pointerPath, isYMirror ? 1 : 0);
  Uint8List imgDatas = result.buffer.asTypedList(result.bufferLen);

  ffiFree(pointerPath);
  return {"imgDatas": imgDatas, "pointer": result.buffer};
}

dynamic ffiGetAiFaceImgByDatas(Uint8List list, int imgWidth, int imgHeight, {bool isYMirror = false}) {
  Pointer<Uint8> pointerBytes = uint8List2Pointer(list);
  Struct2AIFace result = d2c.getAiFaceImgByDatas(pointerBytes, imgWidth, imgHeight, isYMirror ? 1 : 0);
  Uint8List imgDatas = result.buffer.asTypedList(result.bufferLen);

  ffiFree(pointerBytes);
  return {"imgDatas": imgDatas, "pointer": result.buffer};
}

void ffiEnableUltraWhiteLight() {
  d2c.enable_light_white();
}

void ffiEnableUltraVioletLight() {
  d2c.enable_light_ultra_violet();
}

UltraModel ffiGetUltraDeviceInfo() {
  return d2c.get_device_info();
}

//保证使用完成后才能释放
void ffiFree(Pointer pointer) {
  malloc.free(pointer);
}

/// Pointer<Float> -> Float32List
Float32List pointerFloat2FloatList(Pointer<Float> pointer, int len) {
  return pointer.asTypedList(len);
}

/// String -> Pointer
/// 此转换会申请内存，使用完成后务必释放其内存
Pointer<Char> string2Pointer(String str) {
  return str.toNativeUtf8().cast<Char>();
}

/// Uint8List -> Pointer
/// 此转换会手动申请内存，使用完成后务必释放其内存
Pointer<Uint8> uint8List2Pointer(dynamic list) {
  Pointer<Uint8> pointer = malloc.allocate<Uint8>(list.length * sizeOf<Uint8>());
  for (int i = 0; i < list.length; i++) {
    pointer[i] = list[i];
  }
  return pointer;
}

/// Float32List -> Pointer
/// 此转换会手动申请内存，使用完成后务必释放其内存
Pointer<Float> float32List2Pointer(dynamic list) {
  Pointer<Float> pointer = malloc.allocate<Float>(list.length * sizeOf<Float>());
  for (int i = 0; i < list.length; i++) {
    pointer[i] = list[i];
  }
  return pointer;
}

/// Float64List -> Pointer
/// 此转换会手动申请内存，使用完成后务必释放其内存
Pointer<Double> doubleList2Pointer(dynamic list) {
  Pointer<Double> pointer = malloc.allocate<Double>(list.length * sizeOf<Double>());
  for (int i = 0; i < list.length; i++) {
    pointer[i] = list[i];
  }
  return pointer;
}

//Method Channel
const methodChannel = MethodChannel('mooeli_ffi');
/*
* getAppVersion()
* getBrightness()
* setBrightness({"brightness":0.2})
* [注]KeptOn设置常亮需要Android配置权限：<uses-permission android:name="android.permission.WAKE_LOCK" />
* isBrightnessKeptOn()
* setBrightnessKeptOn({"on":true})
* getVolume();
* setVolume({"volume":0.2})
* getDeviceInfo()
* */
Future<dynamic> callMcByFunName(String name, {dynamic params}) async {
  return await methodChannel.invokeMethod(name, params);
}

//监听native->flutter 事件
void addListenNative(String name, dynamic callBack) async {
  methodChannel.setMethodCallHandler((call) {
    dynamic result;
    switch (name) {
      //Future<dynamic> listenVolume(value)async{}
      //callMcByFunName("addListenVolume");
      //addListenNative("ListenVolume", listenVolume);
      //callMcByFunName("removeListenVolume");
      case "ListenVolume":
        if (callBack != null) {
          result = callBack(call.arguments);
        }
        break;
      default:
        result = null;
        break;
    }
    return result;
  });
}
