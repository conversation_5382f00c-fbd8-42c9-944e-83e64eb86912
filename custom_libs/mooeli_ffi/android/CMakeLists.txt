cmake_minimum_required(VERSION 3.4.1)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

project(MooeliFFI LANGUAGES C CXX)

#header
include_directories(${CMAKE_SOURCE_DIR}/src/main/include)

#opencv
add_library(lib_opencv SHARED IMPORTED)
set_target_properties(lib_opencv PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/src/main/jniLibs/${ANDROID_ABI}/libopencv_java4.so)

#tflite
add_library(lib_tflite SHARED IMPORTED)
set_target_properties(lib_tflite PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/src/main/jniLibs/${ANDROID_ABI}/libtensorflowlite_c.so)
#set_target_properties(lib_tflite PROPERTIES IMPORTED_NO_SONAME TRUE)

#flatbuffers
add_library(lib_flatbuffers STATIC IMPORTED)
set_target_properties(lib_flatbuffers PROPERTIES IMPORTED_LOCATION ${CMAKE_CURRENT_SOURCE_DIR}/src/main/jniLibs/libflatbuffers.a)


find_library(log-lib log)

set(SOURCE_ROOT ../ios/Classes)
file(GLOB_RECURSE SOURCES ${SOURCE_ROOT}/*.cpp ${SOURCE_ROOT}/*.hpp)

add_library(mooeli_ffi SHARED ${SOURCES})

target_link_libraries(mooeli_ffi lib_opencv lib_tflite ${log-lib} lib_flatbuffers)