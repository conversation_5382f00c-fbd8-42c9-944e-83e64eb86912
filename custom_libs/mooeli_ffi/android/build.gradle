group 'com.example.mooeli_ffi'
version '1.0'

buildscript {
    repositories {
//        google()
//        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
    }
}

rootProject.allprojects {
    repositories {
//        google()
//        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
    }
}

apply plugin: 'com.android.library'

android {
    externalNativeBuild {
        cmake {
            path "./CMakeLists.txt"
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    compileSdkVersion 31

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 24

        //引入 c++_shared.so 库
        externalNativeBuild {
            cmake {
                // Enabling exception, RTTI and setting C++ standard version
                cppFlags '-frtti -fexceptions -std=c++17'

                // Shared runtime for shared libraries
//                arguments "-DANDROID_STL=c++_shared"
            }
        }
    }
    buildTypes {
        debug {
            jniDebuggable true
        }
    }
}
