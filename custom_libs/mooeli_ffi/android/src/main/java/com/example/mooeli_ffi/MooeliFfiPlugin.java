package com.example.mooeli_ffi;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodChannel.Result;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;

import android.provider.Settings;
import android.view.WindowManager;
import android.app.Activity;

import java.lang.ref.WeakReference;

import android.media.AudioManager;
import android.util.Log;
import java.util.HashMap;
import android.text.TextUtils;
import java.io.File;
import java.io.BufferedReader;
import java.io.FileReader;
import android.os.Debug;
import java.util.Locale;
import android.content.pm.ApplicationInfo;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CameraAccessException;

/**
 * MooeliFfiPlugin
 */
public class MooeliFfiPlugin implements FlutterPlugin, ActivityAware, MethodCallHandler, VolumeChangeObserver.VolumeChangeListener {
    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private MethodChannel channel;
    private Context context;
    private WeakReference<Activity> activity;
    private AudioManager audioManager;

    private VolumeChangeObserver mVolumeChangeObserver;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "mooeli_ffi");
        channel.setMethodCallHandler(this);
        context = flutterPluginBinding.getApplicationContext();

        //实例化对象
        mVolumeChangeObserver = new VolumeChangeObserver(context);
    }


    @Override
    public void onAttachedToActivity(ActivityPluginBinding binding) {
        activity = new WeakReference<>(binding.getActivity());
        audioManager = (AudioManager) activity.get().getSystemService(Context.AUDIO_SERVICE);

    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
    }

    @Override
    public void onReattachedToActivityForConfigChanges(ActivityPluginBinding binding) {
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
    }


    private void addListenVolume() {
        mVolumeChangeObserver.setVolumeChangeListener(this);
        mVolumeChangeObserver.registerReceiver();
    }

    private void removeListenVolume() {
        mVolumeChangeObserver.setVolumeChangeListener(null);
        mVolumeChangeObserver.unregisterReceiver();
    }

    @Override
    public void onVolumeChanged(int volume) {
        float newestVolume = getVolume();
        Log.e("VolumePlugin", "onVolumeChanged()--->volume = " + newestVolume);
        channel.invokeMethod("ListenVolume", newestVolume, new MethodChannel.Result() {
            @Override
            public void success(Object o) {
                Log.e("Mooeli-Plugin", "success=" + o);
            }

            @Override
            public void error(String s, String s1, Object o) {
                Log.e("Mooeli-Plugin", "error=" + s + "|"+s1 + "|" + o);
            }

            @Override
            public void notImplemented() {
                Log.e("Mooeli-Plugin", "notImplemented");
            }
        });
    }


    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "testByMethodChannel":
                result.success("Android " + android.os.Build.VERSION.RELEASE);
                break;
            case "getAppVersion":
                result.success(getVersionName());
                break;
            case "getBrightness":
                result.success(getBrightness());
                break;
            case "setBrightness":
                double brightness = call.argument("brightness");
                WindowManager.LayoutParams layoutParams = activity.get().getWindow().getAttributes();
                layoutParams.screenBrightness = (float) brightness;
                activity.get().getWindow().setAttributes(layoutParams);
                result.success(null);
                break;
            case "isBrightnessKeptOn":
                int flags = activity.get().getWindow().getAttributes().flags;
                result.success((flags & WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON) != 0);
                break;
            case "setBrightnessKeptOn":
                Boolean on = call.argument("on");
                if (on) {
                    System.out.println("Keeping screen on ");
                    activity.get().getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                } else {
                    System.out.println("Not keeping screen on");
                    activity.get().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                }
                result.success(null);
                break;
            case "getVolume":
                result.success(getVolume());
                break;
            case "setVolume":
                double volume = call.argument("volume");
                setVolume((float) volume);
                result.success(null);
                break;
            case "addListenVolume":
                addListenVolume();
                result.success(null);
                break;
            case "removeListenVolume":
                removeListenVolume();
                result.success(null);
                break;
            case "getDeviceInfo":
                HashMap<String, String> deviceInfo = new HashMap<String, String>();
                deviceInfo.put("name", android.os.Build.BRAND + " " + android.os.Build.MODEL);
                deviceInfo.put("version", "Android " + android.os.Build.VERSION.RELEASE);
                Locale locale = Locale.getDefault();
                deviceInfo.put("language", locale.getLanguage());
                result.success(deviceInfo);
                break;
            case "ptraceDebugger":
                //防注入防调试
                ptraceDebugger();
                result.success(null);
                break;
            case "getGeTuiIntentPayLoad"://获取个推intent （点击通知栏打开app后）才能拿到的payload数据
                Intent intent = activity.get().getIntent();
                if (null != intent) {
                    String payload = intent.getStringExtra("payload");
                    result.success(payload);
                }else{
                    result.success("");
                }
                break;
            case "checkProxy"://检测是否设置了代理
                boolean isProxy = isWifiProxy(context);
                result.success(isProxy);
                break;
            case "checkJailbrokenRoot"://检测是否root
                boolean isRoot = hasRootPrivilege();
                result.success(isRoot);
                break;
            case "openFlash"://打开闪光灯
                CameraManager cameraManager= (CameraManager) activity.get().getSystemService(Context.CAMERA_SERVICE);
                try {
                    String CameraId=cameraManager.getCameraIdList()[0];
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        cameraManager.setTorchMode(CameraId,true);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                result.success(null);
                break;
            case "closeFlash"://关闭闪光灯
                CameraManager cameraManagerClose= (CameraManager) activity.get().getSystemService(Context.CAMERA_SERVICE);
                try {
                    String CameraId=cameraManagerClose.getCameraIdList()[0];
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        cameraManagerClose.setTorchMode(CameraId,false);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                result.success(null);
                break;
            case "setLockScreenTime":
                //TODO 设置锁屏时间
                int time = call.argument("time");
                result.success(null);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    //防止so注入防止动态调试
    private void ptraceDebugger() {
        //反调试检测
        if (isDebuggable()) {
            System.exit(0);
        }

        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        Thread.sleep(500);
                        if (Debug.isDebuggerConnected()) {
                            System.exit(0);
                        }

                        if (isUnderTraced()) {
                            System.exit(0);
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }, "SafeGuardThread");
        t.start();
        if (isUnderTraced()) {
            System.exit(0);
        }
    }

    private boolean isUnderTraced() {
        String processStatusFilePath = String.format(Locale.US, "/proc/%d/status", android.os.Process.myPid());
        File procInfoFile = new File(processStatusFilePath);
        try {
            BufferedReader b = new BufferedReader(new FileReader(procInfoFile));
            String readLine;
            while ((readLine = b.readLine()) != null) {
                if (readLine.contains("TracerPid")) {
                    String[] arrays = readLine.split(":");
                    if (arrays.length == 2) {
                        int tracerPid = Integer.parseInt(arrays[1].trim());
                        if (tracerPid != 0) {
                            return true;
                        }
                    }
                }
            }

            b.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    public boolean isDebuggable() {
        return 0 != (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE);
    }


    private static final String[] rootRelatedDirs = new String[]{
            "/su", "/su/bin/su", "/sbin/su",
            "/data/local/xbin/su", "/data/local/bin/su", "/data/local/su",
            "/system/xbin/su",
            "/system/bin/su", "/system/sd/xbin/su", "/system/bin/failsafe/su",
            "/system/bin/cufsdosck", "/system/xbin/cufsdosck", "/system/bin/cufsmgr",
            "/system/xbin/cufsmgr", "/system/bin/cufaevdd", "/system/xbin/cufaevdd",
            "/system/bin/conbb", "/system/xbin/conbb"};

    public boolean hasRootPrivilege() {
        boolean hasRootDir = false;
        String[] rootDirs;
        int dirCount = (rootDirs = rootRelatedDirs).length;
        for (int i = 0; i < dirCount; ++i) {
            String dir = rootDirs[i];
            if ((new File(dir)).exists()) {
                hasRootDir = true;
                break;
            }
        }
        return Build.TAGS != null && Build.TAGS.contains("test-keys") || hasRootDir;
    }

    public boolean isWifiProxy(Context context) {
        final boolean IS_ICS_OR_LATER = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH;
        String proxyAddress;
        int proxyPort;
        if (IS_ICS_OR_LATER) {
            proxyAddress = System.getProperty("http.proxyHost");    //获取代理主机
            String portStr = System.getProperty("http.proxyPort");  //获取代理端口
            proxyPort = Integer.parseInt((portStr != null ? portStr : "-1"));
        } else {
            proxyAddress = android.net.Proxy.getHost(context);
            proxyPort = android.net.Proxy.getPort(context);
        }
        return (!TextUtils.isEmpty(proxyAddress)) && (proxyPort != -1);
    }

    private float getVolume() {
        float max = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        float vol = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        return vol / max;
    }

    private void setVolume(float vol) {
        float max = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        float volIndex = vol * max;
        volIndex = volIndex > max ? max : volIndex;
        volIndex = volIndex > 0 ? volIndex : 0;
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (int) volIndex, AudioManager.FLAG_SHOW_UI);
    }

    private float getBrightness() {
        float result = activity.get().getWindow().getAttributes().screenBrightness;
        if (result < 0) { // the application is using the system brightness
            try {
                result = Settings.System.getInt(context.getContentResolver(), Settings.System.SCREEN_BRIGHTNESS) / (float) 255;
            } catch (Settings.SettingNotFoundException e) {
                result = 1.0f;
                e.printStackTrace();
            }
        }
        return result;
    }

    private String getVersionName() {
        PackageManager manager = context.getPackageManager();
        String name = null;
        try {
            PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
            name = info.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return name;
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
        channel = null;
        context = null;
        mVolumeChangeObserver.unregisterReceiver();
        mVolumeChangeObserver = null;
    }
}
