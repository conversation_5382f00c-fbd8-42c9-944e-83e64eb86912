import 'dart:convert';
import 'dart:io';
import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_uvc_camera/flutter_uvc_camera.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

class UVCCameraController {
  static const String _channelName = "flutter_uvc_camera/channel";

  UVCCameraState _cameraState = UVCCameraState.closed;

  /// 摄像头状态回调
  Function(UVCCameraState)? cameraStateCallback;

  /// 拍照按钮回调
  Function(String path)? clickTakePictureButtonCallback;

  UVCCameraState get getCameraState => _cameraState;
  String _cameraErrorMsg = '';

  String get getCameraErrorMsg => _cameraErrorMsg;
  String _takePicturePath = '';

  String get getTakePicturePath => _takePicturePath;
  final List<String> _callStrings = [];

  List<String> get getCallStrings => _callStrings;
  Function(String)? msgCallback;
  List<PreviewSize> _previewSizes = [];

  List<PreviewSize> get getPreviewSizes => _previewSizes;

  MethodChannel? _cameraChannel;

  ///初始化
  UVCCameraController() {
    debugPrint("------> UVCCameraController init");
  }

  initChannel(int index) {
    debugPrint("------> UVCCameraController init $index");
    _cameraChannel = MethodChannel("${_channelName}_$index");
    _cameraChannel?.setMethodCallHandler(_methodChannelHandler);
  }

  String? getChannelName() {
    return _cameraChannel?.name;
  }

  void dispose() {
    _cameraChannel?.setMethodCallHandler(null);
    _cameraChannel = null;
    debugPrint("------> UVCCameraController dispose");
  }

  ///接收来自Android的消息
  Future<void> _methodChannelHandler(MethodCall call) async {
    debugPrint("------> UVCCameraController methodChannelHandler ${_cameraChannel?.name} $call");
    switch (call.method) {
      case "callFlutter":
        debugPrint('------> Received from Android：${call.arguments}');
        _callStrings.add(call.arguments.toString());
        msgCallback?.call(call.arguments['msg']);

        break;
      case "takePictureSuccess":
        _takePictureSuccess(call.arguments);
        break;
      case "CameraState":
        _setCameraState(call.arguments.toString());
        break;
      case "onEncodeData":
        final Map<dynamic, dynamic> args = call.arguments;
        // capture H264 & AAC only
        debugPrint(args.toString());
        break;
    }
  }

  Future<void> initializeCamera(int cameraId) async {
    debugPrint("------> UVCCameraController initializeCamera $cameraId");
    await _cameraChannel?.invokeMethod('initializeCamera', cameraId);
    // 初始化完成后立即获取预览尺寸
    await getAllPreviewSizes();
  }

  Future<void> openUVCCamera() async {
    debugPrint("openUVCCamera");
    await _cameraChannel?.invokeMethod('openUVCCamera');
  }

  // Future<void> writeToDevice(int data) async {
  //   if (_cameraState == UVCCameraState.opened) {
  //     final result = await _cameraChannel?.invokeMethod('writeToDevice', data);
  //     debugPrint(result.toString());
  //   }
  // }

  void captureStreamStart() {
    _cameraChannel?.invokeMethod('captureStreamStart');
  }

  void captureStreamStop() {
    _cameraChannel?.invokeMethod('captureStreamStop');
  }

  void startCamera() async {
    await _cameraChannel?.invokeMethod('startCamera');
  }

  /// 获取全部预览大小
  Future getAllPreviewSizes() async {
    var result = await _cameraChannel?.invokeMethod('getAllPreviewSizes');
    List<PreviewSize> list = [];
    json.decode(result)?.forEach((element) {
      list.add(PreviewSize.fromJson(element));
    });
    _previewSizes = list;
  }

  /// 获取当前摄像头请求参数
  Future<String?> getCurrentCameraRequestParameters() async {
    return await _cameraChannel?.invokeMethod('getCurrentCameraRequestParameters');
  }

  /// 更新预览大小
  void updateResolution(PreviewSize? previewSize) {
    _cameraChannel?.invokeMethod('updateResolution', previewSize?.toMap());
  }

  ///拍照
  Future<String?> takePicture({String? path, Function(String)? onSuccess, Function(String)? onError}) async {
    if (path == null) {
      // 如果未提供路径，则使用默认路径
      Directory appDir = await getTemporaryDirectory();
      String appDocPath = appDir.path;
      path = '$appDocPath/${DateTime.now().millisecondsSinceEpoch}.jpg';
    }
    
    try {
      // 调用原生方法保存图片
      final Completer<String?> completer = Completer<String?>();
      
      _cameraChannel?.invokeMethod('takePicture', path).then((result) {
        if (result != null) {
          onSuccess?.call(result);
          completer.complete(result);
        } else {
          onError?.call("拍照失败，未返回路径");
          completer.complete(null);
        }
      }).catchError((error) {
        onError?.call("拍照失败: $error");
        completer.complete(null);
      });
      
      return completer.future;
    } catch (ex) {
      debugPrint("Take picture failed: $ex");
      onError?.call("拍照失败: $ex");
      return null;
    }
  }

  Future<String?> test({bool flip = false, int rotate = 0}) async {
    Uint8List? rgbData = await getImageData();
    try {
      if (rgbData != null) {
        // 获取应用的文档目录
        Directory appDir = await getTemporaryDirectory();
        String appDocPath = appDir.path;
        String filePath = '$appDocPath/${DateTime.now().millisecondsSinceEpoch}.png';

        // 获取图像尺寸
        int width = 1920;
        int height = 1080;
        if (_previewSizes.isNotEmpty) {
          width = _previewSizes[0].width!;
          height = _previewSizes[0].height!;
        }

        // 创建图像并设置像素
        img.Image image = img.Image(width, height);
        for (int y = 0; y < height; y++) {
          for (int x = 0; x < width; x++) {
            int index = (y * width + x) * 3;
            // 确保索引在范围内
            if (index + 2 < rgbData.length) {
              int r = rgbData[index];
              int g = rgbData[index + 1];
              int b = rgbData[index + 2];
              // 设置像素，注意RGBA格式
              image.setPixelRgba(x, y, r, g, b, 255);
            }
          }
        }

        // 应用旋转
        img.Image processedImage = image;
        if (rotate == 90) {
          processedImage = img.copyRotate(image, 90);
        } else if (rotate == 180) {
          processedImage = img.copyRotate(image, 180);
        } else if (rotate == 270) {
          processedImage = img.copyRotate(image, 270);
        }

        // 应用翻转
        if (flip) {
          processedImage = img.flipHorizontal(processedImage);
        }

        // 编码为PNG并保存
        List<int> pngData = img.encodePng(processedImage);
        await File(filePath).writeAsBytes(pngData);

        return filePath;
      }
    } catch (ex) {
      debugPrint("Save picture failed: $ex");
    }
    return null;
  }

  Future<Uint8List?> getImageData() async {
    Uint8List? rawData = await _cameraChannel?.invokeMethod('getImageData');
    if (rawData != null) {
      // 获取图像格式
      String? format = await _cameraChannel?.invokeMethod('getImageFormat');
      
      // 根据不同格式处理数据
      if (format == "NV21") {
        // NV21格式需要转换为RGB
        return _convertNV21toRGB(rawData);
      } else if (format == "RGBA") {
        // RGBA格式可以直接使用
        return rawData;
      } else {
        // 未知格式，尝试NV21转换
        debugPrint("Unknown image format: $format, trying NV21 conversion");
        return _convertNV21toRGB(rawData);
      }
    }
    return null;
  }

  // NV21转RGB函数
  Uint8List _convertNV21toRGB(Uint8List nv21) {
    try {
      // 获取相机预览参数
      int width = 1920; // 默认值，应从相机参数中获取
      int height = 1080; // 默认值，应从相机参数中获取

      // 如果有预览尺寸数据，使用第一个预览尺寸
      if (_previewSizes.isNotEmpty) {
        width = _previewSizes[0].width!;
        height = _previewSizes[0].height!;
      }

      // 创建输出RGB数据
      int size = width * height;
      Uint8List rgb = Uint8List(size * 3);

      // NV21格式: YYYYYYYY...VUVU... (注意UV的顺序是VU而不是UV)
      final int frameSize = width * height;

      // 将YUV转换为RGB
      for (int j = 0; j < height; j++) {
        for (int i = 0; i < width; i++) {
          int yIndex = j * width + i;
          int y = (0xff & nv21[yIndex]) - 16;
          if (y < 0) y = 0;

          // NV21格式中，UV是交错排列的，每个UV对应4个Y值
          // 索引计算: frameSize + (j / 2) * width + (i / 2) * 2
          int uvIndex = frameSize + (j >> 1) * width + (i & ~1);

          // NV21中UV的顺序是VU
          int v = (0xff & nv21[uvIndex]) - 128;
          int u = (0xff & nv21[uvIndex + 1]) - 128;

          // YUV转RGB的公式
          int y1192 = 1192 * y;
          int r = (y1192 + 1634 * v);
          int g = (y1192 - 833 * v - 400 * u);
          int b = (y1192 + 2066 * u);

          // 限制RGB值范围
          if (r < 0)
            r = 0;
          else if (r > 262143) r = 262143;
          if (g < 0)
            g = 0;
          else if (g > 262143) g = 262143;
          if (b < 0)
            b = 0;
          else if (b > 262143) b = 262143;

          // 将RGB值写入输出数组
          int rgbIndex = yIndex * 3;
          rgb[rgbIndex] = (r >> 10) & 0xff; // R
          rgb[rgbIndex + 1] = (g >> 10) & 0xff; // G
          rgb[rgbIndex + 2] = (b >> 10) & 0xff; // B
        }
      }

      return rgb;
    } catch (e) {
      debugPrint("Error converting NV21 to RGB: $e");
      return nv21; // 转换失败时返回原数据
    }
  }

  ///录像
  Future<String?> captureVideo() async {
    String? path = await _cameraChannel?.invokeMethod('captureVideo');
    debugPrint("path: $path");
    return path;
  }

  void _setCameraState(String state) {
    debugPrint("Camera: $state");
    switch (state) {
      case "OPENED":
        _cameraState = UVCCameraState.opened;
        cameraStateCallback?.call(UVCCameraState.opened);
        break;
      case "CLOSED":
        _cameraState = UVCCameraState.closed;
        cameraStateCallback?.call(UVCCameraState.closed);
        break;
      default:
        if (state.contains("ERROR")) {
          _cameraState = UVCCameraState.error;
          _cameraErrorMsg = state;
          cameraStateCallback?.call(UVCCameraState.error);
          msgCallback?.call(state);
        }
        break;
    }
  }

  void _takePictureSuccess(String? result) {
    if (result != null) {
      _takePicturePath = result;
      clickTakePictureButtonCallback?.call(result);
    }
  }

  getDeviceIdList() {
    return _cameraChannel?.invokeMethod('getDeviceIdList');
  }

  switchCameraId(int deviceId) {
    _cameraChannel?.invokeMethod('switchCameraId', deviceId);
  }

  void closeCamera() {
    _cameraChannel?.invokeMethod('closeCamera');
  }
}
