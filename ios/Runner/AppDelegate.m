#import "AppDelegate.h"
#import "GeneratedPluginRegistrant.h"

#import <Flutter/Flutter.h>

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    [GeneratedPluginRegistrant registerWithRegistry:self];
    // Override point for customization after application launch.
    
    FlutterViewController * controller = (FlutterViewController*)self.window.rootViewController;
    return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

@end
