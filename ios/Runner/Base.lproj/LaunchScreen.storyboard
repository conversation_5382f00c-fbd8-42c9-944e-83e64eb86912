<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_pad_background.png" translatesAutoresizingMaskIntoConstraints="NO" id="Oly-Jg-H5o">
                                <rect key="frame" x="0.0" y="0.0" width="768" height="1024"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_landscape_background.png" translatesAutoresizingMaskIntoConstraints="NO" id="jN2-Td-r8h">
                                <rect key="frame" x="0.0" y="0.0" width="812" height="375"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dc_launchscreen_portrait_background.png" translatesAutoresizingMaskIntoConstraints="NO" id="Tt8-jS-2H5">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ly_launchscreen_icon.png" translatesAutoresizingMaskIntoConstraints="NO" id="vp6-uo-KS2">
                                <rect key="frame" x="19.666666666666657" y="416.66666666666669" width="375" height="63"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QBH-Ne-rcx">
                                <rect key="frame" x="-16220" y="801.66666666666663" width="32854" height="50.333333333333371"/>
                                <string key="text">CHOHOTECH

杭州朝厚信息科技有限公司</string>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <color key="textColor" red="0.50196078430000002" green="0.50196078430000002" blue="0.50196078430000002" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="IW3-oA-Ytg"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="vp6-uo-KS2" secondAttribute="trailing" id="1Cb-y2-pRQ"/>
                            <constraint firstItem="QBH-Ne-rcx" firstAttribute="bottom" secondItem="Ze5-6b-2t3" secondAttribute="bottom" constant="-44" id="5MD-Bb-oGe"/>
                            <constraint firstAttribute="trailing" secondItem="jN2-Td-r8h" secondAttribute="trailing" id="Bbg-68-h1T"/>
                            <constraint firstItem="Tt8-jS-2H5" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="FH7-NM-QY8"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="GM5-vc-qeO"/>
                            <constraint firstItem="jN2-Td-r8h" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="J9z-eJ-GkK"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="top" relation="greaterThanOrEqual" secondItem="Ze5-6b-2t3" secondAttribute="top" constant="10" id="M4i-XX-uO2"/>
                            <constraint firstItem="jN2-Td-r8h" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="QFR-Dp-WZN"/>
                            <constraint firstAttribute="trailing" secondItem="Tt8-jS-2H5" secondAttribute="trailing" id="QGW-bk-xBw"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="Qji-wc-LLD"/>
                            <constraint firstAttribute="bottom" secondItem="Oly-Jg-H5o" secondAttribute="bottom" id="RrE-yv-HsN"/>
                            <constraint firstItem="Oly-Jg-H5o" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="VcW-dk-n8h"/>
                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="vp6-uo-KS2" secondAttribute="bottom" constant="10" id="dGX-JM-hiU"/>
                            <constraint firstItem="Oly-Jg-H5o" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="fa7-4C-n5e"/>
                            <constraint firstItem="Tt8-jS-2H5" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="fnt-hE-VMi"/>
                            <constraint firstAttribute="bottom" secondItem="jN2-Td-r8h" secondAttribute="bottom" id="iIX-Hl-IOF"/>
                            <constraint firstAttribute="trailing" secondItem="Oly-Jg-H5o" secondAttribute="trailing" id="mm9-f4-toK"/>
                            <constraint firstItem="vp6-uo-KS2" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="oA2-4f-IGA"/>
                            <constraint firstItem="QBH-Ne-rcx" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="rO7-2t-bpH"/>
                            <constraint firstAttribute="bottom" secondItem="Tt8-jS-2H5" secondAttribute="bottom" id="seh-wj-zPF"/>
                        </constraints>
                        <variation key="default">
                            <mask key="subviews">
                                <exclude reference="Oly-Jg-H5o"/>
                                <exclude reference="jN2-Td-r8h"/>
                                <exclude reference="Tt8-jS-2H5"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=compact">
                            <mask key="subviews">
                                <include reference="jN2-Td-r8h"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=regular-widthClass=compact">
                            <mask key="subviews">
                                <include reference="Tt8-jS-2H5"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=regular-widthClass=regular">
                            <mask key="subviews">
                                <include reference="Oly-Jg-H5o"/>
                            </mask>
                        </variation>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.734375" y="374.67057101024886"/>
        </scene>
    </scenes>
    <resources>
        <image name="dc_launchscreen_landscape_background.png" width="812" height="375"/>
        <image name="dc_launchscreen_pad_background.png" width="768" height="1024"/>
        <image name="dc_launchscreen_portrait_background.png" width="375" height="812"/>
        <image name="ly_launchscreen_icon.png" width="375" height="63"/>
    </resources>
</document>
