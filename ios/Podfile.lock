PODS:
  - audioplay<PERSON>_<PERSON><PERSON><PERSON> (0.0.1):
    - Flutter
  - <PERSON><PERSON><PERSON> (2.5.93)
  - camera_avfoundation (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_bugly (0.0.1):
    - Bugly
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - launch_review (0.0.1):
    - Flutter
  - local_auth_ios (0.0.1):
    - Flutter
  - mooeli_ffi (0.0.1):
    - Flutter
  - mooeli_ultra (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - open_file (0.0.1):
    - Flutter
  - orientation (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - UMCommon (7.4.6):
    - UMDevice
  - UMDevice (3.4.0)
  - umeng_common_sdk (0.0.1):
    - Flutter
    - UMCommon
    - UMDevice
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webcontent_converter (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - wifi_iot (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_bugly (from `.symlinks/plugins/flutter_bugly/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - launch_review (from `.symlinks/plugins/launch_review/ios`)
  - local_auth_ios (from `.symlinks/plugins/local_auth_ios/ios`)
  - mooeli_ffi (from `.symlinks/plugins/mooeli_ffi/ios`)
  - mooeli_ultra (from `.symlinks/plugins/mooeli_ultra/ios`)
  - open_file (from `.symlinks/plugins/open_file/ios`)
  - orientation (from `.symlinks/plugins/orientation/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - UMCommon
  - umeng_common_sdk (from `.symlinks/plugins/umeng_common_sdk/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webcontent_converter (from `.symlinks/plugins/webcontent_converter/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)
  - wifi_iot (from `.symlinks/plugins/wifi_iot/ios`)

SPEC REPOS:
  trunk:
    - Bugly
    - FMDB
    - MTBBarcodeScanner
    - UMCommon
    - UMDevice

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_bugly:
    :path: ".symlinks/plugins/flutter_bugly/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  launch_review:
    :path: ".symlinks/plugins/launch_review/ios"
  local_auth_ios:
    :path: ".symlinks/plugins/local_auth_ios/ios"
  mooeli_ffi:
    :path: ".symlinks/plugins/mooeli_ffi/ios"
  mooeli_ultra:
    :path: ".symlinks/plugins/mooeli_ultra/ios"
  open_file:
    :path: ".symlinks/plugins/open_file/ios"
  orientation:
    :path: ".symlinks/plugins/orientation/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  umeng_common_sdk:
    :path: ".symlinks/plugins/umeng_common_sdk/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webcontent_converter:
    :path: ".symlinks/plugins/webcontent_converter/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"
  wifi_iot:
    :path: ".symlinks/plugins/wifi_iot/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  Bugly: b8715e6ec4004b7f7fbffab0643ba80545aee3da
  camera_avfoundation: 3125e8cd1a4387f6f31c6c63abb8a55892a9eeeb
  device_info_plus: e5c5da33f982a436e103237c0c85f9031142abed
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_bugly: c9800f4d5bc5bdc27ffdde3417a26ba44266e0c3
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_pdfview: 25f53dd6097661e6395b17de506e6060585946bd
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  launch_review: 75d5a956ba8eaa493e9c9d4bf4c05e505e8d5ed0
  local_auth_ios: c6cf091ded637a88f24f86a8875d8b0f526e2605
  mooeli_ffi: cd20255eaeb22c731c4f2c8b7849860d43b368c2
  mooeli_ultra: f56134336957ee5b22f64360a526b0ee330d9542
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  open_file: 02eb5cb6b21264bd3a696876f5afbfb7ca4f4b7d
  orientation: 6c9203efe86ce4cff379756910f18b2d745628c3
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  syncfusion_flutter_pdfviewer: bb9998884b864cfedf72628df3503bdf57e397c0
  UMCommon: 9399105b64e099c3c66aad21fc85ae695567a1f1
  UMDevice: dcdf7ec167387837559d149fbc7d793d984faf82
  umeng_common_sdk: a8abd7f86dfd013dbbeeae587ee143760c6582f2
  url_launcher_ios: 68d46cc9766d0c41dbdc884310529557e3cd7a86
  video_player_avfoundation: 81e49bb3d9fb63dccf9fa0f6d877dc3ddbeac126
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webcontent_converter: baa47c5fc5a9e8d703897a0b128f969c58fd2c7f
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f
  wifi_iot: b5aafd6f9b52f8a357383a1deabab45f31cd602d

PODFILE CHECKSUM: a21d5739f6c343faf3fa7b6a7ff900527093bf08

COCOAPODS: 1.12.0
