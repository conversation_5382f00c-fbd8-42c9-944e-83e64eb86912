def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
//    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
    throw GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1' //不要更改这个，去 根目录 pubspec.yaml 中修改构建号
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0' //不要更改这个，去 根目录 pubspec.yaml 中修改版本号
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
//个推-华为
//apply plugin: 'com.huawei.agconnect'

def push_manifestPlaceholders = [
        GETUI_APPID    : "igS0mdtn8P7qOBF2cGoAa3",
        // 下面是多厂商配置，如需要开通使用请联系技术支持
        // 如果不需要使用，预留空字段即可
        // 华为 相关应用参数
        HUAWEI_APP_ID  : "106019331",

        // 小米相关应用参数
        XIAOMI_APP_ID  : "2882303761520152952",
        XIAOMI_APP_KEY : "5952015280952",

        // OPPO 相关应用参数
        OPPO_APP_KEY   : "ec18ad98fc014518b616c276a7451f10",
        OPPO_APP_SECRET: "42ecb306669645dab09e1af24adb529c",

        // VIVO 相关应用参数
        VIVO_APP_ID    : "105555738",
        VIVO_APP_KEY   : "72566116535b8eb0a5153e7a9372255b",

        // 魅族相关应用参数
        MEIZU_APP_ID   : "",
        MEIZU_APP_KEY  : "",

        // 荣耀相关应用参数
        HONOR_APP_ID   : "",
]
project.android.defaultConfig.manifestPlaceholders = project.android.defaultConfig.manifestPlaceholders + push_manifestPlaceholders

android {
    namespace "com.chohotech.probox"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    splits {
        abi {
            enable true
            reset()
            include "arm64-v8a", "armeabi-v7a"
            universalApk true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
//        ndk {
//            abiFilters 'arm64-v8a', 'armeabi-v7a'
//        }
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.chohotech.probox"
        // You can update the following values to match your application needs. 
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 29//flutter.minSdkVersion
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

//    buildTypes {
//        release {
//            // TODO: Add your own signing config for the release build.
//            // Signing with the debug keys for now, so `flutter run --release` works.
//            signingConfig signingConfigs.debug
//        }
//    }
    signingConfigs {
        release {
            v1SigningEnabled true
            v2SigningEnabled true
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.release
            shrinkResources false
            minifyEnabled false
        }
        release {
            signingConfig signingConfigs.release
            //个推-华为/荣耀
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //   个推编译加入荣耀出现 Permission name PROCESS_PUSH_MSG is not unique
    //    lintOptions {
    //        checkReleaseBuilds false
    //        abortOnError false
    //    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.android.support:multidex:1.0.3'
    
    // 添加本地jar包依赖
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    
    //个推-根据所需厂商选择集成
//    implementation 'com.getui.opt:hwp:3.1.1'   // 华为
//    implementation 'com.huawei.hms:push:6.5.0.300'
//
//    implementation 'com.getui.opt:xmp:3.2.1'   // 小米
//    implementation 'com.assist-v3:oppo:3.1.2'  // oppo
//    implementation 'com.assist-v3:vivo:3.1.0'  // vivo
//
//    implementation 'com.getui.opt:mzp:3.2.2'   // 魅族
//    implementation 'com.meizu.flyme.internet:push-internal:4.1.4'
//
//    implementation 'com.getui.opt:ups:3.0.3'   // ups，ups目前支持坚果，索尼，海信手机
//    implementation 'com.getui.opt:honor:3.2.0' // 荣耀
}