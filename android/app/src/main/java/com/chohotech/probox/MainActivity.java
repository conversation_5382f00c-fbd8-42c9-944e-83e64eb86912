package com.chohotech.probox;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;

import java.util.HashMap;
import android.util.Log;
import android.content.Intent;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import androidx.annotation.NonNull;

import com.umeng.commonsdk.UMConfigure;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugins.GeneratedPluginRegistrant;

import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import android.uniwin.UniwinAPI;
import android.uniwin.Gpio;

public class MainActivity extends FlutterActivity {
    private static final String ChannelName = "com.chohotech.box/call_android";
    private MethodChannel channel;

    private List<Map<String, String>> getUsbDevices() {
        StorageManager storageManager = (StorageManager) getSystemService(Context.STORAGE_SERVICE);
        List<Map<String, String>> deviceList = new ArrayList<>();

        // API 24+
        List<StorageVolume> volumes = storageManager.getStorageVolumes();
        for (StorageVolume volume : volumes) {
            if (volume.isRemovable()) { // 检查是否为可移动设备
                Map<String, String> deviceInfo = new HashMap<>();
                deviceInfo.put("name", volume.getDescription(this)); // 获取设备名称

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) { // API 30+
                    if (volume.getDirectory() != null) {
                        deviceInfo.put("path", volume.getDirectory().getAbsolutePath());
                    } else {
                        deviceInfo.put("path", "Unknown");
                    }
                } else {
                    // 对于低版本，手动匹配 /storage 中的路径
                    File storageRoot = Environment.getExternalStorageDirectory().getParentFile();
                    if (storageRoot != null) {
                        File[] storageDirs = storageRoot.listFiles();
                        if (storageDirs != null) {
                            for (File dir : storageDirs) {
                                if (dir.isDirectory() && dir.canRead() && dir.getName().contains("sd")) {
                                    deviceInfo.put("path", dir.getAbsolutePath());
                                }
                            }
                        }
                    }
                }
                deviceList.add(deviceInfo);
            }
        }
        return deviceList;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        UMConfigure.preInit(MainActivity.this, "66271db5cac2a664de23005e", "lyoral");
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        Log.d("com.chohotech.box/call_android", "call_android configureFlutterEngine");
        channel = new MethodChannel(flutterEngine.getDartExecutor(), ChannelName);
        channel.setMethodCallHandler(
                (call, result) -> {
                    Log.d("com.chohotech.box/call_android", "call_android onMethodCall: " + call.method + ", param: " + call.arguments());
                    switch (call.method) {
                        case "getUsbDevices":
                            result.success(getUsbDevices());
                            break;
                        case "getDeviceSn":
                            String sn = UniwinAPI.getProperty("ro.serialno");
                            result.success(sn);
                            break;
                        default:
                            result.error("Flutter<->Platform", "Not Find Platform Logic [Android]", null);
                            break;
                    }
                }
        );
    }
}
