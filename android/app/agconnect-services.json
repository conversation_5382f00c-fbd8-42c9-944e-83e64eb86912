{"agcgw": {"backurl": "connect-drcn.hispace.hicloud.com", "url": "connect-drcn.dbankcloud.cn", "websocketbackurl": "connect-ws-drcn.hispace.dbankcloud.com", "websocketurl": "connect-ws-drcn.hispace.dbankcloud.cn"}, "agcgw_all": {"CN": "connect-drcn.dbankcloud.cn", "CN_back": "connect-drcn.hispace.hicloud.com", "DE": "connect-dre.dbankcloud.cn", "DE_back": "connect-dre.hispace.hicloud.com", "RU": "connect-drru.hispace.dbankcloud.ru", "RU_back": "connect-drru.hispace.dbankcloud.ru", "SG": "connect-dra.dbankcloud.cn", "SG_back": "connect-dra.hispace.hicloud.com"}, "client": {"cp_id": "*****************", "product_id": "*****************", "client_id": "874709894840733120", "client_secret": "ADAB77068B42FC03E22590C780425BDCDB3EA76E9B76E88E376CB8041BC560E3", "project_id": "*****************", "app_id": "*********", "api_key": "DAEDAHdODHEdIIf7RTv0bMhVPs4lK19rMQ4osLwz5c1uvbdsqmYLE7KTyfGBCySaL+mZIEZjtHqyfkWHdWoe725ZIdcIZ3tuzW1PVQ==", "package_name": "com.chohotech.probox"}, "oauth_client": {"client_id": "*********", "client_type": 1}, "app_info": {"app_id": "*********", "package_name": "com.chohotech.probox"}, "service": {"analytics": {"collector_url": "datacollector-drcn.dt.hicloud.com,datacollector-drcn.dt.dbankcloud.cn", "collector_url_ru": "datacollector-drru.dt.dbankcloud.ru,datacollector-drru.dt.hicloud.com", "collector_url_sg": "datacollector-dra.dt.hicloud.com,datacollector-dra.dt.dbankcloud.cn", "collector_url_de": "datacollector-dre.dt.hicloud.com,datacollector-dre.dt.dbankcloud.cn", "collector_url_cn": "datacollector-drcn.dt.hicloud.com,datacollector-drcn.dt.dbankcloud.cn", "resource_id": "p1", "channel_id": ""}, "search": {"url": "https://search-drcn.cloud.huawei.com"}, "cloudstorage": {"storage_url": "https://agc-storage-drcn.platform.dbankcloud.cn"}, "ml": {"mlservice_url": "ml-api-drcn.ai.dbankcloud.com,ml-api-drcn.ai.dbankcloud.cn"}}, "region": "CN", "configuration_version": "3.0", "appInfos": [{"package_name": "com.chohotech.probox", "client": {"app_id": "*********"}, "app_info": {"package_name": "com.chohotech.probox", "app_id": "*********"}, "oauth_client": {"client_type": 1, "client_id": "*********"}}]}