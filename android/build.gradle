buildscript {
//    ext.kotlin_version = '1.6.10'
    ext.kotlin_version = '1.7.10'
    repositories {
//        google()
//        mavenCentral()
        maven { url 'https://download.flutter.io' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        //个推-华为
        maven { url 'https://developer.huawei.com/repo/' }
    }

    dependencies {
//        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath 'com.android.tools.build:gradle:7.4.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        //个推-华为
//        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
    }
}

allprojects {
    repositories {
//        google()
//        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        //个推-华为
        maven { url 'https://developer.huawei.com/repo/' }
        //个推-oppo
        maven {
            url 'https://maven.columbus.heytapmobi.com/repository/releases/'
            credentials {
                username 'nexus'
                password 'c0b08da17e3ec36c3870fed674a0bcb36abc2e23'
            }
        }
        maven { url "https://jitpack.io" }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
