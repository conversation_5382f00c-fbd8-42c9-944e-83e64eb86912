<!DOCTYPE html>
<html>
<head>
    <title>Local Image in WebView</title>
</head>
<body>
<div>This is the text.</div>
<img src="https://www.tanghuaku.com/wp-content/uploads/2023/09/1695827737-2a5c69fa826df9c.png" alt="Web Image">
<script>
    function loadImage(imagePath) {
        var image = document.createElement('img');
        image.src = imagePath;
        document.body.appendChild(image);
    }

    function loadBase64(data) {
        var image = document.createElement('img');
        image.src = 'data:image/png;base64,' + data;
        document.body.appendChild(image);
    }
</script>
</body>
</html>