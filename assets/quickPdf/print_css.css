@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    -moz-print-color-adjust: exact !important;
    -ms-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  html {
    margin: 0 !important;
    padding: 0 !important;
  }
  body {
    margin: 0 !important; /* 去掉外边距 */
    padding: 0 !important;
    -webkit-print-color-adjust: exact !important;
    overflow: visible !important;
    display: block !important;
  }
  /* 隐藏不需要打印的元素 */
  .no-print {
    display: none;
  }
  div {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  .footer_box {
    display: block; /* 或者其他适合的显示样式 */
  }
}
@page {
  size: A4 portrait;
  margin: 0 !important;
  overflow: visible;
  /* cicd */
}
* {
  -webkit-print-color-adjust: exact !important;
  -moz-print-color-adjust: exact !important;
  -ms-print-color-adjust: exact !important;
  print-color-adjust: exact !important;
}

html {
  margin: 0 !important;
  padding: 0 !important;
  overflow: visible !important;
}
body {
  margin: 0 !important; /* 去掉外边距 */
  padding: 0 !important;
  overflow: visible !important;
  -webkit-print-color-adjust: exact !important;
}
/* 隐藏不需要打印的元素 */
.no-print {
  display: none;
}
div {
  overflow: visible !important;
  -webkit-print-color-adjust: exact !important;
  print-color-adjust: exact !important;
  color-adjust: exact !important;
}
.footer_box {
  display: inline-block;
}
