/*
 * @Author: yang<PERSON>
 * @Date: 2022-12-07 22:44:51
 * @LastEditTime: 2023-01-31 11:55:07
 * @Descripttion:
 */
'use strict'

// eslint-disable-next-line camelcase
function getHttpEnv() {
  // return 'https://api.chohotech.com/lyoral-web/'
  // return 'https://api.dev.chohotech.com/lyoral-web'
  return 'https://api.alpha.chohotech.com/lyoral-gateway'
  // return 'https://api.sg.chohotech.com/lyoral-gateway'
  // return 'https://api.uat.chohotech.com/lyoral-gateway'
  // return 'https://api.dev.chohotech.com/lyoral-gateway'
  //  return 'https://api.dev.chohotech.com/lyoral-web-alpha'
  // return 'http://************:8888'
  // return 'https://api.dev.chohotech.com/lyoral-web-tenant'
  // return 'https://aggregator-server.uat.chohotech.com'
}

function getLYEnv() {
  return 'https://doctor-frontend.dev.chohotech.com'
}

function getConfigEnv() {
  return 'https://api.iscanbot.com'
}

function get2DHttpEnv() {
  return 'https://doctor-frontend.dev.chohotech.com/dental-2d-analysis'
}

function get2DSvgHttpEnv() {
  return 'https://doctor-frontend.dev.chohotech.com/dental-snap'
}

function get3DHttpEnv() {
  return 'https://doctor-frontend.dev.chohotech.com/dental-3dstudio'
}

function getCBCTHttpEnv() {
  return 'https://doctor-frontend.dev.chohotech.com/dental-cbct'
}

function getEnv() {
  return 'local'
}

function getHeartInterval() {
  return 20
}

function comparisonSwitch(category) {
  const comparisonSwitch = {
    100: true,
    101: true,
    102: true,
    103: true,
    104: true,
    105: true,
    106: true,
    107: true,
    108: true,
    109: true,
    110: true,
    200: true,
    201: true,
    300: true,
    900: true,
  }
  return comparisonSwitch[category]
}

//埋点相关
function setTrace() {
  return true
}

function getServerCode() {
  return '1_1_1'
}

function getServerVersion() {
  return '1_0_0'
}

function getTraceUrl() {
  return 'https://trace-collector-api.dev.chohotech.com'
}

// 灵芽配置项
function lingOral() {
  return {
    // 对应地区为对应值
    region: 'China',
    // 登录方式为手机号登录还是邮箱登录 国内phone 国外email
    loginType: 'phone',
    // 魔丽版本名称 国内mooeli 国外 iooeli
    mooeliVersionName: 'mooeli',
    // 国外没有灵芽会员公测公告
    notice: ['灵芽版本更新公告', '灵芽网站用户调查邀请', '灵芽会员公测公告'],
  }
}

// 设置
function settings() {
  return {
    // 设置是否显示诊所信息 国内true 海外false
    showClinicInfo: true,
  }
}

// 服务入口
function serviceEnter() {
  return {
    // 是否展示在侧边栏 国内true 海外true
    sideBarShow: true,
  }
}

// 口腔数据采集
function promo() {
  return {
    // 是否展示在侧边栏 国内true 海外false
    sideBarShow: true,
  }
}

// 院内初诊
function diagnosis() {
  return {
    // 是否展示在侧边栏 国内true 海外false
    sideBarShow: true,
  }
}

// 魔丽监控
function monitor() {
  return {
    // 是否展示在侧边栏 国内true 海外true
    sideBarShow: true,
    // 监控入口显示 国内true 海外true
    enterShow: true,
  }
}

// 机构管理
function org() {
  return {
    // 是否展示在侧边栏 国内true 海外false
    sideBarShow: true,
    // 是否显示创建机构/切换机构这一栏 国内true 海外false
    popoverShow: true,
  }
}
