<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>延迟显示图片和文字</title>
    <style>
        #content {
            display: none;
            text-align: center;
            margin-top: 50px;
        }
    </style>
    <script>
        setTimeout(function() {
            document.getElementById('content').style.display = 'block';
        }, 1000);
    </script>
</head>
<body>
<div>
    <img id="content" src="https://www.tanghuaku.com/wp-content/uploads/2023/09/1695827737-2a5c69fa826df9c.png" alt="网络图片" />
    <p>这是一些文字，图片在1秒后显示。</p>
    <p id="parameter"></p>
    <script>
        // 获取URL中的参数
        const urlParams = new URLSearchParams(window.location.search);
        const param = urlParams.get('param'); // 假设参数名为'param'

        // 显示参数
        document.getElementById('parameter').innerText = param ? param : '没有接收到参数';
    </script>
</div>
</body>
</html>