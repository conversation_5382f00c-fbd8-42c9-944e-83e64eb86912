.ly-app-loading-text {
  color: #163367;
  font-size: 32px;
  margin-top: 32px;
  font-weight: 500;
  text-align: center;
}
.ly-app-loading,
.ly-app-loading > div {
  position: relative;
  box-sizing: border-box;
}

.ly-app-loading {
  display: block;
  font-size: 0;
  color: #163367;
}

.ly-app-loading.la-dark {
  color: #163367;
}

.ly-app-loading > div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.ly-app-loading {
  width: 100px;
  height: 100px;
}

.ly-app-loading > div {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -4px;
  margin-left: -4px;
  border-radius: 100%;
  animation: ball-spin-clockwise-fade 1s infinite linear;
}

.ly-app-loading > div:nth-child(1) {
  top: 5%;
  left: 50%;
  animation-delay: -0.875s;
}

.ly-app-loading > div:nth-child(2) {
  top: 18.1801948466%;
  left: 81.8198051534%;
  animation-delay: -0.75s;
}

.ly-app-loading > div:nth-child(3) {
  top: 50%;
  left: 95%;
  animation-delay: -0.625s;
}

.ly-app-loading > div:nth-child(4) {
  top: 81.8198051534%;
  left: 81.8198051534%;
  animation-delay: -0.5s;
}

.ly-app-loading > div:nth-child(5) {
  top: 94.9999999966%;
  left: 50.0000000005%;
  animation-delay: -0.375s;
}

.ly-app-loading > div:nth-child(6) {
  top: 81.8198046966%;
  left: 18.1801949248%;
  animation-delay: -0.25s;
}

.ly-app-loading > div:nth-child(7) {
  top: 49.9999750815%;
  left: 5.0000051215%;
  animation-delay: -0.125s;
}

.ly-app-loading > div:nth-child(8) {
  top: 18.179464974%;
  left: 18.1803700518%;
  animation-delay: 0s;
}

.ly-app-loading.la-sm {
  width: 16px;
  height: 16px;
}

.ly-app-loading.la-sm > div {
  width: 4px;
  height: 4px;
  margin-top: -2px;
  margin-left: -2px;
}

.ly-app-loading.la-2x {
  width: 64px;
  height: 64px;
}

.ly-app-loading.la-2x > div {
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
}

.ly-app-loading.la-3x {
  width: 96px;
  height: 96px;
}

.ly-app-loading.la-3x > div {
  width: 24px;
  height: 24px;
  margin-top: -12px;
  margin-left: -12px;
}

@keyframes ball-spin-clockwise-fade {
  50% {
    opacity: 0.25;
    transform: scale(0.5);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
