/*
 * @Author: ya<PERSON><PERSON>
 * @Date: 2022-11-16 18:54:35
 * @LastEditTime: 2022-12-08 16:54:55
 * @Description:
 */

function getHttpEnv() {
  // 本地开发直接使用alpha环境，部署之后，本文件会被运维替换
  return 'https://api.alpha.iscanbot.com/lyoral-gateway'
  // return "https://aggregator-server.uat.chohotech.com";
  // return 'https://api.dev.chohotech.com/lyoral-web'
}

function getLYEnv() {
  return 'https://doctor-frontend.dev.chohotech.com'
}

function getConfigEnv() {
  return 'https://api.iscanbot.com'
}

// 加密配置
function secretConfig() {
  return {
    k: '',
    i: '',
    mode: '',
    pad: '',
  }
}

// 第三方
function getThirdParty() {
  return ''
}

// loading图配置,图片标签src属性会直接指向配置链接,如果没有配置则采用灵芽默认logo
function getLoadingImage() {
  return {
    // 对应大页面的loading动效链接（上下移动的）, 单位vw
    page_loading_src: '',
    page_loading_width: 27.8,
    page_loading_height: 16.7,
    // 对应组件的loading动效链接（三个小圈圈的）, 单位px
    component_loading_src: '',
    component_loading_width: 32,
    component_loading_height: 32,
  }
}

function getSharePDF() {
  return true
}

function setTrace() {
  return false
}

function getVersionHide() {
  return true
}

function getShareWeChat() {
  return false
}
