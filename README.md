# Mooeli

Flutter for <PERSON><PERSON><PERSON>

## Getting Started

*项目注意事项与规范:
- UI适配：UI设计尺寸:375 x 812, 适配采用flutter_screenutil,因此项目中所有固定像素大小 ?px 相关设置均以 ?.sp 方式进行，否则会出现不同设备不同效果
  例如: TextStyle(color: Colors.black, fontSize: 16px)
  应为: TextStyle(color: Colors.black, fontSize: 16.sp)
- iOS 打包上架AppStore ，由于使用个推SDK（默认带有IDFA）参考 [个推iOS集成一文中关于《IDFA版SDK注意事项说明与选项参考》](https://docs.getui.com/getui/mobile/ios/xcode/) 
- Android打包使用了 --split-per-abi 分别对不同架构进行打包apk，上传各大市场要求VersionCode要一致，flutter 默认不同架构不同标识号，所以要修改
  ```text
  flutter引擎根目录/packages/flutter_tools/gradle/flutter.gradle 的876 行附近可以看到：
  abiVersionCode * 1000 + variant.versionCode
  将其改为统一即可：
  1000 + variant.versionCode
- ``

- 版本号规则与规范:
  版本号分为： 大版本号:小版本号:资源版本号
  大版本号: 当 服务器大版本号 > 当前App的大版本号，App将采用强制更新弹窗策略,不更新边退出
  小版本号: 当 服务器小版本号 > 当前App的小版本号，App采取非强制更新策略, 红点提醒, 不会弹出更新弹窗
  资源版本号: （主用于后续的在线更新版本号预留字段）当 服务器资源版本号 > 当前App的资源版本号，App 不会有任何提醒也不会有红点提示，但是用户手动点击版本更新会弹出提醒。
  修改版本：在项目根目录的 pubspec.yaml 中修改 version 字段，这里的 + 后的数字标识构建号，每次打包可以更改一次，便于在testflight等测试区分包体
  【注】：测试时规则最后一位:奇数为开发环境/偶数为线上环境

项目环境配置与说明:

- Flutter --version 信息：
  ```text
    Flutter 3.0.4 • channel stable • https://github.com/flutter/flutter.git
    Framework • revision 85684f9300 (6 months ago) • 2022-06-30 13:22:47 -0700
    Engine • revision 6ba2af10bb
    Tools • Dart 2.17.5 • DevTools 2.12.2
  ```

- 项目环境配置:
  1. 下载 OpenCV库4.6.0版本的iOS的Framework [下载地址](https://opencv.org/releases/) 到 根目录/custom_libs/mooeli_ffi/ios 下
  

- 注:
  1. 如果Android编译慢卡在-> Running Gradle task 'assembleDebug'... >> 可以参考:[参考链接](https://blog.csdn.net/zhangyiminsunshine/article/details/111137877)

- iOS/Android 最低版本说明:
  - iOS 最低版本定为: iOS 13 (系统最低支持机型：iPhone6s，出产iOS13设备为iPhone11)
    - tflite CoreML支持 iOS 12 及更高版本, 虽然 iOS 13 支持 Core ML 3，但使用 Core ML 2 模型规范进行转换后，该模型的效果可能更好。

  - Android 最低版本定为:  api 29 (Android 10.0)
    - tflite NNAPI 在 API 级别 28 (Android Pie) 及以后的版本上，对运算的支持大有改善;
    - api 28 的 uint8 有重大Bug


Mooeli_ffi 插件:
- 说明
  插件主用于Dart FFI <-> C/C++ 之间直接交互，由于架构于Flutter Plugin因此也支持Method Channel与原生交互的方式。
  - 内置 OpenCV – ver: 4.6.0    [github release website](https://github.com/opencv/opencv/releases/tag/4.6.0)
  - 内置 TFLite – ver: 2.9.1    [github release website](https://github.com/tensorflow/tensorflow/releases/tag/v2.9.1)
  
- 示例代码参考：
  1. import 'package:mooeli_ffi/mooeli_ffi.dart'; //导入
  2. testByFfi(1,2); //测试ffi方式
  3. callMcByFunName("testByMethodChannel").then((params){print(params)}); //测试Method Channel 方式
  
- 编写ffi代码规范与流程： 
  ```text
  【注意!!!!】：
  当创建一个 release 档案（IPA）时，符号会被 Xcode 删除。 
  在 Xcode 中, 点击 Target Runner > Build Settings > Strip Style. 将 All Symbols 修改为 Non-Global Symbols。
  否则会出现上架appstore或者testflight(其实就是产生IPA时，链接手机测试Release都无法测试出问题)，出现如下问题：
  Invalid argument(s): Failed to lookup symbol
  'getAiFacelmgByDatas': disym(RTLD_DEFAULT, getAiFacelmgByDatas):
  symbol n ot found
  ```

  一. 在旧的c++文件中添加原生交互函数
    1. 插件根目录/ios/Classes/MooeliFFI.h 中定义函数 或者 结构体:
  ```c
       typedef struct {
          int height;
          int width;
       }StructTest;
       void test();
  ```
    2. 插件根目录/ios/Classes/MooeliFFI.cpp 中定义函数且使用 DART_API 将函数暴露:
       DART_API void test();
    3. 项目根目录执行 ./runFFI.sh
    4. 插件根目录/lib/mooeli_ffi.dart 中编写业务代码且能使用暴露出的c/c++函数

  二. 新建c++头文件
    1. 添加C/C++与头文件到 插件根目录/ios/Classes/ 插件路径下
    2. 在 插件 .yaml 配置文件中，对ffigen配置添加其新加入的.h头文件路径
    3. 在 插件根目录的/android/CMakeLists.txt 中添加.h头文件路径
    4. 后续同第一种方式步骤...

-关联库配置说明：
  - 配置OpenCV库 [下载地址](https://opencv.org/releases/):
    - iOS: 解压后将 opencv2.framework 框架放在插件根目录/ios/ 目录下即可;
    - Android 解压后:
      - 将OpenCV-android-sdk/sdk/native/jni/include的opencv2文件整个放在插件 android/src/main/include/下
      - 将OpenCV-android-sdk/sdk/native/libs下的文件拷贝到 android/src/main/jniLibs/下

    - 配置TFLite库 [下载TensorFlow源码稳定版编译](https://github.com/tensorflow/tensorflow):
      - iOS:
        1. 将编译好的 TensorFlowLiteC.framework 放在插件根目录/ios/ 目录下即可;
        2. 编译iOS TFLite, 参考链接: [点击查看](https://www.tensorflow.org/lite/guide/build_ios#build_tensorflowlitec_dynamic_framework_recommended)
        3. 参考编译命令：(//非注释也是命令串):
        ```shell
          bazel build --config=ios_fat -c opt --cxxopt=--std=c++17 \
            //tensorflow/lite/ios:TensorFlowLiteC_framework
        ```

      - Android:
        1. 将编译好的 arm64-v8a、armeabi-v7a、x86、x86_64 分别放在插件根目录/android/app/src/main/jniLibs/对应目录下即可
        2. 编译Android TFLite 参考链接：[点击查看参考1](https://github.com/am15h/tflite_flutter_plugin/wiki/Building-Android-and-iOS-binaries)   [点击查看参考2](https://blog.seeso.io/building-tensorflow-lite-c-in-android-1c8de1639e1d)
        3. 参考编译命令:(//非注释也是命令串)
        ```shell
        bazel build -c opt --cxxopt=--std=c++11 --config=android_arm --cpu=armeabi-v7a //tensorflow/lite/c:tensorflowlite_c
        bazel build -c opt --cxxopt=--std=c++11 --config=android_arm64 --cpu=arm64-v8a //tensorflow/lite/c:tensorflowlite_c
        bazel build -c opt --cxxopt=--std=c++11 --config=android_x86 //tensorflow/lite/c:tensorflowlite_c
        bazel build -c opt --cxxopt=--std=c++11 --config=android_x86_64 //tensorflow/lite/c:tensorflowlite_c
        ```  
      
- Apple M1 Build Error ->  Error Regarding undefined method `map' for nil:NilClass for Flutter App / CocoaPod Error: || LoadError - dlopen(/Library/Ruby/Gems/2.6.0/gems/ffi-1.15.5/lib/ffi_c.bundle, 0x0009): tried:
  ```shell
    sudo arch -x86_64 gem install ffi
    # go to ios folder then run
    arch -x86_64 pod install
  ```
  参考链接：[点击查看解决方案](https://stackoverflow.com/questions/67443265/error-regarding-undefined-method-map-for-nilnilclass-for-flutter-app-cocoap)

- 更多详细文档后续补充

[comment]: <> (- [更多详细文档后续补充]&#40;https://e.gitee.com/chohotech/repos/chohotech/mooeli-flutter/sources&#41;)
